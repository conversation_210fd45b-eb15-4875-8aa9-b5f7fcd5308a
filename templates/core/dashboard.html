{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Dashboard" %} | {% trans "ERP System" %}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>{% trans "Dashboard" %}
        </h1>
    </div>
</div>

<!-- Company Info -->
{% if company %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>{% trans "Company Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-3">
                        {% if company.logo %}
                            <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid" style="max-height: 100px;">
                        {% else %}
                            <i class="fas fa-building fa-5x text-secondary"></i>
                        {% endif %}
                    </div>
                    <div class="col-md-9">
                        <h4>{{ company.name }}</h4>
                        <p><strong>{% trans "Code" %}:</strong> {{ company.code }}</p>
                        {% if company.tax_number %}
                            <p><strong>{% trans "Tax Number" %}:</strong> {{ company.tax_number }}</p>
                        {% endif %}
                        {% if company.phone %}
                            <p><strong>{% trans "Phone" %}:</strong> {{ company.phone }}</p>
                        {% endif %}
                        {% if company.email %}
                            <p><strong>{% trans "Email" %}:</strong> {{ company.email }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-code-branch me-2"></i>{% trans "Branch Information" %}
                </h5>
            </div>
            <div class="card-body">
                {% if branch %}
                    <h4>{{ branch.name }}</h4>
                    <p><strong>{% trans "Code" %}:</strong> {{ branch.code }}</p>
                    {% if branch.address %}
                        <p><strong>{% trans "Address" %}:</strong> {{ branch.address }}</p>
                    {% endif %}
                    {% if branch.phone %}
                        <p><strong>{% trans "Phone" %}:</strong> {{ branch.phone }}</p>
                    {% endif %}
                    {% if branch.email %}
                        <p><strong>{% trans "Email" %}:</strong> {{ branch.email }}</p>
                    {% endif %}
                    {% if branch.manager %}
                        <p><strong>{% trans "Manager" %}:</strong> {{ branch.manager }}</p>
                    {% endif %}
                {% else %}
                    <div class="alert alert-warning">
                        {% trans "No branch assigned. Please contact your administrator." %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {% trans "You are not associated with any company. Please contact your administrator." %}
</div>
{% endif %}

<!-- Modules -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="mb-0">
                <i class="fas fa-th-large me-2"></i>{% trans "Available Modules" %}
            </h2>
            <div class="btn-group" role="group" aria-label="{% trans 'View options' %}">
                <button type="button" class="btn btn-outline-primary active" id="grid-view-btn" title="{% trans 'Grid view' %}">
                    <i class="fas fa-th-large" aria-hidden="true"></i>
                    <span class="visually-hidden">{% trans "Grid view" %}</span>
                </button>
                <button type="button" class="btn btn-outline-primary" id="list-view-btn" title="{% trans 'List view' %}">
                    <i class="fas fa-list" aria-hidden="true"></i>
                    <span class="visually-hidden">{% trans "List view" %}</span>
                </button>
            </div>
        </div>
        <hr>
    </div>
</div>

<!-- Module Search and Filter -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="input-group">
            <span class="input-group-text bg-primary text-white">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" id="module-search" class="form-control" placeholder="{% trans 'Search modules...' %}">
        </div>
    </div>
    <div class="col-md-4">
        <select id="module-category" class="form-select" aria-label="{% trans 'Filter by category' %}">
            <option value="all">{% trans "All Categories" %}</option>
            <option value="finance">{% trans "Finance & Accounting" %}</option>
            <option value="sales">{% trans "Sales & Marketing" %}</option>
            <option value="operations">{% trans "Operations" %}</option>
            <option value="hr">{% trans "Human Resources" %}</option>
            <option value="other">{% trans "Other" %}</option>
        </select>
    </div>
</div>

<!-- Grid View (Default) -->
<div id="grid-view" class="row">
    {% for module in modules %}
    <div class="col-md-4 col-lg-3 mb-4 module-card"
         data-name="{{ module.name|lower }}"
         data-code="{{ module.code|lower }}"
         data-category="{% if module.code in 'accounting,finance' %}finance
                       {% elif module.code in 'sales,crm,ecommerce,pos' %}sales
                       {% elif module.code in 'inventory,procurement,manufacturing,project,fleet' %}operations
                       {% elif module.code in 'hr,frontdesk' %}hr
                       {% else %}other{% endif %}">
        <div class="card h-100 shadow-sm module-card-inner module-{{ module.code }}">
            <div class="card-body text-center p-4">
                <div class="module-icon-wrapper mb-3">
                    <i class="{{ module.icon|default:'fas fa-puzzle-piece' }} module-icon"></i>
                </div>
                <h5 class="card-title">
                    {% if LANGUAGE_CODE == 'ar' %}
                        {% with module_code=module.code %}
                            {% if module_code == 'hr' %}الموارد البشرية
                            {% elif module_code == 'inventory' %}المخزون
                            {% elif module_code == 'procurement' %}المشتريات
                            {% elif module_code == 'sales' %}المبيعات
                            {% elif module_code == 'pos' %}نقاط البيع
                            {% elif module_code == 'hotel' %}الفنادق
                            {% elif module_code == 'restaurant' %}المطاعم
                            {% elif module_code == 'accounting' %}الحسابات
                            {% elif module_code == 'finance' %}المالية
                            {% elif module_code == 'manufacturing' %}التصنيع
                            {% elif module_code == 'project' %}المشاريع
                            {% elif module_code == 'fleet' %}الأسطول
                            {% elif module_code == 'document' %}المستندات
                            {% elif module_code == 'crm' %}علاقات العملاء
                            {% elif module_code == 'frontdesk' %}مكتب الاستقبال
                            {% elif module_code == 'ecommerce' %}التجارة الإلكترونية
                            {% elif module_code == 'settings' %}الإعدادات
                            {% else %}{{ module.name }}
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        {{ module.name }}
                    {% endif %}
                </h5>
                <p class="card-text small text-muted">
                    {% if LANGUAGE_CODE == 'ar' %}
                        {% with module_code=module.code %}
                            {% if module_code == 'hr' %}إدارة أهم أصول مؤسستك - موظفيك. تتبع معلومات الموظفين، إدارة الحضور، معالجة الرواتب، والمزيد.
                            {% elif module_code == 'inventory' %}تتبع مستويات المخزون، إدارة المستودعات، وتحسين عمليات المخزون لتحقيق أقصى قدر من الكفاءة.
                            {% elif module_code == 'procurement' %}تبسيط عملية الشراء من الطلب إلى الدفع. إدارة الموردين، تتبع أوامر الشراء، والتحكم في التكاليف.
                            {% elif module_code == 'sales' %}تعزيز أداء المبيعات باستخدام أدوات شاملة لعروض الأسعار والطلبات والفواتير وإدارة العملاء.
                            {% elif module_code == 'pos' %}إدارة عمليات البيع بكفاءة باستخدام نظام نقاط بيع سهل الاستخدام. معالجة المبيعات، تتبع المخزون، وتحليل الأداء.
                            {% elif module_code == 'hotel' %}إدارة عمليات الفندق بكفاءة. التعامل مع الحجوزات وتخصيص الغرف وخدمات الضيوف والفواتير في مكان واحد.
                            {% elif module_code == 'restaurant' %}إدارة أعمال خدمة الطعام بسلاسة. إدارة الطلبات والطاولات وعمليات المطبخ وخدمة العملاء في نظام واحد.
                            {% elif module_code == 'accounting' %}الحفاظ على أموالك منظمة باستخدام أدوات محاسبية شاملة. إدارة دفتر الأستاذ العام والحسابات المدينة والحسابات الدائنة وإعداد التقارير المالية.
                            {% elif module_code == 'finance' %}السيطرة على العمليات المالية الخاصة بك. إدارة الميزانيات والتخطيط المالي والتدفق النقدي والتحليل المالي.
                            {% elif module_code == 'manufacturing' %}تحسين عمليات الإنتاج الخاصة بك. إدارة أوامر الإنتاج وقائمة المواد ومراكز العمل وعمليات التصنيع.
                            {% elif module_code == 'project' %}تسليم المشاريع في الوقت المحدد وضمن الميزانية. تخطيط وتنفيذ ومراقبة المشاريع باستخدام أدوات شاملة لإدارة المهام وتخصيص الموارد وتتبع التقدم.
                            {% elif module_code == 'fleet' %}إدارة أسطول المركبات بكفاءة. تتبع المركبات وجداول الصيانة واستهلاك الوقود وتعيينات السائقين.
                            {% elif module_code == 'document' %}تنظيم وتأمين المستندات الخاصة بك. تخزين وتصنيف وبحث ومشاركة المستندات مع التحكم في الوصول وتتبع الإصدارات.
                            {% elif module_code == 'crm' %}بناء علاقات أقوى مع العملاء. إدارة العملاء المحتملين والفرص واتصالات العملاء وخط أنابيب المبيعات في نظام متكامل واحد.
                            {% elif module_code == 'frontdesk' %}تبسيط عمليات الاستقبال الخاصة بك. إدارة تسجيل وصول الزوار والمواعيد ومهام المكتب الأمامي بكفاءة.
                            {% elif module_code == 'ecommerce' %}توسيع أعمالك عبر الإنترنت. إدارة متجرك عبر الإنترنت والمنتجات والطلبات وحسابات العملاء باستخدام أدوات التجارة الإلكترونية المتكاملة.
                            {% elif module_code == 'settings' %}تكوين النظام الخاص بك ليتناسب مع احتياجات عملك. إدارة المستخدمين والأذونات ومعلومات الشركة وتفضيلات النظام.
                            {% else %}{{ module.description|truncatechars:80 }}
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        {{ module.description|truncatechars:80 }}
                    {% endif %}
                </p>
            </div>
            <div class="card-footer bg-transparent border-top-0 text-center pb-3">
                <a href="{% url 'modules:module_dashboard' module_code=module.code %}" class="btn btn-sm module-btn" title="{% trans 'Open' %} {{ module.name }}">
                    <i class="fas fa-external-link-alt me-1" aria-hidden="true"></i>{% trans "Open" %}
                </a>
            </div>
        </div>
    </div>
    {% empty %}
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "No modules available. Please contact your administrator to install modules." %}
        </div>
    </div>
    {% endfor %}
</div>

<!-- List View (Hidden by default) -->
<div id="list-view" class="row d-none">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th scope="col" width="50">#</th>
                                <th scope="col" width="50">{% trans "Icon" %}</th>
                                <th scope="col">{% trans "Name" %}</th>
                                <th scope="col">{% trans "Description" %}</th>
                                <th scope="col" width="100">{% trans "Version" %}</th>
                                <th scope="col" width="100">{% trans "Action" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for module in modules %}
                            <tr class="module-row"
                                data-name="{{ module.name|lower }}"
                                data-code="{{ module.code|lower }}"
                                data-category="{% if module.code in 'accounting,finance' %}finance
                                            {% elif module.code in 'sales,crm,ecommerce,pos' %}sales
                                            {% elif module.code in 'inventory,procurement,manufacturing,project,fleet' %}operations
                                            {% elif module.code in 'hr,frontdesk' %}hr
                                            {% else %}other{% endif %}">
                                <td class="align-middle">{{ forloop.counter }}</td>
                                <td class="align-middle text-center">
                                    <div class="module-{{ module.code }}">
                                        <i class="{{ module.icon|default:'fas fa-puzzle-piece' }} module-icon"></i>
                                    </div>
                                </td>
                                <td class="align-middle"><strong>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        {% with module_code=module.code %}
                                            {% if module_code == 'hr' %}الموارد البشرية
                                            {% elif module_code == 'inventory' %}المخزون
                                            {% elif module_code == 'procurement' %}المشتريات
                                            {% elif module_code == 'sales' %}المبيعات
                                            {% elif module_code == 'pos' %}نقاط البيع
                                            {% elif module_code == 'hotel' %}الفنادق
                                            {% elif module_code == 'restaurant' %}المطاعم
                                            {% elif module_code == 'accounting' %}الحسابات
                                            {% elif module_code == 'finance' %}المالية
                                            {% elif module_code == 'manufacturing' %}التصنيع
                                            {% elif module_code == 'project' %}المشاريع
                                            {% elif module_code == 'fleet' %}الأسطول
                                            {% elif module_code == 'document' %}المستندات
                                            {% elif module_code == 'crm' %}علاقات العملاء
                                            {% elif module_code == 'frontdesk' %}مكتب الاستقبال
                                            {% elif module_code == 'ecommerce' %}التجارة الإلكترونية
                                            {% elif module_code == 'settings' %}الإعدادات
                                            {% else %}{{ module.name }}
                                            {% endif %}
                                        {% endwith %}
                                    {% else %}
                                        {{ module.name }}
                                    {% endif %}
                                </strong></td>
                                <td class="align-middle">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                        {% with module_code=module.code %}
                                            {% if module_code == 'hr' %}إدارة أهم أصول مؤسستك - موظفيك. تتبع معلومات الموظفين، إدارة الحضور، معالجة الرواتب، والمزيد.
                                            {% elif module_code == 'inventory' %}تتبع مستويات المخزون، إدارة المستودعات، وتحسين عمليات المخزون لتحقيق أقصى قدر من الكفاءة.
                                            {% elif module_code == 'procurement' %}تبسيط عملية الشراء من الطلب إلى الدفع. إدارة الموردين، تتبع أوامر الشراء، والتحكم في التكاليف.
                                            {% elif module_code == 'sales' %}تعزيز أداء المبيعات باستخدام أدوات شاملة لعروض الأسعار والطلبات والفواتير وإدارة العملاء.
                                            {% elif module_code == 'pos' %}إدارة عمليات البيع بكفاءة باستخدام نظام نقاط بيع سهل الاستخدام. معالجة المبيعات، تتبع المخزون، وتحليل الأداء.
                                            {% elif module_code == 'hotel' %}إدارة عمليات الفندق بكفاءة. التعامل مع الحجوزات وتخصيص الغرف وخدمات الضيوف والفواتير في مكان واحد.
                                            {% elif module_code == 'restaurant' %}إدارة أعمال خدمة الطعام بسلاسة. إدارة الطلبات والطاولات وعمليات المطبخ وخدمة العملاء في نظام واحد.
                                            {% elif module_code == 'accounting' %}الحفاظ على أموالك منظمة باستخدام أدوات محاسبية شاملة. إدارة دفتر الأستاذ العام والحسابات المدينة والحسابات الدائنة وإعداد التقارير المالية.
                                            {% elif module_code == 'finance' %}السيطرة على العمليات المالية الخاصة بك. إدارة الميزانيات والتخطيط المالي والتدفق النقدي والتحليل المالي.
                                            {% elif module_code == 'manufacturing' %}تحسين عمليات الإنتاج الخاصة بك. إدارة أوامر الإنتاج وقائمة المواد ومراكز العمل وعمليات التصنيع.
                                            {% elif module_code == 'project' %}تسليم المشاريع في الوقت المحدد وضمن الميزانية. تخطيط وتنفيذ ومراقبة المشاريع باستخدام أدوات شاملة لإدارة المهام وتخصيص الموارد وتتبع التقدم.
                                            {% elif module_code == 'fleet' %}إدارة أسطول المركبات بكفاءة. تتبع المركبات وجداول الصيانة واستهلاك الوقود وتعيينات السائقين.
                                            {% elif module_code == 'document' %}تنظيم وتأمين المستندات الخاصة بك. تخزين وتصنيف وبحث ومشاركة المستندات مع التحكم في الوصول وتتبع الإصدارات.
                                            {% elif module_code == 'crm' %}بناء علاقات أقوى مع العملاء. إدارة العملاء المحتملين والفرص واتصالات العملاء وخط أنابيب المبيعات في نظام متكامل واحد.
                                            {% elif module_code == 'frontdesk' %}تبسيط عمليات الاستقبال الخاصة بك. إدارة تسجيل وصول الزوار والمواعيد ومهام المكتب الأمامي بكفاءة.
                                            {% elif module_code == 'ecommerce' %}توسيع أعمالك عبر الإنترنت. إدارة متجرك عبر الإنترنت والمنتجات والطلبات وحسابات العملاء باستخدام أدوات التجارة الإلكترونية المتكاملة.
                                            {% elif module_code == 'settings' %}تكوين النظام الخاص بك ليتناسب مع احتياجات عملك. إدارة المستخدمين والأذونات ومعلومات الشركة وتفضيلات النظام.
                                            {% else %}{{ module.description|truncatechars:100 }}
                                            {% endif %}
                                        {% endwith %}
                                    {% else %}
                                        {{ module.description|truncatechars:100 }}
                                    {% endif %}
                                </td>
                                <td class="align-middle">{{ module.version }}</td>
                                <td class="align-middle">
                                    <a href="{% url 'modules:module_dashboard' module_code=module.code %}" class="btn btn-sm module-{{ module.code }} module-btn" title="{% trans 'Open' %} {{ module.name }}">
                                        <i class="fas fa-external-link-alt" aria-hidden="true"></i>
                                        <span class="visually-hidden">{% trans "Open" %} {{ module.name }}</span>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Module Dashboard Template (Hidden) -->
<div id="module-dashboard-template" class="d-none">
    <div class="module-dashboard">
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item active module-name" aria-current="page"></li>
                    </ol>
                </nav>
                <h2 class="module-title mb-3"></h2>
                <p class="module-description text-muted"></p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 mb-4">
                <!-- Sidebar Navigation -->
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0 module-sidebar-title"></h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush module-sidebar-menu">
                            <!-- Menu items will be added dynamically -->
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <!-- Module Content -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="module-alert-container"></div>

                        <div class="alert alert-light border mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            {% trans "This is a placeholder for the module content. The actual implementation will be done in future updates." %}
                        </div>

                        <!-- Stats Cards -->
                        <div class="row mb-4 module-stats">
                            <!-- Stat cards will be added dynamically -->
                        </div>

                        <!-- Charts -->
                        <div class="row mb-4 module-charts">
                            <!-- Charts will be added dynamically -->
                        </div>

                        <!-- Recent Activity -->
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="card-title mb-0">{% trans "Recent Activity" %}</h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-secondary">
                                    <i class="fas fa-clock me-2"></i>
                                    {% trans "No recent activity found." %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for Module Dashboard -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // View switching
    const gridViewBtn = document.getElementById('grid-view-btn');
    const listViewBtn = document.getElementById('list-view-btn');
    const gridView = document.getElementById('grid-view');
    const listView = document.getElementById('list-view');

    gridViewBtn.addEventListener('click', function() {
        gridView.classList.remove('d-none');
        listView.classList.add('d-none');
        gridViewBtn.classList.add('active');
        listViewBtn.classList.remove('active');
    });

    listViewBtn.addEventListener('click', function() {
        gridView.classList.add('d-none');
        listView.classList.remove('d-none');
        gridViewBtn.classList.remove('active');
        listViewBtn.classList.add('active');
    });

    // Module search
    const moduleSearch = document.getElementById('module-search');
    const moduleCategory = document.getElementById('module-category');
    const moduleCards = document.querySelectorAll('.module-card');
    const moduleRows = document.querySelectorAll('.module-row');

    function filterModules() {
        const searchTerm = moduleSearch.value.toLowerCase();
        const category = moduleCategory.value;

        // Filter cards
        moduleCards.forEach(card => {
            const name = card.dataset.name;
            const code = card.dataset.code;
            const moduleCategory = card.dataset.category;

            const matchesSearch = name.includes(searchTerm) || code.includes(searchTerm);
            const matchesCategory = category === 'all' || moduleCategory === category;

            if (matchesSearch && matchesCategory) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        // Filter rows
        moduleRows.forEach(row => {
            const name = row.dataset.name;
            const code = row.dataset.code;
            const moduleCategory = row.dataset.category;

            const matchesSearch = name.includes(searchTerm) || code.includes(searchTerm);
            const matchesCategory = category === 'all' || moduleCategory === category;

            if (matchesSearch && matchesCategory) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    moduleSearch.addEventListener('input', filterModules);
    moduleCategory.addEventListener('change', filterModules);

    // Check for module parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const moduleParam = urlParams.get('module');

    if (moduleParam) {
        showModuleDashboard(moduleParam);
    }

    // We no longer need to handle module cards click here as they now link directly to module dashboards

    function showModuleDashboard(moduleCode) {
        // Find the module data
        const moduleCard = document.querySelector(`.module-card[data-code="${moduleCode}"]`);
        if (!moduleCard) return;

        // Get module name from the card
        const moduleName = moduleCard.querySelector('.card-title').textContent;
        const moduleDescription = moduleCard.querySelector('.card-text').textContent;
        const moduleIcon = moduleCard.querySelector('.module-icon').className;

        // Clone the template
        const template = document.getElementById('module-dashboard-template');
        const dashboard = template.querySelector('.module-dashboard').cloneNode(true);

        // Update the dashboard content
        dashboard.querySelector('.module-name').textContent = moduleName;
        dashboard.querySelector('.module-title').innerHTML = `<i class="${moduleIcon} me-2"></i>${moduleName}`;
        dashboard.querySelector('.module-description').textContent = moduleDescription;
        dashboard.querySelector('.module-sidebar-title').textContent = moduleName;

        // Load module alert
        const alertContainer = dashboard.querySelector('.module-alert-container');
        fetch(`/static/alerts/${moduleCode}.html`)
            .then(response => {
                if (!response.ok) {
                    return `
                        <div class="alert alert-info alert-dismissible fade show module-alert">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <i class="fas fa-info-circle fa-2x text-info"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">${moduleName}</h5>
                                    <p>${moduleDescription}</p>
                                </div>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;
                }
                return response.text();
            })
            .then(html => {
                alertContainer.innerHTML = html;
            });

        // Generate sidebar menu based on module code
        const sidebarMenu = dashboard.querySelector('.module-sidebar-menu');
        generateSidebarMenu(sidebarMenu, moduleCode);

        // Generate stats cards based on module code
        const statsContainer = dashboard.querySelector('.module-stats');
        generateStatsCards(statsContainer, moduleCode);

        // Hide the module grid/list and show the dashboard
        document.getElementById('grid-view').classList.add('d-none');
        document.getElementById('list-view').classList.add('d-none');
        document.querySelector('.row:has(#grid-view-btn)').classList.add('d-none');
        document.querySelector('.row:has(#module-search)').classList.add('d-none');

        // Remove any existing dashboard
        const existingDashboard = document.querySelector('.module-dashboard');
        if (existingDashboard) {
            existingDashboard.remove();
        }

        // Add the dashboard to the page
        template.parentNode.insertBefore(dashboard, template);
        dashboard.classList.remove('d-none');
    }

    function generateSidebarMenu(container, moduleCode) {
        // Define menu items based on module code
        let menuItems = [];

        switch(moduleCode) {
            case 'hr':
                menuItems = [
                    { icon: 'fas fa-users', text: '{% trans "Employees" %}' },
                    { icon: 'fas fa-sitemap', text: '{% trans "Departments" %}' },
                    { icon: 'fas fa-file-contract', text: '{% trans "Contracts" %}' },
                    { icon: 'fas fa-money-bill-wave', text: '{% trans "Payroll" %}' },
                    { icon: 'fas fa-calendar-check', text: '{% trans "Attendance" %}' },
                    { icon: 'fas fa-plane-departure', text: '{% trans "Leave Management" %}' },
                    { icon: 'fas fa-chart-line', text: '{% trans "Reports" %}' }
                ];
                break;
            case 'inventory':
                menuItems = [
                    { icon: 'fas fa-warehouse', text: '{% trans "Warehouses" %}' },
                    { icon: 'fas fa-boxes', text: '{% trans "Products" %}' },
                    { icon: 'fas fa-exchange-alt', text: '{% trans "Stock Movements" %}' },
                    { icon: 'fas fa-clipboard-check', text: '{% trans "Inventory Adjustments" %}' },
                    { icon: 'fas fa-barcode', text: '{% trans "Barcodes" %}' },
                    { icon: 'fas fa-chart-line', text: '{% trans "Reports" %}' }
                ];
                break;
            case 'procurement':
                menuItems = [
                    { icon: 'fas fa-file-alt', text: '{% trans "Purchase Requests" %}' },
                    { icon: 'fas fa-file-invoice-dollar', text: '{% trans "Quotations" %}' },
                    { icon: 'fas fa-shopping-cart', text: '{% trans "Purchase Orders" %}' },
                    { icon: 'fas fa-truck', text: '{% trans "Vendors" %}' },
                    { icon: 'fas fa-chart-line', text: '{% trans "Reports" %}' }
                ];
                break;
            case 'sales':
                menuItems = [
                    { icon: 'fas fa-users', text: '{% trans "Customers" %}' },
                    { icon: 'fas fa-file-invoice-dollar', text: '{% trans "Quotations" %}' },
                    { icon: 'fas fa-file-alt', text: '{% trans "Orders" %}' },
                    { icon: 'fas fa-file-invoice', text: '{% trans "Invoices" %}' },
                    { icon: 'fas fa-chart-line', text: '{% trans "Reports" %}' }
                ];
                break;
            // Add more cases for other modules
            default:
                menuItems = [
                    { icon: 'fas fa-tachometer-alt', text: '{% trans "Overview" %}' },
                    { icon: 'fas fa-cog', text: '{% trans "Settings" %}' },
                    { icon: 'fas fa-chart-line', text: '{% trans "Reports" %}' }
                ];
        }

        // Create menu items
        menuItems.forEach(item => {
            const link = document.createElement('a');
            link.href = '#';
            link.className = 'list-group-item list-group-item-action';
            link.innerHTML = `<i class="${item.icon} me-2"></i> ${item.text}`;
            container.appendChild(link);
        });
    }

    function generateStatsCards(container, moduleCode) {
        // Define stats cards based on module code
        let statsCards = [];

        switch(moduleCode) {
            case 'hr':
                statsCards = [
                    { icon: 'fas fa-users', title: '{% trans "Total Employees" %}', value: '0', color: 'primary' },
                    { icon: 'fas fa-user-clock', title: '{% trans "On Leave" %}', value: '0', color: 'warning' },
                    { icon: 'fas fa-money-bill-wave', title: '{% trans "Payroll This Month" %}', value: '$0', color: 'success' },
                    { icon: 'fas fa-calendar-check', title: '{% trans "Attendance Rate" %}', value: '0%', color: 'info' }
                ];
                break;
            case 'inventory':
                statsCards = [
                    { icon: 'fas fa-boxes', title: '{% trans "Total Products" %}', value: '0', color: 'primary' },
                    { icon: 'fas fa-exclamation-triangle', title: '{% trans "Low Stock Items" %}', value: '0', color: 'warning' },
                    { icon: 'fas fa-warehouse', title: '{% trans "Warehouses" %}', value: '0', color: 'success' },
                    { icon: 'fas fa-exchange-alt', title: '{% trans "Movements Today" %}', value: '0', color: 'info' }
                ];
                break;
            // Add more cases for other modules
            default:
                statsCards = [
                    { icon: 'fas fa-chart-line', title: '{% trans "Total" %}', value: '0', color: 'primary' },
                    { icon: 'fas fa-clock', title: '{% trans "Pending" %}', value: '0', color: 'warning' },
                    { icon: 'fas fa-check-circle', title: '{% trans "Completed" %}', value: '0', color: 'success' },
                    { icon: 'fas fa-calendar-day', title: '{% trans "Today" %}', value: '0', color: 'info' }
                ];
        }

        // Create stats cards
        statsCards.forEach(card => {
            const col = document.createElement('div');
            col.className = 'col-md-3 col-sm-6 mb-4';
            col.innerHTML = `
                <div class="card border-${card.color} h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <div class="bg-${card.color} text-white rounded p-3">
                                    <i class="${card.icon} fa-2x"></i>
                                </div>
                            </div>
                            <div>
                                <h6 class="card-subtitle text-muted">${card.title}</h6>
                                <h3 class="card-title mb-0">${card.value}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.appendChild(col);
        });
    }
});
</script>
{% endblock %}
