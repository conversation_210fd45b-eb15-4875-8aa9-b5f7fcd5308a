{% load i18n %}

{% if module_code == 'hr' %}
<div class="alert alert-info alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-info-circle fa-2x text-info"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Human Resources Management" %}</h5>
            <p>{% trans "Manage your organization's most valuable asset - your people. Track employee information, manage attendance, handle payroll, and more." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'inventory' %}
<div class="alert alert-success alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-boxes fa-2x text-success"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Inventory Management" %}</h5>
            <p>{% trans "Keep track of your stock levels, manage warehouses, and optimize inventory operations for maximum efficiency." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'procurement' %}
<div class="alert alert-primary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-shopping-cart fa-2x text-primary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Procurement Management" %}</h5>
            <p>{% trans "Streamline your purchasing process from requisition to payment. Manage vendors, track purchase orders, and control costs." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'sales' %}
<div class="alert alert-warning alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-chart-line fa-2x text-warning"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Sales Management" %}</h5>
            <p>{% trans "Boost your sales performance with comprehensive tools for quotations, orders, invoicing, and customer management." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'pos' %}
<div class="alert alert-danger alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-cash-register fa-2x text-danger"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Point of Sale" %}</h5>
            <p>{% trans "Manage your retail operations with an easy-to-use point of sale system. Process sales, track inventory, and analyze performance." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'hotel' %}
<div class="alert alert-info alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-hotel fa-2x text-info"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Hotel Management" %}</h5>
            <p>{% trans "Manage your hotel operations efficiently. Handle reservations, room assignments, guest services, and billing in one place." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'restaurant' %}
<div class="alert alert-warning alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-utensils fa-2x text-warning"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Restaurant & Cafe Management" %}</h5>
            <p>{% trans "Run your food service business smoothly. Manage orders, tables, kitchen operations, and customer service all in one system." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'accounting' %}
<div class="alert alert-success alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-calculator fa-2x text-success"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Accounting Management" %}</h5>
            <p>{% trans "Keep your finances in order with comprehensive accounting tools. Manage general ledger, accounts receivable, accounts payable, and financial reporting." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'finance' %}
<div class="alert alert-primary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-money-bill-wave fa-2x text-primary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Financial Management" %}</h5>
            <p>{% trans "Take control of your financial operations. Manage budgets, financial planning, cash flow, and financial analysis." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'manufacturing' %}
<div class="alert alert-secondary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-industry fa-2x text-secondary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Manufacturing Management" %}</h5>
            <p>{% trans "Optimize your production processes. Manage production orders, bill of materials, work centers, and manufacturing operations." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'project' %}
<div class="alert alert-info alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-project-diagram fa-2x text-info"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Project Management" %}</h5>
            <p>{% trans "Deliver projects on time and within budget. Plan, execute, and monitor projects with comprehensive tools for task management, resource allocation, and progress tracking." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'fleet' %}
<div class="alert alert-dark alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-truck fa-2x text-dark"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Fleet Management" %}</h5>
            <p>{% trans "Manage your vehicle fleet efficiently. Track vehicles, maintenance schedules, fuel consumption, and driver assignments." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'document' %}
<div class="alert alert-secondary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-file-archive fa-2x text-secondary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Document Management" %}</h5>
            <p>{% trans "Organize and secure your documents. Store, categorize, search, and share documents with controlled access and version tracking." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'crm' %}
<div class="alert alert-warning alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-handshake fa-2x text-warning"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Customer Relationship Management" %}</h5>
            <p>{% trans "Build stronger customer relationships. Manage leads, opportunities, customer communications, and sales pipeline in one integrated system." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'frontdesk' %}
<div class="alert alert-primary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-desktop fa-2x text-primary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Front Desk Management" %}</h5>
            <p>{% trans "Streamline your reception operations. Manage visitor check-ins, appointments, and front office tasks efficiently." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'ecommerce' %}
<div class="alert alert-danger alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-shopping-bag fa-2x text-danger"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "E-Commerce Management" %}</h5>
            <p>{% trans "Expand your business online. Manage your online store, products, orders, and customer accounts with integrated e-commerce tools." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>

{% elif module_code == 'settings' %}
<div class="alert alert-dark alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-cogs fa-2x text-dark"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "System Settings" %}</h5>
            <p>{% trans "Configure your system to match your business needs. Manage users, permissions, company information, and system preferences." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endif %}
