<!DOCTYPE html>
{% load i18n %}
{% load static %}
<html lang="{{ LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS for printing -->
    <style>
        /* General Styles */
        body {
            font-family: 'Roboto', sans-serif;
            font-size: 14pt;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }

        html[lang="ar"] body {
            font-family: 'Tajawal', sans-serif;
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 500;
            margin-bottom: 1rem;
        }

        /* Print Specific Styles */
        @page {
            size: A4;
            margin: 1.5cm 1cm;
        }

        @media print {
            body {
                font-size: 12pt;
                line-height: 1.5;
            }

            .d-print-none {
                display: none !important;
            }

            .container {
                width: 100%;
                max-width: 100%;
                padding: 0;
            }

            .card {
                border: 1px solid #ddd;
                margin-bottom: 20px;
                break-inside: avoid;
                page-break-inside: avoid;
            }

            .card-header {
                background-color: #f8f9fa !important;
                color: #000 !important;
                padding: 10px 15px;
                font-weight: 500;
            }

            .table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 1rem;
                page-break-inside: auto;
            }

            .table th {
                background-color: #f8f9fa !important;
                font-weight: 500;
                text-align: left;
                vertical-align: middle;
            }

            html[lang="ar"] .table th {
                text-align: right;
            }

            .table th, .table td {
                border: 1px solid #ddd;
                padding: 8px;
                page-break-inside: avoid;
                line-height: 1.4;
            }

            .table tr {
                page-break-inside: avoid;
            }

            .badge {
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: normal;
                display: inline-block;
            }

            .badge.bg-success {
                background-color: #28a745 !important;
                color: #fff !important;
            }

            .badge.bg-secondary {
                background-color: #6c757d !important;
                color: #fff !important;
            }

            .print-footer {
                margin-top: 30px;
                font-size: 10pt;
                color: #6c757d;
                text-align: center;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
            }

            .company-logo, .branch-header, .branch-footer {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
            }

            .branch-header {
                max-height: 150px;
                margin-bottom: 20px;
            }

            .branch-footer {
                max-height: 100px;
                margin-top: 20px;
            }

            /* Avoid page breaks inside important sections */
            .row {
                page-break-inside: avoid;
            }

            /* Page break before main sections */
            .page-break-before {
                page-break-before: always;
            }

            /* Improve table appearance */
            .table-bordered {
                border: 1px solid #dee2e6;
            }

            /* Improve text readability */
            p, li, td, th {
                orphans: 3;
                widows: 3;
            }

            /* Print background colors and images */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }
        }

        /* RTL Support */
        html[lang="ar"] {
            direction: rtl;
            text-align: right;
        }

        html[lang="ar"] .ms-2 {
            margin-right: 0.5rem !important;
            margin-left: 0 !important;
        }

        html[lang="ar"] .me-1 {
            margin-left: 0.25rem !important;
            margin-right: 0 !important;
        }

        /* Additional styles for the print view */
        .print-container {
            max-width: 1140px;
            margin: 0 auto;
            padding: 20px 0;
        }

        .company-logo, .branch-header, .branch-footer {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
        }

        .branch-header {
            max-height: 150px;
            margin-bottom: 20px;
        }

        .branch-footer {
            max-height: 100px;
            margin-top: 20px;
        }

        /* Print button styling */
        .print-actions {
            margin: 30px 0;
            text-align: center;
        }

        .print-actions .btn {
            padding: 10px 20px;
            font-size: 16px;
            margin: 0 5px;
        }

        /* Document header styling */
        .document-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .document-header h2 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .document-header h3 {
            font-size: 20px;
            font-weight: 500;
            color: #007bff;
            margin-bottom: 5px;
        }

        .document-header h5 {
            font-size: 16px;
            font-weight: 400;
            color: #6c757d;
        }

        /* Table improvements */
        .table th {
            background-color: #f8f9fa;
            font-weight: 500;
        }

        /* Card improvements */
        .card {
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #f8f9fa;
            padding: 12px 15px;
            font-weight: 500;
        }

        .card-body {
            padding: 15px;
        }
    </style>
</head>
<body>
    {% block content %}{% endblock %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
