{% extends 'print_base.html' %}
{% load i18n %}

{% block title %}{% if LANGUAGE_CODE == 'ar' %}طباعة بيانات الفرع{% else %}{% trans "Branch Information Print" %}{% endif %} - {{ branch.name }}{% endblock %}

{% block content %}
<div class="container mt-4 print-container">
    {% if branch.header_image %}
    <div class="row mb-4">
        <div class="col-12 text-center">
            <img src="{{ branch.header_image.url }}" alt="{% if LANGUAGE_CODE == 'ar' %}ترويسة الفرع{% else %}{% trans 'Branch Header' %}{% endif %}" class="img-fluid branch-header">
        </div>
    </div>
    {% endif %}

    <div class="document-header">
        <h2>{% if LANGUAGE_CODE == 'ar' %}بيانات الفرع{% else %}{% trans "Branch Information" %}{% endif %}</h2>
        <h3>{{ branch.name }}</h3>
        <h5>{{ branch.company.name }}</h5>
        <div class="mt-2 text-muted">
            {% if branch.code %}
            <span class="me-3">{% if LANGUAGE_CODE == 'ar' %}رمز الفرع: {{ branch.code }}{% else %}{% trans "Branch Code" %}: {{ branch.code }}{% endif %}</span>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات أساسية{% else %}{% trans "Basic Information" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th width="40%">{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %}</th>
                                <td>{{ branch.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رمز الفرع{% else %}{% trans "Branch Code" %}{% endif %}</th>
                                <td>{{ branch.code }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                <td>{{ branch.company.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</th>
                                <td>{{ branch.manager|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <td>
                                    {% if branch.active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات الاتصال{% else %}{% trans "Contact Information" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th width="40%">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                                <td>{{ branch.address|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <td>{{ branch.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <td>{{ branch.email|default:"-" }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {% if branch.users.exists %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}مستخدمو الفرع{% else %}{% trans "Branch Users" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in branch.users.all %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.get_full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% if branch.footer_image %}
    <div class="row mt-4">
        <div class="col-12 text-center">
            <img src="{{ branch.footer_image.url }}" alt="{% if LANGUAGE_CODE == 'ar' %}تذييل الفرع{% else %}{% trans 'Branch Footer' %}{% endif %}" class="img-fluid branch-footer">
        </div>
    </div>
    {% endif %}

    <div class="row mt-4 print-footer">
        <div class="col-12 text-center">
            <p class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                تم إنشاء هذا التقرير في {{ now|date:"Y-m-d H:i" }}
                {% else %}
                {% trans "This report was generated on" %} {{ now|date:"Y-m-d H:i" }}
                {% endif %}
            </p>
        </div>
    </div>
</div>

<div class="print-actions d-print-none">
    <button type="button" onclick="window.print()" class="btn btn-primary">
        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'companies:branch_list' %}" class="btn btn-secondary ms-2">
        <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
    </a>
</div>
{% endblock %}
