{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة الشركات{% else %}{% trans "Company Management" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة الشركات{% else %}{% trans "Company List" %}{% endif %}</h5>
                {% if perms.companies.add_company %}
                <a href="{% url 'companies:company_create' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة شركة جديدة{% else %}{% trans "Add New Company" %}{% endif %}
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if companies %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشعار{% else %}{% trans "Logo" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم الضريبي{% else %}{% trans "Tax Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم السجل التجاري{% else %}{% trans "Commercial Register" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for company in companies %}
                            <tr>
                                <td>
                                    {% if company.logo %}
                                    <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-thumbnail" width="50">
                                    {% else %}
                                    <i class="fas fa-building fa-2x text-secondary"></i>
                                    {% endif %}
                                </td>
                                <td>{{ company.name }}</td>
                                <td>{{ company.code }}</td>
                                <td>{{ company.tax_number|default:"-" }}</td>
                                <td>{{ company.commercial_register|default:"-" }}</td>
                                <td>{{ company.phone|default:"-" }}</td>
                                <td>{{ company.email|default:"-" }}</td>
                                <td>
                                    {% if company.active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perms.companies.view_company %}
                                    <a href="{% url 'companies:company_print' company.id %}" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans 'Print' %}{% endif %}" target="_blank">
                                        <i class="fas fa-print"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans 'Print' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                    {% if perms.companies.change_company %}
                                    <a href="{% url 'companies:company_edit' company.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                    {% if perms.companies.delete_company %}
                                    <a href="{% url 'companies:company_delete' company.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد شركات حتى الآن. انقر على "إضافة شركة جديدة" لإنشاء أول شركة.{% else %}{% trans "No companies yet. Click 'Add New Company' to create your first company." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات مفيدة{% else %}{% trans "Useful Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    يمكنك إدارة الشركات في النظام من هذه الصفحة. كل شركة يمكن أن يكون لها عدة فروع وإعدادات خاصة بها.
                    {% else %}
                    {% trans "You can manage companies in the system from this page. Each company can have multiple branches and its own settings." %}
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الشركات والفروع{% else %}{% trans "Companies and Branches" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <p>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    النظام يدعم هيكل متعدد الشركات، حيث يمكن إدارة عدة شركات في نفس النظام. كل شركة يمكن أن يكون لها عدة فروع.
                                    {% else %}
                                    {% trans "The system supports a multi-company structure, where multiple companies can be managed in the same system. Each company can have multiple branches." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'companies:branch_list' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-sitemap me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الفروع{% else %}{% trans "Manage Branches" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إعدادات الشركة{% else %}{% trans "Company Settings" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <p>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    يمكنك تخصيص إعدادات كل شركة بشكل منفصل، بما في ذلك الإعدادات المالية والضريبية وإعدادات النظام الأخرى.
                                    {% else %}
                                    {% trans "You can customize settings for each company separately, including financial and tax settings and other system settings." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'core:settings:company_settings_list' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-cogs me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعدادات الشركة{% else %}{% trans "Company Settings" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
