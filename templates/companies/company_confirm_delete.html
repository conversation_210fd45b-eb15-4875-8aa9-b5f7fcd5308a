{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}حذف الشركة{% else %}{% trans "Delete Company" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تأكيد الحذف{% else %}{% trans "Confirm Deletion" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد حذف الشركة "{{ company.name }}"؟ هذا الإجراء لا يمكن التراجع عنه.
                    {% else %}
                    {% trans "Are you sure you want to delete the company" %} "{{ company.name }}"? {% trans "This action cannot be undone." %}
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}تفاصيل الشركة:{% else %}{% trans "Company Details:" %}{% endif %}</h6>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">{% if LANGUAGE_CODE == 'ar' %}اسم الشركة{% else %}{% trans "Company Name" %}{% endif %}</th>
                            <td>{{ company.name }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                            <td>{{ company.code }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الرقم الضريبي{% else %}{% trans "Tax Number" %}{% endif %}</th>
                            <td>{{ company.tax_number|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}عدد الفروع{% else %}{% trans "Branch Count" %}{% endif %}</th>
                            <td>{{ company.branches.count }}</td>
                        </tr>
                    </table>
                </div>
                
                {% if company.branches.exists %}
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تحذير: هناك {{ company.branches.count }} فرع مرتبط بهذه الشركة. حذف الشركة سيؤدي إلى حذف جميع الفروع المرتبطة بها.
                    {% else %}
                    {% trans "Warning: There are" %} {{ company.branches.count }} {% trans "branches associated with this company. Deleting the company will also delete all associated branches." %}
                    {% endif %}
                </div>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'companies:company_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if company.branches.exists %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الفروع المتأثرة{% else %}{% trans "Affected Branches" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for branch in company.branches.all %}
                            <tr>
                                <td>{{ branch.name }}</td>
                                <td>{{ branch.code }}</td>
                                <td>{{ branch.address|default:"-" }}</td>
                                <td>{{ branch.phone|default:"-" }}</td>
                                <td>{{ branch.manager|default:"-" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
