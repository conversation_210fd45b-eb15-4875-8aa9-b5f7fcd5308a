{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if branch %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل الفرع{% else %}{% trans "Edit Branch" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة فرع جديد{% else %}{% trans "Add New Branch" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if branch %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل الفرع: {{ branch.name }}{% else %}{% trans "Edit Branch" %}: {{ branch.name }}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إضافة فرع جديد{% else %}{% trans "Add New Branch" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات أساسية{% else %}{% trans "Basic Information" %}{% endif %}</h6>

                            <div class="mb-3">
                                <label for="{{ form.company.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.company }}
                                {% if form.company.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.company.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رمز الفرع{% else %}{% trans "Branch Code" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    رمز فريد للفرع داخل الشركة، يستخدم في التقارير والمستندات.
                                    {% else %}
                                    {% trans "A unique code for the branch within the company, used in reports and documents." %}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                {{ form.active }}
                                <label class="form-check-label" for="{{ form.active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات الاتصال{% else %}{% trans "Contact Information" %}{% endif %}</h6>

                            <div class="mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.manager.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</label>
                                {{ form.manager }}
                                {% if form.manager.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.manager.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}صور المستندات{% else %}{% trans "Document Images" %}{% endif %}</h6>
                            <p class="text-muted mb-4">
                                {% if LANGUAGE_CODE == 'ar' %}
                                هذه الصور ستستخدم في ترويسة وتذييل المستندات والتقارير الخاصة بهذا الفرع.
                                {% else %}
                                {% trans "These images will be used in the header and footer of documents and reports for this branch." %}
                                {% endif %}
                            </p>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.header_image.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صورة الترويسة{% else %}{% trans "Header Image" %}{% endif %}</label>
                                        {{ form.header_image }}
                                        {% if form.header_image.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.header_image.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="form-text">
                                            {% if LANGUAGE_CODE == 'ar' %}
                                            صورة ستظهر في أعلى المستندات والتقارير (الحجم المثالي: 1000×200 بكسل).
                                            {% else %}
                                            {% trans "Image that will appear at the top of documents and reports (ideal size: 1000×200 pixels)." %}
                                            {% endif %}
                                        </div>

                                        {% if branch and branch.header_image %}
                                        <div class="mt-2">
                                            <img src="{{ branch.header_image.url }}" alt="{% if LANGUAGE_CODE == 'ar' %}صورة الترويسة{% else %}{% trans 'Header Image' %}{% endif %}" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="{{ form.footer_image.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صورة التذييل{% else %}{% trans "Footer Image" %}{% endif %}</label>
                                        {{ form.footer_image }}
                                        {% if form.footer_image.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.footer_image.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="form-text">
                                            {% if LANGUAGE_CODE == 'ar' %}
                                            صورة ستظهر في أسفل المستندات والتقارير (الحجم المثالي: 1000×150 بكسل).
                                            {% else %}
                                            {% trans "Image that will appear at the bottom of documents and reports (ideal size: 1000×150 pixels)." %}
                                            {% endif %}
                                        </div>

                                        {% if branch and branch.footer_image %}
                                        <div class="mt-2">
                                            <img src="{{ branch.footer_image.url }}" alt="{% if LANGUAGE_CODE == 'ar' %}صورة التذييل{% else %}{% trans 'Footer Image' %}{% endif %}" class="img-thumbnail" style="max-height: 100px;">
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <a href="{% url 'companies:branch_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                            </a>
                            {% if branch %}
                            <a href="{% url 'companies:branch_print' branch.id %}" class="btn btn-info ms-2" target="_blank">
                                <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                            </a>
                            {% endif %}
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if branch %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}مستخدمو الفرع{% else %}{% trans "Branch Users" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                {% if branch.users.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in branch.users.all %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.get_full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'users:user_edit' user.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}</span>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا يوجد مستخدمون مرتبطون بهذا الفرع حتى الآن.{% else %}{% trans "No users associated with this branch yet." %}{% endif %}
                </div>
                {% endif %}

                <div class="mt-3">
                    <a href="{% url 'users:user_create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة مستخدم جديد{% else %}{% trans "Add New User" %}{% endif %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize Select2 for dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5'
        });
    });
</script>
{% endblock %}
