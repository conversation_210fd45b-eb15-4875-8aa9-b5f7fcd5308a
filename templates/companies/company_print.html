{% extends 'print_base.html' %}
{% load i18n %}

{% block title %}{% if LANGUAGE_CODE == 'ar' %}طباعة بيانات الشركة{% else %}{% trans "Company Information Print" %}{% endif %} - {{ company.name }}{% endblock %}

{% block content %}
<div class="container mt-4 print-container">
    <div class="document-header">
        {% if company.logo %}
        <div class="text-center mb-3">
            <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid company-logo" style="max-height: 120px;">
        </div>
        {% endif %}

        <h2>{% if LANGUAGE_CODE == 'ar' %}بيانات الشركة{% else %}{% trans "Company Information" %}{% endif %}</h2>
        <h3>{{ company.name }}</h3>
        <div class="mt-2 text-muted">
            {% if company.registration_number %}
            <span class="me-3">{% if LANGUAGE_CODE == 'ar' %}رقم السجل التجاري: {{ company.commercial_register }}{% else %}{% trans "Commercial Register" %}: {{ company.commercial_register }}{% endif %}</span>
            {% endif %}
            {% if company.tax_number %}
            <span>{% if LANGUAGE_CODE == 'ar' %}الرقم الضريبي: {{ company.tax_number }}{% else %}{% trans "Tax Number" %}: {{ company.tax_number }}{% endif %}</span>
            {% endif %}
        </div>
    </div>

    <div class="row">

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات أساسية{% else %}{% trans "Basic Information" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th width="40%">{% if LANGUAGE_CODE == 'ar' %}اسم الشركة{% else %}{% trans "Company Name" %}{% endif %}</th>
                                <td>{{ company.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رمز الشركة{% else %}{% trans "Company Code" %}{% endif %}</th>
                                <td>{{ company.code }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم الضريبي{% else %}{% trans "Tax Number" %}{% endif %}</th>
                                <td>{{ company.tax_number|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم السجل التجاري{% else %}{% trans "Commercial Register" %}{% endif %}</th>
                                <td>{{ company.commercial_register|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <td>
                                    {% if company.active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات الاتصال{% else %}{% trans "Contact Information" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th width="40%">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                                <td>{{ company.address|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <td>{{ company.phone|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <td>{{ company.email|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الموقع الإلكتروني{% else %}{% trans "Website" %}{% endif %}</th>
                                <td>{{ company.website|default:"-" }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}وسائل التواصل الاجتماعي{% else %}{% trans "Social Media" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th width="40%">{% if LANGUAGE_CODE == 'ar' %}فيسبوك{% else %}{% trans "Facebook" %}{% endif %}</th>
                                <td>{{ company.facebook|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تويتر{% else %}{% trans "Twitter" %}{% endif %}</th>
                                <td>{{ company.twitter|default:"-" }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {% if company.branches.exists %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}فروع الشركة{% else %}{% trans "Company Branches" %}{% endif %}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for branch in company.branches.all %}
                            <tr>
                                <td>{{ branch.name }}</td>
                                <td>{{ branch.code }}</td>
                                <td>{{ branch.address|default:"-" }}</td>
                                <td>{{ branch.phone|default:"-" }}</td>
                                <td>{{ branch.manager|default:"-" }}</td>
                                <td>
                                    {% if branch.active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row mt-4 print-footer">
        <div class="col-12 text-center">
            <p class="text-muted">
                {% if LANGUAGE_CODE == 'ar' %}
                تم إنشاء هذا التقرير في {{ now|date:"Y-m-d H:i" }}
                {% else %}
                {% trans "This report was generated on" %} {{ now|date:"Y-m-d H:i" }}
                {% endif %}
            </p>
        </div>
    </div>
</div>

<div class="print-actions d-print-none">
    <button type="button" onclick="window.print()" class="btn btn-primary">
        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'companies:company_list' %}" class="btn btn-secondary ms-2">
        <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
    </a>
</div>
{% endblock %}
