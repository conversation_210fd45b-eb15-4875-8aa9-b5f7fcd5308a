{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}حذف الفرع{% else %}{% trans "Delete Branch" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تأكيد الحذف{% else %}{% trans "Confirm Deletion" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد حذف الفرع "{{ branch.name }}"؟ هذا الإجراء لا يمكن التراجع عنه.
                    {% else %}
                    {% trans "Are you sure you want to delete the branch" %} "{{ branch.name }}"? {% trans "This action cannot be undone." %}
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}تفاصيل الفرع:{% else %}{% trans "Branch Details:" %}{% endif %}</h6>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %}</th>
                            <td>{{ branch.name }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                            <td>{{ branch.company.name }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                            <td>{{ branch.code }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                            <td>{{ branch.address|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</th>
                            <td>{{ branch.manager|default:"-" }}</td>
                        </tr>
                    </table>
                </div>
                
                {% if branch.users.exists %}
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تحذير: هناك {{ branch.users.count }} مستخدم مرتبط بهذا الفرع. حذف الفرع سيؤثر على هؤلاء المستخدمين.
                    {% else %}
                    {% trans "Warning: There are" %} {{ branch.users.count }} {% trans "users associated with this branch. Deleting the branch will affect these users." %}
                    {% endif %}
                </div>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'companies:branch_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if branch.users.exists %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المستخدمون المتأثرون{% else %}{% trans "Affected Users" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in branch.users.all %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.get_full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
