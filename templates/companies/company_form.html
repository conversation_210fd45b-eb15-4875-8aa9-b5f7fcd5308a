{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if company %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل الشركة{% else %}{% trans "Edit Company" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة شركة جديدة{% else %}{% trans "Add New Company" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if company %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل الشركة: {{ company.name }}{% else %}{% trans "Edit Company" %}: {{ company.name }}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إضافة شركة جديدة{% else %}{% trans "Add New Company" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات أساسية{% else %}{% trans "Basic Information" %}{% endif %}</h6>

                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم الشركة{% else %}{% trans "Company Name" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رمز الشركة{% else %}{% trans "Company Code" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.code }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    رمز فريد للشركة، يستخدم في التقارير والمستندات.
                                    {% else %}
                                    {% trans "A unique code for the company, used in reports and documents." %}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.tax_number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الرقم الضريبي{% else %}{% trans "Tax Number" %}{% endif %}</label>
                                {{ form.tax_number }}
                                {% if form.tax_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.tax_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.commercial_register.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم السجل التجاري{% else %}{% trans "Commercial Register" %}{% endif %}</label>
                                {{ form.commercial_register }}
                                {% if form.commercial_register.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.commercial_register.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.logo.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}شعار الشركة{% else %}{% trans "Company Logo" %}{% endif %}</label>
                                {{ form.logo }}
                                {% if form.logo.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.logo.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}

                                {% if company and company.logo %}
                                <div class="mt-2">
                                    <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-thumbnail" style="max-height: 100px;">
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3 form-check">
                                {{ form.active }}
                                <label class="form-check-label" for="{{ form.active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات الاتصال{% else %}{% trans "Contact Information" %}{% endif %}</h6>

                            <div class="mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.website.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الموقع الإلكتروني{% else %}{% trans "Website" %}{% endif %}</label>
                                {{ form.website }}
                                {% if form.website.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.website.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.facebook.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صفحة الفيسبوك{% else %}{% trans "Facebook Page" %}{% endif %}</label>
                                {{ form.facebook }}
                                {% if form.facebook.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.facebook.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    مثال: https://www.facebook.com/yourcompany
                                    {% else %}
                                    {% trans "Example: https://www.facebook.com/yourcompany" %}
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.twitter.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صفحة تويتر{% else %}{% trans "Twitter Page" %}{% endif %}</label>
                                {{ form.twitter }}
                                {% if form.twitter.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.twitter.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    مثال: https://twitter.com/yourcompany
                                    {% else %}
                                    {% trans "Example: https://twitter.com/yourcompany" %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <a href="{% url 'companies:company_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                            </a>
                            {% if company %}
                            <a href="{% url 'companies:company_print' company.id %}" class="btn btn-info ms-2" target="_blank">
                                <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                            </a>
                            {% endif %}
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if company %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}فروع الشركة{% else %}{% trans "Company Branches" %}{% endif %}</h5>
                <a href="{% url 'companies:branch_create' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة فرع جديد{% else %}{% trans "Add New Branch" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if company.branches.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for branch in company.branches.all %}
                            <tr>
                                <td>{{ branch.name }}</td>
                                <td>{{ branch.code }}</td>
                                <td>{{ branch.address|default:"-" }}</td>
                                <td>{{ branch.phone|default:"-" }}</td>
                                <td>{{ branch.manager|default:"-" }}</td>
                                <td>
                                    {% if branch.active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'companies:branch_edit' branch.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}</span>
                                    </a>
                                    <a href="{% url 'companies:branch_delete' branch.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}</span>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد فروع لهذه الشركة حتى الآن. انقر على "إضافة فرع جديد" لإنشاء أول فرع.{% else %}{% trans "No branches for this company yet. Click 'Add New Branch' to create your first branch." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
