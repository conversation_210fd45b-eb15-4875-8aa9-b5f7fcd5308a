{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة الفروع{% else %}{% trans "Branch Management" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة الفروع{% else %}{% trans "Branch List" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'companies:company_list' %}" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="fas fa-building me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الشركات{% else %}{% trans "Manage Companies" %}{% endif %}
                    </a>
                    {% if perms.companies.add_branch %}
                    <a href="{% url 'companies:branch_create' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة فرع جديد{% else %}{% trans "Add New Branch" %}{% endif %}
                    </a>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                {% if branches %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الفرع{% else %}{% trans "Branch Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المدير{% else %}{% trans "Manager" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}صور المستندات{% else %}{% trans "Document Images" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for branch in branches %}
                            <tr>
                                <td>{{ branch.name }}</td>
                                <td>
                                    <a href="{% url 'companies:company_edit' branch.company.id %}">
                                        {{ branch.company.name }}
                                    </a>
                                </td>
                                <td>{{ branch.code }}</td>
                                <td>{{ branch.address|default:"-" }}</td>
                                <td>{{ branch.phone|default:"-" }}</td>
                                <td>{{ branch.manager|default:"-" }}</td>
                                <td>
                                    <div class="d-flex gap-2">
                                        {% if branch.header_image %}
                                        <a href="{{ branch.header_image.url }}" target="_blank" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}عرض صورة الترويسة{% else %}{% trans 'View Header Image' %}{% endif %}">
                                            <i class="fas fa-image"></i>
                                            <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}عرض صورة الترويسة{% else %}{% trans 'View Header Image' %}{% endif %}</span>
                                        </a>
                                        {% else %}
                                        <span class="badge bg-light text-dark">{% if LANGUAGE_CODE == 'ar' %}لا توجد ترويسة{% else %}{% trans "No Header" %}{% endif %}</span>
                                        {% endif %}

                                        {% if branch.footer_image %}
                                        <a href="{{ branch.footer_image.url }}" target="_blank" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}عرض صورة التذييل{% else %}{% trans 'View Footer Image' %}{% endif %}">
                                            <i class="fas fa-image"></i>
                                            <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}عرض صورة التذييل{% else %}{% trans 'View Footer Image' %}{% endif %}</span>
                                        </a>
                                        {% else %}
                                        <span class="badge bg-light text-dark">{% if LANGUAGE_CODE == 'ar' %}لا يوجد تذييل{% else %}{% trans "No Footer" %}{% endif %}</span>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if branch.active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if perms.companies.view_branch %}
                                    <a href="{% url 'companies:branch_print' branch.id %}" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans 'Print' %}{% endif %}" target="_blank">
                                        <i class="fas fa-print"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans 'Print' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                    {% if perms.companies.change_branch %}
                                    <a href="{% url 'companies:branch_edit' branch.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                    {% if perms.companies.delete_branch %}
                                    <a href="{% url 'companies:branch_delete' branch.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد فروع حتى الآن. انقر على "إضافة فرع جديد" لإنشاء أول فرع.{% else %}{% trans "No branches yet. Click 'Add New Branch' to create your first branch." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات مفيدة{% else %}{% trans "Useful Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    يمكنك إدارة فروع الشركات في النظام من هذه الصفحة. كل فرع ينتمي إلى شركة محددة ويمكن أن يكون له مستخدمين وإعدادات خاصة به.
                    {% else %}
                    {% trans "You can manage company branches in the system from this page. Each branch belongs to a specific company and can have its own users and settings." %}
                    {% endif %}
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الفروع والمستخدمين{% else %}{% trans "Branches and Users" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <p>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    يمكن تعيين المستخدمين إلى فروع محددة، مما يسمح لهم بالوصول إلى البيانات المتعلقة بهذه الفروع فقط.
                                    {% else %}
                                    {% trans "Users can be assigned to specific branches, allowing them to access data related to those branches only." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'users:user_list' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-users me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}{% trans "Manage Users" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الهيكل التنظيمي{% else %}{% trans "Organizational Structure" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <p>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    الهيكل التنظيمي في النظام يتكون من شركات وفروع. يمكن للمستخدمين الوصول إلى بيانات الشركات والفروع التي لديهم صلاحيات للوصول إليها.
                                    {% else %}
                                    {% trans "The organizational structure in the system consists of companies and branches. Users can access data for companies and branches they have permissions to access." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'companies:company_list' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-building me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الشركات{% else %}{% trans "Manage Companies" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
