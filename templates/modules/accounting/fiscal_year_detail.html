{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل السنة المالية{% else %}{% trans "Fiscal Year Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل السنة المالية{% else %}{% trans "Fiscal Year Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل السنة المالية والفترات المرتبطة بها.{% else %}{% trans "View fiscal year details and associated periods." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-list-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_type_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:fiscal_year_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    {% if fiscal_year.state == 'draft' %}
    <a href="{% url 'accounting:fiscal_year_edit' fiscal_year.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:fiscal_year_open' fiscal_year.id %}" class="btn btn-success" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من فتح هذه السنة المالية؟{% else %}{% trans "Are you sure you want to open this fiscal year?" %}{% endif %}')">
        <i class="fas fa-lock-open me-2"></i> {% if LANGUAGE_CODE == 'ar' %}فتح{% else %}{% trans "Open" %}{% endif %}
    </a>
    {% endif %}
    {% if fiscal_year.state == 'open' %}
    <a href="{% url 'accounting:fiscal_year_close' fiscal_year.id %}" class="btn btn-outline-danger" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من إغلاق هذه السنة المالية؟ سيتم ترحيل الأرصدة الختامية إلى الأرصدة الافتتاحية للسنة المالية التالية.{% else %}{% trans "Are you sure you want to close this fiscal year? Closing balances will be transferred to opening balances of the next fiscal year." %}{% endif %}')">
        <i class="fas fa-lock me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans "Close" %}{% endif %}
    </a>
    {% endif %}
    <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-success">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سنة مالية جديدة{% else %}{% trans "New Fiscal Year" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل السنة المالية{% else %}{% trans "Fiscal Year Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات السنة المالية{% else %}{% trans "Fiscal Year Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}:</th>
                                <td>{{ fiscal_year.code }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</th>
                                <td>{{ fiscal_year.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ البداية{% else %}{% trans "Start Date" %}{% endif %}:</th>
                                <td>{{ fiscal_year.start_date }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ النهاية{% else %}{% trans "End Date" %}{% endif %}:</th>
                                <td>{{ fiscal_year.end_date }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}:</th>
                                <td>
                                    {% if fiscal_year.state == 'draft' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif fiscal_year.state == 'open' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفتوح{% else %}{% trans "Open" %}{% endif %}</span>
                                    {% elif fiscal_year.state == 'closed' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مغلق{% else %}{% trans "Closed" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}:</th>
                                <td>
                                    {% if fiscal_year.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نعم{% else %}{% trans "Yes" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}لا{% else %}{% trans "No" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}:</th>
                                <td>{{ fiscal_year.company.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created Date" %}{% endif %}:</th>
                                <td>{{ fiscal_year.created_date }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if fiscal_year.notes %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}:</h6>
                        <p>{{ fiscal_year.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إحصائيات السنة المالية{% else %}{% trans "Fiscal Year Statistics" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}</h5>
                                <p class="card-text display-4">{{ journal_entries_count }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipts" %}{% endif %}</h5>
                                <p class="card-text display-4">{{ receipts_count }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payments" %}{% endif %}</h5>
                                <p class="card-text display-4">{{ payments_count }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}</h5>
                                <p class="card-text display-4">{{ checks_count }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if journal_entries %}
                <div class="mt-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}أحدث قيود اليومية{% else %}{% trans "Recent Journal Entries" %}{% endif %}</h6>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in journal_entries %}
                                <tr>
                                    <td>{{ entry.date }}</td>
                                    <td>
                                        <a href="{% url 'accounting:journal_entry_detail' entry.id %}">
                                            {{ entry.reference }}
                                        </a>
                                    </td>
                                    <td>{{ entry.journal.name }}</td>
                                    <td>{{ entry.name }}</td>
                                    <td>${{ entry.amount|floatformat:2 }}</td>
                                    <td>
                                        {% if entry.state == 'draft' %}
                                        <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                        {% elif entry.state == 'posted' %}
                                        <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                        {% elif entry.state == 'cancelled' %}
                                        <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if journal_entries_count > 5 %}
                    <div class="mt-3">
                        <a href="{% url 'accounting:journal_entry_list' %}?fiscal_year={{ fiscal_year.id }}" class="btn btn-outline-primary">
                            {% if LANGUAGE_CODE == 'ar' %}عرض جميع قيود اليومية ({{ journal_entries_count }}){% else %}{% trans "View All Journal Entries" %} ({{ journal_entries_count }}){% endif %}
                        </a>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
