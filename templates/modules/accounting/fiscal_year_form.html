{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}
    {% if fiscal_year %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل السنة المالية{% else %}{% trans "Edit Fiscal Year" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية جديدة{% else %}{% trans "Create New Fiscal Year" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if fiscal_year %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل السنة المالية{% else %}{% trans "Edit Fiscal Year" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية جديدة{% else %}{% trans "Create New Fiscal Year" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if fiscal_year %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات السنة المالية.{% else %}{% trans "Edit fiscal year information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية جديدة لتنظيم الفترات المحاسبية.{% else %}{% trans "Create a new fiscal year to organize accounting periods." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-list-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_type_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:fiscal_year_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if fiscal_year %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل السنة المالية{% else %}{% trans "Edit Fiscal Year" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية جديدة{% else %}{% trans "Create New Fiscal Year" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if fiscal_year %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل السنة المالية{% else %}{% trans "Edit Fiscal Year" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية جديدة{% else %}{% trans "Create New Fiscal Year" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.code }}
                            <script>document.getElementById('{{ form.code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}مثال: FY2023{% else %}{% trans "Example: FY2023" %}{% endif %}</small>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}مثال: السنة المالية 2023{% else %}{% trans "Example: Fiscal Year 2023" %}{% endif %}</small>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ البداية{% else %}{% trans "Start Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.start_date }}
                            <script>document.getElementById('{{ form.start_date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.start_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.start_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ النهاية{% else %}{% trans "End Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.end_date }}
                            <script>document.getElementById('{{ form.end_date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.end_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.end_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted d-block">{% if LANGUAGE_CODE == 'ar' %}تنشيط هذه السنة المالية سيؤدي إلى إلغاء تنشيط السنوات المالية الأخرى.{% else %}{% trans "Activating this fiscal year will deactivate other fiscal years." %}{% endif %}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if fiscal_year %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:fiscal_year_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-calculate end date based on start date (1 year later)
        const startDateInput = document.getElementById('{{ form.start_date.id_for_label }}');
        const endDateInput = document.getElementById('{{ form.end_date.id_for_label }}');
        
        startDateInput.addEventListener('change', function() {
            if (startDateInput.value && !endDateInput.value) {
                const startDate = new Date(startDateInput.value);
                const endDate = new Date(startDate);
                endDate.setFullYear(endDate.getFullYear() + 1);
                endDate.setDate(endDate.getDate() - 1);
                
                // Format date as YYYY-MM-DD
                const year = endDate.getFullYear();
                const month = String(endDate.getMonth() + 1).padStart(2, '0');
                const day = String(endDate.getDate()).padStart(2, '0');
                endDateInput.value = `${year}-${month}-${day}`;
            }
        });
    });
</script>
{% endblock %}
