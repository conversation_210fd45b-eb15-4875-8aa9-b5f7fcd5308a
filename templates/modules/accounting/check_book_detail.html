{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دفتر الشيكات{% else %}{% trans "Check Book Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دفتر الشيكات{% else %}{% trans "Check Book Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل دفتر الشيكات.{% else %}{% trans "View check book details." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:check_book_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    <a href="{% url 'accounting:check_book_edit' check_book.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_account_detail' check_book.bank_account.id %}" class="btn btn-outline-info">
        <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض الحساب البنكي{% else %}{% trans "View Bank Account" %}{% endif %}
    </a>
    <a href="{% url 'accounting:check_create' %}?check_book={{ check_book.id }}" class="btn btn-outline-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
    </a>
    <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دفتر الشيكات{% else %}{% trans "Check Book Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات دفتر الشيكات{% else %}{% trans "Check Book Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</th>
                                <td>{{ check_book.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}:</th>
                                <td>{{ check_book.code }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحساب البنكي{% else %}{% trans "Bank Account" %}{% endif %}:</th>
                                <td>{{ check_book.bank_account.bank.name }} - {{ check_book.bank_account.number }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}رقم البداية{% else %}{% trans "Start Number" %}{% endif %}:</th>
                                <td>{{ check_book.start_number }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم النهاية{% else %}{% trans "End Number" %}{% endif %}:</th>
                                <td>{{ check_book.end_number }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم التالي{% else %}{% trans "Next Number" %}{% endif %}:</th>
                                <td>{{ check_book.next_number }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}:</th>
                                <td>
                                    {% if check_book.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if check_book.notes %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}:</h6>
                        <p>{{ check_book.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات النظام{% else %}{% trans "System Information" %}{% endif %}:</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تم الإنشاء بواسطة{% else %}{% trans "Created By" %}{% endif %}:</td>
                                <td>
                                    {% if check_book.created_by %}
                                        {{ check_book.created_by.get_full_name|default:check_book.created_by.username }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}غير معروف{% else %}{% trans "Unknown" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created At" %}{% endif %}:</td>
                                <td>{{ check_book.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:check_create' %}?check_book={{ check_book.id }}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if checks %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الاستحقاق{% else %}{% trans "Due Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المستفيد{% else %}{% trans "Beneficiary" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in checks %}
                            <tr>
                                <td>{{ check.number }}</td>
                                <td>{{ check.date }}</td>
                                <td>{{ check.due_date }}</td>
                                <td>{{ check.amount }}</td>
                                <td>{{ check.partner_name }}</td>
                                <td>
                                    {% if check.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif check.state == 'registered' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}مسجل{% else %}{% trans "Registered" %}{% endif %}</span>
                                    {% elif check.state == 'pending' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}معلق{% else %}{% trans "Pending" %}{% endif %}</span>
                                    {% elif check.state == 'collected' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}محصل{% else %}{% trans "Collected" %}{% endif %}</span>
                                    {% elif check.state == 'deposited' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مودع{% else %}{% trans "Deposited" %}{% endif %}</span>
                                    {% elif check.state == 'bounced' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مرتجع{% else %}{% trans "Bounced" %}{% endif %}</span>
                                    {% elif check.state == 'cancelled' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:check_detail' check.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:check_edit' check.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد شيكات لهذا الدفتر. يرجى إنشاء شيك جديد.{% else %}{% trans "No checks found for this check book. Please create a new check." %}{% endif %}
                    <a href="{% url 'accounting:check_create' %}?check_book={{ check_book.id }}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
