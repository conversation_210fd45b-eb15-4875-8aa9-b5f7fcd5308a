{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if asset %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل أصل{% else %}{% trans "Edit Asset" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء أصل جديد{% else %}{% trans "Create New Asset" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if asset %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل أصل{% else %}{% trans "Edit Asset" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء أصل جديد{% else %}{% trans "Create New Asset" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if asset %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات الأصل.{% else %}{% trans "Edit asset information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء أصل جديد.{% else %}{% trans "Create a new asset." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book-open me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر اليومية{% else %}{% trans "Journals" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_method_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-bill-wave me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طرق الدفع{% else %}{% trans "Payment Methods" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:asset_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if asset %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل أصل{% else %}{% trans "Edit Asset" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء أصل جديد{% else %}{% trans "Create New Asset" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if asset %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل أصل{% else %}{% trans "Edit Asset" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء أصل جديد{% else %}{% trans "Create New Asset" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    {{ form.company }}
                    {{ form.branch }}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.code }}
                            <script>document.getElementById('{{ form.code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.category.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الفئة{% else %}{% trans "Category" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.category }}
                            <script>document.getElementById('{{ form.category.id_for_label }}').classList.add('form-select');</script>
                            {% if form.category.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.category.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.depreciation_method.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}طريقة الإهلاك{% else %}{% trans "Depreciation Method" %}{% endif %}</label>
                            {{ form.depreciation_method }}
                            <script>document.getElementById('{{ form.depreciation_method.id_for_label }}').classList.add('form-select');</script>
                            {% if form.depreciation_method.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.depreciation_method.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.purchase_date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ الشراء{% else %}{% trans "Purchase Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.purchase_date }}
                            <script>document.getElementById('{{ form.purchase_date.id_for_label }}').classList.add('form-control');</script>
                            <script>document.getElementById('{{ form.purchase_date.id_for_label }}').setAttribute('type', 'date');</script>
                            {% if form.purchase_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.purchase_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.in_service_date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ بدء الخدمة{% else %}{% trans "In Service Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.in_service_date }}
                            <script>document.getElementById('{{ form.in_service_date.id_for_label }}').classList.add('form-control');</script>
                            <script>document.getElementById('{{ form.in_service_date.id_for_label }}').setAttribute('type', 'date');</script>
                            {% if form.in_service_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.in_service_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="{{ form.purchase_value.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}قيمة الشراء{% else %}{% trans "Purchase Value" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.purchase_value }}
                            <script>document.getElementById('{{ form.purchase_value.id_for_label }}').classList.add('form-control');</script>
                            {% if form.purchase_value.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.purchase_value.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label for="{{ form.salvage_value.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}القيمة المتبقية{% else %}{% trans "Salvage Value" %}{% endif %}</label>
                            {{ form.salvage_value }}
                            <script>document.getElementById('{{ form.salvage_value.id_for_label }}').classList.add('form-control');</script>
                            {% if form.salvage_value.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.salvage_value.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label for="{{ form.useful_life_years.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العمر الإنتاجي (سنوات){% else %}{% trans "Useful Life (Years)" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.useful_life_years }}
                            <script>document.getElementById('{{ form.useful_life_years.id_for_label }}').classList.add('form-control');</script>
                            {% if form.useful_life_years.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.useful_life_years.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="{{ form.depreciation_frequency.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تكرار الإهلاك{% else %}{% trans "Depreciation Frequency" %}{% endif %}</label>
                            {{ form.depreciation_frequency }}
                            <script>document.getElementById('{{ form.depreciation_frequency.id_for_label }}').classList.add('form-select');</script>
                            {% if form.depreciation_frequency.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.depreciation_frequency.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label for="{{ form.asset_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}حساب الأصل{% else %}{% trans "Asset Account" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.asset_account }}
                            <script>document.getElementById('{{ form.asset_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.asset_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.asset_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label for="{{ form.depreciation_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}حساب مصروف الإهلاك{% else %}{% trans "Depreciation Expense Account" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.depreciation_account }}
                            <script>document.getElementById('{{ form.depreciation_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.depreciation_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.depreciation_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.accumulated_depreciation_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}حساب مجمع الإهلاك{% else %}{% trans "Accumulated Depreciation Account" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.accumulated_depreciation_account }}
                            <script>document.getElementById('{{ form.accumulated_depreciation_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.accumulated_depreciation_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.accumulated_depreciation_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if asset %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:asset_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
