{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if account_type %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل نوع الحساب{% else %}{% trans "Edit Account Type" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء نوع حساب جديد{% else %}{% trans "Create New Account Type" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if account_type %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل نوع الحساب{% else %}{% trans "Edit Account Type" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء نوع حساب جديد{% else %}{% trans "Create New Account Type" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if account_type %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات نوع الحساب.{% else %}{% trans "Edit account type information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء نوع حساب جديد لتصنيف الحسابات المالية.{% else %}{% trans "Create a new account type to categorize financial accounts." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_type_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tags me-2"></i> {% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:account_type_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if account_type %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل نوع الحساب{% else %}{% trans "Edit Account Type" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء نوع حساب جديد{% else %}{% trans "Create New Account Type" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if account_type %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل نوع الحساب{% else %}{% trans "Edit Account Type" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء نوع حساب جديد{% else %}{% trans "Create New Account Type" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.name.help_text %}
                            <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.category.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الفئة{% else %}{% trans "Category" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.category }}
                            <script>document.getElementById('{{ form.category.id_for_label }}').classList.add('form-select');</script>
                            {% if form.category.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.category.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.category.help_text %}
                            <div class="form-text">{{ form.category.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.report_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع التقرير{% else %}{% trans "Report Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.report_type }}
                            <script>document.getElementById('{{ form.report_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.report_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.report_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.report_type.help_text %}
                            <div class="form-text">{{ form.report_type.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.is_debit_balance }}
                                <script>document.getElementById('{{ form.is_debit_balance.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_debit_balance.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}رصيد مدين{% else %}{% trans "Debit Balance" %}{% endif %}
                                </label>
                                {% if form.is_debit_balance.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_debit_balance.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        {{ form.company }}
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}</label>
                            {{ form.code }}
                            <script>document.getElementById('{{ form.code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.code.help_text %}
                            <div class="form-text">{{ form.code.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.description.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</label>
                            {{ form.description }}
                            <script>document.getElementById('{{ form.description.id_for_label }}').classList.add('form-control');</script>
                            {% if form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if account_type %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:account_type_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
