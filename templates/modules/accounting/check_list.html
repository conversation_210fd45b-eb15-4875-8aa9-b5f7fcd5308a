{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة الشيكات. إنشاء وتعديل وتتبع حالة الشيكات.{% else %}{% trans "Manage checks. Create, edit, and track check status." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:check_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}شيك جديد{% else %}{% trans "New Check" %}{% endif %}
    </a>
    <a href="{% url 'accounting:check_book_list' %}" class="btn btn-outline-success">
        <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:check_create' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}جديد{% else %}{% trans "New" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="check_type" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الشيك{% else %}{% trans "Check Type" %}{% endif %}</label>
                            <select name="check_type" id="check_type" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="incoming" {% if selected_type == 'incoming' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}شيك وارد{% else %}{% trans "Incoming" %}{% endif %}</option>
                                <option value="outgoing" {% if selected_type == 'outgoing' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}شيك صادر{% else %}{% trans "Outgoing" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="state" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</label>
                            <select name="state" id="state" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="draft" {% if selected_state == 'draft' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</option>
                                <option value="received" {% if selected_state == 'received' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مستلم{% else %}{% trans "Received" %}{% endif %}</option>
                                <option value="issued" {% if selected_state == 'issued' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مصدر{% else %}{% trans "Issued" %}{% endif %}</option>
                                <option value="deposited" {% if selected_state == 'deposited' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مودع{% else %}{% trans "Deposited" %}{% endif %}</option>
                                <option value="cleared" {% if selected_state == 'cleared' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}محصل{% else %}{% trans "Cleared" %}{% endif %}</option>
                                <option value="returned" {% if selected_state == 'returned' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مرتجع{% else %}{% trans "Returned" %}{% endif %}</option>
                                <option value="cancelled" {% if selected_state == 'cancelled' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-12 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                            <a href="{% url 'accounting:check_list' %}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </form>
                </div>

                {% if checks %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الشيك{% else %}{% trans "Check Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الإصدار{% else %}{% trans "Issue Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الاستحقاق{% else %}{% trans "Due Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحساب البنكي{% else %}{% trans "Bank Account" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المستفيد/المصدر{% else %}{% trans "Beneficiary/Source" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in checks %}
                            <tr>
                                <td>{{ check.number }}</td>
                                <td>
                                    {% if check.check_type == 'incoming' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}وارد{% else %}{% trans "Incoming" %}{% endif %}</span>
                                    {% elif check.check_type == 'outgoing' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}صادر{% else %}{% trans "Outgoing" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ check.issue_date }}</td>
                                <td>{{ check.due_date }}</td>
                                <td>${{ check.amount|floatformat:2 }}</td>
                                <td>{{ check.bank_account.name }}</td>
                                <td>{{ check.beneficiary_name }}</td>
                                <td>
                                    {% if check.state == 'draft' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif check.state == 'received' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}مستلم{% else %}{% trans "Received" %}{% endif %}</span>
                                    {% elif check.state == 'issued' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}مصدر{% else %}{% trans "Issued" %}{% endif %}</span>
                                    {% elif check.state == 'deposited' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مودع{% else %}{% trans "Deposited" %}{% endif %}</span>
                                    {% elif check.state == 'cleared' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}محصل{% else %}{% trans "Cleared" %}{% endif %}</span>
                                    {% elif check.state == 'returned' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مرتجع{% else %}{% trans "Returned" %}{% endif %}</span>
                                    {% elif check.state == 'cancelled' %}
                                    <span class="badge bg-dark">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:check_detail' check.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if check.state == 'draft' %}
                                    <a href="{% url 'accounting:check_edit' check.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{% url 'accounting:check_change_state' check.id %}" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}تغيير الحالة{% else %}{% trans 'Change State' %}{% endif %}">
                                        <i class="fas fa-exchange-alt"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد شيكات. يرجى إنشاء شيك جديد.{% else %}{% trans "No checks found. Please create a new check." %}{% endif %}
                    <a href="{% url 'accounting:check_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك{% else %}{% trans "Create Check" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
