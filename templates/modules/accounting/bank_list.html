{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة البنوك. إنشاء وتعديل وعرض البنوك.{% else %}{% trans "Manage banks. Create, edit, and view banks." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-credit-card me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:bank_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة بنك جديد{% else %}{% trans "Add New Bank" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_account_create' %}" class="btn btn-outline-success">
        <i class="fas fa-credit-card me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة حساب بنكي{% else %}{% trans "Add Bank Account" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة البنوك{% else %}{% trans "Banks List" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:bank_create' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة بنك جديد{% else %}{% trans "Add New Bank" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter Form -->
                <div class="mb-3">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البحث{% else %}{% trans "Search" %}{% endif %}</label>
                            <input type="text" name="search" id="search" class="form-control"
                                   placeholder="{% if LANGUAGE_CODE == 'ar' %}اسم البنك، الكود، أو رمز SWIFT{% else %}{% trans 'Bank name, code, or SWIFT code' %}{% endif %}"
                                   value="{{ search_query }}">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع الحالات{% else %}{% trans "All Status" %}{% endif %}</option>
                                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</option>
                                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-5 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                            <a href="{% url 'accounting:bank_list' %}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </form>
                </div>

                {% if banks %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم البنك{% else %}{% trans "Bank Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رمز SWIFT{% else %}{% trans "SWIFT Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for bank in banks %}
                            <tr>
                                <td>
                                    <strong>{{ bank.name }}</strong>
                                    {% if bank.address %}
                                    <br><small class="text-muted">{{ bank.address|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ bank.code }}</td>
                                <td>{{ bank.swift_code|default:"-" }}</td>
                                <td>{{ bank.phone|default:"-" }}</td>
                                <td>{{ bank.email|default:"-" }}</td>
                                <td>
                                    {% if bank.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:bank_detail' bank.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:bank_edit' bank.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد بنوك. يرجى إضافة بنك جديد.{% else %}{% trans "No banks found. Please add a new bank." %}{% endif %}
                    <a href="{% url 'accounting:bank_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إضافة بنك جديد{% else %}{% trans "Add New Bank" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}