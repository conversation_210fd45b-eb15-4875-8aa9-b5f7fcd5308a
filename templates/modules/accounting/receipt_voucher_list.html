{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة سندات القبض. إنشاء وتعديل وترحيل سندات القبض.{% else %}{% trans "Manage receipt vouchers. Create, edit, and post receipt vouchers." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:receipt_voucher_create' %}" class="btn btn-success mb-2">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سند قبض جديد{% else %}{% trans "New Receipt" %}{% endif %}
    </a>
    <a href="{% url 'accounting:payment_voucher_list' %}" class="btn btn-outline-danger">
        <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% endif %}

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:receipt_voucher_create' %}" class="btn btn-sm btn-light">
                        <i class="fas fa-plus-circle me-1"></i> {% if LANGUAGE_CODE == 'ar' %}سند قبض جديد{% else %}{% trans "New Receipt" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %}</label>
                            <select name="payment_method" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع طرق الدفع{% else %}{% trans "All Payment Methods" %}{% endif %}</option>
                                {% for method in payment_methods %}
                                <option value="{{ method.id }}" {% if method.id|stringformat:"s" == selected_method %}selected{% endif %}>
                                    {{ method.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</label>
                            <select name="state" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع الحالات{% else %}{% trans "All States" %}{% endif %}</option>
                                <option value="draft" {% if selected_state == 'draft' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}
                                </option>
                                <option value="posted" {% if selected_state == 'posted' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}
                                </option>
                                <option value="cancelled" {% if selected_state == 'cancelled' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                                </button>
                                <a href="{% url 'accounting:receipt_voucher_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                {% if vouchers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العميل{% else %}{% trans "Customer" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for voucher in vouchers %}
                            <tr>
                                <td>{{ voucher.date }}</td>
                                <td>{{ voucher.reference }}</td>
                                <td>{{ voucher.partner_name }}</td>
                                <td>{{ voucher.payment_method.name }}</td>
                                <td>
                                    {% if voucher.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif voucher.state == 'posted' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                    {% elif voucher.state == 'cancelled' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>${{ voucher.amount|floatformat:2 }}</td>
                                <td>
                                    <a href="{% url 'accounting:voucher_detail' voucher.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if voucher.state == 'draft' %}
                                    <a href="{% url 'accounting:voucher_edit' voucher.id %}" class="btn btn-sm btn-outline-warning" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounting:voucher_post' voucher.id %}" class="btn btn-sm btn-outline-success" title="{% if LANGUAGE_CODE == 'ar' %}ترحيل{% else %}{% trans 'Post' %}{% endif %}" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من ترحيل هذا السند؟{% else %}{% trans 'Are you sure you want to post this voucher?' %}{% endif %}')">
                                        <i class="fas fa-check"></i>
                                    </a>
                                    {% elif voucher.state == 'posted' %}
                                    <a href="{% url 'accounting:voucher_cancel' voucher.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans 'Cancel' %}{% endif %}" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من إلغاء هذا السند؟{% else %}{% trans 'Are you sure you want to cancel this voucher?' %}{% endif %}')">
                                        <i class="fas fa-times"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد سندات قبض{% else %}{% trans "No receipt vouchers found" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}ابدأ بإنشاء سند قبض جديد{% else %}{% trans "Start by creating a new receipt voucher" %}{% endif %}</p>
                    <a href="{% url 'accounting:receipt_voucher_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء سند قبض جديد{% else %}{% trans "Create New Receipt" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-success {
        background-color: #28a745 !important;
    }
    .btn-outline-success:hover {
        background-color: #28a745;
        border-color: #28a745;
    }
    .table th {
        background-color: #f8f9fa;
        font-weight: 500;
    }
    .badge {
        font-size: 0.75em;
    }
</style>
{% endblock %}
