{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة قيود اليومية. إنشاء وتعديل وترحيل قيود اليومية المحاسبية.{% else %}{% trans "Manage journal entries. Create, edit, and post accounting journal entries." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-credit-card me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:journal_entry_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيد يومية جديد{% else %}{% trans "New Journal Entry" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% endif %}

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:journal_entry_create' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}جديد{% else %}{% trans "New" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="journal" class="form-label">{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %}</label>
                            <select name="journal" id="journal" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                {% for journal in journals %}
                                <option value="{{ journal.id }}" {% if selected_journal == journal.id %}selected{% endif %}>{{ journal.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="state" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</label>
                            <select name="state" id="state" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="draft" {% if selected_state == 'draft' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</option>
                                <option value="posted" {% if selected_state == 'posted' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</option>
                                <option value="cancelled" {% if selected_state == 'cancelled' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-12 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                            <a href="{% url 'accounting:journal_entry_list' %}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </form>
                </div>

                {% if journal_entries %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in journal_entries %}
                            <tr>
                                <td>{{ entry.date }}</td>
                                <td>{{ entry.reference }}</td>
                                <td>{{ entry.journal.name }}</td>
                                <td>{{ entry.name }}</td>
                                <td>
                                    {% if entry.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif entry.state == 'posted' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                    {% elif entry.state == 'cancelled' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% with total_debit=entry.lines.all|dictsumattr:"debit" %}
                                    ${{ total_debit|floatformat:2 }}
                                    {% endwith %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:journal_entry_detail' entry.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if entry.state == 'draft' %}
                                    <a href="{% url 'accounting:journal_entry_edit' entry.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounting:journal_entry_post' entry.id %}" class="btn btn-sm btn-outline-success" title="{% if LANGUAGE_CODE == 'ar' %}ترحيل{% else %}{% trans 'Post' %}{% endif %}">
                                        <i class="fas fa-check"></i>
                                    </a>
                                    {% endif %}
                                    {% if entry.state == 'posted' %}
                                    <a href="{% url 'accounting:journal_entry_cancel' entry.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans 'Cancel' %}{% endif %}">
                                        <i class="fas fa-times"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد قيود يومية. يرجى إنشاء قيد يومية جديد.{% else %}{% trans "No journal entries found. Please create a new journal entry." %}{% endif %}
                    <a href="{% url 'accounting:journal_entry_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد يومية{% else %}{% trans "Create Journal Entry" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
