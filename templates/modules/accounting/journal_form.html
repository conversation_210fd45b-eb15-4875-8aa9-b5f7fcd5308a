{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if journal %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر يومية{% else %}{% trans "Edit Journal" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر يومية جديد{% else %}{% trans "Create New Journal" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if journal %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر يومية{% else %}{% trans "Edit Journal" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر يومية جديد{% else %}{% trans "Create New Journal" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if journal %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات دفتر اليومية.{% else %}{% trans "Edit journal information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر يومية جديد.{% else %}{% trans "Create a new journal." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-book-open me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر اليومية{% else %}{% trans "Journals" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:journal_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if journal %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر يومية{% else %}{% trans "Edit Journal" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر يومية جديد{% else %}{% trans "Create New Journal" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if journal %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر يومية{% else %}{% trans "Edit Journal" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر يومية جديد{% else %}{% trans "Create New Journal" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    {{ form.company }}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.code }}
                            <script>document.getElementById('{{ form.code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.type }}
                            <script>document.getElementById('{{ form.type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.default_debit_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}حساب المدين{% else %}{% trans "Debit Account" %}{% endif %}</label>
                            {{ form.default_debit_account }}
                            <script>document.getElementById('{{ form.default_debit_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.default_debit_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.default_debit_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}حساب المدين الافتراضي لهذا الدفتر (اختياري).{% else %}{% trans "The default debit account for this journal (optional)." %}{% endif %}</small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.default_credit_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}حساب الدائن{% else %}{% trans "Credit Account" %}{% endif %}</label>
                            {{ form.default_credit_account }}
                            <script>document.getElementById('{{ form.default_credit_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.default_credit_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.default_credit_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}حساب الدائن الافتراضي لهذا الدفتر (اختياري).{% else %}{% trans "The default credit account for this journal (optional)." %}{% endif %}</small>
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.number_prefix.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}بادئة الترقيم{% else %}{% trans "Number Prefix" %}{% endif %}</label>
                            {{ form.number_prefix }}
                            <script>document.getElementById('{{ form.number_prefix.id_for_label }}').classList.add('form-control');</script>
                            {% if form.number_prefix.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.number_prefix.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}بادئة لأرقام القيود (مثال: INV/).{% else %}{% trans "Prefix for entry numbers (e.g., INV/)." %}{% endif %}</small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.next_number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الرقم التالي{% else %}{% trans "Next Number" %}{% endif %}</label>
                            {{ form.next_number }}
                            <script>document.getElementById('{{ form.next_number.id_for_label }}').classList.add('form-control');</script>
                            {% if form.next_number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.next_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}الرقم التالي للقيد.{% else %}{% trans "Next number for entries." %}{% endif %}</small>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.auto_numbering }}
                                <script>document.getElementById('{{ form.auto_numbering.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.auto_numbering.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}ترقيم تلقائي{% else %}{% trans "Auto Numbering" %}{% endif %}
                                </label>
                                {% if form.auto_numbering.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.auto_numbering.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">{% if LANGUAGE_CODE == 'ar' %}ترقيم القيود تلقائيًا.{% else %}{% trans "Automatically number entries." %}{% endif %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if journal %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:journal_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
