{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض الأصول والخصوم وحقوق الملكية في تاريخ محدد.{% else %}{% trans "Display assets, liabilities, and equity at a specific date." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-primary{% endblock %}
{% block quick_actions_header_class %}bg-primary{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button onclick="window.print()" class="btn btn-primary">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'accounting:report_trial_balance' %}" class="btn btn-outline-info">
        <i class="fas fa-balance-scale me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}
    </a>
    <a href="{% url 'accounting:report_income_statement' %}" class="btn btn-outline-success">
        <i class="fas fa-chart-line me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% else %}

        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}
                </h5>
                <div class="d-flex gap-2">
                    <button onclick="window.print()" class="btn btn-sm btn-light">
                        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}</h5>
                        <p class="text-muted mb-0">
                            {% if LANGUAGE_CODE == 'ar' %}كما في{% else %}{% trans "As of" %}{% endif %} {{ report_date }}
                        </p>
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %}
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                <!-- Filter Form -->
                <form method="get" class="mb-4 no-print">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير{% else %}{% trans "Report Date" %}{% endif %}</label>
                            <input type="date" name="report_date" class="form-control" value="{{ report_date }}">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تطبيق{% else %}{% trans "Apply" %}{% endif %}
                                </button>
                                <a href="{% url 'accounting:report_balance_sheet' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="row">
                    <!-- Assets Column -->
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th colspan="2">
                                            <h5 class="mb-0">
                                                <i class="fas fa-building me-2"></i>
                                                {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
                                            </h5>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if asset_data %}
                                        {% for asset in asset_data %}
                                        <tr>
                                            <td>{{ asset.name }}</td>
                                            <td class="text-end">{{ asset.balance|floatformat:2 }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="2" class="text-center text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد أصول{% else %}{% trans "No assets found" %}{% endif %}</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                                <tfoot class="table-primary">
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}إجمالي الأصول{% else %}{% trans "Total Assets" %}{% endif %}</th>
                                        <th class="text-end">{{ total_assets|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>

                    <!-- Liabilities and Equity Column -->
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <!-- Liabilities Section -->
                                <thead class="table-danger">
                                    <tr>
                                        <th colspan="2">
                                            <h5 class="mb-0">
                                                <i class="fas fa-credit-card me-2"></i>
                                                {% if LANGUAGE_CODE == 'ar' %}الخصوم{% else %}{% trans "Liabilities" %}{% endif %}
                                            </h5>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if liability_data %}
                                        {% for liability in liability_data %}
                                        <tr>
                                            <td>{{ liability.name }}</td>
                                            <td class="text-end">{{ liability.balance|floatformat:2 }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="2" class="text-center text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد خصوم{% else %}{% trans "No liabilities found" %}{% endif %}</td>
                                        </tr>
                                    {% endif %}
                                    <tr class="table-danger">
                                        <td><strong>{% if LANGUAGE_CODE == 'ar' %}إجمالي الخصوم{% else %}{% trans "Total Liabilities" %}{% endif %}</strong></td>
                                        <td class="text-end"><strong>{{ total_liabilities|floatformat:2 }}</strong></td>
                                    </tr>
                                </tbody>

                                <!-- Equity Section -->
                                <thead class="table-success">
                                    <tr>
                                        <th colspan="2">
                                            <h5 class="mb-0">
                                                <i class="fas fa-user-tie me-2"></i>
                                                {% if LANGUAGE_CODE == 'ar' %}حقوق الملكية{% else %}{% trans "Equity" %}{% endif %}
                                            </h5>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if equity_data %}
                                        {% for equity in equity_data %}
                                        <tr>
                                            <td>{{ equity.name }}</td>
                                            <td class="text-end">{{ equity.balance|floatformat:2 }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="2" class="text-center text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد حقوق ملكية{% else %}{% trans "No equity found" %}{% endif %}</td>
                                        </tr>
                                    {% endif %}
                                    <tr class="table-success">
                                        <td><strong>{% if LANGUAGE_CODE == 'ar' %}إجمالي حقوق الملكية{% else %}{% trans "Total Equity" %}{% endif %}</strong></td>
                                        <td class="text-end"><strong>{{ total_equity|floatformat:2 }}</strong></td>
                                    </tr>
                                </tbody>

                                <!-- Total Liabilities and Equity -->
                                <tfoot class="table-dark">
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}إجمالي الخصوم وحقوق الملكية{% else %}{% trans "Total Liabilities & Equity" %}{% endif %}</th>
                                        <th class="text-end">{{ total_liabilities_equity|floatformat:2 }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Balance Check -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card border-{% if total_assets == total_liabilities_equity %}success{% else %}danger{% endif %}">
                            <div class="card-body text-center">
                                <h5 class="card-title text-{% if total_assets == total_liabilities_equity %}success{% else %}danger{% endif %}">
                                    {% if total_assets == total_liabilities_equity %}
                                        <i class="fas fa-check-circle me-2"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}الميزانية متوازنة{% else %}{% trans "Balance Sheet is Balanced" %}{% endif %}
                                    {% else %}
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}الميزانية غير متوازنة{% else %}{% trans "Balance Sheet is Unbalanced" %}{% endif %}
                                    {% endif %}
                                </h5>
                                <p class="mb-0">
                                    {% if LANGUAGE_CODE == 'ar' %}الفرق:{% else %}{% trans "Difference:" %}{% endif %}
                                    <strong class="text-{% if total_assets == total_liabilities_equity %}success{% else %}danger{% endif %}">
                                        {{ total_assets|add:"-"|add:total_liabilities_equity|floatformat:2 }}
                                    </strong>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title text-primary">{% if LANGUAGE_CODE == 'ar' %}إجمالي الأصول{% else %}{% trans "Total Assets" %}{% endif %}</h5>
                                <h3 class="text-primary">{{ total_assets|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title text-danger">{% if LANGUAGE_CODE == 'ar' %}إجمالي الخصوم{% else %}{% trans "Total Liabilities" %}{% endif %}</h5>
                                <h3 class="text-danger">{{ total_liabilities|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">{% if LANGUAGE_CODE == 'ar' %}إجمالي حقوق الملكية{% else %}{% trans "Total Equity" %}{% endif %}</h5>
                                <h3 class="text-success">{{ total_equity|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-primary {
        background-color: #007bff !important;
    }

    @media print {
        .sidebar, .quick-actions, .btn, .card-header .btn, .no-print {
            display: none !important;
        }
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .print-header {
            margin-bottom: 30px !important;
        }
        .table {
            font-size: 12px;
        }
        .table th, .table td {
            padding: 8px 4px;
        }
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
</style>
{% endblock %}
