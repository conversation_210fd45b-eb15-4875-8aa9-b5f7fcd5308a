{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}جدول الإهلاك{% else %}{% trans "Depreciation Schedule" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}جدول الإهلاك{% else %}{% trans "Depreciation Schedule" %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-danger{% endblock %}
{% block quick_actions_header_class %}bg-danger{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:reports_index' %}" class="btn btn-outline-danger">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة للتقارير{% else %}{% trans "Back to Reports" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}جدول الإهلاك{% else %}{% trans "Depreciation Schedule" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}جدول الإهلاك{% else %}{% trans "Depreciation Schedule" %}{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}جدول الإهلاك{% else %}{% trans "Depreciation Schedule" %}{% endif %}</h5>
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %}
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                <!-- Filter Form -->
                <form method="get" class="mb-4 no-print">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}السنة{% else %}{% trans "Year" %}{% endif %}</label>
                            <select name="year" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع السنوات{% else %}{% trans "All Years" %}{% endif %}</option>
                                {% for year in years %}
                                <option value="{{ year }}" {% if year|stringformat:"s" == selected_year %}selected{% endif %}>
                                    {{ year }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}فئة الأصل{% else %}{% trans "Asset Category" %}{% endif %}</label>
                            <select name="category_id" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع الفئات{% else %}{% trans "All Categories" %}{% endif %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if category.id|stringformat:"s" == selected_category_id %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تطبيق{% else %}{% trans "Apply" %}{% endif %}
                                </button>
                                <a href="{% url 'accounting:report_depreciation_schedule' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}هذا التقرير يعرض جدول الإهلاك المتوقع للأصول الثابتة. البيانات الفعلية ستعتمد على الأصول المسجلة في النظام.{% else %}{% trans "This report shows the expected depreciation schedule for fixed assets. Actual data will depend on assets registered in the system." %}{% endif %}
                </div>

                <!-- Sample Depreciation Schedule Table -->
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead class="table-danger">
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الأصل{% else %}{% trans "Asset Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الفئة{% else %}{% trans "Category" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الشراء{% else %}{% trans "Purchase Date" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}قيمة الشراء{% else %}{% trans "Purchase Value" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}العمر الإنتاجي{% else %}{% trans "Useful Life" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}الإهلاك السنوي{% else %}{% trans "Annual Depreciation" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}الإهلاك المتراكم{% else %}{% trans "Accumulated Depreciation" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}القيمة الدفترية{% else %}{% trans "Book Value" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                    {% if LANGUAGE_CODE == 'ar' %}لا توجد أصول مسجلة حالياً{% else %}{% trans "No assets registered currently" %}{% endif %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title text-primary">{% if LANGUAGE_CODE == 'ar' %}إجمالي الأصول{% else %}{% trans "Total Assets" %}{% endif %}</h5>
                                <h3 class="text-primary">0</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h5 class="card-title text-warning">{% if LANGUAGE_CODE == 'ar' %}إجمالي قيمة الشراء{% else %}{% trans "Total Purchase Value" %}{% endif %}</h5>
                                <h3 class="text-warning">0.00</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title text-danger">{% if LANGUAGE_CODE == 'ar' %}إجمالي الإهلاك{% else %}{% trans "Total Depreciation" %}{% endif %}</h5>
                                <h3 class="text-danger">0.00</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">{% if LANGUAGE_CODE == 'ar' %}إجمالي القيمة الدفترية{% else %}{% trans "Total Book Value" %}{% endif %}</h5>
                                <h3 class="text-success">0.00</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
