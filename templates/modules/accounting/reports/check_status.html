{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تقرير حالة الشيكات{% else %}{% trans "Check Status Report" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تقرير حالة الشيكات{% else %}{% trans "Check Status Report" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض حالة الشيكات الصادرة والواردة.{% else %}{% trans "Display status of issued and received checks." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-secondary{% endblock %}
{% block quick_actions_header_class %}bg-secondary{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button onclick="window.print()" class="btn btn-secondary">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'accounting:reports_index' %}" class="btn btn-outline-info">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة للتقارير{% else %}{% trans "Back to Reports" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تقرير حالة الشيكات{% else %}{% trans "Check Status Report" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-check me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}تقرير حالة الشيكات{% else %}{% trans "Check Status Report" %}{% endif %}
                </h5>
                <div class="d-flex gap-2">
                    <button onclick="window.print()" class="btn btn-sm btn-light">
                        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}تقرير حالة الشيكات{% else %}{% trans "Check Status Report" %}{% endif %}</h5>
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %}
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                <!-- Filter Form -->
                <form method="get" class="mb-4 no-print">
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</label>
                            <select name="status" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع الحالات{% else %}{% trans "All Status" %}{% endif %}</option>
                                <option value="pending" {% if selected_status == 'pending' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}قيد الانتظار{% else %}{% trans "Pending" %}{% endif %}
                                </option>
                                <option value="collected" {% if selected_status == 'collected' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}مصروف{% else %}{% trans "Collected" %}{% endif %}
                                </option>
                                <option value="bounced" {% if selected_status == 'bounced' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}مرتد{% else %}{% trans "Bounced" %}{% endif %}
                                </option>
                                <option value="cancelled" {% if selected_status == 'cancelled' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تطبيق{% else %}{% trans "Apply" %}{% endif %}
                                </button>
                                <a href="{% url 'accounting:report_check_status' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                {% if checks %}
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead class="table-secondary">
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الشيك{% else %}{% trans "Check Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المستفيد{% else %}{% trans "Payee" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البنك{% else %}{% trans "Bank" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check in checks %}
                            <tr>
                                <td>{{ check.number }}</td>
                                <td>{{ check.date }}</td>
                                <td>
                                    {% if check.type == 'incoming' %}
                                        <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}وارد{% else %}{% trans "Incoming" %}{% endif %}</span>
                                    {% else %}
                                        <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}صادر{% else %}{% trans "Outgoing" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ check.payee_name|default:"-" }}</td>
                                <td>{{ check.bank_account.bank.name }}</td>
                                <td class="text-end">{{ check.amount|floatformat:2 }}</td>
                                <td>
                                    {% if check.state == 'pending' %}
                                        <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}قيد الانتظار{% else %}{% trans "Pending" %}{% endif %}</span>
                                    {% elif check.state == 'collected' %}
                                        <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مصروف{% else %}{% trans "Collected" %}{% endif %}</span>
                                    {% elif check.state == 'bounced' %}
                                        <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مرتد{% else %}{% trans "Bounced" %}{% endif %}</span>
                                    {% elif check.state == 'cancelled' %}
                                        <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-money-check fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد شيكات{% else %}{% trans "No checks found" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد شيكات مطابقة للمعايير المحددة.{% else %}{% trans "No checks found matching the specified criteria." %}{% endif %}</p>
                </div>
                {% endif %}

                <!-- Status Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h5 class="card-title text-warning">{% if LANGUAGE_CODE == 'ar' %}قيد الانتظار{% else %}{% trans "Pending" %}{% endif %}</h5>
                                <h3 class="text-warning">{{ status_summary.pending }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">{% if LANGUAGE_CODE == 'ar' %}مصروف{% else %}{% trans "Collected" %}{% endif %}</h5>
                                <h3 class="text-success">{{ status_summary.collected }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title text-danger">{% if LANGUAGE_CODE == 'ar' %}مرتد{% else %}{% trans "Bounced" %}{% endif %}</h5>
                                <h3 class="text-danger">{{ status_summary.bounced }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-secondary">
                            <div class="card-body text-center">
                                <h5 class="card-title text-secondary">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</h5>
                                <h3 class="text-secondary">{{ status_summary.cancelled }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-secondary {
        background-color: #6c757d !important;
    }

    @media print {
        .sidebar, .quick-actions, .btn, .card-header .btn, .no-print {
            display: none !important;
        }
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .print-header {
            margin-bottom: 30px !important;
        }
        .table {
            font-size: 12px;
        }
        .table th, .table td {
            padding: 8px 4px;
        }
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
</style>
{% endblock %}
