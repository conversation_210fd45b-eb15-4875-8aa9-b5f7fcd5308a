{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض الإيرادات والمصروفات وصافي الربح أو الخسارة لفترة محددة.{% else %}{% trans "Show revenues, expenses, and net profit or loss for a specific period." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button onclick="window.print()" class="btn btn-success">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'accounting:report_trial_balance' %}" class="btn btn-outline-info">
        <i class="fas fa-balance-scale me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}
    </a>
    <a href="{% url 'accounting:report_balance_sheet' %}" class="btn btn-outline-primary">
        <i class="fas fa-file-invoice me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% else %}

        <div class="card shadow-sm">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}
                </h5>
                <div class="d-flex gap-2">
                    <button onclick="window.print()" class="btn btn-sm btn-light">
                        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}</h5>
                        <p class="text-muted mb-0">
                            {% if start_date and end_date %}
                                {% if LANGUAGE_CODE == 'ar' %}للفترة من{% else %}{% trans "For the period from" %}{% endif %} 
                                {{ start_date }} {% if LANGUAGE_CODE == 'ar' %}إلى{% else %}{% trans "to" %}{% endif %} {{ end_date }}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ar' %}السنة المالية:{% else %}{% trans "Fiscal Year:" %}{% endif %} 
                                {{ fiscal_year.name }} ({{ fiscal_year.start_date }} - {{ fiscal_year.end_date }})
                            {% endif %}
                        </p>
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %} 
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                <!-- Filter Form -->
                <form method="get" class="mb-4 no-print">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="start_date" class="form-control" value="{{ start_date }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="end_date" class="form-control" value="{{ end_date }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تطبيق{% else %}{% trans "Apply" %}{% endif %}
                                </button>
                                <a href="{% url 'accounting:report_income_statement' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <!-- Revenue Section -->
                        <thead class="table-success">
                            <tr>
                                <th colspan="2">
                                    <h5 class="mb-0">
                                        <i class="fas fa-arrow-up me-2"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}الإيرادات{% else %}{% trans "Revenue" %}{% endif %}
                                    </h5>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if revenue_data %}
                                {% for revenue in revenue_data %}
                                <tr>
                                    <td>{{ revenue.name }}</td>
                                    <td class="text-end">{{ revenue.balance|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="2" class="text-center text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد إيرادات{% else %}{% trans "No revenue accounts found" %}{% endif %}</td>
                                </tr>
                            {% endif %}
                            <tr class="table-success">
                                <td><strong>{% if LANGUAGE_CODE == 'ar' %}إجمالي الإيرادات{% else %}{% trans "Total Revenue" %}{% endif %}</strong></td>
                                <td class="text-end"><strong>{{ total_revenue|floatformat:2 }}</strong></td>
                            </tr>
                        </tbody>

                        <!-- Expense Section -->
                        <thead class="table-danger">
                            <tr>
                                <th colspan="2">
                                    <h5 class="mb-0">
                                        <i class="fas fa-arrow-down me-2"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}المصروفات{% else %}{% trans "Expenses" %}{% endif %}
                                    </h5>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if expense_data %}
                                {% for expense in expense_data %}
                                <tr>
                                    <td>{{ expense.name }}</td>
                                    <td class="text-end">{{ expense.balance|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="2" class="text-center text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد مصروفات{% else %}{% trans "No expense accounts found" %}{% endif %}</td>
                                </tr>
                            {% endif %}
                            <tr class="table-danger">
                                <td><strong>{% if LANGUAGE_CODE == 'ar' %}إجمالي المصروفات{% else %}{% trans "Total Expenses" %}{% endif %}</strong></td>
                                <td class="text-end"><strong>{{ total_expense|floatformat:2 }}</strong></td>
                            </tr>
                        </tbody>

                        <!-- Net Income Section -->
                        <tfoot class="table-dark">
                            <tr>
                                <th>
                                    <h5 class="mb-0">
                                        {% if net_income >= 0 %}
                                            <i class="fas fa-arrow-up text-success me-2"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}صافي الربح{% else %}{% trans "Net Profit" %}{% endif %}
                                        {% else %}
                                            <i class="fas fa-arrow-down text-danger me-2"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}صافي الخسارة{% else %}{% trans "Net Loss" %}{% endif %}
                                        {% endif %}
                                    </h5>
                                </th>
                                <th class="text-end">
                                    <h5 class="mb-0 {% if net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {{ net_income|floatformat:2 }}
                                    </h5>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">{% if LANGUAGE_CODE == 'ar' %}إجمالي الإيرادات{% else %}{% trans "Total Revenue" %}{% endif %}</h5>
                                <h3 class="text-success">{{ total_revenue|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title text-danger">{% if LANGUAGE_CODE == 'ar' %}إجمالي المصروفات{% else %}{% trans "Total Expenses" %}{% endif %}</h5>
                                <h3 class="text-danger">{{ total_expense|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-{% if net_income >= 0 %}success{% else %}danger{% endif %}">
                            <div class="card-body text-center">
                                <h5 class="card-title text-{% if net_income >= 0 %}success{% else %}danger{% endif %}">
                                    {% if net_income >= 0 %}
                                        {% if LANGUAGE_CODE == 'ar' %}صافي الربح{% else %}{% trans "Net Profit" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}صافي الخسارة{% else %}{% trans "Net Loss" %}{% endif %}
                                    {% endif %}
                                </h5>
                                <h3 class="text-{% if net_income >= 0 %}success{% else %}danger{% endif %}">
                                    {% if net_income >= 0 %}
                                        <i class="fas fa-arrow-up me-2"></i>
                                    {% else %}
                                        <i class="fas fa-arrow-down me-2"></i>
                                    {% endif %}
                                    {{ net_income|floatformat:2 }}
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profit Margin Calculation -->
                {% if total_revenue > 0 %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">{% if LANGUAGE_CODE == 'ar' %}هامش الربح{% else %}{% trans "Profit Margin" %}{% endif %}</h5>
                                {% with profit_margin=net_income|floatformat:2|add:0|div:total_revenue|mul:100 %}
                                <h3 class="text-info">{{ profit_margin|floatformat:2 }}%</h3>
                                {% endwith %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-success {
        background-color: #28a745 !important;
    }
    
    @media print {
        .sidebar, .quick-actions, .btn, .card-header .btn, .no-print {
            display: none !important;
        }
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .print-header {
            margin-bottom: 30px !important;
        }
        .table {
            font-size: 12px;
        }
        .table th, .table td {
            padding: 8px 4px;
        }
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
</style>
{% endblock %}
