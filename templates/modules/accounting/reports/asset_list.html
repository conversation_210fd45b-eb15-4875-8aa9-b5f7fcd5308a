{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تقرير الأصول{% else %}{% trans "Asset Report" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تقرير الأصول{% else %}{% trans "Asset Report" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض قائمة الأصول الثابتة وقيمها والإهلاكات.{% else %}{% trans "Display list of fixed assets with their values and depreciation." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-dark{% endblock %}
{% block quick_actions_header_class %}bg-dark{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button onclick="window.print()" class="btn btn-dark">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'accounting:reports_index' %}" class="btn btn-outline-info">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة للتقارير{% else %}{% trans "Back to Reports" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تقرير الأصول{% else %}{% trans "Asset Report" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-dark text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}تقرير الأصول{% else %}{% trans "Asset Report" %}{% endif %}
                </h5>
                <div class="d-flex gap-2">
                    <button onclick="window.print()" class="btn btn-sm btn-light">
                        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}تقرير الأصول{% else %}{% trans "Asset Report" %}{% endif %}</h5>
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %} 
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                <!-- Filter Form -->
                <form method="get" class="mb-4 no-print">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}فئة الأصل{% else %}{% trans "Asset Category" %}{% endif %}</label>
                            <select name="category_id" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع الفئات{% else %}{% trans "All Categories" %}{% endif %}</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}" {% if category.id|stringformat:"s" == selected_category_id %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</label>
                            <select name="state" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}جميع الحالات{% else %}{% trans "All States" %}{% endif %}</option>
                                <option value="active" {% if selected_state == 'active' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </option>
                                <option value="disposed" {% if selected_state == 'disposed' %}selected{% endif %}>
                                    {% if LANGUAGE_CODE == 'ar' %}مستبعد{% else %}{% trans "Disposed" %}{% endif %}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-dark">
                                    <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تطبيق{% else %}{% trans "Apply" %}{% endif %}
                                </button>
                                <a href="{% url 'accounting:report_asset_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </form>

                {% if assets %}
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الأصل{% else %}{% trans "Asset Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الفئة{% else %}{% trans "Category" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الشراء{% else %}{% trans "Purchase Date" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}قيمة الشراء{% else %}{% trans "Purchase Value" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}قيمة الخردة{% else %}{% trans "Salvage Value" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}الإهلاك المتراكم{% else %}{% trans "Accumulated Depreciation" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}القيمة الحالية{% else %}{% trans "Current Value" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for asset in assets %}
                            <tr>
                                <td>{{ asset.name }}</td>
                                <td>{{ asset.category.name }}</td>
                                <td>{{ asset.purchase_date }}</td>
                                <td class="text-end">{{ asset.purchase_value|floatformat:2 }}</td>
                                <td class="text-end">{{ asset.salvage_value|floatformat:2 }}</td>
                                <td class="text-end">{{ asset.accumulated_depreciation|floatformat:2 }}</td>
                                <td class="text-end">
                                    {% with current_value=asset.purchase_value|add:"-"|add:asset.accumulated_depreciation %}
                                    {{ current_value|floatformat:2 }}
                                    {% endwith %}
                                </td>
                                <td>
                                    {% if asset.state == 'active' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% elif asset.state == 'disposed' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مستبعد{% else %}{% trans "Disposed" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="3">{% if LANGUAGE_CODE == 'ar' %}الإجمالي:{% else %}{% trans "Total:" %}{% endif %}</th>
                                <th class="text-end">{{ total_purchase_value|floatformat:2 }}</th>
                                <th class="text-end">{{ total_salvage_value|floatformat:2 }}</th>
                                <th class="text-end">{{ total_depreciated_value|floatformat:2 }}</th>
                                <th class="text-end">{{ total_current_value|floatformat:2 }}</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Summary Cards -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title text-primary">{% if LANGUAGE_CODE == 'ar' %}إجمالي قيمة الشراء{% else %}{% trans "Total Purchase Value" %}{% endif %}</h5>
                                <h3 class="text-primary">{{ total_purchase_value|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h5 class="card-title text-warning">{% if LANGUAGE_CODE == 'ar' %}إجمالي قيمة الخردة{% else %}{% trans "Total Salvage Value" %}{% endif %}</h5>
                                <h3 class="text-warning">{{ total_salvage_value|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h5 class="card-title text-danger">{% if LANGUAGE_CODE == 'ar' %}إجمالي الإهلاك{% else %}{% trans "Total Depreciation" %}{% endif %}</h5>
                                <h3 class="text-danger">{{ total_depreciated_value|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">{% if LANGUAGE_CODE == 'ar' %}إجمالي القيمة الحالية{% else %}{% trans "Total Current Value" %}{% endif %}</h5>
                                <h3 class="text-success">{{ total_current_value|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-building fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد أصول{% else %}{% trans "No assets found" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد أصول مطابقة للمعايير المحددة.{% else %}{% trans "No assets found matching the specified criteria." %}{% endif %}</p>
                    <a href="{% url 'accounting:asset_create' %}" class="btn btn-dark">
                        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة أصل جديد{% else %}{% trans "Add New Asset" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-dark {
        background-color: #343a40 !important;
    }
    
    @media print {
        .sidebar, .quick-actions, .btn, .card-header .btn, .no-print {
            display: none !important;
        }
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .print-header {
            margin-bottom: 30px !important;
        }
        .table {
            font-size: 12px;
        }
        .table th, .table td {
            padding: 8px 4px;
        }
    }
    
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
</style>
{% endblock %}
