{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}كشف حساب الموردين{% else %}{% trans "Vendor Statement" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}كشف حساب الموردين{% else %}{% trans "Vendor Statement" %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-warning{% endblock %}
{% block quick_actions_header_class %}bg-warning{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:reports_index' %}" class="btn btn-outline-warning">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة للتقارير{% else %}{% trans "Back to Reports" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}كشف حساب الموردين{% else %}{% trans "Vendor Statement" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-truck me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}كشف حساب الموردين{% else %}{% trans "Vendor Statement" %}{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}هذا التقرير قيد التطوير. سيتم إضافة البيانات الفعلية قريباً.{% else %}{% trans "This report is under development. Actual data will be added soon." %}{% endif %}
                </div>
                
                <div class="text-center py-5">
                    <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}كشف حساب الموردين{% else %}{% trans "Vendor Statement" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}سيتم إضافة هذه الميزة قريباً{% else %}{% trans "This feature will be added soon" %}{% endif %}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
