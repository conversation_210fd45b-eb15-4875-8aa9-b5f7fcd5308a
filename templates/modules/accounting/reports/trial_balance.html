{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض جميع الحسابات مع أرصدتها المدينة والدائنة للتأكد من توازن الدفاتر.{% else %}{% trans "Display all accounts with their debit and credit balances to ensure books are balanced." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-info{% endblock %}
{% block quick_actions_header_class %}bg-info{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button onclick="window.print()" class="btn btn-info">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'accounting:report_income_statement' %}" class="btn btn-outline-success">
        <i class="fas fa-chart-line me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}
    </a>
    <a href="{% url 'accounting:report_balance_sheet' %}" class="btn btn-outline-primary">
        <i class="fas fa-file-invoice me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% else %}

        <div class="card shadow-sm">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-balance-scale me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}
                </h5>
                <div class="d-flex gap-2">
                    <button onclick="window.print()" class="btn btn-sm btn-light">
                        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}</h5>
                        <p class="text-muted mb-0">
                            {% if LANGUAGE_CODE == 'ar' %}السنة المالية:{% else %}{% trans "Fiscal Year:" %}{% endif %}
                            {{ fiscal_year.name }} ({{ fiscal_year.start_date }} - {{ fiscal_year.end_date }})
                        </p>
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %}
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                {% if accounts_data %}
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead class="table-info">
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الحساب{% else %}{% trans "Account Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الحساب{% else %}{% trans "Account Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نوع الحساب{% else %}{% trans "Account Type" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}مدين{% else %}{% trans "Debit" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}دائن{% else %}{% trans "Credit" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts_data %}
                            <tr>
                                <td>{{ account.code }}</td>
                                <td>{{ account.name }}</td>
                                <td>{{ account.type }}</td>
                                <td class="text-end">
                                    {% if account.debit > 0 %}
                                        {{ account.debit|floatformat:2 }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    {% if account.credit > 0 %}
                                        {{ account.credit|floatformat:2 }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="3" class="text-end">{% if LANGUAGE_CODE == 'ar' %}الإجمالي:{% else %}{% trans "Total:" %}{% endif %}</th>
                                <th class="text-end">{{ total_debit|floatformat:2 }}</th>
                                <th class="text-end">{{ total_credit|floatformat:2 }}</th>
                            </tr>
                            <tr>
                                <th colspan="3" class="text-end">{% if LANGUAGE_CODE == 'ar' %}الفرق:{% else %}{% trans "Difference:" %}{% endif %}</th>
                                <th colspan="2" class="text-end">
                                    {% with difference=total_debit|add:total_credit|add:"-"|add:total_credit %}
                                    {% if difference == 0 %}
                                        <span class="text-success">{{ difference|floatformat:2 }} ({% if LANGUAGE_CODE == 'ar' %}متوازن{% else %}{% trans "Balanced" %}{% endif %})</span>
                                    {% else %}
                                        <span class="text-danger">{{ difference|floatformat:2 }} ({% if LANGUAGE_CODE == 'ar' %}غير متوازن{% else %}{% trans "Unbalanced" %}{% endif %})</span>
                                    {% endif %}
                                    {% endwith %}
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">{% if LANGUAGE_CODE == 'ar' %}إجمالي المدين{% else %}{% trans "Total Debit" %}{% endif %}</h5>
                                <h3 class="text-info">{{ total_debit|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">{% if LANGUAGE_CODE == 'ar' %}إجمالي الدائن{% else %}{% trans "Total Credit" %}{% endif %}</h5>
                                <h3 class="text-info">{{ total_credit|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-{% if total_debit == total_credit %}success{% else %}danger{% endif %}">
                            <div class="card-body text-center">
                                <h5 class="card-title text-{% if total_debit == total_credit %}success{% else %}danger{% endif %}">{% if LANGUAGE_CODE == 'ar' %}حالة التوازن{% else %}{% trans "Balance Status" %}{% endif %}</h5>
                                <h3 class="text-{% if total_debit == total_credit %}success{% else %}danger{% endif %}">
                                    {% if total_debit == total_credit %}
                                        <i class="fas fa-check-circle me-2"></i>{% if LANGUAGE_CODE == 'ar' %}متوازن{% else %}{% trans "Balanced" %}{% endif %}
                                    {% else %}
                                        <i class="fas fa-exclamation-triangle me-2"></i>{% if LANGUAGE_CODE == 'ar' %}غير متوازن{% else %}{% trans "Unbalanced" %}{% endif %}
                                    {% endif %}
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات لعرضها{% else %}{% trans "No data to display" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد حسابات بأرصدة في السنة المالية الحالية.{% else %}{% trans "No accounts with balances found in the current fiscal year." %}{% endif %}</p>
                    <a href="{% url 'accounting:journal_entry_create' %}" class="btn btn-info">
                        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد محاسبي{% else %}{% trans "Create Journal Entry" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-info {
        background-color: #17a2b8 !important;
    }

    @media print {
        .sidebar, .quick-actions, .btn, .card-header .btn {
            display: none !important;
        }
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .print-header {
            margin-bottom: 30px !important;
        }
        .table {
            font-size: 12px;
        }
        .table th, .table td {
            padding: 8px 4px;
        }
    }

    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
</style>
{% endblock %}
