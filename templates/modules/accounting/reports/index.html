{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إنشاء وعرض التقارير المالية المختلفة مثل ميزان المراجعة وقائمة الدخل والميزانية العمومية.{% else %}{% trans "Generate and view various financial reports such as trial balance, income statement, and balance sheet." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-info{% endblock %}
{% block quick_actions_header_class %}bg-info{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:report_trial_balance' %}" class="btn btn-info">
        <i class="fas fa-balance-scale me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}
    </a>
    <a href="{% url 'accounting:report_income_statement' %}" class="btn btn-outline-info">
        <i class="fas fa-chart-line me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}
    </a>
    <a href="{% url 'accounting:report_balance_sheet' %}" class="btn btn-outline-info">
        <i class="fas fa-file-invoice me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% endif %}

        <!-- Financial Reports Grid -->
        <div class="row">
            <!-- Primary Reports -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm border-info">
                    <div class="card-header bg-info text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-balance-scale me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{% if LANGUAGE_CODE == 'ar' %}عرض جميع الحسابات مع أرصدتها المدينة والدائنة للتأكد من توازن الدفاتر.{% else %}{% trans "Display all accounts with their debit and credit balances to ensure books are balanced." %}{% endif %}</p>
                        <a href="{% url 'accounting:report_trial_balance' %}" class="btn btn-info">
                            <i class="fas fa-eye me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقرير{% else %}{% trans "View Report" %}{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{% if LANGUAGE_CODE == 'ar' %}عرض الإيرادات والمصروفات وصافي الربح أو الخسارة لفترة محددة.{% else %}{% trans "Show revenues, expenses, and net profit or loss for a specific period." %}{% endif %}</p>
                        <a href="{% url 'accounting:report_income_statement' %}" class="btn btn-success">
                            <i class="fas fa-eye me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقرير{% else %}{% trans "View Report" %}{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-invoice me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{% if LANGUAGE_CODE == 'ar' %}عرض الأصول والخصوم وحقوق الملكية في تاريخ محدد.{% else %}{% trans "Display assets, liabilities, and equity at a specific date." %}{% endif %}</p>
                        <a href="{% url 'accounting:report_balance_sheet' %}" class="btn btn-primary">
                            <i class="fas fa-eye me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقرير{% else %}{% trans "View Report" %}{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Secondary Reports -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm border-warning">
                    <div class="card-header bg-warning text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}كشف حساب{% else %}{% trans "Account Statement" %}{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل حركة حساب معين خلال فترة محددة.{% else %}{% trans "Show detailed transactions for a specific account during a period." %}{% endif %}</p>
                        <a href="{% url 'accounting:report_account_statement' %}" class="btn btn-warning">
                            <i class="fas fa-eye me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقرير{% else %}{% trans "View Report" %}{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm border-secondary">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-money-check me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}تقرير الشيكات{% else %}{% trans "Check Status Report" %}{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{% if LANGUAGE_CODE == 'ar' %}عرض حالة الشيكات الصادرة والواردة.{% else %}{% trans "Display status of issued and received checks." %}{% endif %}</p>
                        <a href="{% url 'accounting:report_check_status' %}" class="btn btn-secondary">
                            <i class="fas fa-eye me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقرير{% else %}{% trans "View Report" %}{% endif %}
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm border-dark">
                    <div class="card-header bg-dark text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}تقرير الأصول{% else %}{% trans "Asset Report" %}{% endif %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">{% if LANGUAGE_CODE == 'ar' %}عرض قائمة الأصول الثابتة وقيمها والإهلاكات.{% else %}{% trans "Display list of fixed assets with their values and depreciation." %}{% endif %}</p>
                        <a href="{% url 'accounting:report_asset_list' %}" class="btn btn-dark">
                            <i class="fas fa-eye me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقرير{% else %}{% trans "View Report" %}{% endif %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Reports Section -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تقارير إضافية{% else %}{% trans "Additional Reports" %}{% endif %}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-users me-2 text-info"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}كشف حساب العملاء{% else %}{% trans "Customer Statement" %}{% endif %}
                                        </span>
                                        <a href="{% url 'accounting:report_customer_statement' %}" class="btn btn-sm btn-outline-info">{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans "View" %}{% endif %}</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-truck me-2 text-warning"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}كشف حساب الموردين{% else %}{% trans "Vendor Statement" %}{% endif %}
                                        </span>
                                        <a href="{% url 'accounting:report_vendor_statement' %}" class="btn btn-sm btn-outline-warning">{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans "View" %}{% endif %}</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-university me-2 text-primary"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}كشف حساب البنك{% else %}{% trans "Bank Statement" %}{% endif %}
                                        </span>
                                        <a href="{% url 'accounting:report_bank_statement' %}" class="btn btn-sm btn-outline-primary">{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans "View" %}{% endif %}</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-cash-register me-2 text-success"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}كشف حساب الخزينة{% else %}{% trans "Cash Statement" %}{% endif %}
                                        </span>
                                        <a href="{% url 'accounting:report_cash_statement' %}" class="btn btn-sm btn-outline-success">{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans "View" %}{% endif %}</a>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>
                                            <i class="fas fa-chart-bar me-2 text-danger"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}جدول الإهلاك{% else %}{% trans "Depreciation Schedule" %}{% endif %}
                                        </span>
                                        <a href="{% url 'accounting:report_depreciation_schedule' %}" class="btn btn-sm btn-outline-danger">{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans "View" %}{% endif %}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-info {
        background-color: #17a2b8 !important;
    }
    .card-header.bg-success {
        background-color: #28a745 !important;
    }
    .card-header.bg-primary {
        background-color: #007bff !important;
    }
    .card-header.bg-warning {
        background-color: #ffc107 !important;
    }
    .card-header.bg-secondary {
        background-color: #6c757d !important;
    }
    .card-header.bg-dark {
        background-color: #343a40 !important;
    }
    .card {
        transition: transform 0.2s;
    }
    .card:hover {
        transform: translateY(-2px);
    }
</style>
{% endblock %}
