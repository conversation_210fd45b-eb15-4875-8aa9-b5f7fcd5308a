{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}كشف حساب{% else %}{% trans "Account Statement" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}كشف حساب{% else %}{% trans "Account Statement" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل حركة حساب معين خلال فترة محددة.{% else %}{% trans "Show detailed transactions for a specific account during a period." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-warning{% endblock %}
{% block quick_actions_header_class %}bg-warning{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيود اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button onclick="window.print()" class="btn btn-warning">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </button>
    <a href="{% url 'accounting:report_trial_balance' %}" class="btn btn-outline-info">
        <i class="fas fa-balance-scale me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ميزان المراجعة{% else %}{% trans "Trial Balance" %}{% endif %}
    </a>
    <a href="{% url 'accounting:report_income_statement' %}" class="btn btn-outline-success">
        <i class="fas fa-chart-line me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}كشف حساب{% else %}{% trans "Account Statement" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% else %}

        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}كشف حساب{% else %}{% trans "Account Statement" %}{% endif %}
                </h5>
                <div class="d-flex gap-2">
                    {% if account %}
                    <button onclick="window.print()" class="btn btn-sm btn-dark">
                        <i class="fas fa-print me-1"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
                    </button>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <!-- Account Selection Form -->
                <form method="get" class="mb-4 no-print">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}اختر الحساب{% else %}{% trans "Select Account" %}{% endif %}</label>
                            <select name="account_id" class="form-select" required>
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}-- اختر حساب --{% else %}{% trans "-- Select Account --" %}{% endif %}</option>
                                {% for acc in accounts %}
                                <option value="{{ acc.id }}" {% if account and acc.id == account.id %}selected{% endif %}>
                                    {{ acc.code }} - {{ acc.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="start_date" class="form-control" value="{{ request.GET.start_date }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="end_date" class="form-control" value="{{ request.GET.end_date }}">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-search me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض كشف الحساب{% else %}{% trans "Show Statement" %}{% endif %}
                            </button>
                            <a href="{% url 'accounting:report_account_statement' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>

                {% if account %}
                <!-- Report Header -->
                <div class="row mb-4 print-header">
                    <div class="col-md-12 text-center">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }}" class="img-fluid mb-2" style="max-height: 80px;">
                        {% endif %}
                        <h4 class="mb-1">{{ company.name }}</h4>
                        <h5 class="text-muted mb-1">{% if LANGUAGE_CODE == 'ar' %}كشف حساب{% else %}{% trans "Account Statement" %}{% endif %}</h5>
                        <p class="text-muted mb-0">
                            <strong>{% if LANGUAGE_CODE == 'ar' %}الحساب:{% else %}{% trans "Account:" %}{% endif %}</strong> 
                            {{ account.code }} - {{ account.name }}
                        </p>
                        {% if request.GET.start_date and request.GET.end_date %}
                        <p class="text-muted mb-0">
                            {% if LANGUAGE_CODE == 'ar' %}للفترة من{% else %}{% trans "For the period from" %}{% endif %} 
                            {{ request.GET.start_date }} {% if LANGUAGE_CODE == 'ar' %}إلى{% else %}{% trans "to" %}{% endif %} {{ request.GET.end_date }}
                        </p>
                        {% endif %}
                        <p class="text-muted">
                            {% if LANGUAGE_CODE == 'ar' %}تاريخ التقرير:{% else %}{% trans "Report Date:" %}{% endif %} 
                            {% now "Y-m-d H:i" %}
                        </p>
                    </div>
                </div>

                <!-- Account Information -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card border-warning">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>{% if LANGUAGE_CODE == 'ar' %}رقم الحساب:{% else %}{% trans "Account Code:" %}{% endif %}</strong><br>
                                        {{ account.code }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>{% if LANGUAGE_CODE == 'ar' %}اسم الحساب:{% else %}{% trans "Account Name:" %}{% endif %}</strong><br>
                                        {{ account.name }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>{% if LANGUAGE_CODE == 'ar' %}نوع الحساب:{% else %}{% trans "Account Type:" %}{% endif %}</strong><br>
                                        {{ account.type.name }}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>{% if LANGUAGE_CODE == 'ar' %}الحالة:{% else %}{% trans "Status:" %}{% endif %}</strong><br>
                                        {% if account.is_active %}
                                            <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {% if transactions %}
                <div class="table-responsive">
                    <table class="table table-hover table-bordered">
                        <thead class="table-warning">
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البيان{% else %}{% trans "Description" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}مدين{% else %}{% trans "Debit" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}دائن{% else %}{% trans "Credit" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}الرصيد{% else %}{% trans "Balance" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in transactions %}
                            <tr>
                                <td>{{ transaction.date }}</td>
                                <td>{{ transaction.reference }}</td>
                                <td>{{ transaction.description }}</td>
                                <td class="text-end">
                                    {% if transaction.debit > 0 %}
                                        {{ transaction.debit|floatformat:2 }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    {% if transaction.credit > 0 %}
                                        {{ transaction.credit|floatformat:2 }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    <span class="{% if transaction.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {{ transaction.balance|floatformat:2 }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="3">{% if LANGUAGE_CODE == 'ar' %}الإجمالي:{% else %}{% trans "Total:" %}{% endif %}</th>
                                <th class="text-end">{{ transactions|sum_debit|floatformat:2 }}</th>
                                <th class="text-end">{{ transactions|sum_credit|floatformat:2 }}</th>
                                <th class="text-end">
                                    {% with final_balance=transactions|last_balance %}
                                    <span class="{% if final_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                                        {{ final_balance|floatformat:2 }}
                                    </span>
                                    {% endwith %}
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Summary Statistics -->
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">{% if LANGUAGE_CODE == 'ar' %}إجمالي المدين{% else %}{% trans "Total Debit" %}{% endif %}</h5>
                                <h3 class="text-info">{{ transactions|sum_debit|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">{% if LANGUAGE_CODE == 'ar' %}إجمالي الدائن{% else %}{% trans "Total Credit" %}{% endif %}</h5>
                                <h3 class="text-info">{{ transactions|sum_credit|floatformat:2 }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h5 class="card-title text-warning">{% if LANGUAGE_CODE == 'ar' %}عدد الحركات{% else %}{% trans "Transaction Count" %}{% endif %}</h5>
                                <h3 class="text-warning">{{ transactions|length }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-{% if transactions|last_balance >= 0 %}success{% else %}danger{% endif %}">
                            <div class="card-body text-center">
                                <h5 class="card-title text-{% if transactions|last_balance >= 0 %}success{% else %}danger{% endif %}">{% if LANGUAGE_CODE == 'ar' %}الرصيد النهائي{% else %}{% trans "Final Balance" %}{% endif %}</h5>
                                <h3 class="text-{% if transactions|last_balance >= 0 %}success{% else %}danger{% endif %}">
                                    {{ transactions|last_balance|floatformat:2 }}
                                </h3>
                            </div>
                        </div>
                    </div>
                </div>

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد حركات{% else %}{% trans "No transactions found" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد حركات لهذا الحساب في الفترة المحددة.{% else %}{% trans "No transactions found for this account in the specified period." %}{% endif %}</p>
                </div>
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% if LANGUAGE_CODE == 'ar' %}اختر حساب لعرض كشف الحساب{% else %}{% trans "Select an account to view statement" %}{% endif %}</h5>
                    <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}استخدم النموذج أعلاه لاختيار الحساب والفترة الزمنية.{% else %}{% trans "Use the form above to select an account and time period." %}{% endif %}</p>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card-header.bg-warning {
        background-color: #ffc107 !important;
    }
    
    @media print {
        .sidebar, .quick-actions, .btn, .card-header .btn, .no-print {
            display: none !important;
        }
        .main-content {
            margin-left: 0 !important;
            width: 100% !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .print-header {
            margin-bottom: 30px !important;
        }
        .table {
            font-size: 12px;
        }
        .table th, .table td {
            padding: 8px 4px;
        }
    }
    
    .table-bordered th,
    .table-bordered td {
        border: 1px solid #dee2e6;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.075);
    }
</style>
{% endblock %}
