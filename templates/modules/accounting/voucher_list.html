{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}
    {% if voucher_type == 'receipt' %}
        {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if voucher_type == 'receipt' %}
        {% if LANGUAGE_CODE == 'ar' %}إدارة سندات القبض. إنشاء وتعديل وعرض سندات القبض.{% else %}{% trans "Manage receipt vouchers. Create, edit, and view receipt vouchers." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إدارة سندات الصرف. إنشاء وتعديل وعرض سندات الصرف.{% else %}{% trans "Manage payment vouchers. Create, edit, and view payment vouchers." %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}?voucher_type=receipt" class="list-group-item list-group-item-action{% if voucher_type == 'receipt' %} active{% endif %}">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}?voucher_type=payment" class="list-group-item list-group-item-action{% if voucher_type == 'payment' %} active{% endif %}">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    {% if voucher_type == 'receipt' %}
        <a href="{% url 'accounting:receipt_voucher_create' %}" class="btn btn-success">
            <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سند قبض جديد{% else %}{% trans "New Receipt Voucher" %}{% endif %}
        </a>
        <a href="{% url 'accounting:voucher_list' %}?voucher_type=payment" class="btn btn-outline-danger">
            <i class="fas fa-exchange-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض سندات الصرف{% else %}{% trans "View Payment Vouchers" %}{% endif %}
        </a>
    {% else %}
        <a href="{% url 'accounting:payment_voucher_create' %}" class="btn btn-danger">
            <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سند صرف جديد{% else %}{% trans "New Payment Voucher" %}{% endif %}
        </a>
        <a href="{% url 'accounting:voucher_list' %}?voucher_type=receipt" class="btn btn-outline-success">
            <i class="fas fa-exchange-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض سندات القبض{% else %}{% trans "View Receipt Vouchers" %}{% endif %}
        </a>
    {% endif %}
</div>
{% endblock %}

{% block content_title %}
    {% if voucher_type == 'receipt' %}
        {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}
            لا يوجد سنة مالية نشطة. يرجى <a href="{% url 'accounting:fiscal_year_create' %}">إنشاء سنة مالية</a> أولاً.
            {% else %}
            No active fiscal year found. Please <a href="{% url 'accounting:fiscal_year_create' %}">create a fiscal year</a> first.
            {% endif %}
        </div>
        {% else %}
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center {% if voucher_type == 'receipt' %}bg-success-subtle{% else %}bg-danger-subtle{% endif %}">
                <h5 class="card-title mb-0">
                    {% if voucher_type == 'receipt' %}
                        {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <form method="get" class="row g-3">
                        <input type="hidden" name="voucher_type" value="{{ voucher_type }}">
                        <div class="col-md-3">
                            <label for="payment_method" class="form-label">{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %}</label>
                            <select name="payment_method" id="payment_method" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                {% for method in payment_methods %}
                                <option value="{{ method.id }}" {% if selected_method == method.id %}selected{% endif %}>{{ method.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="state" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</label>
                            <select name="state" id="state" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="draft" {% if selected_state == 'draft' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</option>
                                <option value="posted" {% if selected_state == 'posted' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</option>
                                <option value="cancelled" {% if selected_state == 'cancelled' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-3">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block w-100">
                                <i class="fas fa-search me-2"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                        </div>
                    </form>
                </div>

                {% if vouchers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشريك{% else %}{% trans "Partner" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for voucher in vouchers %}
                            <tr>
                                <td>{{ voucher.date }}</td>
                                <td>{{ voucher.number }}</td>
                                <td>{{ voucher.reference }}</td>
                                <td>{{ voucher.partner_name }}</td>
                                <td>{{ voucher.payment_method.name }}</td>
                                <td>
                                    {% if voucher.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif voucher.state == 'posted' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                    {% elif voucher.state == 'cancelled' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td class="text-end">{{ voucher.amount }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'accounting:voucher_detail' voucher.id %}" class="btn btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if voucher.state == 'draft' %}
                                        <a href="{% url 'accounting:voucher_edit' voucher.id %}" class="btn btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'accounting:voucher_post' voucher.id %}" class="btn btn-outline-success" title="{% if LANGUAGE_CODE == 'ar' %}ترحيل{% else %}{% trans 'Post' %}{% endif %}" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من ترحيل هذا السند؟{% else %}{% trans 'Are you sure you want to post this voucher?' %}{% endif %}')">
                                            <i class="fas fa-check"></i>
                                        </a>
                                        {% elif voucher.state == 'posted' %}
                                        <a href="{% url 'accounting:voucher_cancel' voucher.id %}" class="btn btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans 'Cancel' %}{% endif %}" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من إلغاء هذا السند؟{% else %}{% trans 'Are you sure you want to cancel this voucher?' %}{% endif %}')">
                                            <i class="fas fa-times"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد سندات{% else %}{% trans "No vouchers found" %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
