{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة الحسابات البنكية. إنشاء وتعديل وعرض الحسابات البنكية.{% else %}{% trans "Manage bank accounts. Create, edit, and view bank accounts." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:bank_account_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}حساب بنكي جديد{% else %}{% trans "New Bank Account" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_list' %}" class="btn btn-outline-success">
        <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قائمة البنوك{% else %}{% trans "Banks List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:bank_account_create' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}جديد{% else %}{% trans "New" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="bank" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البنك{% else %}{% trans "Bank" %}{% endif %}</label>
                            <select name="bank" id="bank" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                {% for bank in banks %}
                                <option value="{{ bank.id }}" {% if selected_bank == bank.id %}selected{% endif %}>{{ bank.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="account_type" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الحساب{% else %}{% trans "Account Type" %}{% endif %}</label>
                            <select name="account_type" id="account_type" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="current" {% if selected_account_type == 'current' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}حساب جاري{% else %}{% trans "Current Account" %}{% endif %}</option>
                                <option value="savings" {% if selected_account_type == 'savings' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}حساب توفير{% else %}{% trans "Savings Account" %}{% endif %}</option>
                                <option value="deposit" {% if selected_account_type == 'deposit' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}حساب وديعة{% else %}{% trans "Deposit Account" %}{% endif %}</option>
                                <option value="credit" {% if selected_account_type == 'credit' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}حساب ائتمان{% else %}{% trans "Credit Account" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="currency" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العملة{% else %}{% trans "Currency" %}{% endif %}</label>
                            <select name="currency" id="currency" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="USD" {% if selected_currency == 'USD' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}دولار أمريكي{% else %}{% trans "US Dollar" %}{% endif %}</option>
                                <option value="EUR" {% if selected_currency == 'EUR' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}يورو{% else %}{% trans "Euro" %}{% endif %}</option>
                                <option value="GBP" {% if selected_currency == 'GBP' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}جنيه إسترليني{% else %}{% trans "British Pound" %}{% endif %}</option>
                                <option value="SAR" {% if selected_currency == 'SAR' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}ريال سعودي{% else %}{% trans "Saudi Riyal" %}{% endif %}</option>
                                <option value="AED" {% if selected_currency == 'AED' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}درهم إماراتي{% else %}{% trans "UAE Dirham" %}{% endif %}</option>
                                <option value="EGP" {% if selected_currency == 'EGP' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}جنيه مصري{% else %}{% trans "Egyptian Pound" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="is_active" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</label>
                            <select name="is_active" id="is_active" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="1" {% if selected_is_active == '1' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</option>
                                <option value="0" {% if selected_is_active == '0' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-12 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                            <a href="{% url 'accounting:bank_account_list' %}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </form>
                </div>

                {% if bank_accounts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البنك{% else %}{% trans "Bank" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الحساب{% else %}{% trans "Account Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نوع الحساب{% else %}{% trans "Account Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العملة{% else %}{% trans "Currency" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in bank_accounts %}
                            <tr>
                                <td>{{ account.bank.name }}</td>
                                <td>{{ account.number }}</td>
                                <td>{{ account.name }}</td>
                                <td>
                                    {% if account.account_type == 'current' %}
                                    {% if LANGUAGE_CODE == 'ar' %}حساب جاري{% else %}{% trans "Current Account" %}{% endif %}
                                    {% elif account.account_type == 'savings' %}
                                    {% if LANGUAGE_CODE == 'ar' %}حساب توفير{% else %}{% trans "Savings Account" %}{% endif %}
                                    {% elif account.account_type == 'deposit' %}
                                    {% if LANGUAGE_CODE == 'ar' %}حساب وديعة{% else %}{% trans "Deposit Account" %}{% endif %}
                                    {% elif account.account_type == 'credit' %}
                                    {% if LANGUAGE_CODE == 'ar' %}حساب ائتمان{% else %}{% trans "Credit Account" %}{% endif %}
                                    {% endif %}
                                </td>
                                <td>{{ account.currency }}</td>
                                <td>
                                    {% if account.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:bank_account_detail' account.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:bank_account_edit' account.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounting:check_book_list' %}?bank_account={{ account.id }}" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans 'Check Books' %}{% endif %}">
                                        <i class="fas fa-book"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد حسابات بنكية. يرجى إنشاء حساب بنكي جديد.{% else %}{% trans "No bank accounts found. Please create a new bank account." %}{% endif %}
                    <a href="{% url 'accounting:bank_account_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء حساب بنكي{% else %}{% trans "Create Bank Account" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
