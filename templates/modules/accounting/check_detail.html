{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الشيك{% else %}{% trans "Check Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الشيك{% else %}{% trans "Check Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل الشيك.{% else %}{% trans "View check details." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:check_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    
    {% if check.state == 'draft' %}
    <a href="{% url 'accounting:check_edit' check.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    {% endif %}
    
    {% if check.check_book %}
    <a href="{% url 'accounting:check_book_detail' check.check_book.id %}" class="btn btn-outline-info">
        <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض دفتر الشيكات{% else %}{% trans "View Check Book" %}{% endif %}
    </a>
    {% endif %}
    
    {% if check.bank_account %}
    <a href="{% url 'accounting:bank_account_detail' check.bank_account.id %}" class="btn btn-outline-primary">
        <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض الحساب البنكي{% else %}{% trans "View Bank Account" %}{% endif %}
    </a>
    {% endif %}
    
    {% if check.check_type == 'outgoing' %}
        {% if check.state == 'draft' %}
        <a href="{% url 'accounting:check_change_state' check.id %}?state=registered" class="btn btn-outline-success">
            <i class="fas fa-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تسجيل{% else %}{% trans "Register" %}{% endif %}
        </a>
        {% elif check.state == 'registered' %}
        <a href="{% url 'accounting:check_change_state' check.id %}?state=collected" class="btn btn-outline-success">
            <i class="fas fa-check-double me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تحصيل{% else %}{% trans "Collect" %}{% endif %}
        </a>
        <a href="{% url 'accounting:check_change_state' check.id %}?state=bounced" class="btn btn-outline-danger">
            <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ارتجاع{% else %}{% trans "Bounce" %}{% endif %}
        </a>
        {% endif %}
    {% else %}
        {% if check.state == 'draft' %}
        <a href="{% url 'accounting:check_change_state' check.id %}?state=registered" class="btn btn-outline-success">
            <i class="fas fa-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تسجيل{% else %}{% trans "Register" %}{% endif %}
        </a>
        {% elif check.state == 'registered' %}
        <a href="{% url 'accounting:check_change_state' check.id %}?state=deposited" class="btn btn-outline-success">
            <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إيداع{% else %}{% trans "Deposit" %}{% endif %}
        </a>
        <a href="{% url 'accounting:check_change_state' check.id %}?state=bounced" class="btn btn-outline-danger">
            <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ارتجاع{% else %}{% trans "Bounce" %}{% endif %}
        </a>
        {% endif %}
    {% endif %}
    
    {% if check.state != 'cancelled' and check.state != 'collected' and check.state != 'deposited' %}
    <a href="{% url 'accounting:check_change_state' check.id %}?state=cancelled" class="btn btn-outline-danger">
        <i class="fas fa-ban me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
    </a>
    {% endif %}
    
    <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الشيك{% else %}{% trans "Check Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {% if check.check_type == 'outgoing' %}
                        {% if LANGUAGE_CODE == 'ar' %}شيك صادر{% else %}{% trans "Outgoing Check" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}شيك وارد{% else %}{% trans "Incoming Check" %}{% endif %}
                    {% endif %}
                    #{{ check.number }}
                </h5>
                <span class="badge {% if check.state == 'draft' %}bg-warning{% elif check.state == 'registered' %}bg-info{% elif check.state == 'pending' %}bg-primary{% elif check.state == 'collected' or check.state == 'deposited' %}bg-success{% elif check.state == 'bounced' %}bg-danger{% else %}bg-secondary{% endif %}">
                    {% if check.state == 'draft' %}
                        {% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}
                    {% elif check.state == 'registered' %}
                        {% if LANGUAGE_CODE == 'ar' %}مسجل{% else %}{% trans "Registered" %}{% endif %}
                    {% elif check.state == 'pending' %}
                        {% if LANGUAGE_CODE == 'ar' %}معلق{% else %}{% trans "Pending" %}{% endif %}
                    {% elif check.state == 'collected' %}
                        {% if LANGUAGE_CODE == 'ar' %}محصل{% else %}{% trans "Collected" %}{% endif %}
                    {% elif check.state == 'deposited' %}
                        {% if LANGUAGE_CODE == 'ar' %}مودع{% else %}{% trans "Deposited" %}{% endif %}
                    {% elif check.state == 'bounced' %}
                        {% if LANGUAGE_CODE == 'ar' %}مرتجع{% else %}{% trans "Bounced" %}{% endif %}
                    {% elif check.state == 'cancelled' %}
                        {% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}
                    {% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات الشيك{% else %}{% trans "Check Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %}:</td>
                                <td>{{ check.number }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}:</td>
                                <td>
                                    {% if check.check_type == 'outgoing' %}
                                        {% if LANGUAGE_CODE == 'ar' %}شيك صادر{% else %}{% trans "Outgoing Check" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}شيك وارد{% else %}{% trans "Incoming Check" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}:</td>
                                <td>{{ check.date }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الاستحقاق{% else %}{% trans "Due Date" %}{% endif %}:</td>
                                <td>{{ check.due_date }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}:</td>
                                <td>{{ check.amount }} {{ check.get_currency_display }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات البنك{% else %}{% trans "Bank Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}البنك{% else %}{% trans "Bank" %}{% endif %}:</td>
                                <td>{{ check.bank_account.bank.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الحساب البنكي{% else %}{% trans "Bank Account" %}{% endif %}:</td>
                                <td>{{ check.bank_account.number }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}دفتر الشيكات{% else %}{% trans "Check Book" %}{% endif %}:</td>
                                <td>{{ check.check_book.name|default:"-" }}</td>
                            </tr>
                        </table>
                        
                        <h6 class="fw-bold mt-4">{% if LANGUAGE_CODE == 'ar' %}معلومات الشريك{% else %}{% trans "Partner Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}نوع الشريك{% else %}{% trans "Partner Type" %}{% endif %}:</td>
                                <td>
                                    {% if check.partner_type == 'customer' %}
                                        {% if LANGUAGE_CODE == 'ar' %}عميل{% else %}{% trans "Customer" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}مورد{% else %}{% trans "Vendor" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}اسم الشريك{% else %}{% trans "Partner Name" %}{% endif %}:</td>
                                <td>{{ check.partner_name }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if check.notes %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}:</h6>
                        <p>{{ check.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                {% if check.image %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}صورة الشيك{% else %}{% trans "Check Image" %}{% endif %}:</h6>
                        <img src="{{ check.image.url }}" alt="Check Image" class="img-fluid img-thumbnail" style="max-height: 300px;">
                    </div>
                </div>
                {% endif %}
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات النظام{% else %}{% trans "System Information" %}{% endif %}:</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}:</td>
                                <td>{{ check.company.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}:</td>
                                <td>{{ check.branch.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تم الإنشاء بواسطة{% else %}{% trans "Created By" %}{% endif %}:</td>
                                <td>
                                    {% if check.created_by %}
                                        {{ check.created_by.get_full_name|default:check.created_by.username }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}غير معروف{% else %}{% trans "Unknown" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created At" %}{% endif %}:</td>
                                <td>{{ check.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        {% if journal_entries %}
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}القيود المحاسبية المرتبطة{% else %}{% trans "Related Journal Entries" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البيان{% else %}{% trans "Description" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in journal_entries %}
                            <tr>
                                <td>{{ entry.number }}</td>
                                <td>{{ entry.date }}</td>
                                <td>{{ entry.reference }}</td>
                                <td>{{ entry.name }}</td>
                                <td class="text-end">{{ entry.total_debit }}</td>
                                <td>
                                    <a href="{% url 'accounting:journal_entry_detail' entry.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
