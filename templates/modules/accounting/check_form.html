{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if check %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل شيك{% else %}{% trans "Edit Check" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if check %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل شيك{% else %}{% trans "Edit Check" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if check %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات الشيك.{% else %}{% trans "Edit check information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد.{% else %}{% trans "Create a new check." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:check_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    {% if check_book %}
    <a href="{% url 'accounting:check_book_detail' check_book.id %}" class="btn btn-outline-primary">
        <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض دفتر الشيكات{% else %}{% trans "View Check Book" %}{% endif %}
    </a>
    {% endif %}
</div>
{% endblock %}

{% block content_title %}
    {% if check %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل شيك{% else %}{% trans "Edit Check" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if check %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل شيك{% else %}{% trans "Edit Check" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء شيك جديد{% else %}{% trans "Create New Check" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate enctype="multipart/form-data">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    {{ form.company }}
                    {{ form.branch }}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.check_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الشيك{% else %}{% trans "Check Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.check_type }}
                            <script>document.getElementById('{{ form.check_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.check_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.check_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.bank_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحساب البنكي{% else %}{% trans "Bank Account" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.bank_account }}
                            <script>document.getElementById('{{ form.bank_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.bank_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.bank_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.check_book.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}دفتر الشيكات{% else %}{% trans "Check Book" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.check_book }}
                            <script>document.getElementById('{{ form.check_book.id_for_label }}').classList.add('form-select');</script>
                            {% if form.check_book.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.check_book.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم الشيك{% else %}{% trans "Check Number" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.number }}
                            <script>document.getElementById('{{ form.number.id_for_label }}').classList.add('form-control');</script>
                            {% if form.number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ الشيك{% else %}{% trans "Check Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.date }}
                            <script>document.getElementById('{{ form.date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.due_date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ الاستحقاق{% else %}{% trans "Due Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.due_date }}
                            <script>document.getElementById('{{ form.due_date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.due_date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.due_date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.amount.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.amount }}
                            <script>document.getElementById('{{ form.amount.id_for_label }}').classList.add('form-control');</script>
                            {% if form.amount.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.amount.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.currency.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العملة{% else %}{% trans "Currency" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.currency }}
                            <script>document.getElementById('{{ form.currency.id_for_label }}').classList.add('form-select');</script>
                            {% if form.currency.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.currency.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.partner_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الشريك{% else %}{% trans "Partner Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.partner_type }}
                            <script>document.getElementById('{{ form.partner_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.partner_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.partner_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.partner_name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم الشريك{% else %}{% trans "Partner Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.partner_name }}
                            <script>document.getElementById('{{ form.partner_name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.partner_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.partner_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.state.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.state }}
                            <script>document.getElementById('{{ form.state.id_for_label }}').classList.add('form-select');</script>
                            {% if form.state.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.state.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.image.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صورة الشيك{% else %}{% trans "Check Image" %}{% endif %}</label>
                            {{ form.image }}
                            <script>document.getElementById('{{ form.image.id_for_label }}').classList.add('form-control');</script>
                            {% if form.image.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.image.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if check %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:check_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
