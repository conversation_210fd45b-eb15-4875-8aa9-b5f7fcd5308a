{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if bank_account %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل حساب بنكي{% else %}{% trans "Edit Bank Account" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء حساب بنكي جديد{% else %}{% trans "Create New Bank Account" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if bank_account %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل حساب بنكي{% else %}{% trans "Edit Bank Account" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء حساب بنكي جديد{% else %}{% trans "Create New Bank Account" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if bank_account %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات الحساب البنكي.{% else %}{% trans "Edit bank account information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء حساب بنكي جديد.{% else %}{% trans "Create a new bank account." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:bank_account_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if bank_account %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل حساب بنكي{% else %}{% trans "Edit Bank Account" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء حساب بنكي جديد{% else %}{% trans "Create New Bank Account" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if bank_account %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل حساب بنكي{% else %}{% trans "Edit Bank Account" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء حساب بنكي جديد{% else %}{% trans "Create New Bank Account" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم الحساب{% else %}{% trans "Account Number" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.number }}
                            <script>document.getElementById('{{ form.number.id_for_label }}').classList.add('form-control');</script>
                            {% if form.number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.bank.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البنك{% else %}{% trans "Bank" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.bank }}
                            <script>document.getElementById('{{ form.bank.id_for_label }}').classList.add('form-select');</script>
                            {% if form.bank.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.bank.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.iban.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم الآيبان{% else %}{% trans "IBAN" %}{% endif %}</label>
                            {{ form.iban }}
                            <script>document.getElementById('{{ form.iban.id_for_label }}').classList.add('form-control');</script>
                            {% if form.iban.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.iban.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحساب المحاسبي{% else %}{% trans "Account" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.account }}
                            <script>document.getElementById('{{ form.account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.account_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الحساب{% else %}{% trans "Account Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.account_type }}
                            <script>document.getElementById('{{ form.account_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.account_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.account_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.currency.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العملة{% else %}{% trans "Currency" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.currency }}
                            <script>document.getElementById('{{ form.currency.id_for_label }}').classList.add('form-select');</script>
                            {% if form.currency.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.currency.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {{ form.company }}
                    {{ form.branch }}

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if bank_account %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:bank_account_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
