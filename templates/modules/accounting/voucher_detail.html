{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل السند{% else %}{% trans "Voucher Details" %}{% endif %}{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل السند{% else %}{% trans "Voucher Details" %}{% endif %}{% endblock %}

{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل السند.{% else %}{% trans "View voucher details." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:voucher_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    
    {% if voucher.state == 'draft' %}
    <a href="{% url 'accounting:voucher_edit' voucher_id=voucher.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:voucher_post' voucher_id=voucher.id %}" class="btn btn-outline-success">
        <i class="fas fa-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ترحيل{% else %}{% trans "Post" %}{% endif %}
    </a>
    {% elif voucher.state == 'posted' %}
    <a href="{% url 'accounting:voucher_cancel' voucher_id=voucher.id %}" class="btn btn-outline-danger">
        <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
    </a>
    {% endif %}
    
    {% if voucher.journal_entry %}
    <a href="{% url 'accounting:journal_entry_detail' journal_entry_id=voucher.journal_entry.id %}" class="btn btn-outline-info">
        <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض القيد المحاسبي{% else %}{% trans "View Journal Entry" %}{% endif %}
    </a>
    {% endif %}
    
    <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل السند{% else %}{% trans "Voucher Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {% if voucher.voucher_type == 'receipt' %}
                        {% if LANGUAGE_CODE == 'ar' %}سند قبض{% else %}{% trans "Receipt Voucher" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}سند صرف{% else %}{% trans "Payment Voucher" %}{% endif %}
                    {% endif %}
                    #{{ voucher.number }}
                </h5>
                <span class="badge {% if voucher.state == 'draft' %}bg-warning{% elif voucher.state == 'posted' %}bg-success{% else %}bg-danger{% endif %}">
                    {% if voucher.state == 'draft' %}
                        {% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}
                    {% elif voucher.state == 'posted' %}
                        {% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}
                    {% endif %}
                </span>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات السند{% else %}{% trans "Voucher Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %}:</td>
                                <td>{{ voucher.number }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}:</td>
                                <td>{{ voucher.reference }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}:</td>
                                <td>{{ voucher.date }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}:</td>
                                <td>
                                    {% if voucher.voucher_type == 'receipt' %}
                                        {% if LANGUAGE_CODE == 'ar' %}سند قبض{% else %}{% trans "Receipt" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}سند صرف{% else %}{% trans "Payment" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}:</td>
                                <td>{{ voucher.amount }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات الشريك{% else %}{% trans "Partner Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</td>
                                <td>{{ voucher.partner_name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}:</td>
                                <td>
                                    {% if voucher.partner_type == 'customer' %}
                                        {% if LANGUAGE_CODE == 'ar' %}عميل{% else %}{% trans "Customer" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}مورد{% else %}{% trans "Vendor" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                        
                        <h6 class="fw-bold mt-4">{% if LANGUAGE_CODE == 'ar' %}معلومات الدفع{% else %}{% trans "Payment Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %}:</td>
                                <td>{{ voucher.payment_method.name }}</td>
                            </tr>
                            {% if voucher.payment_check %}
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الشيك{% else %}{% trans "Check" %}{% endif %}:</td>
                                <td>{{ voucher.payment_check.number }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات إضافية{% else %}{% trans "Additional Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}:</td>
                                <td>{{ voucher.company.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}:</td>
                                <td>{{ voucher.branch.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}:</td>
                                <td>{{ voucher.fiscal_year.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Memo" %}{% endif %}:</td>
                                <td>{{ voucher.memo|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if voucher.journal_entry %}
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}القيد المحاسبي{% else %}{% trans "Journal Entry" %}{% endif %}</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الحساب{% else %}{% trans "Account" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}البيان{% else %}{% trans "Description" %}{% endif %}</th>
                                    <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}مدين{% else %}{% trans "Debit" %}{% endif %}</th>
                                    <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}دائن{% else %}{% trans "Credit" %}{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for line in voucher.journal_entry.lines.all %}
                                <tr>
                                    <td>{{ line.account.code }} - {{ line.account.name }}</td>
                                    <td>{{ line.name }}</td>
                                    <td class="text-end">{{ line.debit }}</td>
                                    <td class="text-end">{{ line.credit }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="2" class="text-end">{% if LANGUAGE_CODE == 'ar' %}المجموع{% else %}{% trans "Total" %}{% endif %}</th>
                                    <th class="text-end">{{ voucher.journal_entry.total_debit }}</th>
                                    <th class="text-end">{{ voucher.journal_entry.total_credit }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
                {% endif %}
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6 class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}معلومات النظام{% else %}{% trans "System Information" %}{% endif %}</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تم الإنشاء بواسطة{% else %}{% trans "Created By" %}{% endif %}:</td>
                                <td>{{ voucher.created_by.get_full_name|default:voucher.created_by.username }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created At" %}{% endif %}:</td>
                                <td>{{ voucher.created_at }}</td>
                            </tr>
                            {% if voucher.posted_by %}
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تم الترحيل بواسطة{% else %}{% trans "Posted By" %}{% endif %}:</td>
                                <td>{{ voucher.posted_by.get_full_name|default:voucher.posted_by.username }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الترحيل{% else %}{% trans "Posted At" %}{% endif %}:</td>
                                <td>{{ voucher.posted_at }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
