{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الحساب البنكي{% else %}{% trans "Bank Account Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الحساب البنكي{% else %}{% trans "Bank Account Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل الحساب البنكي.{% else %}{% trans "View bank account details." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:bank_account_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_account_edit' bank_account.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:check_book_list' %}?bank_account={{ bank_account.id }}" class="btn btn-outline-info">
        <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
    </a>
    <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الحساب البنكي{% else %}{% trans "Bank Account Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات الحساب البنكي{% else %}{% trans "Bank Account Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</th>
                                <td>{{ bank_account.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الحساب{% else %}{% trans "Account Number" %}{% endif %}:</th>
                                <td>{{ bank_account.number }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الآيبان{% else %}{% trans "IBAN" %}{% endif %}:</th>
                                <td>{{ bank_account.iban|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البنك{% else %}{% trans "Bank" %}{% endif %}:</th>
                                <td>{{ bank_account.bank.name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}نوع الحساب{% else %}{% trans "Account Type" %}{% endif %}:</th>
                                <td>
                                    {% if bank_account.account_type == 'current' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}حساب جاري{% else %}{% trans "Current Account" %}{% endif %}</span>
                                    {% elif bank_account.account_type == 'savings' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}حساب توفير{% else %}{% trans "Savings Account" %}{% endif %}</span>
                                    {% elif bank_account.account_type == 'deposit' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}حساب وديعة{% else %}{% trans "Deposit Account" %}{% endif %}</span>
                                    {% elif bank_account.account_type == 'credit' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}حساب ائتمان{% else %}{% trans "Credit Account" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العملة{% else %}{% trans "Currency" %}{% endif %}:</th>
                                <td>
                                    {% if bank_account.currency == 'USD' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}دولار أمريكي{% else %}{% trans "US Dollar" %}{% endif %}</span>
                                    {% elif bank_account.currency == 'EUR' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}يورو{% else %}{% trans "Euro" %}{% endif %}</span>
                                    {% elif bank_account.currency == 'GBP' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}جنيه إسترليني{% else %}{% trans "British Pound" %}{% endif %}</span>
                                    {% elif bank_account.currency == 'SAR' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}ريال سعودي{% else %}{% trans "Saudi Riyal" %}{% endif %}</span>
                                    {% elif bank_account.currency == 'AED' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}درهم إماراتي{% else %}{% trans "UAE Dirham" %}{% endif %}</span>
                                    {% elif bank_account.currency == 'EGP' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}جنيه مصري{% else %}{% trans "Egyptian Pound" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحساب المحاسبي{% else %}{% trans "Account" %}{% endif %}:</th>
                                <td>{{ bank_account.account.code }} - {{ bank_account.account.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}:</th>
                                <td>
                                    {% if bank_account.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if bank_account.notes %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}:</h6>
                        <p>{{ bank_account.notes }}</p>
                    </div>
                </div>
                {% endif %}
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات النظام{% else %}{% trans "System Information" %}{% endif %}:</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}:</td>
                                <td>{{ bank_account.company.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}:</td>
                                <td>{{ bank_account.branch.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تم الإنشاء بواسطة{% else %}{% trans "Created By" %}{% endif %}:</td>
                                <td>
                                    {% if bank_account.created_by %}
                                        {{ bank_account.created_by.get_full_name|default:bank_account.created_by.username }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}غير معروف{% else %}{% trans "Unknown" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created At" %}{% endif %}:</td>
                                <td>{{ bank_account.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:check_book_create' %}?bank_account={{ bank_account.id }}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة دفتر شيكات{% else %}{% trans "Add Check Book" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if bank_account.check_books.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم البداية{% else %}{% trans "Start Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم النهاية{% else %}{% trans "End Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم التالي{% else %}{% trans "Next Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for check_book in bank_account.check_books.all %}
                            <tr>
                                <td>{{ check_book.code }}</td>
                                <td>{{ check_book.name }}</td>
                                <td>{{ check_book.start_number }}</td>
                                <td>{{ check_book.end_number }}</td>
                                <td>{{ check_book.next_number }}</td>
                                <td>
                                    {% if check_book.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:check_book_detail' check_book.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:check_book_edit' check_book.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد دفاتر شيكات لهذا الحساب البنكي. يرجى إضافة دفتر شيكات.{% else %}{% trans "No check books found for this bank account. Please add a check book." %}{% endif %}
                    <a href="{% url 'accounting:check_book_create' %}?bank_account={{ bank_account.id }}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إضافة دفتر شيكات{% else %}{% trans "Add Check Book" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
