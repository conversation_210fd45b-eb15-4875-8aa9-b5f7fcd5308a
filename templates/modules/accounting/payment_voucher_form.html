{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل سند صرف{% else %}{% trans "Edit Payment Voucher" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند صرف جديد{% else %}{% trans "Create New Payment Voucher" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل سند صرف{% else %}{% trans "Edit Payment Voucher" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند صرف جديد{% else %}{% trans "Create New Payment Voucher" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات سند الصرف.{% else %}{% trans "Edit payment voucher information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند صرف جديد.{% else %}{% trans "Create a new payment voucher." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-danger{% endblock %}
{% block quick_actions_header_class %}bg-danger{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:payment_voucher_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل سند صرف{% else %}{% trans "Edit Payment Voucher" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند صرف جديد{% else %}{% trans "Create New Payment Voucher" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if voucher %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل سند صرف{% else %}{% trans "Edit Payment Voucher" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند صرف جديد{% else %}{% trans "Create New Payment Voucher" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    {{ form.voucher_type }}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.number }}
                            <script>document.getElementById('{{ form.number.id_for_label }}').classList.add('form-control');</script>
                            {% if form.number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.date }}
                            <script>document.getElementById('{{ form.date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.amount.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.amount }}
                            <script>document.getElementById('{{ form.amount.id_for_label }}').classList.add('form-control');</script>
                            {% if form.amount.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.amount.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.branch.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.branch }}
                            <script>document.getElementById('{{ form.branch.id_for_label }}').classList.add('form-select');</script>
                            {% if form.branch.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.branch.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.partner_name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم المستفيد{% else %}{% trans "Beneficiary Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.partner_name }}
                            <script>document.getElementById('{{ form.partner_name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.partner_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.partner_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.partner_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع المستفيد{% else %}{% trans "Beneficiary Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.partner_type }}
                            <script>document.getElementById('{{ form.partner_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.partner_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.partner_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.payment_method.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.payment_method }}
                            <script>document.getElementById('{{ form.payment_method.id_for_label }}').classList.add('form-select');</script>
                            {% if form.payment_method.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.payment_method.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6" id="check_selection_container" style="display: none;">
                            <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}خيارات الشيك{% else %}{% trans "Check Options" %}{% endif %}</label>

                            <!-- Check option radio buttons -->
                            <div class="mb-3">
                                {{ form.check_option }}
                                {% if form.check_option.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.check_option.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <!-- Check book selection for new check -->
                            <div id="new_check_container" style="display: none;">
                                <label for="{{ form.check_book.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}دفتر الشيكات{% else %}{% trans "Check Book" %}{% endif %}</label>
                                {{ form.check_book }}
                                <script>document.getElementById('{{ form.check_book.id_for_label }}').classList.add('form-select');</script>
                                {% if form.check_book.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.check_book.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}سيتم إنشاء شيك جديد من هذا الدفتر{% else %}{% trans "A new check will be created from this book" %}{% endif %}</small>
                            </div>

                            <!-- Existing check selection -->
                            <div id="existing_check_container" style="display: none;">
                                <label for="{{ form.existing_check.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الشيك الموجود{% else %}{% trans "Existing Check" %}{% endif %}</label>
                                <div class="input-group">
                                    {{ form.existing_check }}
                                    <button type="button" class="btn btn-outline-secondary" id="refreshChecksBtn">
                                        <i class="fas fa-refresh"></i>
                                    </button>
                                </div>
                                <script>document.getElementById('{{ form.existing_check.id_for_label }}').classList.add('form-select');</script>
                                {% if form.existing_check.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.existing_check.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted" id="check_info">{% if LANGUAGE_CODE == 'ar' %}اختر شيك غير مستخدم{% else %}{% trans "Select an unused check" %}{% endif %}</small>
                            </div>

                            <!-- Hidden field for backward compatibility -->
                            <div style="display: none;">
                                {{ form.payment_check }}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.memo.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Memo" %}{% endif %}</label>
                            {{ form.memo }}
                            <script>document.getElementById('{{ form.memo.id_for_label }}').classList.add('form-control');</script>
                            {% if form.memo.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.memo.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-save me-2"></i>
                                {% if voucher %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:payment_voucher_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .form-control:focus, .form-select:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
    .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }
    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    /* Check option styling */
    #check_selection_container {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        background-color: #f8f9fa;
    }

    .check-option-radio {
        margin-bottom: 0.75rem;
    }

    .check-option-radio input[type="radio"] {
        margin-right: 0.5rem;
    }

    .check-option-radio label {
        font-weight: 500;
        margin-bottom: 0;
    }

    #new_check_container, #existing_check_container {
        margin-top: 1rem;
        padding: 0.75rem;
        border: 1px solid #e9ecef;
        border-radius: 0.25rem;
        background-color: #ffffff;
    }

    .check-info-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paymentMethodSelect = document.getElementById('{{ form.payment_method.id_for_label }}');
        const checkContainer = document.getElementById('check_selection_container');
        const checkOptionRadios = document.querySelectorAll('input[name="check_option"]');
        const newCheckContainer = document.getElementById('new_check_container');
        const existingCheckContainer = document.getElementById('existing_check_container');
        const checkBookSelect = document.getElementById('{{ form.check_book.id_for_label }}');
        const existingCheckSelect = document.getElementById('{{ form.existing_check.id_for_label }}');
        const checkInfo = document.getElementById('check_info');
        const refreshChecksBtn = document.getElementById('refreshChecksBtn');

        // Function to toggle check selection visibility
        function toggleCheckSelection() {
            // Get the selected payment method
            const selectedOption = paymentMethodSelect.options[paymentMethodSelect.selectedIndex];
            const paymentMethodText = selectedOption.text || '';

            // Check if the payment method contains "شيك" (Arabic) or "check" (English)
            const isCheckPayment = paymentMethodText.toLowerCase().includes('شيك') ||
                                  paymentMethodText.toLowerCase().includes('check');

            if (isCheckPayment) {
                checkContainer.style.display = 'block';
                // Set default option to 'new' if none selected
                if (!document.querySelector('input[name="check_option"]:checked')) {
                    document.querySelector('input[name="check_option"][value="new"]').checked = true;
                }
                toggleCheckOptions();
            } else {
                checkContainer.style.display = 'none';
                newCheckContainer.style.display = 'none';
                existingCheckContainer.style.display = 'none';
            }
        }

        // Function to toggle between new and existing check options
        function toggleCheckOptions() {
            const selectedOption = document.querySelector('input[name="check_option"]:checked');

            if (selectedOption && selectedOption.value === 'new') {
                newCheckContainer.style.display = 'block';
                existingCheckContainer.style.display = 'none';
                loadCheckBooks();
            } else if (selectedOption && selectedOption.value === 'existing') {
                newCheckContainer.style.display = 'none';
                existingCheckContainer.style.display = 'block';
                loadExistingChecks();
            } else {
                newCheckContainer.style.display = 'none';
                existingCheckContainer.style.display = 'none';
            }
        }

        // Load check books for new check option
        function loadCheckBooks() {
            // Check books are already loaded via Django form
            // No additional loading needed
        }

        // Load existing unused checks
        function loadExistingChecks() {
            // Existing checks are already loaded via Django form
            // But we can refresh them if needed
            updateExistingCheckInfo();
        }

        // Update existing check information display
        function updateExistingCheckInfo() {
            if (existingCheckSelect.value) {
                const selectedOption = existingCheckSelect.options[existingCheckSelect.selectedIndex];
                const checkText = selectedOption.textContent;
                checkInfo.textContent = checkText;
            } else {
                checkInfo.textContent = '{% if LANGUAGE_CODE == "ar" %}اختر شيك غير مستخدم{% else %}{% trans "Select an unused check" %}{% endif %}';
            }
        }

        // Event listeners
        paymentMethodSelect.addEventListener('change', toggleCheckSelection);

        // Add event listeners for check option radio buttons
        checkOptionRadios.forEach(radio => {
            radio.addEventListener('change', toggleCheckOptions);
        });

        existingCheckSelect.addEventListener('change', updateExistingCheckInfo);

        refreshChecksBtn.addEventListener('click', function() {
            // Refresh existing checks by reloading the page or making an AJAX call
            location.reload();
        });

        // Initial setup
        toggleCheckSelection();
    });
</script>
{% endblock %}
