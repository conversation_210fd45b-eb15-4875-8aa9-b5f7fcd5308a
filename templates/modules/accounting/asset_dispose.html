{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Dispose Asset" %} - {{ asset.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Dispose Asset" %} - {{ asset.name }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'accounting:asset_detail' asset.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Asset" %}
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Asset Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Name" %}</th>
                                            <td>{{ asset.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Code" %}</th>
                                            <td>{{ asset.code }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Category" %}</th>
                                            <td>{{ asset.category }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Purchase Date" %}</th>
                                            <td>{{ asset.purchase_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Purchase Value" %}</th>
                                            <td>{{ asset.purchase_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Current Book Value" %}</th>
                                            <td>{{ asset.current_value }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Disposal Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <form method="post" action="{% url 'accounting:asset_dispose' asset.id %}">
                                        {% csrf_token %}
                                        <div class="form-group">
                                            <label for="disposal_date">{% trans "Disposal Date" %}</label>
                                            <input type="date" class="form-control" id="disposal_date" name="disposal_date" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="disposal_value">{% trans "Disposal Value" %}</label>
                                            <input type="number" step="0.01" class="form-control" id="disposal_value" name="disposal_value" value="0.00" required>
                                            <small class="form-text text-muted">{% trans "The amount received from selling or disposing of the asset." %}</small>
                                        </div>
                                        <div class="form-group">
                                            <label for="disposal_reason">{% trans "Disposal Reason" %}</label>
                                            <textarea class="form-control" id="disposal_reason" name="disposal_reason" rows="3"></textarea>
                                        </div>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i> {% trans "Warning: This action cannot be undone. Disposing an asset will create a journal entry to record the disposal and any gain or loss." %}
                                        </div>
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> {% trans "Dispose Asset" %}
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
