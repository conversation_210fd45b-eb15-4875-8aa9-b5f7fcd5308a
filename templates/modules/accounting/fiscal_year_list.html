{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة السنوات المالية. إنشاء وتعديل وإغلاق الفترات المحاسبية.{% else %}{% trans "Manage fiscal years. Create, edit, and close accounting periods." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}السنوات المالية{% else %}{% trans "Fiscal Years" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}جديد{% else %}{% trans "New" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if fiscal_years %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ البداية{% else %}{% trans "Start Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ النهاية{% else %}{% trans "End Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "State" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for fiscal_year in fiscal_years %}
                            <tr>
                                <td>{{ fiscal_year.code }}</td>
                                <td>{{ fiscal_year.name }}</td>
                                <td>{{ fiscal_year.start_date }}</td>
                                <td>{{ fiscal_year.end_date }}</td>
                                <td>
                                    {% if fiscal_year.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif fiscal_year.state == 'open' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفتوح{% else %}{% trans "Open" %}{% endif %}</span>
                                    {% elif fiscal_year.state == 'closed' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مغلق{% else %}{% trans "Closed" %}{% endif %}</span>
                                    {% elif fiscal_year.state == 'archived' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}مؤرشف{% else %}{% trans "Archived" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if fiscal_year.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نعم{% else %}{% trans "Yes" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}لا{% else %}{% trans "No" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:fiscal_year_detail' fiscal_year.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:fiscal_year_edit' fiscal_year.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if fiscal_year.state == 'draft' %}
                                    <a href="{% url 'accounting:fiscal_year_open' fiscal_year.id %}" class="btn btn-sm btn-outline-success" title="{% if LANGUAGE_CODE == 'ar' %}فتح{% else %}{% trans 'Open' %}{% endif %}">
                                        <i class="fas fa-lock-open"></i>
                                    </a>
                                    {% elif fiscal_year.state == 'open' %}
                                    <a href="{% url 'accounting:fiscal_year_close' fiscal_year.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans 'Close' %}{% endif %}">
                                        <i class="fas fa-lock"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد سنوات مالية. يرجى إنشاء سنة مالية جديدة.{% else %}{% trans "No fiscal years found. Please create a new fiscal year." %}{% endif %}
                    <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
