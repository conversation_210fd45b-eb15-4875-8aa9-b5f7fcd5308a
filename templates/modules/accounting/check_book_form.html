{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if check_book %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر شيكات{% else %}{% trans "Edit Check Book" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر شيكات جديد{% else %}{% trans "Create New Check Book" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if check_book %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر شيكات{% else %}{% trans "Edit Check Book" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر شيكات جديد{% else %}{% trans "Create New Check Book" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if check_book %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات دفتر الشيكات.{% else %}{% trans "Edit check book information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر شيكات جديد.{% else %}{% trans "Create a new check book." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:check_book_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    {% if bank_account %}
    <a href="{% url 'accounting:bank_account_detail' bank_account.id %}" class="btn btn-outline-primary">
        <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض الحساب البنكي{% else %}{% trans "View Bank Account" %}{% endif %}
    </a>
    {% endif %}
</div>
{% endblock %}

{% block content_title %}
    {% if check_book %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر شيكات{% else %}{% trans "Edit Check Book" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر شيكات جديد{% else %}{% trans "Create New Check Book" %}{% endif %}
    {% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .check-number-info {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    .check-number-preview {
        font-family: monospace;
        font-size: 1.2em;
        padding: 10px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 10px;
    }
    .check-count {
        font-weight: bold;
        color: #28a745;
    }
</style>
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if check_book %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل دفتر شيكات{% else %}{% trans "Edit Check Book" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء دفتر شيكات جديد{% else %}{% trans "Create New Check Book" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" id="checkBookForm" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.code }}
                            <script>document.getElementById('{{ form.code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.bank_account.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحساب البنكي{% else %}{% trans "Bank Account" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.bank_account }}
                            <script>document.getElementById('{{ form.bank_account.id_for_label }}').classList.add('form-select');</script>
                            {% if form.bank_account.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.bank_account.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}اختر الحساب البنكي الذي ينتمي إليه دفتر الشيكات{% else %}{% trans "Select the bank account this check book belongs to" %}{% endif %}</small>
                        </div>

                        <div class="col-md-6">
                            <div class="form-check mt-4">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="check-number-info">
                        <h5>{% if LANGUAGE_CODE == 'ar' %}نطاق أرقام الشيكات{% else %}{% trans "Check Number Range" %}{% endif %}</h5>
                        <p>{% if LANGUAGE_CODE == 'ar' %}حدد نطاق أرقام الشيكات في هذا الدفتر. يمكنك تضمين بادئة (مثل ABC-) إذا لزم الأمر.{% else %}{% trans "Define the range of check numbers in this book. You can include a prefix (e.g., ABC-) if needed." %}{% endif %}</p>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="{{ form.start_number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم البداية{% else %}{% trans "Start Number" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.start_number }}
                                <script>document.getElementById('{{ form.start_number.id_for_label }}').classList.add('form-control');</script>
                                {% if form.start_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.start_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}مثال: 1000 أو ABC-1000{% else %}{% trans "e.g. 1000 or ABC-1000" %}{% endif %}</small>
                            </div>

                            <div class="col-md-4">
                                <label for="{{ form.end_number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم النهاية{% else %}{% trans "End Number" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.end_number }}
                                <script>document.getElementById('{{ form.end_number.id_for_label }}').classList.add('form-control');</script>
                                {% if form.end_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.end_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}مثال: 1100 أو ABC-1100{% else %}{% trans "e.g. 1100 or ABC-1100" %}{% endif %}</small>
                            </div>

                            <div class="col-md-4">
                                <label for="{{ form.next_number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الرقم التالي{% else %}{% trans "Next Number" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.next_number }}
                                <script>document.getElementById('{{ form.next_number.id_for_label }}').classList.add('form-control');</script>
                                {% if form.next_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.next_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">{% if LANGUAGE_CODE == 'ar' %}اتركه فارغًا للبدء من الرقم الأول{% else %}{% trans "Leave empty to start from the first number" %}{% endif %}</small>
                            </div>
                        </div>

                        <div id="checkNumberPreview" class="mt-3">
                            <p>{% if LANGUAGE_CODE == 'ar' %}معاينة أرقام الشيكات:{% else %}{% trans "Check Number Preview:" %}{% endif %}</p>
                            <div class="check-number-preview">
                                <span id="startPreview">-</span> → <span id="endPreview">-</span>
                            </div>
                            <p class="mt-2">{% if LANGUAGE_CODE == 'ar' %}إجمالي الشيكات في هذا الدفتر:{% else %}{% trans "Total checks in this book:" %}{% endif %} <span id="checkCount" class="check-count">0</span></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if check_book %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:check_book_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const startNumberInput = document.getElementById('{{ form.start_number.id_for_label }}');
        const endNumberInput = document.getElementById('{{ form.end_number.id_for_label }}');
        const nextNumberInput = document.getElementById('{{ form.next_number.id_for_label }}');

        const startPreview = document.getElementById('startPreview');
        const endPreview = document.getElementById('endPreview');
        const checkCount = document.getElementById('checkCount');

        function updatePreview() {
            const startNum = startNumberInput.value;
            const endNum = endNumberInput.value;

            startPreview.textContent = startNum || '-';
            endPreview.textContent = endNum || '-';

            // Calculate check count if both numbers are provided
            if (startNum && endNum) {
                // Extract numeric parts
                const startNumeric = startNum.replace(/\D/g, '');
                const endNumeric = endNum.replace(/\D/g, '');

                // Get prefixes
                const startPrefix = startNum.replace(startNumeric, '');
                const endPrefix = endNum.replace(endNumeric, '');

                if (startNumeric && endNumeric && startPrefix === endPrefix) {
                    const count = parseInt(endNumeric) - parseInt(startNumeric) + 1;
                    checkCount.textContent = count > 0 ? count : 0;
                } else {
                    checkCount.textContent = '0';
                }
            } else {
                checkCount.textContent = '0';
            }

            // Auto-fill next number if empty
            if (!nextNumberInput.value && startNum) {
                nextNumberInput.value = startNum;
            }
        }

        startNumberInput.addEventListener('input', updatePreview);
        endNumberInput.addEventListener('input', updatePreview);

        // Initial update
        updatePreview();
    });
</script>
{% endblock %}
