{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load static %}

{% block module_title %}
    {% if journal_entry %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل قيد اليومية{% else %}{% trans "Edit Journal Entry" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد يومية جديد{% else %}{% trans "Create New Journal Entry" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if journal_entry %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل قيد اليومية{% else %}{% trans "Edit Journal Entry" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد يومية جديد{% else %}{% trans "Create New Journal Entry" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if journal_entry %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات قيد اليومية.{% else %}{% trans "Edit journal entry information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد يومية جديد في دفتر اليومية.{% else %}{% trans "Create a new journal entry in the journal." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-list-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_type_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:journal_entry_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if journal_entry %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل قيد اليومية{% else %}{% trans "Edit Journal Entry" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد يومية جديد{% else %}{% trans "Create New Journal Entry" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% endif %}

        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if journal_entry %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل قيد اليومية{% else %}{% trans "Edit Journal Entry" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد يومية جديد{% else %}{% trans "Create New Journal Entry" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="{{ form.journal.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.journal }}
                            <script>document.getElementById('{{ form.journal.id_for_label }}').classList.add('form-select');</script>
                            {% if form.journal.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.journal.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label for="{{ form.date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.date }}
                            <script>document.getElementById('{{ form.date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-4">
                            <label for="{{ form.reference.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.reference }}
                            <script>document.getElementById('{{ form.reference.id_for_label }}').classList.add('form-control');</script>
                            {% if form.reference.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.reference.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <h5>{% if LANGUAGE_CODE == 'ar' %}بنود القيد{% else %}{% trans "Journal Entry Lines" %}{% endif %}</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered" id="journal-entry-lines">
                                    <thead>
                                        <tr>
                                            <th>{% if LANGUAGE_CODE == 'ar' %}الحساب{% else %}{% trans "Account" %}{% endif %}</th>
                                            <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                            <th>{% if LANGUAGE_CODE == 'ar' %}مدين{% else %}{% trans "Debit" %}{% endif %}</th>
                                            <th>{% if LANGUAGE_CODE == 'ar' %}دائن{% else %}{% trans "Credit" %}{% endif %}</th>
                                            <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {{ formset.management_form }}
                                        {% for line_form in formset %}
                                        <tr class="line-form">
                                            <td>
                                                {{ line_form.id }}
                                                {{ line_form.account }}
                                                <script>document.getElementById('{{ line_form.account.id_for_label }}').classList.add('form-select');</script>
                                                {% if line_form.account.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in line_form.account.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ line_form.name }}
                                                <script>document.getElementById('{{ line_form.name.id_for_label }}').classList.add('form-control');</script>
                                                {% if line_form.name.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in line_form.name.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ line_form.debit }}
                                                <script>document.getElementById('{{ line_form.debit.id_for_label }}').classList.add('form-control');</script>
                                                {% if line_form.debit.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in line_form.debit.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {{ line_form.credit }}
                                                <script>document.getElementById('{{ line_form.credit.id_for_label }}').classList.add('form-control');</script>
                                                {% if line_form.credit.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {% for error in line_form.credit.errors %}
                                                        {{ error }}
                                                    {% endfor %}
                                                </div>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if forloop.counter > 1 %}
                                                <button type="button" class="btn btn-sm btn-danger remove-line">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                {% endif %}
                                                {{ line_form.DELETE }}
                                                <script>document.getElementById('{{ line_form.DELETE.id_for_label }}').classList.add('d-none');</script>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <td colspan="2" class="text-end">
                                                <strong>{% if LANGUAGE_CODE == 'ar' %}المجموع{% else %}{% trans "Total" %}{% endif %}:</strong>
                                            </td>
                                            <td>
                                                <span id="total-debit">0.00</span>
                                            </td>
                                            <td>
                                                <span id="total-credit">0.00</span>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-success" id="add-line">
                                                    <i class="fas fa-plus"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة بند{% else %}{% trans "Add Line" %}{% endif %}
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan="2" class="text-end">
                                                <strong>{% if LANGUAGE_CODE == 'ar' %}الفرق{% else %}{% trans "Difference" %}{% endif %}:</strong>
                                            </td>
                                            <td colspan="2">
                                                <span id="difference" class="text-danger">0.00</span>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success" id="submit-button">
                                <i class="fas fa-save me-2"></i>
                                {% if journal_entry %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:journal_entry_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const formsetPrefix = "{{ formset.prefix }}";
        const totalForms = document.getElementById(`id_${formsetPrefix}-TOTAL_FORMS`);
        const maxForms = document.getElementById(`id_${formsetPrefix}-MAX_NUM_FORMS`);
        const addLineButton = document.getElementById('add-line');
        const submitButton = document.getElementById('submit-button');
        const table = document.getElementById('journal-entry-lines');

        // Function to update totals
        function updateTotals() {
            let totalDebit = 0;
            let totalCredit = 0;

            // Get all debit and credit inputs
            const debitInputs = document.querySelectorAll(`input[id$="-debit"]`);
            const creditInputs = document.querySelectorAll(`input[id$="-credit"]`);

            // Calculate totals
            debitInputs.forEach(input => {
                const value = parseFloat(input.value) || 0;
                totalDebit += value;
            });

            creditInputs.forEach(input => {
                const value = parseFloat(input.value) || 0;
                totalCredit += value;
            });

            // Update display
            document.getElementById('total-debit').textContent = totalDebit.toFixed(2);
            document.getElementById('total-credit').textContent = totalCredit.toFixed(2);

            // Calculate and display difference
            const difference = Math.abs(totalDebit - totalCredit);
            const differenceElement = document.getElementById('difference');
            differenceElement.textContent = difference.toFixed(2);

            // Disable submit button if not balanced
            if (difference > 0.01) {
                differenceElement.classList.add('text-danger');
                submitButton.disabled = true;
            } else {
                differenceElement.classList.remove('text-danger');
                submitButton.disabled = false;
            }
        }

        // Add new line
        addLineButton.addEventListener('click', function() {
            const currentForms = parseInt(totalForms.value);
            const maxNumForms = parseInt(maxForms.value);

            if (currentForms < maxNumForms) {
                // Clone the first form
                const firstForm = table.querySelector('tbody tr.line-form');
                const newForm = firstForm.cloneNode(true);

                // Update IDs and names
                const formElements = newForm.querySelectorAll('input, select');
                formElements.forEach(element => {
                    const name = element.getAttribute('name');
                    const id = element.getAttribute('id');

                    if (name) {
                        element.setAttribute('name', name.replace(/\d+/, currentForms));
                    }

                    if (id) {
                        const newId = id.replace(/\d+/, currentForms);
                        element.setAttribute('id', newId);

                        // Update associated labels
                        const labels = newForm.querySelectorAll(`label[for="${id}"]`);
                        labels.forEach(label => {
                            label.setAttribute('for', newId);
                        });
                    }

                    // Clear values
                    if (element.tagName === 'INPUT' && element.type !== 'hidden' && element.type !== 'checkbox') {
                        element.value = '';
                    }

                    // Add event listeners
                    if (element.tagName === 'INPUT' && (element.name.includes('debit') || element.name.includes('credit'))) {
                        element.addEventListener('input', updateTotals);
                    }
                });

                // Add remove button functionality
                const removeButton = newForm.querySelector('.remove-line');
                if (removeButton) {
                    removeButton.addEventListener('click', function() {
                        const deleteCheckbox = newForm.querySelector('input[type="checkbox"][name$="-DELETE"]');
                        if (deleteCheckbox) {
                            deleteCheckbox.checked = true;
                            newForm.style.display = 'none';
                            updateTotals();
                        }
                    });
                }

                // Append the new form
                table.querySelector('tbody').appendChild(newForm);

                // Update form count
                totalForms.value = currentForms + 1;

                // Update totals
                updateTotals();
            }
        });

        // Add event listeners to existing debit and credit inputs
        const debitInputs = document.querySelectorAll(`input[id$="-debit"]`);
        const creditInputs = document.querySelectorAll(`input[id$="-credit"]`);

        debitInputs.forEach(input => {
            input.addEventListener('input', updateTotals);
        });

        creditInputs.forEach(input => {
            input.addEventListener('input', updateTotals);
        });

        // Add event listeners to remove buttons
        const removeButtons = document.querySelectorAll('.remove-line');
        removeButtons.forEach(button => {
            button.addEventListener('click', function() {
                const row = button.closest('tr');
                const deleteCheckbox = row.querySelector('input[type="checkbox"][name$="-DELETE"]');
                if (deleteCheckbox) {
                    deleteCheckbox.checked = true;
                    row.style.display = 'none';
                    updateTotals();
                }
            });
        });

        // Initial update
        updateTotals();
    });
</script>
{% endblock %}
