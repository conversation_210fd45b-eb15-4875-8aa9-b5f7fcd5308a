{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة الحسابات{% else %}{% trans "Accounting Management" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إدارة الحسابات{% else %}{% trans "Accounting Management" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}الحفاظ على أموالك منظمة باستخدام أدوات محاسبية شاملة. إدارة دفتر الأستاذ العام والحسابات المدينة والحسابات الدائنة وإعداد التقارير المالية.{% else %}{% trans "Keep your finances in order with comprehensive accounting tools. Manage general ledger, accounts receivable, accounts payable, and financial reporting." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block module_alert %}
<div class="alert alert-success alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-calculator fa-2x text-success"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% if LANGUAGE_CODE == 'ar' %}مرحباً بك في إدارة الحسابات{% else %}{% trans "Welcome to Accounting Management" %}{% endif %}</h5>
            <p>{% if LANGUAGE_CODE == 'ar' %}يساعدك هذا الموديول على إدارة جميع جوانب عمليات المحاسبة الخاصة بك. استخدم قائمة التنقل للوصول إلى وظائف المحاسبة المختلفة.{% else %}{% trans "This module helps you manage all aspects of your accounting operations. Use the navigation menu to access different accounting functions." %}{% endif %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans 'Close' %}{% endif %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:receipt_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض{% else %}{% trans "Receipt Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات الصرف{% else %}{% trans "Payment Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-credit-card me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:reports_index' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:journal_entry_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيد يومية جديد{% else %}{% trans "New Journal Entry" %}{% endif %}
    </a>
    <a href="{% url 'accounting:receipt_voucher_create' %}" class="btn btn-outline-success">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سند قبض جديد{% else %}{% trans "New Receipt" %}{% endif %}
    </a>
    <a href="{% url 'accounting:payment_voucher_create' %}" class="btn btn-outline-danger">
        <i class="fas fa-minus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سند صرف جديد{% else %}{% trans "New Payment" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_create' %}" class="btn btn-outline-success">
        <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة بنك{% else %}{% trans "Add Bank" %}{% endif %}
    </a>
    <a href="{% url 'accounting:reports_index' %}" class="btn btn-outline-success">
        <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الحسابات المدينة{% else %}{% trans "Accounts Receivable" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">${{ accounts_receivable|floatformat:2 }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-file-invoice fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الحسابات الدائنة{% else %}{% trans "Accounts Payable" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">${{ accounts_payable|floatformat:2 }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-money-check-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}رصيد البنك{% else %}{% trans "Bank Balance" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">${{ bank_balance|floatformat:2 }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الإيرادات (الشهر حتى تاريخه){% else %}{% trans "Revenue (MTD)" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">${{ revenue_mtd|floatformat:2 }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}نظرة عامة على الحسابات{% else %}{% trans "Accounting Overview" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        {% if not fiscal_year %}
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد سنة مالية نشطة. يرجى إنشاء وتنشيط سنة مالية.{% else %}{% trans "No active fiscal year found. Please create and activate a fiscal year." %}{% endif %}
            <a href="{% url 'accounting:fiscal_year_create' %}" class="btn btn-sm btn-warning ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء سنة مالية{% else %}{% trans "Create Fiscal Year" %}{% endif %}
            </a>
        </div>
        {% endif %}

        <h5 class="mt-4 mb-3">{% if LANGUAGE_CODE == 'ar' %}المعاملات الأخيرة{% else %}{% trans "Recent Transactions" %}{% endif %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% if recent_entries %}
                        {% for entry in recent_entries %}
                        <tr>
                            <td>{{ entry.date }}</td>
                            <td>{{ entry.reference }}</td>
                            <td>{{ entry.journal.name }}</td>
                            <td>{{ entry.name }}</td>
                            <td>
                                {% if entry.state == 'draft' %}
                                <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                {% elif entry.state == 'posted' %}
                                <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                {% elif entry.state == 'cancelled' %}
                                <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% with total_debit=entry.lines.all|dictsumattr:"debit" %}
                                ${{ total_debit|floatformat:2 }}
                                {% endwith %}
                            </td>
                            <td>
                                <a href="{% url 'accounting:journal_entry_detail' entry.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض التفاصيل{% else %}{% trans 'View Details' %}{% endif %}">
                                    <i class="fas fa-eye"></i>
                                    <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}عرض التفاصيل{% else %}{% trans 'View Details' %}{% endif %}</span>
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد معاملات حديثة{% else %}{% trans "No recent transactions" %}{% endif %}</td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <div class="text-end mt-3">
            <a href="{% url 'accounting:journal_entry_list' %}" class="btn btn-outline-success">
                {% if LANGUAGE_CODE == 'ar' %}عرض جميع المعاملات{% else %}{% trans "View All Transactions" %}{% endif %}
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الأرباح والخسائر{% else %}{% trans "Profit & Loss" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات مالية متاحة{% else %}{% trans "No financial data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}التدفق النقدي{% else %}{% trans "Cash Flow" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات تدفق نقدي متاحة{% else %}{% trans "No cash flow data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Accounting Module loaded');
    });
</script>
{% endblock %}
