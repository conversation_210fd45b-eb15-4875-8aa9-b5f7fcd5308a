{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Depreciation Detail" %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Depreciation Detail" %}</h3>
                    <div class="card-tools">
                        <a href="{% url 'accounting:asset_detail' asset.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Asset" %}
                        </a>
                        {% if depreciation.state == 'draft' %}
                            <form method="post" action="{% url 'accounting:depreciation_post' depreciation.id %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-success">
                                    <i class="fas fa-check"></i> {% trans "Post" %}
                                </button>
                            </form>
                        {% elif depreciation.state == 'posted' %}
                            <form method="post" action="{% url 'accounting:depreciation_cancel' depreciation.id %}" style="display: inline;">
                                {% csrf_token %}
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="fas fa-times"></i> {% trans "Cancel" %}
                                </button>
                            </form>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Depreciation Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Asset" %}</th>
                                            <td>{{ asset.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Period" %}</th>
                                            <td>{{ depreciation.period }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Date" %}</th>
                                            <td>{{ depreciation.date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Amount" %}</th>
                                            <td>{{ depreciation.amount }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Accumulated Depreciation" %}</th>
                                            <td>{{ depreciation.accumulated_depreciation }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Remaining Value" %}</th>
                                            <td>{{ depreciation.remaining_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "State" %}</th>
                                            <td>
                                                {% if depreciation.state == 'draft' %}
                                                    <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                                {% elif depreciation.state == 'posted' %}
                                                    <span class="badge badge-success">{% trans "Posted" %}</span>
                                                {% elif depreciation.state == 'cancelled' %}
                                                    <span class="badge badge-danger">{% trans "Cancelled" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Asset Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Name" %}</th>
                                            <td>{{ asset.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Code" %}</th>
                                            <td>{{ asset.code }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Category" %}</th>
                                            <td>{{ asset.category }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Purchase Date" %}</th>
                                            <td>{{ asset.purchase_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Purchase Value" %}</th>
                                            <td>{{ asset.purchase_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Depreciation Method" %}</th>
                                            <td>{{ asset.depreciation_method }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {% if journal_entry %}
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Journal Entry" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Reference" %}</th>
                                            <td>{{ journal_entry.reference }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Date" %}</th>
                                            <td>{{ journal_entry.date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Journal" %}</th>
                                            <td>{{ journal_entry.journal }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "State" %}</th>
                                            <td>
                                                {% if journal_entry.state == 'draft' %}
                                                    <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                                {% elif journal_entry.state == 'posted' %}
                                                    <span class="badge badge-success">{% trans "Posted" %}</span>
                                                {% elif journal_entry.state == 'cancelled' %}
                                                    <span class="badge badge-danger">{% trans "Cancelled" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                    
                                    <h5 class="mt-4">{% trans "Journal Entry Lines" %}</h5>
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Account" %}</th>
                                                <th>{% trans "Name" %}</th>
                                                <th>{% trans "Debit" %}</th>
                                                <th>{% trans "Credit" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for line in journal_entry.lines.all %}
                                                <tr>
                                                    <td>{{ line.account }}</td>
                                                    <td>{{ line.name }}</td>
                                                    <td>{{ line.debit }}</td>
                                                    <td>{{ line.credit }}</td>
                                                </tr>
                                            {% empty %}
                                                <tr>
                                                    <td colspan="4" class="text-center">{% trans "No journal entry lines found." %}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                        <tfoot>
                                            <tr>
                                                <th colspan="2">{% trans "Total" %}</th>
                                                <th>{{ journal_entry.total_debit }}</th>
                                                <th>{{ journal_entry.total_credit }}</th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
