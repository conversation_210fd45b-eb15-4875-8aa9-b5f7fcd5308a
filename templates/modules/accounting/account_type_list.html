{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة أنواع الحسابات. إنشاء وتعديل وعرض أنواع الحسابات.{% else %}{% trans "Manage account types. Create, edit, and view account types." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_type_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tags me-2"></i> {% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:account_type_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}نوع حساب جديد{% else %}{% trans "New Account Type" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:account_type_create' %}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}جديد{% else %}{% trans "New" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="name" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</label>
                            <input type="text" name="name" id="name" class="form-control" value="{{ search_name }}">
                        </div>
                        <div class="col-md-3">
                            <label for="category" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الفئة{% else %}{% trans "Category" %}{% endif %}</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="asset" {% if selected_category == 'asset' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}أصول{% else %}{% trans "Asset" %}{% endif %}</option>
                                <option value="liability" {% if selected_category == 'liability' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}خصوم{% else %}{% trans "Liability" %}{% endif %}</option>
                                <option value="equity" {% if selected_category == 'equity' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}حقوق ملكية{% else %}{% trans "Equity" %}{% endif %}</option>
                                <option value="revenue" {% if selected_category == 'revenue' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}إيرادات{% else %}{% trans "Revenue" %}{% endif %}</option>
                                <option value="expense" {% if selected_category == 'expense' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}مصروفات{% else %}{% trans "Expense" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="report_type" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع التقرير{% else %}{% trans "Report Type" %}{% endif %}</label>
                            <select name="report_type" id="report_type" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="balance_sheet" {% if selected_report_type == 'balance_sheet' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}</option>
                                <option value="income_statement" {% if selected_report_type == 'income_statement' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}</option>
                                <option value="cash_flow" {% if selected_report_type == 'cash_flow' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}التدفق النقدي{% else %}{% trans "Cash Flow" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="is_active" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</label>
                            <select name="is_active" id="is_active" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                <option value="1" {% if selected_is_active == '1' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</option>
                                <option value="0" {% if selected_is_active == '0' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</option>
                            </select>
                        </div>
                        <div class="col-md-12 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                            <a href="{% url 'accounting:account_type_list' %}" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </form>
                </div>

                {% if account_types %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الفئة{% else %}{% trans "Category" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نوع التقرير{% else %}{% trans "Report Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account_type in account_types %}
                            <tr>
                                <td>{{ account_type.name }}</td>
                                <td>
                                    {% if account_type.category == 'asset' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}أصول{% else %}{% trans "Asset" %}{% endif %}</span>
                                    {% elif account_type.category == 'liability' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}خصوم{% else %}{% trans "Liability" %}{% endif %}</span>
                                    {% elif account_type.category == 'equity' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}حقوق ملكية{% else %}{% trans "Equity" %}{% endif %}</span>
                                    {% elif account_type.category == 'revenue' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}إيرادات{% else %}{% trans "Revenue" %}{% endif %}</span>
                                    {% elif account_type.category == 'expense' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مصروفات{% else %}{% trans "Expense" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account_type.report_type == 'balance_sheet' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}الميزانية العمومية{% else %}{% trans "Balance Sheet" %}{% endif %}</span>
                                    {% elif account_type.report_type == 'income_statement' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}قائمة الدخل{% else %}{% trans "Income Statement" %}{% endif %}</span>
                                    {% elif account_type.report_type == 'cash_flow' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}التدفق النقدي{% else %}{% trans "Cash Flow" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ account_type.description|truncatechars:50 }}</td>
                                <td>
                                    {% if account_type.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:account_type_detail' account_type.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:account_type_edit' account_type.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounting:account_type_delete' account_type.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد أنواع حسابات. يرجى إنشاء نوع حساب جديد.{% else %}{% trans "No account types found. Please create a new account type." %}{% endif %}
                    <a href="{% url 'accounting:account_type_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء نوع حساب{% else %}{% trans "Create Account Type" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
