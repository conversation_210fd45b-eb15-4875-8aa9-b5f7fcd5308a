{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دفتر اليومية{% else %}{% trans "Journal Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دفتر اليومية{% else %}{% trans "Journal Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل دفتر اليومية.{% else %}{% trans "View journal details." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-book-open me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر اليومية{% else %}{% trans "Journals" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:journal_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    <a href="{% url 'accounting:journal_edit' journal.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:journal_entry_create' %}?journal={{ journal.id }}" class="btn btn-outline-success">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد جديد{% else %}{% trans "Create New Entry" %}{% endif %}
    </a>
    <a href="{% url 'accounting:journal_entry_list' %}?journal={{ journal.id }}" class="btn btn-outline-info">
        <i class="fas fa-list me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض كل القيود{% else %}{% trans "View All Entries" %}{% endif %}
    </a>
    <a href="#" class="btn btn-outline-secondary" onclick="window.print()">
        <i class="fas fa-print me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طباعة{% else %}{% trans "Print" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دفتر اليومية{% else %}{% trans "Journal Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات دفتر اليومية{% else %}{% trans "Journal Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</th>
                                <td>{{ journal.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}:</th>
                                <td>{{ journal.code }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}:</th>
                                <td>
                                    {% if journal.type == 'general' %}
                                        {% if LANGUAGE_CODE == 'ar' %}عام{% else %}{% trans "General" %}{% endif %}
                                    {% elif journal.type == 'sale' %}
                                        {% if LANGUAGE_CODE == 'ar' %}مبيعات{% else %}{% trans "Sales" %}{% endif %}
                                    {% elif journal.type == 'purchase' %}
                                        {% if LANGUAGE_CODE == 'ar' %}مشتريات{% else %}{% trans "Purchases" %}{% endif %}
                                    {% elif journal.type == 'cash' %}
                                        {% if LANGUAGE_CODE == 'ar' %}نقدي{% else %}{% trans "Cash" %}{% endif %}
                                    {% elif journal.type == 'bank' %}
                                        {% if LANGUAGE_CODE == 'ar' %}بنكي{% else %}{% trans "Bank" %}{% endif %}
                                    {% else %}
                                        {{ journal.get_type_display }}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}حساب المدين{% else %}{% trans "Debit Account" %}{% endif %}:</th>
                                <td>{{ journal.default_debit_account.name|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}حساب الدائن{% else %}{% trans "Credit Account" %}{% endif %}:</th>
                                <td>{{ journal.default_credit_account.name|default:"-" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}بادئة الترقيم{% else %}{% trans "Number Prefix" %}{% endif %}:</th>
                                <td>{{ journal.number_prefix|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم التالي{% else %}{% trans "Next Number" %}{% endif %}:</th>
                                <td>{{ journal.next_number }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}ترقيم تلقائي{% else %}{% trans "Auto Numbering" %}{% endif %}:</th>
                                <td>
                                    {% if journal.auto_numbering %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نعم{% else %}{% trans "Yes" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}لا{% else %}{% trans "No" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}:</th>
                                <td>
                                    {% if journal.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if journal.notes %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}:</h6>
                        <p>{{ journal.notes }}</p>
                    </div>
                </div>
                {% endif %}

                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات النظام{% else %}{% trans "System Information" %}{% endif %}:</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}:</td>
                                <td>{{ journal.company.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تم الإنشاء بواسطة{% else %}{% trans "Created By" %}{% endif %}:</td>
                                <td>
                                    {% if journal.created_by %}
                                        {{ journal.created_by.get_full_name|default:journal.created_by.username }}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}غير معروف{% else %}{% trans "Unknown" %}{% endif %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created At" %}{% endif %}:</td>
                                <td>{{ journal.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        {% if journal_entries %}
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}آخر القيود{% else %}{% trans "Recent Entries" %}{% endif %}</h5>
                <a href="{% url 'accounting:journal_entry_create' %}?journal={{ journal.id }}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد جديد{% else %}{% trans "Create New Entry" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البيان{% else %}{% trans "Description" %}{% endif %}</th>
                                <th class="text-end">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in journal_entries %}
                            <tr>
                                <td>{{ entry.number }}</td>
                                <td>{{ entry.date }}</td>
                                <td>{{ entry.reference }}</td>
                                <td>{{ entry.name }}</td>
                                <td class="text-end">{{ entry.total_debit }}</td>
                                <td>
                                    {% if entry.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif entry.state == 'posted' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                    {% elif entry.state == 'cancelled' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:journal_entry_detail' entry.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if entry.state == 'draft' %}
                                    <a href="{% url 'accounting:journal_entry_edit' entry.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'accounting:journal_entry_list' %}?journal={{ journal.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض كل القيود{% else %}{% trans "View All Entries" %}{% endif %}
                    </a>
                </div>
            </div>
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}لا توجد قيود لهذا الدفتر. يرجى إنشاء قيد جديد.{% else %}{% trans "No entries found for this journal. Please create a new entry." %}{% endif %}
            <a href="{% url 'accounting:journal_entry_create' %}?journal={{ journal.id }}" class="btn btn-sm btn-info ms-3">
                {% if LANGUAGE_CODE == 'ar' %}إنشاء قيد جديد{% else %}{% trans "Create New Entry" %}{% endif %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
