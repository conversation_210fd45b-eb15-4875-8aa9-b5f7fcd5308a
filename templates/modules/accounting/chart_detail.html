{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دليل الحسابات{% else %}{% trans "Chart of Accounts Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دليل الحسابات{% else %}{% trans "Chart of Accounts Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل دليل الحسابات والحسابات المرتبطة به.{% else %}{% trans "View chart of accounts details and associated accounts." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:chart_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    <a href="{% url 'accounting:chart_edit' chart.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:account_create' %}?chart={{ chart.id }}" class="btn btn-success">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة حساب{% else %}{% trans "Add Account" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل دليل الحسابات{% else %}{% trans "Chart of Accounts Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات دليل الحسابات{% else %}{% trans "Chart Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</th>
                                <td>{{ chart.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرمز{% else %}{% trans "Code" %}{% endif %}:</th>
                                <td>{{ chart.code|default:"-" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}:</th>
                                <td>{{ chart.company.name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}:</th>
                                <td>
                                    {% if chart.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created On" %}{% endif %}:</th>
                                <td>{{ chart.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}بواسطة{% else %}{% trans "Created By" %}{% endif %}:</th>
                                <td>{{ chart.created_by.get_full_name|default:chart.created_by.username }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                {% if chart.description %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}:</h6>
                        <p>{{ chart.description }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounts" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'accounting:account_create' %}?chart={{ chart.id }}" class="btn btn-sm btn-success">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة حساب{% else %}{% trans "Add Account" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if accounts %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحساب الأب{% else %}{% trans "Parent Account" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الرصيد{% else %}{% trans "Balance" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts %}
                            <tr>
                                <td>{{ account.code }}</td>
                                <td>{{ account.name }}</td>
                                <td>{{ account.type.name }}</td>
                                <td>{{ account.parent.name|default:"-" }}</td>
                                <td>
                                    {% if account.balance %}
                                    ${{ account.balance|floatformat:2 }}
                                    {% else %}
                                    $0.00
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:account_detail' account.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:account_edit' account.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounting:account_delete' account.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد حسابات في دليل الحسابات هذا. يرجى إضافة حسابات.{% else %}{% trans "No accounts found in this chart. Please add accounts." %}{% endif %}
                    <a href="{% url 'accounting:account_create' %}?chart={{ chart.id }}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إضافة حساب{% else %}{% trans "Add Account" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
