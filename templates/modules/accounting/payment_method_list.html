{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}طرق الدفع{% else %}{% trans "Payment Methods" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}طرق الدفع{% else %}{% trans "Payment Methods" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة طرق الدفع.{% else %}{% trans "Manage payment methods." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book-open me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر اليومية{% else %}{% trans "Journals" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:payment_method_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-money-bill-wave me-2"></i> {% if LANGUAGE_CODE == 'ar' %}طرق الدفع{% else %}{% trans "Payment Methods" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:check_book_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفاتر الشيكات{% else %}{% trans "Check Books" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:payment_method_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء طريقة دفع جديدة{% else %}{% trans "Create New Payment Method" %}{% endif %}
    </a>
    <a href="{% url 'accounting:voucher_list' %}" class="btn btn-outline-primary">
        <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
    </a>
    <a href="{% url 'accounting:voucher_create' %}" class="btn btn-outline-info">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء سند جديد{% else %}{% trans "Create New Voucher" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}طرق الدفع{% else %}{% trans "Payment Methods" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label for="name" class="form-label">{% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ search_name|default:'' }}" placeholder="{% if LANGUAGE_CODE == 'ar' %}الاسم، الكود{% else %}{% trans "Name, Code" %}{% endif %}">
                    </div>
                    <div class="col-md-4">
                        <label for="type" class="form-label">{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</label>
                        <select name="type" id="type" class="form-select">
                            <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                            {% for type_code, type_name in payment_method_types %}
                            <option value="{{ type_code }}" {% if selected_type == type_code %}selected{% endif %}>
                                {% if LANGUAGE_CODE == 'ar' %}
                                    {% if type_code == 'cash' %}نقدي{% elif type_code == 'bank' %}بنكي{% elif type_code == 'check' %}شيك{% elif type_code == 'credit_card' %}بطاقة ائتمان{% elif type_code == 'online' %}إلكتروني{% else %}{{ type_name }}{% endif %}
                                {% else %}
                                    {{ type_name }}
                                {% endif %}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="is_active" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</label>
                        <select name="is_active" id="is_active" class="form-select">
                            <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                            <option value="1" {% if selected_is_active == '1' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</option>
                            <option value="0" {% if selected_is_active == '0' %}selected{% endif %}>{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                        </button>
                        <a href="{% url 'accounting:payment_method_list' %}" class="btn btn-secondary">
                            <i class="fas fa-redo me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة طرق الدفع{% else %}{% trans "Payment Methods List" %}{% endif %}</h5>
                <a href="{% url 'accounting:payment_method_create' %}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء جديد{% else %}{% trans "Create New" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if payment_methods %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الكود{% else %}{% trans "Code" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for method in payment_methods %}
                            <tr>
                                <td>{{ method.code }}</td>
                                <td>{{ method.name }}</td>
                                <td>
                                    {% if method.type == 'cash' %}
                                        {% if LANGUAGE_CODE == 'ar' %}نقدي{% else %}{% trans "Cash" %}{% endif %}
                                    {% elif method.type == 'bank' %}
                                        {% if LANGUAGE_CODE == 'ar' %}بنكي{% else %}{% trans "Bank" %}{% endif %}
                                    {% elif method.type == 'check' %}
                                        {% if LANGUAGE_CODE == 'ar' %}شيك{% else %}{% trans "Check" %}{% endif %}
                                    {% elif method.type == 'credit_card' %}
                                        {% if LANGUAGE_CODE == 'ar' %}بطاقة ائتمان{% else %}{% trans "Credit Card" %}{% endif %}
                                    {% elif method.type == 'online' %}
                                        {% if LANGUAGE_CODE == 'ar' %}إلكتروني{% else %}{% trans "Online" %}{% endif %}
                                    {% else %}
                                        {{ method.get_type_display }}
                                    {% endif %}
                                </td>
                                <td>{{ method.journal.name|default:"-" }}</td>
                                <td>
                                    {% if method.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'accounting:payment_method_detail' method.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'accounting:payment_method_edit' method.id %}" class="btn btn-sm btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'accounting:voucher_create' %}?payment_method={{ method.id }}" class="btn btn-sm btn-outline-success" title="{% if LANGUAGE_CODE == 'ar' %}إنشاء سند{% else %}{% trans 'Create Voucher' %}{% endif %}">
                                        <i class="fas fa-plus-circle"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد طرق دفع. يرجى إنشاء طريقة دفع جديدة.{% else %}{% trans "No payment methods found. Please create a new payment method." %}{% endif %}
                    <a href="{% url 'accounting:payment_method_create' %}" class="btn btn-sm btn-info ms-3">
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء طريقة دفع جديدة{% else %}{% trans "Create New Payment Method" %}{% endif %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
