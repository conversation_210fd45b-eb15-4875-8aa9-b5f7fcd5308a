{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Depreciation Board" %} - {{ asset.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Depreciation Board" %} - {{ asset.name }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'accounting:asset_detail' asset.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-arrow-left"></i> {% trans "Back to Asset" %}
                        </a>
                        <button class="btn btn-sm btn-info" onclick="window.print()">
                            <i class="fas fa-print"></i> {% trans "Print" %}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Asset Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Name" %}</th>
                                            <td>{{ asset.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Code" %}</th>
                                            <td>{{ asset.code }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Category" %}</th>
                                            <td>{{ asset.category }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Company" %}</th>
                                            <td>{{ asset.company }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Branch" %}</th>
                                            <td>{{ asset.branch }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Financial Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Purchase Date" %}</th>
                                            <td>{{ asset.purchase_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "In Service Date" %}</th>
                                            <td>{{ asset.in_service_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Purchase Value" %}</th>
                                            <td>{{ asset.purchase_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Salvage Value" %}</th>
                                            <td>{{ asset.salvage_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Depreciation Method" %}</th>
                                            <td>{{ asset.depreciation_method }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Depreciation Board" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Period" %}</th>
                                                <th>{% trans "Date" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Accumulated Depreciation" %}</th>
                                                <th>{% trans "Remaining Value" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for entry in depreciation_board %}
                                                <tr>
                                                    <td>{{ entry.period }}</td>
                                                    <td>{{ entry.date }}</td>
                                                    <td>{{ entry.amount }}</td>
                                                    <td>{{ entry.accumulated_depreciation }}</td>
                                                    <td>{{ entry.remaining_value }}</td>
                                                </tr>
                                            {% empty %}
                                                <tr>
                                                    <td colspan="5" class="text-center">{% trans "No depreciation entries found." %}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
