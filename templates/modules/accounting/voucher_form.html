{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل سند{% else %}{% trans "Edit Voucher" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند جديد{% else %}{% trans "Create New Voucher" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}

{% block module_header %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل سند{% else %}{% trans "Edit Voucher" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند جديد{% else %}{% trans "Create New Voucher" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات السند.{% else %}{% trans "Edit voucher information." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند قبض أو صرف جديد.{% else %}{% trans "Create a new payment or receipt voucher." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:voucher_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}
    {% if voucher %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل سند{% else %}{% trans "Edit Voucher" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند جديد{% else %}{% trans "Create New Voucher" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if voucher %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل سند{% else %}{% trans "Edit Voucher" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إنشاء سند جديد{% else %}{% trans "Create New Voucher" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.number.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الرقم{% else %}{% trans "Number" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.number }}
                            <script>document.getElementById('{{ form.number.id_for_label }}').classList.add('form-control');</script>
                            {% if form.number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.date.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.date }}
                            <script>document.getElementById('{{ form.date.id_for_label }}').classList.add('form-control');</script>
                            {% if form.date.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.date.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.voucher_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع السند{% else %}{% trans "Voucher Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.voucher_type }}
                            <script>document.getElementById('{{ form.voucher_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.voucher_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.voucher_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.amount.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.amount }}
                            <script>document.getElementById('{{ form.amount.id_for_label }}').classList.add('form-control');</script>
                            {% if form.amount.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.amount.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.branch.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.branch }}
                            <script>document.getElementById('{{ form.branch.id_for_label }}').classList.add('form-select');</script>
                            {% if form.branch.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.branch.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.fiscal_year.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.fiscal_year }}
                            <script>document.getElementById('{{ form.fiscal_year.id_for_label }}').classList.add('form-select');</script>
                            {% if form.fiscal_year.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.fiscal_year.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.partner_name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم الشريك{% else %}{% trans "Partner Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.partner_name }}
                            <script>document.getElementById('{{ form.partner_name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.partner_name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.partner_name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.partner_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الشريك{% else %}{% trans "Partner Type" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.partner_type }}
                            <script>document.getElementById('{{ form.partner_type.id_for_label }}').classList.add('form-select');</script>
                            {% if form.partner_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.partner_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.payment_method.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}طريقة الدفع{% else %}{% trans "Payment Method" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.payment_method }}
                            <script>document.getElementById('{{ form.payment_method.id_for_label }}').classList.add('form-select');</script>
                            {% if form.payment_method.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.payment_method.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6" id="check_selection_container" style="display: none;">
                            <label for="{{ form.payment_check.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الشيك{% else %}{% trans "Check" %}{% endif %}</label>
                            <div class="input-group">
                                {{ form.payment_check }}
                                <button type="button" class="btn btn-outline-secondary" id="searchCheckBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <script>document.getElementById('{{ form.payment_check.id_for_label }}').classList.add('form-select');</script>
                            {% if form.payment_check.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.payment_check.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <small class="form-text text-muted" id="check_info"></small>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.memo.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Memo" %}{% endif %}</label>
                            {{ form.memo }}
                            <script>document.getElementById('{{ form.memo.id_for_label }}').classList.add('form-control');</script>
                            {% if form.memo.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.memo.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if voucher %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:voucher_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paymentMethodSelect = document.getElementById('{{ form.payment_method.id_for_label }}');
        const checkContainer = document.getElementById('check_selection_container');
        const checkSelect = document.getElementById('{{ form.payment_check.id_for_label }}');
        const checkInfo = document.getElementById('check_info');
        const searchCheckBtn = document.getElementById('searchCheckBtn');
        const voucherTypeSelect = document.getElementById('{{ form.voucher_type.id_for_label }}');

        // Function to toggle check selection visibility
        function toggleCheckSelection() {
            // Get the selected payment method
            const selectedOption = paymentMethodSelect.options[paymentMethodSelect.selectedIndex];
            const paymentMethodText = selectedOption.text || '';

            // Check if the payment method contains "شيك" (Arabic) or "check" (English)
            const isCheckPayment = paymentMethodText.toLowerCase().includes('شيك') ||
                                  paymentMethodText.toLowerCase().includes('check');

            // Only show check selection for payment vouchers with check payment method
            const isPaymentVoucher = voucherTypeSelect.value === 'payment';

            if (isCheckPayment && isPaymentVoucher) {
                checkContainer.style.display = 'block';
                loadAvailableChecks();
            } else {
                checkContainer.style.display = 'none';
                // Clear check selection if not using check payment
                checkSelect.value = '';
                checkInfo.textContent = '';
            }
        }

        // Load available checks from the selected bank account
        function loadAvailableChecks() {
            const selectedPaymentMethodId = paymentMethodSelect.value;

            if (!selectedPaymentMethodId) {
                return;
            }

            // Make AJAX request to get available checks
            fetch(`/accounting/api/available-checks/?payment_method_id=${selectedPaymentMethodId}`)
                .then(response => response.json())
                .then(data => {
                    // Clear existing options
                    checkSelect.innerHTML = '<option value="">---------</option>';

                    // Add new options
                    data.checks.forEach(check => {
                        const option = document.createElement('option');
                        option.value = check.id;
                        option.textContent = `${check.number} - ${check.checkbook_name}`;
                        option.setAttribute('data-checkbook', check.checkbook_name);
                        option.setAttribute('data-bank', check.bank_name);
                        checkSelect.appendChild(option);
                    });

                    // Update check info if a check is already selected
                    updateCheckInfo();
                })
                .catch(error => {
                    console.error('Error loading checks:', error);
                });
        }

        // Update check information display
        function updateCheckInfo() {
            if (checkSelect.value) {
                const selectedOption = checkSelect.options[checkSelect.selectedIndex];
                const checkbookName = selectedOption.getAttribute('data-checkbook');
                const bankName = selectedOption.getAttribute('data-bank');

                if (checkbookName && bankName) {
                    checkInfo.textContent = `${bankName} - ${checkbookName}`;
                } else {
                    checkInfo.textContent = '';
                }
            } else {
                checkInfo.textContent = '';
            }
        }

        // Event listeners
        paymentMethodSelect.addEventListener('change', toggleCheckSelection);
        voucherTypeSelect.addEventListener('change', toggleCheckSelection);
        checkSelect.addEventListener('change', updateCheckInfo);

        searchCheckBtn.addEventListener('click', function() {
            loadAvailableChecks();
        });

        // Initial setup
        toggleCheckSelection();
    });
</script>
{% endblock %}