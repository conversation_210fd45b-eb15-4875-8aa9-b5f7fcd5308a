{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل قيد اليومية{% else %}{% trans "Journal Entry Details" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounting" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-calculator{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل قيد اليومية{% else %}{% trans "Journal Entry Details" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل قيد اليومية والبنود المرتبطة به.{% else %}{% trans "View journal entry details and associated lines." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:chart_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دليل الحسابات{% else %}{% trans "Chart of Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-list-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات{% else %}{% trans "Accounts" %}{% endif %}
</a>
<a href="{% url 'accounting:account_type_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% if LANGUAGE_CODE == 'ar' %}أنواع الحسابات{% else %}{% trans "Account Types" %}{% endif %}
</a>
<a href="{% url 'accounting:journal_entry_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-book me-2"></i> {% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal Entries" %}{% endif %}
</a>
<a href="{% url 'accounting:voucher_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سندات القبض والصرف{% else %}{% trans "Vouchers" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:cash_register_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الخزينة{% else %}{% trans "Cash Registers" %}{% endif %}
</a>
<a href="{% url 'accounting:check_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الشيكات{% else %}{% trans "Checks" %}{% endif %}
</a>
<a href="{% url 'accounting:asset_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأصول{% else %}{% trans "Assets" %}{% endif %}
</a>
<a href="{% url 'accounting:report_trial_balance' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير المالية{% else %}{% trans "Financial Reports" %}{% endif %}
</a>
<a href="{% url 'accounting:fiscal_year_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:journal_entry_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى القائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
    {% if journal_entry.state == 'draft' %}
    <a href="{% url 'accounting:journal_entry_edit' journal_entry.id %}" class="btn btn-outline-primary">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Edit" %}{% endif %}
    </a>
    <a href="{% url 'accounting:journal_entry_post' journal_entry.id %}" class="btn btn-success" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من ترحيل هذا القيد؟ لا يمكن التراجع عن هذا الإجراء.{% else %}{% trans "Are you sure you want to post this journal entry? This action cannot be undone." %}{% endif %}')">
        <i class="fas fa-check me-2"></i> {% if LANGUAGE_CODE == 'ar' %}ترحيل{% else %}{% trans "Post" %}{% endif %}
    </a>
    {% endif %}
    {% if journal_entry.state == 'posted' %}
    <a href="{% url 'accounting:journal_entry_cancel' journal_entry.id %}" class="btn btn-outline-danger" onclick="return confirm('{% if LANGUAGE_CODE == 'ar' %}هل أنت متأكد من إلغاء هذا القيد؟ سيتم إنشاء قيد عكسي.{% else %}{% trans "Are you sure you want to cancel this journal entry? A reversal entry will be created." %}{% endif %}')">
        <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
    </a>
    {% endif %}
    <a href="{% url 'accounting:journal_entry_create' %}" class="btn btn-success">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}قيد يومية جديد{% else %}{% trans "New Journal Entry" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل قيد اليومية{% else %}{% trans "Journal Entry Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات قيد اليومية{% else %}{% trans "Journal Entry Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}المرجع{% else %}{% trans "Reference" %}{% endif %}:</th>
                                <td>{{ journal_entry.reference }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}:</th>
                                <td>{{ journal_entry.date }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}دفتر اليومية{% else %}{% trans "Journal" %}{% endif %}:</th>
                                <td>{{ journal_entry.journal.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}:</th>
                                <td>{{ journal_entry.name }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 30%;">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}:</th>
                                <td>
                                    {% if journal_entry.state == 'draft' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}مسودة{% else %}{% trans "Draft" %}{% endif %}</span>
                                    {% elif journal_entry.state == 'posted' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مرحل{% else %}{% trans "Posted" %}{% endif %}</span>
                                    {% elif journal_entry.state == 'cancelled' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}ملغي{% else %}{% trans "Cancelled" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}السنة المالية{% else %}{% trans "Fiscal Year" %}{% endif %}:</th>
                                <td>{{ journal_entry.fiscal_year.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}:</th>
                                <td>{{ journal_entry.branch.name }}</td>
                            </tr>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المبلغ{% else %}{% trans "Amount" %}{% endif %}:</th>
                                <td>
                                    {% with total_debit=journal_entry.lines.all|dictsumattr:"debit" %}
                                    ${{ total_debit|floatformat:2 }}
                                    {% endwith %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if journal_entry.memo %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Memo" %}{% endif %}:</h6>
                        <p>{{ journal_entry.memo }}</p>
                    </div>
                </div>
                {% endif %}

                {% if journal_entry.partner_name %}
                <div class="row mt-3">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات الشريك{% else %}{% trans "Partner Information" %}{% endif %}:</h6>
                        <p>
                            <strong>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}:</strong> {{ journal_entry.partner_name }}<br>
                            <strong>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}:</strong>
                            {% if journal_entry.partner_type == 'customer' %}
                                {% if LANGUAGE_CODE == 'ar' %}عميل{% else %}{% trans "Customer" %}{% endif %}
                            {% elif journal_entry.partner_type == 'vendor' %}
                                {% if LANGUAGE_CODE == 'ar' %}مورد{% else %}{% trans "Vendor" %}{% endif %}
                            {% elif journal_entry.partner_type == 'employee' %}
                                {% if LANGUAGE_CODE == 'ar' %}موظف{% else %}{% trans "Employee" %}{% endif %}
                            {% else %}
                                {{ journal_entry.partner_type }}
                            {% endif %}
                            <br>
                            <strong>{% if LANGUAGE_CODE == 'ar' %}المعرف{% else %}{% trans "ID" %}{% endif %}:</strong> {{ journal_entry.partner_id }}
                        </p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}بنود القيد{% else %}{% trans "Journal Entry Lines" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحساب{% else %}{% trans "Account" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}مدين{% else %}{% trans "Debit" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}دائن{% else %}{% trans "Credit" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشريك{% else %}{% trans "Partner" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for line in journal_entry.lines.all %}
                            <tr>
                                <td>
                                    <a href="{% url 'accounting:account_detail' line.account.id %}">
                                        {{ line.account.code }} - {{ line.account.name }}
                                    </a>
                                </td>
                                <td>{{ line.name|default:"-" }}</td>
                                <td>${{ line.debit|floatformat:2 }}</td>
                                <td>${{ line.credit|floatformat:2 }}</td>
                                <td>
                                    {% if line.partner_name %}
                                    {{ line.partner_name }}
                                    {% else %}
                                    -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <th colspan="2" class="text-end">{% if LANGUAGE_CODE == 'ar' %}المجموع{% else %}{% trans "Total" %}{% endif %}:</th>
                                <th>
                                    {% with total_debit=journal_entry.lines.all|dictsumattr:"debit" %}
                                    ${{ total_debit|floatformat:2 }}
                                    {% endwith %}
                                </th>
                                <th>
                                    {% with total_credit=journal_entry.lines.all|dictsumattr:"credit" %}
                                    ${{ total_credit|floatformat:2 }}
                                    {% endwith %}
                                </th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
