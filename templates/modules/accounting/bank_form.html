{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load accounting_tags %}

{% block module_title %}
    {% if bank %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات البنك{% else %}{% trans "Edit Bank" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة بنك جديد{% else %}{% trans "Add New Bank" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_description %}
    {% if bank %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات البنك وتحديث معلوماته.{% else %}{% trans "Edit bank information and update its details." %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة بنك جديد إلى النظام.{% else %}{% trans "Add a new bank to the system." %}{% endif %}
    {% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-bill-wave me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:bank_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة للقائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if bank %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل بيانات البنك{% else %}{% trans "Edit Bank" %}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إضافة بنك جديد{% else %}{% trans "Add New Bank" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم البنك{% else %}{% trans "Bank Name" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.name }}
                            <script>document.getElementById('{{ form.name.id_for_label }}').classList.add('form-control');</script>
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}كود البنك{% else %}{% trans "Bank Code" %}{% endif %} <span class="text-danger">*</span></label>
                            {{ form.code }}
                            <script>document.getElementById('{{ form.code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.swift_code.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رمز السويفت{% else %}{% trans "SWIFT Code" %}{% endif %}</label>
                            {{ form.swift_code }}
                            <script>document.getElementById('{{ form.swift_code.id_for_label }}').classList.add('form-control');</script>
                            {% if form.swift_code.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.swift_code.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رقم الهاتف{% else %}{% trans "Phone Number" %}{% endif %}</label>
                            {{ form.phone }}
                            <script>document.getElementById('{{ form.phone.id_for_label }}').classList.add('form-control');</script>
                            {% if form.phone.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</label>
                            {{ form.email }}
                            <script>document.getElementById('{{ form.email.id_for_label }}').classList.add('form-control');</script>
                            {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="{{ form.website.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الموقع الإلكتروني{% else %}{% trans "Website" %}{% endif %}</label>
                            {{ form.website }}
                            <script>document.getElementById('{{ form.website.id_for_label }}').classList.add('form-control');</script>
                            {% if form.website.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.website.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.address.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</label>
                            {{ form.address }}
                            <script>document.getElementById('{{ form.address.id_for_label }}').classList.add('form-control');</script>
                            {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="form-check">
                                {{ form.is_active }}
                                <script>document.getElementById('{{ form.is_active.id_for_label }}').classList.add('form-check-input');</script>
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="{{ form.notes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</label>
                            {{ form.notes }}
                            <script>document.getElementById('{{ form.notes.id_for_label }}').classList.add('form-control');</script>
                            {% if form.notes.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save me-2"></i>
                                {% if bank %}
                                    {% if LANGUAGE_CODE == 'ar' %}حفظ التغييرات{% else %}{% trans "Save Changes" %}{% endif %}
                                {% else %}
                                    {% if LANGUAGE_CODE == 'ar' %}إنشاء{% else %}{% trans "Create" %}{% endif %}
                                {% endif %}
                            </button>
                            <a href="{% url 'accounting:bank_list' %}" class="btn btn-secondary ms-2">
                                <i class="fas fa-times me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}