{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل البنك{% else %}{% trans "Bank Details" %}{% endif %}{% endblock %}

{% block module_description %}
    {% if LANGUAGE_CODE == 'ar' %}عرض تفاصيل البنك وحساباته المرتبطة.{% else %}{% trans "View bank details and its associated accounts." %}{% endif %}
{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'accounting:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-university me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البنوك{% else %}{% trans "Banks" %}{% endif %}
</a>
<a href="{% url 'accounting:bank_account_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-bill-wave me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'accounting:bank_edit' bank.id %}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i> {% if LANGUAGE_CODE == 'ar' %}تعديل البنك{% else %}{% trans "Edit Bank" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_account_create' %}?bank={{ bank.id }}" class="btn btn-success">
        <i class="fas fa-plus-circle me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة حساب بنكي{% else %}{% trans "Add Bank Account" %}{% endif %}
    </a>
    <a href="{% url 'accounting:bank_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العودة للقائمة{% else %}{% trans "Back to List" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <!-- Bank Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}معلومات البنك{% else %}{% trans "Bank Information" %}{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}اسم البنك{% else %}{% trans "Bank Name" %}{% endif %}</dt>
                            <dd class="col-sm-8">{{ bank.name }}</dd>

                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}كود البنك{% else %}{% trans "Bank Code" %}{% endif %}</dt>
                            <dd class="col-sm-8">{{ bank.code }}</dd>

                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}رمز السويفت{% else %}{% trans "SWIFT Code" %}{% endif %}</dt>
                            <dd class="col-sm-8">{{ bank.swift_code|default:"--" }}</dd>

                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}رقم الهاتف{% else %}{% trans "Phone Number" %}{% endif %}</dt>
                            <dd class="col-sm-8">{{ bank.phone|default:"--" }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</dt>
                            <dd class="col-sm-8">{{ bank.email|default:"--" }}</dd>

                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}الموقع الإلكتروني{% else %}{% trans "Website" %}{% endif %}</dt>
                            <dd class="col-sm-8">
                                {% if bank.website %}
                                <a href="{{ bank.website }}" target="_blank">{{ bank.website }}</a>
                                {% else %}
                                --
                                {% endif %}
                            </dd>

                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</dt>
                            <dd class="col-sm-8">{{ bank.address|default:"--"|linebreaks }}</dd>

                            <dt class="col-sm-4">{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</dt>
                            <dd class="col-sm-8">
                                {% if bank.is_active %}
                                <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                {% else %}
                                <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>

                {% if bank.notes %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}ملاحظات{% else %}{% trans "Notes" %}{% endif %}</h6>
                        <p class="mb-0">{{ bank.notes|linebreaks }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Bank Accounts -->
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}الحسابات البنكية{% else %}{% trans "Bank Accounts" %}{% endif %}
                </h5>
                <a href="{% url 'accounting:bank_account_create' %}?bank={{ bank.id }}" class="btn btn-sm btn-success">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة حساب{% else %}{% trans "Add Account" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if bank.accounts.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}رقم الحساب{% else %}{% trans "Account Number" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نوع الحساب{% else %}{% trans "Account Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}العملة{% else %}{% trans "Currency" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in bank.accounts.all %}
                            <tr>
                                <td>{{ account.number }}</td>
                                <td>{{ account.name }}</td>
                                <td>{{ account.get_account_type_display }}</td>
                                <td>{{ account.get_currency_display }}</td>
                                <td>
                                    {% if account.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'accounting:bank_account_detail' account.id %}" class="btn btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans 'View' %}{% endif %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'accounting:bank_account_edit' account.id %}" class="btn btn-outline-secondary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد حسابات بنكية{% else %}{% trans "No bank accounts found" %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}