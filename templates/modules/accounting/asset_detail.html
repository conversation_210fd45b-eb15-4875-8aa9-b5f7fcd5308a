{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Asset Detail" %} - {{ asset.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% trans "Asset Detail" %} - {{ asset.name }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'accounting:asset_edit' asset.id %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> {% trans "Edit" %}
                        </a>
                        <a href="{% url 'accounting:asset_depreciation_board' asset.id %}" class="btn btn-sm btn-info">
                            <i class="fas fa-table"></i> {% trans "Depreciation Board" %}
                        </a>
                        {% if asset.state == 'active' %}
                            <a href="{% url 'accounting:asset_dispose' asset.id %}" class="btn btn-sm btn-danger">
                                <i class="fas fa-trash"></i> {% trans "Dispose" %}
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Asset Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Name" %}</th>
                                            <td>{{ asset.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Code" %}</th>
                                            <td>{{ asset.code }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Category" %}</th>
                                            <td>{{ asset.category }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Company" %}</th>
                                            <td>{{ asset.company }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Branch" %}</th>
                                            <td>{{ asset.branch }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "State" %}</th>
                                            <td>
                                                {% if asset.state == 'draft' %}
                                                    <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                                {% elif asset.state == 'active' %}
                                                    <span class="badge badge-success">{% trans "Active" %}</span>
                                                {% elif asset.state == 'fully_depreciated' %}
                                                    <span class="badge badge-info">{% trans "Fully Depreciated" %}</span>
                                                {% elif asset.state == 'disposed' %}
                                                    <span class="badge badge-danger">{% trans "Disposed" %}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Financial Information" %}</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th>{% trans "Purchase Date" %}</th>
                                            <td>{{ asset.purchase_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "In Service Date" %}</th>
                                            <td>{{ asset.in_service_date }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Purchase Value" %}</th>
                                            <td>{{ asset.purchase_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Salvage Value" %}</th>
                                            <td>{{ asset.salvage_value }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Depreciation Method" %}</th>
                                            <td>{{ asset.depreciation_method }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Useful Life (Years)" %}</th>
                                            <td>{{ asset.useful_life_years }}</td>
                                        </tr>
                                        <tr>
                                            <th>{% trans "Depreciation Frequency" %}</th>
                                            <td>
                                                {% if asset.depreciation_frequency == 'monthly' %}
                                                    {% trans "Monthly" %}
                                                {% elif asset.depreciation_frequency == 'quarterly' %}
                                                    {% trans "Quarterly" %}
                                                {% elif asset.depreciation_frequency == 'semi_annual' %}
                                                    {% trans "Semi-Annual" %}
                                                {% elif asset.depreciation_frequency == 'annual' %}
                                                    {% trans "Annual" %}
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">{% trans "Depreciation Entries" %}</h4>
                                    <div class="card-tools">
                                        {% if asset.state == 'active' and not depreciations %}
                                            <a href="{% url 'accounting:asset_create_depreciation' asset.id %}" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus"></i> {% trans "Generate Depreciation" %}
                                            </a>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Period" %}</th>
                                                <th>{% trans "Date" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Accumulated Depreciation" %}</th>
                                                <th>{% trans "Remaining Value" %}</th>
                                                <th>{% trans "State" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for depreciation in depreciations %}
                                                <tr>
                                                    <td>{{ depreciation.period }}</td>
                                                    <td>{{ depreciation.date }}</td>
                                                    <td>{{ depreciation.amount }}</td>
                                                    <td>{{ depreciation.accumulated_depreciation }}</td>
                                                    <td>{{ depreciation.remaining_value }}</td>
                                                    <td>
                                                        {% if depreciation.state == 'draft' %}
                                                            <span class="badge badge-secondary">{% trans "Draft" %}</span>
                                                        {% elif depreciation.state == 'posted' %}
                                                            <span class="badge badge-success">{% trans "Posted" %}</span>
                                                        {% elif depreciation.state == 'cancelled' %}
                                                            <span class="badge badge-danger">{% trans "Cancelled" %}</span>
                                                        {% endif %}
                                                    </td>
                                                    <td>
                                                        <a href="{% url 'accounting:depreciation_detail' depreciation.id %}" class="btn btn-xs btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        {% if depreciation.state == 'draft' %}
                                                            <form method="post" action="{% url 'accounting:depreciation_post' depreciation.id %}" style="display: inline;">
                                                                {% csrf_token %}
                                                                <button type="submit" class="btn btn-xs btn-success">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            </form>
                                                        {% elif depreciation.state == 'posted' %}
                                                            <form method="post" action="{% url 'accounting:depreciation_cancel' depreciation.id %}" style="display: inline;">
                                                                {% csrf_token %}
                                                                <button type="submit" class="btn btn-xs btn-danger">
                                                                    <i class="fas fa-times"></i>
                                                                </button>
                                                            </form>
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% empty %}
                                                <tr>
                                                    <td colspan="7" class="text-center">{% trans "No depreciation entries found." %}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
