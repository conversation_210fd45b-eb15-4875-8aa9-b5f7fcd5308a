{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة التراخيص{% else %}{% trans "License Management" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تراخيص النظام{% else %}{% trans "System Licenses" %}{% endif %}</h5>
                <a href="#" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addLicenseModal">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة ترخيص جديد{% else %}{% trans "Add New License" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if licenses %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوحدة{% else %}{% trans "Module" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}نوع الترخيص{% else %}{% trans "License Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ البدء{% else %}{% trans "Start Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الانتهاء{% else %}{% trans "End Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}عدد المستخدمين{% else %}{% trans "User Count" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for license in licenses %}
                            <tr>
                                <td>{{ license.company.name }}</td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-2">
                                            <i class="{{ license.module.icon|default:'fas fa-puzzle-piece' }}"></i>
                                        </div>
                                        <div>
                                            {{ license.module.name }}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if license.license_type == 'trial' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}تجريبي{% else %}{% trans "Trial" %}{% endif %}</span>
                                    {% elif license.license_type == 'standard' %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}قياسي{% else %}{% trans "Standard" %}{% endif %}</span>
                                    {% elif license.license_type == 'premium' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}متميز{% else %}{% trans "Premium" %}{% endif %}</span>
                                    {% elif license.license_type == 'enterprise' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}مؤسسة{% else %}{% trans "Enterprise" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ license.start_date|date:"Y-m-d" }}</td>
                                <td>{{ license.end_date|date:"Y-m-d" }}</td>
                                <td>{{ license.max_users }}</td>
                                <td>
                                    {% if license.is_active %}
                                        {% if license.is_expired %}
                                        <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}منتهي{% else %}{% trans "Expired" %}{% endif %}</span>
                                        {% else %}
                                        <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                        {% endif %}
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editLicenseModal{{ license.id }}" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}</span>
                                    </a>
                                    <a href="#" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteLicenseModal{{ license.id }}" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}</span>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد تراخيص مسجلة حتى الآن. انقر على "إضافة ترخيص جديد" لإضافة أول ترخيص.{% else %}{% trans "No licenses registered yet. Click 'Add New License' to add your first license." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات الترخيص{% else %}{% trans "License Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    التراخيص تسمح للشركات باستخدام وحدات محددة في النظام. كل ترخيص يحدد الوحدة، نوع الترخيص، فترة الصلاحية، وعدد المستخدمين المسموح بهم.
                    {% else %}
                    {% trans "Licenses allow companies to use specific modules in the system. Each license defines the module, license type, validity period, and number of allowed users." %}
                    {% endif %}
                </div>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}أنواع التراخيص{% else %}{% trans "License Types" %}{% endif %}</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}تجريبي{% else %}{% trans "Trial" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}ترخيص محدود المدة للتجربة، عادة 30 يوم.{% else %}{% trans "Time-limited license for evaluation, typically 30 days." %}{% endif %}</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}قياسي{% else %}{% trans "Standard" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}ترخيص أساسي مع ميزات محدودة وعدد محدود من المستخدمين.{% else %}{% trans "Basic license with limited features and user count." %}{% endif %}</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}متميز{% else %}{% trans "Premium" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}ترخيص متقدم مع ميزات إضافية وعدد أكبر من المستخدمين.{% else %}{% trans "Advanced license with additional features and higher user count." %}{% endif %}</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}مؤسسة{% else %}{% trans "Enterprise" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}ترخيص شامل مع جميع الميزات وعدد غير محدود من المستخدمين.{% else %}{% trans "Comprehensive license with all features and unlimited users." %}{% endif %}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إحصائيات التراخيص{% else %}{% trans "License Statistics" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}إجمالي التراخيص{% else %}{% trans "Total Licenses" %}{% endif %}</h6>
                                <h2 class="mb-0">{{ licenses|length }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}التراخيص النشطة{% else %}{% trans "Active Licenses" %}{% endif %}</h6>
                                <h2 class="mb-0">{{ active_licenses_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}التراخيص المنتهية{% else %}{% trans "Expired Licenses" %}{% endif %}</h6>
                                <h2 class="mb-0">{{ expired_licenses_count }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الشركات المرخصة{% else %}{% trans "Licensed Companies" %}{% endif %}</h6>
                                <h2 class="mb-0">{{ licensed_companies_count }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- Add License Modal -->
<div class="modal fade" id="addLicenseModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% if LANGUAGE_CODE == 'ar' %}إضافة ترخيص جديد{% else %}{% trans "Add New License" %}{% endif %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addLicenseForm" method="post" action="{% url 'modules:license_add' %}">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="company" name="company" required>
                                    <option value="">{% if LANGUAGE_CODE == 'ar' %}اختر الشركة{% else %}{% trans "Select Company" %}{% endif %}</option>
                                    {% for company in companies %}
                                    <option value="{{ company.id }}">{{ company.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="module" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الوحدة{% else %}{% trans "Module" %}{% endif %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="module" name="module" required>
                                    <option value="">{% if LANGUAGE_CODE == 'ar' %}اختر الوحدة{% else %}{% trans "Select Module" %}{% endif %}</option>
                                    {% for module in modules %}
                                    <option value="{{ module.id }}">{{ module.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="license_type" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الترخيص{% else %}{% trans "License Type" %}{% endif %} <span class="text-danger">*</span></label>
                                <select class="form-select" id="license_type" name="license_type" required>
                                    <option value="">{% if LANGUAGE_CODE == 'ar' %}اختر النوع{% else %}{% trans "Select Type" %}{% endif %}</option>
                                    <option value="trial">{% if LANGUAGE_CODE == 'ar' %}تجريبي{% else %}{% trans "Trial" %}{% endif %}</option>
                                    <option value="standard">{% if LANGUAGE_CODE == 'ar' %}قياسي{% else %}{% trans "Standard" %}{% endif %}</option>
                                    <option value="premium">{% if LANGUAGE_CODE == 'ar' %}متميز{% else %}{% trans "Premium" %}{% endif %}</option>
                                    <option value="enterprise">{% if LANGUAGE_CODE == 'ar' %}مؤسسة{% else %}{% trans "Enterprise" %}{% endif %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_users" class="form-label">{% if LANGUAGE_CODE == 'ar' %}عدد المستخدمين{% else %}{% trans "User Count" %}{% endif %} <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="max_users" name="max_users" min="1" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ البدء{% else %}{% trans "Start Date" %}{% endif %} <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تاريخ الانتهاء{% else %}{% trans "End Date" %}{% endif %} <span class="text-danger">*</span></label>
                                <input type="date" class="form-control" id="end_date" name="end_date" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}</button>
                <button type="submit" form="addLicenseForm" class="btn btn-primary">{% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit License Modals -->
{% for license in licenses %}
<div class="modal fade" id="editLicenseModal{{ license.id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% if LANGUAGE_CODE == 'ar' %}تعديل الترخيص{% else %}{% trans "Edit License" %}{% endif %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editLicenseForm{{ license.id }}" method="post" action="{% url 'modules:license_edit' license.id %}">
                    {% csrf_token %}
                    <!-- Similar form fields as add license, but with values pre-filled -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}</button>
                <button type="submit" form="editLicenseForm{{ license.id }}" class="btn btn-primary">{% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

<!-- Delete License Modals -->
{% for license in licenses %}
<div class="modal fade" id="deleteLicenseModal{{ license.id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% if LANGUAGE_CODE == 'ar' %}حذف الترخيص{% else %}{% trans "Delete License" %}{% endif %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد حذف ترخيص "{{ license.module.name }}" للشركة "{{ license.company.name }}"؟ هذا الإجراء لا يمكن التراجع عنه.
                    {% else %}
                    {% trans "Are you sure you want to delete the" %} "{{ license.module.name }}" {% trans "license for company" %} "{{ license.company.name }}"? {% trans "This action cannot be undone." %}
                    {% endif %}
                </div>
                <form id="deleteLicenseForm{{ license.id }}" method="post" action="{% url 'modules:license_delete' license.id %}">
                    {% csrf_token %}
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}</button>
                <button type="submit" form="deleteLicenseForm{{ license.id }}" class="btn btn-danger">{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}
