{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Leave Request Details" %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:leave_request_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h2>{% trans "Leave Request" %} #{{ leave_request.id }}</h2>
            <small class="text-muted">{{ leave_request.employee.full_name }} - {{ leave_request.leave_type.name }}</small>
        </div>
    </div>
    <div class="btn-group">
        {% if leave_request.status == 'pending' %}
        <a href="{% url 'hr:leave_request_edit' leave_request.id %}" class="btn btn-warning">
            <i class="fas fa-edit me-1"></i>
            {% trans "Edit" %}
        </a>
        <form method="post" action="{% url 'hr:leave_request_approve' leave_request.id %}" class="d-inline">
            {% csrf_token %}
            <button type="submit" class="btn btn-success">
                <i class="fas fa-check me-1"></i>
                {% trans "Approve" %}
            </button>
        </form>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#rejectModal">
            <i class="fas fa-times me-1"></i>
            {% trans "Reject" %}
        </button>
        {% endif %}
    </div>
</div>

<div class="row">
    <!-- Leave Request Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    {% trans "Leave Request Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{% trans "Employee" %}:</td>
                                <td>
                                    <a href="{% url 'hr:employee_detail' leave_request.employee.id %}">
                                        {{ leave_request.employee.full_name }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ leave_request.employee.employee_id }}</small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Leave Type" %}:</td>
                                <td>
                                    <a href="{% url 'hr:leave_type_detail' leave_request.leave_type.id %}">
                                        {{ leave_request.leave_type.name }}
                                    </a>
                                    <br>
                                    <small class="text-muted">{{ leave_request.leave_type.code }}</small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Start Date" %}:</td>
                                <td><strong>{{ leave_request.start_date|date:"d/m/Y" }}</strong></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "End Date" %}:</td>
                                <td><strong>{{ leave_request.end_date|date:"d/m/Y" }}</strong></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Total Days" %}:</td>
                                <td>
                                    <span class="badge bg-primary">{{ leave_request.total_days }} {% trans "days" %}</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{% trans "Status" %}:</td>
                                <td>
                                    {% if leave_request.status == 'pending' %}
                                        <span class="badge bg-warning">{% trans "Pending" %}</span>
                                    {% elif leave_request.status == 'approved' %}
                                        <span class="badge bg-success">{% trans "Approved" %}</span>
                                    {% elif leave_request.status == 'rejected' %}
                                        <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                    {% elif leave_request.status == 'cancelled' %}
                                        <span class="badge bg-secondary">{% trans "Cancelled" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Request Date" %}:</td>
                                <td>{{ leave_request.created_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                            {% if leave_request.approved_by %}
                            <tr>
                                <td class="fw-bold">{% trans "Approved By" %}:</td>
                                <td>{{ leave_request.approved_by.get_full_name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Approval Date" %}:</td>
                                <td>{{ leave_request.approved_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                            {% endif %}
                            {% if leave_request.emergency_contact %}
                            <tr>
                                <td class="fw-bold">{% trans "Emergency Contact" %}:</td>
                                <td>{{ leave_request.emergency_contact }}</td>
                            </tr>
                            {% endif %}
                            {% if leave_request.replacement_employee %}
                            <tr>
                                <td class="fw-bold">{% trans "Replacement Employee" %}:</td>
                                <td>
                                    <a href="{% url 'hr:employee_detail' leave_request.replacement_employee.id %}">
                                        {{ leave_request.replacement_employee.full_name }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>

                <!-- Reason -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>{% trans "Reason" %}</h6>
                        <div class="bg-light p-3 rounded">
                            {{ leave_request.reason|linebreaks }}
                        </div>
                    </div>
                </div>

                <!-- Rejection Reason -->
                {% if leave_request.status == 'rejected' and leave_request.rejection_reason %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6 class="text-danger">{% trans "Rejection Reason" %}</h6>
                        <div class="bg-danger bg-opacity-10 p-3 rounded border border-danger">
                            {{ leave_request.rejection_reason|linebreaks }}
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Attachments -->
                {% if leave_request.medical_certificate or leave_request.attachment %}
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>{% trans "Attachments" %}</h6>
                        <div class="d-flex gap-2">
                            {% if leave_request.medical_certificate %}
                            <a href="{{ leave_request.medical_certificate.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-file-medical me-1"></i>
                                {% trans "Medical Certificate" %}
                            </a>
                            {% endif %}
                            {% if leave_request.attachment %}
                            <a href="{{ leave_request.attachment.url }}" target="_blank" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-paperclip me-1"></i>
                                {% trans "Attachment" %}
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Actions & Info -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>
                    {% trans "Actions" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if leave_request.status == 'pending' %}
                    <a href="{% url 'hr:leave_request_edit' leave_request.id %}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        {% trans "Edit Request" %}
                    </a>
                    {% endif %}
                    
                    <a href="{% url 'hr:employee_detail' leave_request.employee.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-user me-1"></i>
                        {% trans "View Employee" %}
                    </a>
                    
                    <a href="{% url 'hr:leave_type_detail' leave_request.leave_type.id %}" class="btn btn-outline-info">
                        <i class="fas fa-calendar-alt me-1"></i>
                        {% trans "View Leave Type" %}
                    </a>
                    
                    <a href="{% url 'hr:leave_request_list' %}?employee={{ leave_request.employee.id }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list me-1"></i>
                        {% trans "Employee's Leaves" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Leave Balance -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    {% trans "Leave Balance" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <h4 class="text-primary">{{ leave_request.employee.get_remaining_leave_days:leave_request.leave_type }}</h4>
                    <small class="text-muted">{% trans "Days remaining for" %} {{ leave_request.leave_type.name }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
{% if leave_request.status == 'pending' %}
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Reject Leave Request" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" action="{% url 'hr:leave_request_reject' leave_request.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <p>{% trans "Are you sure you want to reject this leave request?" %}</p>
                    <div class="mb-3">
                        <label for="rejection_reason" class="form-label">{% trans "Rejection Reason" %}</label>
                        <textarea name="rejection_reason" id="rejection_reason" class="form-control" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                    <button type="submit" class="btn btn-danger">{% trans "Reject" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
