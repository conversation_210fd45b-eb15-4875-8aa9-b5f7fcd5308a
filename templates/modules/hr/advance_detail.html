{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Advance Details" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:advance_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="h3 mb-0">{% trans "Advance Details" %}</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Advance Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Advance Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "Employee" %}:</strong></td>
                                <td>{{ advance.employee.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Employee ID" %}:</strong></td>
                                <td>{{ advance.employee.employee_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Type" %}:</strong></td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ advance.get_advance_type_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Amount" %}:</strong></td>
                                <td><strong class="text-primary">{{ advance.amount|floatformat:2 }}</strong></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "Installments" %}:</strong></td>
                                <td>
                                    {% if advance.installments > 1 %}
                                        {{ advance.installments }} {% trans "installments" %}
                                        {% if advance.installment_amount %}
                                        <br><small class="text-muted">{{ advance.installment_amount|floatformat:2 }} {% trans "per installment" %}</small>
                                        {% endif %}
                                    {% else %}
                                        {% trans "Single payment" %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Required Date" %}:</strong></td>
                                <td>{{ advance.required_date|date:"d/m/Y" }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Status" %}:</strong></td>
                                <td>
                                    {% if advance.status == 'pending' %}
                                        <span class="badge bg-warning">{% trans "Pending" %}</span>
                                    {% elif advance.status == 'approved' %}
                                        <span class="badge bg-success">{% trans "Approved" %}</span>
                                    {% elif advance.status == 'rejected' %}
                                        <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                    {% elif advance.status == 'paid' %}
                                        <span class="badge bg-primary">{% trans "Paid" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Request Date" %}:</strong></td>
                                <td>{{ advance.created_at|date:"d/m/Y H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                {% if advance.reason %}
                <div class="mt-3">
                    <h6>{% trans "Reason" %}:</h6>
                    <p class="text-muted">{{ advance.reason }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Actions -->
        {% if advance.status == 'pending' %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Actions" %}</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <form method="post" action="{% url 'hr:advance_approve' advance.id %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-success w-100" onclick="return confirm('{% trans "Are you sure you want to approve this advance?" %}')">
                            <i class="fas fa-check me-2"></i>
                            {% trans "Approve" %}
                        </button>
                    </form>

                    <form method="post" action="{% url 'hr:advance_reject' advance.id %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('{% trans "Are you sure you want to reject this advance?" %}')">
                            <i class="fas fa-times me-2"></i>
                            {% trans "Reject" %}
                        </button>
                    </form>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Employee Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Employee Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if advance.employee.photo %}
                    <img src="{{ advance.employee.photo.url }}" class="rounded-circle" width="80" height="80" alt="{{ advance.employee.full_name }}">
                    {% else %}
                    <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    {% endif %}
                </div>

                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>{% trans "Name" %}:</strong></td>
                        <td>{{ advance.employee.full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Department" %}:</strong></td>
                        <td>{{ advance.employee.department|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Position" %}:</strong></td>
                        <td>{{ advance.employee.position|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Basic Salary" %}:</strong></td>
                        <td>{{ advance.employee.basic_salary|floatformat:2 }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
