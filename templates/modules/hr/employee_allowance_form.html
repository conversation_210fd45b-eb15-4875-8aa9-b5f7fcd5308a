{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2 text-success"></i>
                    {{ title }}
                </h5>
                <div class="text-muted">
                    {% trans "الموظف" %} (Employee): <strong>{{ employee.get_full_name }}</strong> ({{ employee.employee_id }})
                </div>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="allowance_type" class="form-label">
                                    {% trans "نوع البدل" %} (Allowance Type) <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="allowance_type" name="allowance_type" required>
                                    <option value="">{% trans "اختر نوع البدل" %} (Select Type)</option>
                                    {% for allowance_type in allowance_types %}
                                    <option value="{{ allowance_type.id }}" 
                                            data-calculation-type="{{ allowance_type.calculation_type }}"
                                            data-fixed-amount="{{ allowance_type.fixed_amount|default:'' }}">
                                        {{ allowance_type.name }} ({{ allowance_type.code }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">
                                    {% trans "تاريخ البداية" %} (Start Date) <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6" id="fixed_amount_field" style="display: none;">
                            <div class="mb-3">
                                <label for="fixed_amount" class="form-label">
                                    {% trans "المبلغ الثابت" %} (Fixed Amount)
                                </label>
                                <input type="number" class="form-control" id="fixed_amount" name="fixed_amount" step="0.01" min="0">
                                <div class="form-text">{% trans "المبلغ الثابت للبدل" %} (Fixed allowance amount)</div>
                            </div>
                        </div>
                        <div class="col-md-6" id="percentage_field" style="display: none;">
                            <div class="mb-3">
                                <label for="percentage" class="form-label">
                                    {% trans "النسبة %" %} (Percentage %)
                                </label>
                                <input type="number" class="form-control" id="percentage" name="percentage" step="0.01" min="0" max="100">
                                <div class="form-text">{% trans "النسبة من الراتب الأساسي" %} (Percentage of basic salary)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">
                                    {% trans "تاريخ النهاية" %} (End Date)
                                </label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="form-text">{% trans "اتركه فارغاً للبدل الدائم" %} (Leave empty for permanent allowance)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            {% trans "ملاحظات" %} (Notes)
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Salary Calculation Preview -->
                    <div class="card bg-light mb-3" id="salary_preview" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-calculator me-2"></i>
                                {% trans "معاينة حساب الراتب" %} (Salary Calculation Preview)
                            </h6>
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-muted">{% trans "الراتب الأساسي" %} (Basic Salary)</div>
                                    <div class="h6 text-primary">{{ employee.basic_salary|floatformat:2 }}</div>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted">{% trans "قيمة البدل" %} (Allowance Amount)</div>
                                    <div class="h6 text-success" id="allowance_amount">0.00</div>
                                </div>
                                <div class="col-4">
                                    <div class="text-muted">{% trans "إجمالي الراتب" %} (Total Salary)</div>
                                    <div class="h6 text-info" id="total_salary">{{ employee.basic_salary|floatformat:2 }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:employee_detail' employee.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            {% trans "رجوع" %} (Back)
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            {% trans "حفظ البدل" %} (Save Allowance)
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const allowanceTypeSelect = document.getElementById('allowance_type');
    const fixedAmountField = document.getElementById('fixed_amount_field');
    const percentageField = document.getElementById('percentage_field');
    const fixedAmountInput = document.getElementById('fixed_amount');
    const percentageInput = document.getElementById('percentage');
    const salaryPreview = document.getElementById('salary_preview');
    const allowanceAmountDisplay = document.getElementById('allowance_amount');
    const totalSalaryDisplay = document.getElementById('total_salary');
    
    const basicSalary = {{ employee.basic_salary }};
    
    allowanceTypeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const calculationType = selectedOption.dataset.calculationType;
        const fixedAmount = selectedOption.dataset.fixedAmount;
        
        // Reset fields
        fixedAmountField.style.display = 'none';
        percentageField.style.display = 'none';
        fixedAmountInput.required = false;
        percentageInput.required = false;
        
        if (calculationType === 'fixed') {
            fixedAmountField.style.display = 'block';
            fixedAmountInput.required = true;
            if (fixedAmount) {
                fixedAmountInput.value = fixedAmount;
            }
        } else if (calculationType === 'percentage') {
            percentageField.style.display = 'block';
            percentageInput.required = true;
        }
        
        updateSalaryPreview();
        salaryPreview.style.display = calculationType ? 'block' : 'none';
    });
    
    fixedAmountInput.addEventListener('input', updateSalaryPreview);
    percentageInput.addEventListener('input', updateSalaryPreview);
    
    function updateSalaryPreview() {
        const selectedOption = allowanceTypeSelect.options[allowanceTypeSelect.selectedIndex];
        const calculationType = selectedOption.dataset.calculationType;
        let allowanceAmount = 0;
        
        if (calculationType === 'fixed') {
            allowanceAmount = parseFloat(fixedAmountInput.value) || 0;
        } else if (calculationType === 'percentage') {
            const percentage = parseFloat(percentageInput.value) || 0;
            allowanceAmount = (basicSalary * percentage) / 100;
        }
        
        const totalSalary = basicSalary + allowanceAmount;
        
        allowanceAmountDisplay.textContent = allowanceAmount.toFixed(2);
        totalSalaryDisplay.textContent = totalSalary.toFixed(2);
    }
});
</script>
{% endblock %}
