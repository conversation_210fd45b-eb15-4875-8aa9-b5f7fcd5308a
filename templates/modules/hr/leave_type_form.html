{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.code.id_for_label }}" class="form-label">{{ form.code.label }}</label>
                            {{ form.code }}
                            {% if form.code.errors %}
                                <div class="text-danger small">{{ form.code.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">{% trans "Unique code for this leave type (e.g., ANNUAL, SICK)" %}</small>
                        </div>
                    </div>

                    <!-- Leave Limits -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="{{ form.max_days_per_year.id_for_label }}" class="form-label">{{ form.max_days_per_year.label }}</label>
                            {{ form.max_days_per_year }}
                            {% if form.max_days_per_year.errors %}
                                <div class="text-danger small">{{ form.max_days_per_year.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.max_consecutive_days.id_for_label }}" class="form-label">{{ form.max_consecutive_days.label }}</label>
                            {{ form.max_consecutive_days }}
                            {% if form.max_consecutive_days.errors %}
                                <div class="text-danger small">{{ form.max_consecutive_days.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.min_notice_days.id_for_label }}" class="form-label">{{ form.min_notice_days.label }}</label>
                            {{ form.min_notice_days }}
                            {% if form.min_notice_days.errors %}
                                <div class="text-danger small">{{ form.min_notice_days.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Salary Settings -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                {{ form.is_paid }}
                                <label class="form-check-label" for="{{ form.is_paid.id_for_label }}">
                                    {{ form.is_paid.label }}
                                </label>
                            </div>
                            {% if form.is_paid.errors %}
                                <div class="text-danger small">{{ form.is_paid.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.salary_percentage.id_for_label }}" class="form-label">{{ form.salary_percentage.label }}</label>
                            <div class="input-group">
                                {{ form.salary_percentage }}
                                <span class="input-group-text">%</span>
                            </div>
                            {% if form.salary_percentage.errors %}
                                <div class="text-danger small">{{ form.salary_percentage.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Requirements -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-check">
                                {{ form.requires_approval }}
                                <label class="form-check-label" for="{{ form.requires_approval.id_for_label }}">
                                    {{ form.requires_approval.label }}
                                </label>
                            </div>
                            {% if form.requires_approval.errors %}
                                <div class="text-danger small">{{ form.requires_approval.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                {{ form.requires_medical_certificate }}
                                <label class="form-check-label" for="{{ form.requires_medical_certificate.id_for_label }}">
                                    {{ form.requires_medical_certificate.label }}
                                </label>
                            </div>
                            {% if form.requires_medical_certificate.errors %}
                                <div class="text-danger small">{{ form.requires_medical_certificate.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                {{ form.carry_forward }}
                                <label class="form-check-label" for="{{ form.carry_forward.id_for_label }}">
                                    {{ form.carry_forward.label }}
                                </label>
                            </div>
                            {% if form.carry_forward.errors %}
                                <div class="text-danger small">{{ form.carry_forward.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:leave_type_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            {% trans "Back to List" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save Leave Type" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Help" %}</h6>
            </div>
            <div class="card-body">
                <h6>{% trans "Leave Type Configuration" %}</h6>
                <ul class="small">
                    <li><strong>{% trans "Name" %}:</strong> {% trans "Display name for this leave type" %}</li>
                    <li><strong>{% trans "Code" %}:</strong> {% trans "Unique identifier (e.g., ANNUAL, SICK)" %}</li>
                    <li><strong>{% trans "Max Days/Year" %}:</strong> {% trans "Maximum days allowed per year" %}</li>
                    <li><strong>{% trans "Consecutive Days" %}:</strong> {% trans "Maximum consecutive days allowed" %}</li>
                    <li><strong>{% trans "Notice Days" %}:</strong> {% trans "Minimum advance notice required" %}</li>
                    <li><strong>{% trans "Salary %" %}:</strong> {% trans "Percentage of salary paid during leave" %}</li>
                </ul>
            </div>
        </div>

        <!-- Common Leave Types -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Common Leave Types" %}</h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <strong>{% trans "Annual Leave" %}</strong><br>
                        <small class="text-muted">Code: ANNUAL, 30 days/year, 100% paid</small>
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "Sick Leave" %}</strong><br>
                        <small class="text-muted">Code: SICK, 15 days/year, Medical cert required</small>
                    </div>
                    <div class="mb-2">
                        <strong>{% trans "Emergency Leave" %}</strong><br>
                        <small class="text-muted">Code: EMERGENCY, 5 days/year, No notice</small>
                    </div>
                    <div>
                        <strong>{% trans "Maternity Leave" %}</strong><br>
                        <small class="text-muted">Code: MATERNITY, 90 days/year, 100% paid</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Quick Actions" %}</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'hr:leave_type_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>
                        {% trans "All Leave Types" %}
                    </a>
                    <a href="{% url 'hr:leave_request_list' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-calendar-check me-1"></i>
                        {% trans "Leave Requests" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-generate code from name
document.addEventListener('DOMContentLoaded', function() {
    const nameField = document.getElementById('{{ form.name.id_for_label }}');
    const codeField = document.getElementById('{{ form.code.id_for_label }}');
    
    nameField.addEventListener('blur', function() {
        if (!codeField.value && nameField.value) {
            // Generate code from name
            let code = nameField.value
                .toUpperCase()
                .replace(/[^A-Z\s]/g, '')
                .split(' ')
                .map(word => word.substring(0, 3))
                .join('');
            
            if (code.length > 10) {
                code = code.substring(0, 10);
            }
            
            codeField.value = code;
        }
    });
    
    // Toggle salary percentage based on is_paid
    const isPaidField = document.getElementById('{{ form.is_paid.id_for_label }}');
    const salaryPercentageField = document.getElementById('{{ form.salary_percentage.id_for_label }}');
    
    function toggleSalaryPercentage() {
        if (isPaidField.checked) {
            salaryPercentageField.disabled = false;
            if (!salaryPercentageField.value) {
                salaryPercentageField.value = '100';
            }
        } else {
            salaryPercentageField.disabled = true;
            salaryPercentageField.value = '0';
        }
    }
    
    isPaidField.addEventListener('change', toggleSalaryPercentage);
    toggleSalaryPercentage(); // Initial call
});
</script>
{% endblock %}
