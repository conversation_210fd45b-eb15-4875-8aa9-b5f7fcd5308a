{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-box me-2"></i>
        {{ title }}
    </h1>
    <a href="{% url 'hr:custody_list' %}" class="btn btn-secondary">
        <i class="fas fa-list me-1"></i>
        {% trans "Back to List" %}
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <!-- Employee and Type -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.employee.id_for_label }}" class="form-label">
                                    {{ form.employee.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.employee }}
                                {% if form.employee.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.employee.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.custody_type.id_for_label }}" class="form-label">
                                    {{ form.custody_type.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.custody_type }}
                                {% if form.custody_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.custody_type.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Item Details -->
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="{{ form.item_name.id_for_label }}" class="form-label">
                                    {{ form.item_name.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.item_name }}
                                {% if form.item_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.item_name.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.serial_number.id_for_label }}" class="form-label">
                                    {{ form.serial_number.label }}
                                </label>
                                {{ form.serial_number }}
                                {% if form.serial_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.serial_number.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.item_description.id_for_label }}" class="form-label">
                            {{ form.item_description.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.item_description }}
                        {% if form.item_description.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.item_description.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Quantity and Value -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.quantity.id_for_label }}" class="form-label">
                                    {{ form.quantity.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.quantity }}
                                {% if form.quantity.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.quantity.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="{{ form.unit_value.id_for_label }}" class="form-label">
                                    {{ form.unit_value.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.unit_value }}
                                {% if form.unit_value.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.unit_value.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">{% trans "Total Value" %}</label>
                                <input type="text" class="form-control" id="total-value" readonly>
                                <div class="form-text">{% trans "Calculated automatically" %}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Dates -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.delivery_date.id_for_label }}" class="form-label">
                                    {{ form.delivery_date.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.delivery_date }}
                                {% if form.delivery_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.delivery_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.expected_return_date.id_for_label }}" class="form-label">
                                    {{ form.expected_return_date.label }}
                                </label>
                                {{ form.expected_return_date }}
                                {% if form.expected_return_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.expected_return_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Delivered By -->
                    <div class="mb-3">
                        <label for="{{ form.delivered_by.id_for_label }}" class="form-label">
                            {{ form.delivered_by.label }}
                        </label>
                        {{ form.delivered_by }}
                        {% if form.delivered_by.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.delivered_by.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Condition -->
                    <div class="mb-3">
                        <label for="{{ form.condition_on_delivery.id_for_label }}" class="form-label">
                            {{ form.condition_on_delivery.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.condition_on_delivery }}
                        {% if form.condition_on_delivery.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.condition_on_delivery.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.notes.errors.0 }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:custody_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form classes
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        control.classList.add('form-control');
    });
    
    // Calculate total value
    const quantityField = document.getElementById('{{ form.quantity.id_for_label }}');
    const unitValueField = document.getElementById('{{ form.unit_value.id_for_label }}');
    const totalValueField = document.getElementById('total-value');
    
    function calculateTotal() {
        const quantity = parseFloat(quantityField.value) || 0;
        const unitValue = parseFloat(unitValueField.value) || 0;
        const totalValue = quantity * unitValue;
        
        totalValueField.value = totalValue.toFixed(2);
    }
    
    if (quantityField && unitValueField && totalValueField) {
        quantityField.addEventListener('input', calculateTotal);
        unitValueField.addEventListener('input', calculateTotal);
        calculateTotal(); // Initial calculation
    }
});
</script>
{% endblock %}
