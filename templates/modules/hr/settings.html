{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "HR Settings" %}{% endblock %}

{% block page_title %}
<h1 class="h3 mb-0">
    <i class="fas fa-cog me-2"></i>
    {% trans "HR Settings" %}
</h1>
{% endblock %}

{% block content %}
<div class="row">
    <!-- General Settings -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-sitemap fa-3x text-primary mb-3"></i>
                <h5 class="card-title">{% trans "Departments" %}</h5>
                <p class="card-text text-muted">{% trans "Manage company departments and structure" %}</p>
                <a href="{% url 'hr:department_list' %}" class="btn btn-primary">
                    {% trans "Manage" %}
                </a>
            </div>
        </div>
    </div>
    
    <!-- Position Settings -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-briefcase fa-3x text-success mb-3"></i>
                <h5 class="card-title">{% trans "Positions" %}</h5>
                <p class="card-text text-muted">{% trans "Define job positions and roles" %}</p>
                <a href="{% url 'hr:position_list' %}" class="btn btn-success">
                    {% trans "Manage" %}
                </a>
            </div>
        </div>
    </div>
    
    <!-- Work Schedules -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>
                <h5 class="card-title">{% trans "Work Schedules" %}</h5>
                <p class="card-text text-muted">{% trans "Configure working hours and schedules" %}</p>
                <a href="{% url 'hr:work_schedule_list' %}" class="btn btn-info">
                    {% trans "Manage" %}
                </a>
            </div>
        </div>
    </div>
    
    <!-- Attendance Devices -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-fingerprint fa-3x text-warning mb-3"></i>
                <h5 class="card-title">{% trans "Attendance Devices" %}</h5>
                <p class="card-text text-muted">{% trans "Manage biometric and attendance devices" %}</p>
                <a href="{% url 'hr:attendance_device_list' %}" class="btn btn-warning">
                    {% trans "Manage" %}
                </a>
            </div>
        </div>
    </div>
    
    <!-- Sales Teams -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-users-cog fa-3x text-secondary mb-3"></i>
                <h5 class="card-title">{% trans "Sales Teams" %}</h5>
                <p class="card-text text-muted">{% trans "Configure sales teams and territories" %}</p>
                <a href="{% url 'hr:sales_team_list' %}" class="btn btn-secondary">
                    {% trans "Manage" %}
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Settings -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-body text-center">
                <i class="fas fa-tools fa-3x text-danger mb-3"></i>
                <h5 class="card-title">{% trans "System Settings" %}</h5>
                <p class="card-text text-muted">{% trans "General HR system configuration" %}</p>
                <button class="btn btn-danger" disabled>
                    {% trans "Coming Soon" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
