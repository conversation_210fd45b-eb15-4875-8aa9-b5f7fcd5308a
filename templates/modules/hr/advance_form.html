{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Advance Request Form" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:advance_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="h3 mb-0">{% trans "Advance Request" %}</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<!-- Smart Guidelines for Advance Requests -->
<div class="alert alert-info alert-dismissible fade show">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-info-circle fa-lg text-info"></i>
        </div>
        <div>
            <h6 class="alert-heading">{% trans "Advance Request Guidelines" %}</h6>
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-2"><strong>{% trans "Before submitting:" %}</strong></p>
                    <ul class="mb-0">
                        <li>{% trans "Ensure you have a valid reason for the advance" %}</li>
                        <li>{% trans "Check your current salary and advance limits" %}</li>
                        <li>{% trans "Consider the repayment schedule carefully" %}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <p class="mb-2"><strong>{% trans "Processing time:" %}</strong></p>
                    <ul class="mb-0">
                        <li>{% trans "Emergency requests: 1-2 business days" %}</li>
                        <li>{% trans "Regular requests: 3-5 business days" %}</li>
                        <li>{% trans "Large amounts may require additional approval" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ title|default:"Advance Request Form" }}</h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.employee.id_for_label }}" class="form-label">
                                    {{ form.employee.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.employee }}
                                {% if form.employee.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.employee.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.advance_type.id_for_label }}" class="form-label">
                                    {{ form.advance_type.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.advance_type }}
                                {% if form.advance_type.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.advance_type.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.amount.id_for_label }}" class="form-label">
                                    {{ form.amount.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.amount }}
                                {% if form.amount.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.amount.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.installments.id_for_label }}" class="form-label">
                                    {{ form.installments.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.installments }}
                                {% if form.installments.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.installments.errors.0 }}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    {% trans "Number of monthly installments (1 for single payment)" %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.required_date.id_for_label }}" class="form-label">
                            {{ form.required_date.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.required_date }}
                        {% if form.required_date.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.required_date.errors.0 }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="{{ form.reason.id_for_label }}" class="form-label">
                            {{ form.reason.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.reason }}
                        {% if form.reason.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.reason.errors.0 }}
                        </div>
                        {% endif %}
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:advance_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form classes
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        control.classList.add('form-control');
    });
});
</script>
{% endblock %}
