{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Leave Requests" %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% trans "Leave Requests" %}</h2>
    <a href="{% url 'hr:leave_request_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        {% trans "New Leave Request" %}
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="status" class="form-label">{% trans "Status" %}</label>
                <select name="status" id="status" class="form-select">
                    <option value="">{% trans "All Statuses" %}</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                    <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="employee" class="form-label">{% trans "Employee" %}</label>
                <select name="employee" id="employee" class="form-select">
                    <option value="">{% trans "All Employees" %}</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>
                        {{ employee.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="leave_type" class="form-label">{% trans "Leave Type" %}</label>
                <select name="leave_type" id="leave_type" class="form-select">
                    <option value="">{% trans "All Types" %}</option>
                    {% for leave_type in leave_types %}
                    <option value="{{ leave_type.id }}" {% if leave_type_filter == leave_type.id|stringformat:"s" %}selected{% endif %}>
                        {{ leave_type.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-filter me-1"></i>
                    {% trans "Filter" %}
                </button>
                <a href="{% url 'hr:leave_request_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    {% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Leave Requests List -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Employee" %}</th>
                        <th>{% trans "Leave Type" %}</th>
                        <th>{% trans "Period" %}</th>
                        <th>{% trans "Days" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Request Date" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for leave_request in page_obj %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if leave_request.employee.photo %}
                                <img src="{{ leave_request.employee.photo.url }}" alt="{{ leave_request.employee.full_name }}" 
                                     class="rounded-circle me-2" width="32" height="32">
                                {% else %}
                                <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                                     style="width: 32px; height: 32px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ leave_request.employee.full_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ leave_request.employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ leave_request.leave_type.name }}</span>
                        </td>
                        <td>
                            <div>
                                <i class="fas fa-calendar me-1"></i>
                                {{ leave_request.start_date|date:"d/m/Y" }} - {{ leave_request.end_date|date:"d/m/Y" }}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ leave_request.total_days }} {% trans "days" %}</span>
                        </td>
                        <td>
                            {% if leave_request.status == 'pending' %}
                                <span class="badge bg-warning">{% trans "Pending" %}</span>
                            {% elif leave_request.status == 'approved' %}
                                <span class="badge bg-success">{% trans "Approved" %}</span>
                            {% elif leave_request.status == 'rejected' %}
                                <span class="badge bg-danger">{% trans "Rejected" %}</span>
                            {% elif leave_request.status == 'cancelled' %}
                                <span class="badge bg-secondary">{% trans "Cancelled" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ leave_request.created_at|date:"d/m/Y H:i" }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'hr:leave_request_detail' leave_request.id %}" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if leave_request.status == 'pending' %}
                                <a href="{% url 'hr:leave_request_edit' leave_request.id %}" class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="post" action="{% url 'hr:leave_request_approve' leave_request.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-success" title="{% trans 'Approve' %}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <button type="button" class="btn btn-outline-danger" title="{% trans 'Reject' %}" 
                                        data-bs-toggle="modal" data-bs-target="#rejectModal{{ leave_request.id }}">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>

                    <!-- Reject Modal -->
                    {% if leave_request.status == 'pending' %}
                    <div class="modal fade" id="rejectModal{{ leave_request.id }}" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">{% trans "Reject Leave Request" %}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <form method="post" action="{% url 'hr:leave_request_reject' leave_request.id %}">
                                    {% csrf_token %}
                                    <div class="modal-body">
                                        <p>{% trans "Are you sure you want to reject this leave request?" %}</p>
                                        <div class="mb-3">
                                            <label for="rejection_reason{{ leave_request.id }}" class="form-label">{% trans "Rejection Reason" %}</label>
                                            <textarea name="rejection_reason" id="rejection_reason{{ leave_request.id }}" 
                                                    class="form-control" rows="3" required></textarea>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                                        <button type="submit" class="btn btn-danger">{% trans "Reject" %}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}">{% trans "Previous" %}</a>
                </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                <li class="page-item active">
                    <span class="page-link">{{ num }}</span>
                </li>
                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                </li>
                {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}">{% trans "Next" %}</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No leave requests found" %}</h5>
            <p class="text-muted">{% trans "Create your first leave request to get started." %}</p>
            <a href="{% url 'hr:leave_request_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create First Leave Request" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
