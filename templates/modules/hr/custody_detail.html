{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Custody Details" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-box me-2"></i>
        {% trans "Custody Details" %}
    </h1>
    <div>
        {% if custody.status == 'active' %}
        <a href="{% url 'hr:custody_edit' custody.id %}" class="btn btn-outline-primary">
            <i class="fas fa-edit me-1"></i>
            {% trans "Edit" %}
        </a>
        <a href="{% url 'hr:custody_return' custody.id %}" class="btn btn-outline-success">
            <i class="fas fa-undo me-1"></i>
            {% trans "Return" %}
        </a>
        {% endif %}
        <a href="{% url 'hr:custody_list' %}" class="btn btn-secondary">
            <i class="fas fa-list me-1"></i>
            {% trans "Back to List" %}
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Custody Information -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% trans "Custody Information" %}</h5>
                {% if custody.is_overdue %}
                <span class="badge bg-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    {% trans "Overdue" %} ({{ custody.days_overdue }} {% trans "days" %})
                </span>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "Custody Number" %}:</strong></td>
                                <td>{{ custody.custody_number }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Employee" %}:</strong></td>
                                <td>{{ custody.employee.full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Employee ID" %}:</strong></td>
                                <td>{{ custody.employee.employee_id }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Type" %}:</strong></td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ custody.get_custody_type_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Item Name" %}:</strong></td>
                                <td>{{ custody.item_name }}</td>
                            </tr>
                            {% if custody.serial_number %}
                            <tr>
                                <td><strong>{% trans "Serial Number" %}:</strong></td>
                                <td>{{ custody.serial_number }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "Quantity" %}:</strong></td>
                                <td>{{ custody.quantity }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Unit Value" %}:</strong></td>
                                <td>{{ custody.unit_value|floatformat:2 }}</td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Total Value" %}:</strong></td>
                                <td><strong class="text-primary">{{ custody.total_value|floatformat:2 }}</strong></td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Status" %}:</strong></td>
                                <td>
                                    {% if custody.status == 'active' %}
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% elif custody.status == 'returned' %}
                                        <span class="badge bg-primary">{% trans "Returned" %}</span>
                                    {% elif custody.status == 'lost' %}
                                        <span class="badge bg-danger">{% trans "Lost" %}</span>
                                    {% elif custody.status == 'damaged' %}
                                        <span class="badge bg-warning">{% trans "Damaged" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>{% trans "Delivery Date" %}:</strong></td>
                                <td>{{ custody.delivery_date|date:"d/m/Y" }}</td>
                            </tr>
                            {% if custody.expected_return_date %}
                            <tr>
                                <td><strong>{% trans "Expected Return" %}:</strong></td>
                                <td>{{ custody.expected_return_date|date:"d/m/Y" }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if custody.item_description %}
                <div class="mt-3">
                    <h6>{% trans "Item Description" %}:</h6>
                    <p class="text-muted">{{ custody.item_description }}</p>
                </div>
                {% endif %}
                
                <div class="mt-3">
                    <h6>{% trans "Condition on Delivery" %}:</h6>
                    <p class="text-muted">{{ custody.condition_on_delivery }}</p>
                </div>
                
                {% if custody.condition_on_return %}
                <div class="mt-3">
                    <h6>{% trans "Condition on Return" %}:</h6>
                    <p class="text-muted">{{ custody.condition_on_return }}</p>
                </div>
                {% endif %}
                
                {% if custody.notes %}
                <div class="mt-3">
                    <h6>{% trans "Notes" %}:</h6>
                    <p class="text-muted">{{ custody.notes }}</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Return Information -->
        {% if custody.status == 'returned' and custody.actual_return_date %}
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Return Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>{% trans "Return Date" %}:</strong></td>
                                <td>{{ custody.actual_return_date|date:"d/m/Y" }}</td>
                            </tr>
                            {% if custody.received_by %}
                            <tr>
                                <td><strong>{% trans "Received By" %}:</strong></td>
                                <td>{{ custody.received_by.get_full_name|default:custody.received_by.username }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <!-- Employee Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Employee Information" %}</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if custody.employee.photo %}
                    <img src="{{ custody.employee.photo.url }}" class="rounded-circle" width="80" height="80" alt="{{ custody.employee.full_name }}">
                    {% else %}
                    <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                        <i class="fas fa-user fa-2x text-white"></i>
                    </div>
                    {% endif %}
                </div>
                
                <table class="table table-borderless table-sm">
                    <tr>
                        <td><strong>{% trans "Name" %}:</strong></td>
                        <td>{{ custody.employee.full_name }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Department" %}:</strong></td>
                        <td>{{ custody.employee.department|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Position" %}:</strong></td>
                        <td>{{ custody.employee.position|default:"-" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Phone" %}:</strong></td>
                        <td>{{ custody.employee.phone|default:"-" }}</td>
                    </tr>
                </table>
                
                <div class="d-grid">
                    <a href="{% url 'hr:employee_detail' custody.employee.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-user me-1"></i>
                        {% trans "View Employee" %}
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Delivery Information -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Delivery Information" %}</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless table-sm">
                    {% if custody.delivered_by %}
                    <tr>
                        <td><strong>{% trans "Delivered By" %}:</strong></td>
                        <td>{{ custody.delivered_by.get_full_name|default:custody.delivered_by.username }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>{% trans "Created" %}:</strong></td>
                        <td>{{ custody.created_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Last Updated" %}:</strong></td>
                        <td>{{ custody.updated_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
