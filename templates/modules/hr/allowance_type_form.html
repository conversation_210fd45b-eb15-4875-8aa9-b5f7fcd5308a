{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2 text-success"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    {% trans "اسم البدل" %} (Allowance Name) <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">{% trans "مثال: بدل سكن، بدل مواصلات" %} (e.g., Housing, Transportation)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">
                                    {% trans "كود البدل" %} (Allowance Code) <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="code" name="code" required>
                                <div class="form-text">{% trans "مثال: HOUSING, TRANSPORT" %} (e.g., HOUSING, TRANSPORT)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="calculation_type" class="form-label">
                                    {% trans "نوع الحساب" %} (Calculation Type) <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="calculation_type" name="calculation_type" required>
                                    <option value="">{% trans "اختر نوع الحساب" %} (Select Type)</option>
                                    <option value="fixed">{% trans "مبلغ ثابت" %} (Fixed Amount)</option>
                                    <option value="percentage">{% trans "نسبة من الراتب" %} (Percentage of Salary)</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_amount" class="form-label">
                                    {% trans "المبلغ الثابت" %} (Fixed Amount)
                                </label>
                                <input type="number" class="form-control" id="default_amount" name="default_amount" step="0.01" min="0">
                                <div class="form-text">{% trans "يُستخدم فقط مع المبلغ الثابت" %} (Used only with fixed amount)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            {% trans "الوصف" %} (Description)
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        <div class="form-text">{% trans "وصف اختياري للبدل" %} (Optional description)</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                {% trans "نشط" %} (Active)
                            </label>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:allowance_type_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            {% trans "رجوع" %} (Back)
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            {% trans "حفظ" %} (Save)
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculationType = document.getElementById('calculation_type');
    const defaultAmount = document.getElementById('default_amount');

    calculationType.addEventListener('change', function() {
        if (this.value === 'fixed') {
            defaultAmount.required = true;
            defaultAmount.parentElement.style.display = 'block';
        } else if (this.value === 'percentage') {
            defaultAmount.required = false;
            defaultAmount.value = '';
            defaultAmount.parentElement.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
