{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Employee List" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-users me-2"></i>
        {% trans "Employee List" %}
    </h1>
    <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        {% trans "Add Employee" %}
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">{% trans "Search" %}</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="{% trans 'Name, ID, or National ID' %}">
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">{% trans "Department" %}</label>
                <select class="form-select" id="department" name="department">
                    <option value="">{% trans "All Departments" %}</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if dept.id|stringformat:"s" == selected_department %}selected{% endif %}>
                        {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">{% trans "Status" %}</label>
                <select class="form-select" id="status" name="status">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="active" {% if selected_status == "active" %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="inactive" {% if selected_status == "inactive" %}selected{% endif %}>{% trans "Inactive" %}</option>
                    <option value="terminated" {% if selected_status == "terminated" %}selected{% endif %}>{% trans "Terminated" %}</option>
                    <option value="suspended" {% if selected_status == "suspended" %}selected{% endif %}>{% trans "Suspended" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        {% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Employee List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            {% trans "Employees" %}
            <span class="badge bg-secondary ms-2">{{ page_obj.paginator.count }}</span>
        </h5>
    </div>
    <div class="card-body p-0">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{% trans "Photo" %}</th>
                        <th>{% trans "Employee ID" %}</th>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Department" %}</th>
                        <th>{% trans "Position" %}</th>
                        <th>{% trans "Mobile" %}</th>
                        <th>{% trans "Hire Date" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in page_obj %}
                    <tr>
                        <td>
                            {% if employee.photo %}
                            <img src="{{ employee.photo.url }}" alt="{{ employee.full_name }}" 
                                 class="rounded-circle" width="40" height="40">
                            {% else %}
                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ employee.employee_id }}</strong>
                        </td>
                        <td>
                            <div>
                                <strong>{{ employee.full_name }}</strong>
                                {% if employee.arabic_name %}
                                <br><small class="text-muted">{{ employee.arabic_name }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ employee.department.name }}</td>
                        <td>{{ employee.position.title }}</td>
                        <td>{{ employee.mobile }}</td>
                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                        <td>
                            {% if employee.status == 'active' %}
                            <span class="badge bg-success">{% trans "Active" %}</span>
                            {% elif employee.status == 'inactive' %}
                            <span class="badge bg-warning">{% trans "Inactive" %}</span>
                            {% elif employee.status == 'terminated' %}
                            <span class="badge bg-danger">{% trans "Terminated" %}</span>
                            {% elif employee.status == 'suspended' %}
                            <span class="badge bg-secondary">{% trans "Suspended" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'hr:employee_detail' employee.id %}" 
                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'hr:employee_edit' employee.id %}" 
                                   class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if employee.status == 'active' %}
                                <a href="{% url 'hr:employee_delete' employee.id %}" 
                                   class="btn btn-outline-danger" title="{% trans 'Terminate' %}"
                                   onclick="return confirm('{% trans "Are you sure you want to terminate this employee?" %}')">
                                    <i class="fas fa-user-times"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No employees found" %}</h5>
            <p class="text-muted">{% trans "Start by adding your first employee" %}</p>
            <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                {% trans "Add Employee" %}
            </a>
        </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="card-footer">
        <nav aria-label="{% trans 'Employee pagination' %}">
            <ul class="pagination justify-content-center mb-0">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">
                        {% trans "First" %}
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">
                        {% trans "Previous" %}
                    </a>
                </li>
                {% endif %}
                
                <li class="page-item active">
                    <span class="page-link">
                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                    </span>
                </li>
                
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">
                        {% trans "Next" %}
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}">
                        {% trans "Last" %}
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}
