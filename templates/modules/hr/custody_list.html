{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Custodies" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-box me-2"></i>
        {% trans "Employee Custodies" %}
    </h1>
    <a href="{% url 'hr:custody_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        {% trans "New Custody" %}
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">{% trans "Employee" %}</label>
                <select name="employee" class="form-select">
                    <option value="">{% trans "All Employees" %}</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>
                        {{ employee.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">{% trans "Status" %}</label>
                <select name="status" class="form-select">
                    <option value="">{% trans "All Statuses" %}</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="returned" {% if status_filter == 'returned' %}selected{% endif %}>{% trans "Returned" %}</option>
                    <option value="lost" {% if status_filter == 'lost' %}selected{% endif %}>{% trans "Lost" %}</option>
                    <option value="damaged" {% if status_filter == 'damaged' %}selected{% endif %}>{% trans "Damaged" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">{% trans "Type" %}</label>
                <select name="custody_type" class="form-select">
                    <option value="">{% trans "All Types" %}</option>
                    <option value="equipment" {% if type_filter == 'equipment' %}selected{% endif %}>{% trans "Equipment" %}</option>
                    <option value="vehicle" {% if type_filter == 'vehicle' %}selected{% endif %}>{% trans "Vehicle" %}</option>
                    <option value="tools" {% if type_filter == 'tools' %}selected{% endif %}>{% trans "Tools" %}</option>
                    <option value="documents" {% if type_filter == 'documents' %}selected{% endif %}>{% trans "Documents" %}</option>
                    <option value="cash" {% if type_filter == 'cash' %}selected{% endif %}>{% trans "Cash" %}</option>
                    <option value="other" {% if type_filter == 'other' %}selected{% endif %}>{% trans "Other" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-filter me-2"></i>
                        {% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Custodies List -->
<div class="card">
    <div class="card-body">
        {% if custodies %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Custody Number" %}</th>
                        <th>{% trans "Employee" %}</th>
                        <th>{% trans "Item" %}</th>
                        <th>{% trans "Type" %}</th>
                        <th>{% trans "Value" %}</th>
                        <th>{% trans "Delivery Date" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for custody in custodies %}
                    <tr {% if custody.is_overdue %}class="table-warning"{% endif %}>
                        <td>
                            <strong>{{ custody.custody_number }}</strong>
                            {% if custody.is_overdue %}
                            <br>
                            <small class="text-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                {% trans "Overdue" %} ({{ custody.days_overdue }} {% trans "days" %})
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if custody.employee.photo %}
                                <img src="{{ custody.employee.photo.url }}" class="employee-avatar me-2" alt="{{ custody.employee.full_name }}">
                                {% else %}
                                <div class="employee-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ custody.employee.full_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ custody.employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <strong>{{ custody.item_name }}</strong>
                            {% if custody.serial_number %}
                            <br>
                            <small class="text-muted">S/N: {{ custody.serial_number }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ custody.get_custody_type_display }}
                            </span>
                        </td>
                        <td>
                            <strong>{{ custody.total_value|floatformat:2 }}</strong>
                            {% if custody.quantity > 1 %}
                            <br>
                            <small class="text-muted">{{ custody.quantity }} × {{ custody.unit_value|floatformat:2 }}</small>
                            {% endif %}
                        </td>
                        <td>{{ custody.delivery_date|date:"d/m/Y" }}</td>
                        <td>
                            {% if custody.status == 'active' %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% elif custody.status == 'returned' %}
                                <span class="badge bg-primary">{% trans "Returned" %}</span>
                            {% elif custody.status == 'lost' %}
                                <span class="badge bg-danger">{% trans "Lost" %}</span>
                            {% elif custody.status == 'damaged' %}
                                <span class="badge bg-warning">{% trans "Damaged" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'hr:custody_detail' custody.id %}" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if custody.status == 'active' %}
                                <a href="{% url 'hr:custody_edit' custody.id %}" class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'hr:custody_return' custody.id %}" class="btn btn-outline-success" title="{% trans 'Return' %}">
                                    <i class="fas fa-undo"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-box fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No custodies found" %}</h5>
            <p class="text-muted">{% trans "Create your first custody record to get started." %}</p>
            <a href="{% url 'hr:custody_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create Custody" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
