{% extends 'modules/hr/base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}
{% load hr_tags %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="h3 mb-0">{{ title }}</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<!-- Quick Setup for New Employee -->
{% if not form.instance.pk %}
<div class="alert alert-light border mb-4">
    <h6 class="alert-heading">
        <i class="fas fa-tools me-2"></i>
        {% trans "إعداد سريع للنظام" %} (Quick System Setup)
    </h6>
    <p class="mb-3">{% trans "قبل إضافة الموظفين، تأكد من إعداد أنواع البدلات والتأمينات:" %}</p>
    <div class="row">
        <div class="col-md-6">
            <div class="d-flex gap-2 mb-2">
                <a href="{% url 'hr:allowance_type_list' %}" class="btn btn-outline-success btn-sm" target="_blank">
                    <i class="fas fa-money-bill-wave me-1"></i>
                    {% trans "إدارة أنواع البدلات" %}
                </a>
                <a href="{% url 'hr:allowance_type_create' %}" class="btn btn-success btn-sm" target="_blank">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "إضافة نوع بدل" %}
                </a>
            </div>
        </div>
        <div class="col-md-6">
            <div class="d-flex gap-2 mb-2">
                <a href="{% url 'hr:insurance_type_list' %}" class="btn btn-outline-info btn-sm" target="_blank">
                    <i class="fas fa-shield-alt me-1"></i>
                    {% trans "إدارة أنواع التأمينات" %}
                </a>
                <a href="{% url 'hr:insurance_type_create' %}" class="btn btn-info btn-sm" target="_blank">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "إضافة نوع تأمين" %}
                </a>
            </div>
        </div>
    </div>
    <div class="text-center mt-2">
        <small class="text-muted">
            <i class="fas fa-info-circle me-1"></i>
            {% trans "هذه الروابط ستفتح في نوافذ جديدة حتى لا تفقد بيانات النموذج" %}
        </small>
    </div>
</div>
{% endif %}

<form method="post" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                                <i class="fas fa-user me-1"></i>{% trans "Personal Information" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#employment" role="tab">
                                <i class="fas fa-briefcase me-1"></i>{% trans "Employment Details" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#contact" role="tab">
                                <i class="fas fa-phone me-1"></i>{% trans "Contact Information" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#additional" role="tab">
                                <i class="fas fa-info-circle me-1"></i>{% trans "Additional Info" %}
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.first_name|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.last_name|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.arabic_name|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.national_id|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.passport_number|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    {{ form.birth_date|as_crispy_field }}
                                </div>
                                <div class="col-md-4">
                                    {{ form.gender|as_crispy_field }}
                                </div>
                                <div class="col-md-4">
                                    {{ form.marital_status|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <!-- Employment Details Tab -->
                        <div class="tab-pane fade" id="employment" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.employee_id|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.hire_date|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.department|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.position|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.direct_manager|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.basic_salary|as_crispy_field }}
                                </div>
                            </div>

                            <!-- Work Shift Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-clock me-2"></i>
                                            {% trans "شفت العمل" %} (Work Shift)
                                        </h6>
                                        <p class="mb-2">{% trans "Select the work shift for this employee. This determines working hours, break times, and working days." %}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    {{ form.shift|as_crispy_field }}
                                    <small class="form-text text-muted">
                                        {% trans "Choose the appropriate work shift for this employee" %}
                                    </small>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{% trans "Quick Actions" %}</label>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'hr:shift_create' %}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus me-1"></i>
                                            {% trans "إنشاء شفت جديد" %}
                                        </a>
                                        <a href="{% url 'hr:shift_list' %}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-list me-1"></i>
                                            {% trans "عرض جميع الشفتات" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% if form.user %}
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.user|as_crispy_field }}
                                    <small class="form-text text-muted">
                                        {% trans "Link this employee to a user account for system access" %}
                                    </small>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Contact Information Tab -->
                        <div class="tab-pane fade" id="contact" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.mobile|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.phone|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.email|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.address|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.emergency_contact_name|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.emergency_contact_phone|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information Tab -->
                        <div class="tab-pane fade" id="additional" role="tabpanel">
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.notes|as_crispy_field }}
                                </div>
                            </div>

                            <!-- Allowances & Insurance Management -->
                            {% if form.instance.pk %}
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-money-bill-wave me-2"></i>
                                            {% trans "إدارة البدلات" %} (Allowances)
                                        </h6>
                                        <p class="mb-2">{% trans "Manage employee allowances and benefits." %}</p>
                                        <div class="d-grid gap-2">
                                            <a href="{% url 'hr:employee_allowance_create' form.instance.pk %}" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "إضافة بدل" %}
                                            </a>
                                            <a href="{% url 'hr:employee_detail' form.instance.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>
                                                {% trans "عرض البدلات" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            {% trans "إدارة التأمينات" %} (Insurance)
                                        </h6>
                                        <p class="mb-2">{% trans "Manage employee insurance and deductions." %}</p>
                                        <div class="d-grid gap-2">
                                            <a href="{% url 'hr:employee_insurance_create' form.instance.pk %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "إضافة تأمين" %}
                                            </a>
                                            <a href="{% url 'hr:employee_detail' form.instance.pk %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-eye me-1"></i>
                                                {% trans "عرض التأمينات" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Leave Management Info -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-calendar-check me-2"></i>
                                            {% trans "إدارة الإجازات" %} (Leave Management)
                                        </h6>
                                        <p class="mb-2">{% trans "After saving the employee, you can manage their leave requests and view leave balance." %}</p>
                                        <div class="d-flex gap-2">
                                            <a href="{% url 'hr:leave_request_create' %}?employee={{ form.instance.pk }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "طلب إجازة جديد" %}
                                            </a>
                                            <a href="{% url 'hr:employee_detail' form.instance.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>
                                                {% trans "عرض تفاصيل الموظف" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <!-- Features Available After Creation -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="alert alert-primary border-0">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-rocket me-2"></i>
                                            {% trans "المميزات المتاحة بعد إنشاء الموظف" %} (Features Available After Creation)
                                        </h6>
                                        <p class="mb-3">{% trans "بمجرد إنشاء هذا الموظف، ستتمكن من الوصول إلى جميع هذه المميزات المتقدمة:" %}</p>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-money-bill-wave fa-2x text-success"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">{% trans "إدارة البدلات" %}</h6>
                                                        <small class="text-muted">{% trans "إضافة بدلات السكن، المواصلات، الطعام وغيرها" %}</small>
                                                    </div>
                                                </div>

                                                <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-shield-alt fa-2x text-info"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">{% trans "إدارة التأمينات" %}</h6>
                                                        <small class="text-muted">{% trans "إعداد التأمينات الاجتماعية والطبية والخصومات" %}</small>
                                                    </div>
                                                </div>

                                                <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-calendar-check fa-2x text-primary"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">{% trans "إدارة الإجازات" %}</h6>
                                                        <small class="text-muted">{% trans "إنشاء وإدارة طلبات الإجازات ومتابعة الرصيد" %}</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-clock fa-2x text-warning"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">{% trans "تتبع الحضور" %}</h6>
                                                        <small class="text-muted">{% trans "مراقبة أوقات الدخول والخروج وساعات العمل" %}</small>
                                                    </div>
                                                </div>

                                                <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-gavel fa-2x text-danger"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">{% trans "الإجراءات التأديبية" %}</h6>
                                                        <small class="text-muted">{% trans "تطبيق الجزاءات وإدارة المخالفات" %}</small>
                                                    </div>
                                                </div>

                                                <div class="d-flex align-items-center mb-3 p-2 bg-light rounded">
                                                    <div class="flex-shrink-0">
                                                        <i class="fas fa-chart-line fa-2x text-success"></i>
                                                    </div>
                                                    <div class="flex-grow-1 ms-3">
                                                        <h6 class="mb-1">{% trans "التقارير والإحصائيات" %}</h6>
                                                        <small class="text-muted">{% trans "تقارير شاملة عن الأداء والحضور والراتب" %}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="text-center mt-3">
                                            <small class="text-muted">
                                                <i class="fas fa-lightbulb me-1"></i>
                                                {% trans "نصيحة: احفظ الموظف أولاً ثم ستظهر جميع هذه الخيارات في صفحة التفاصيل" %}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Photo and Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">{% trans "Employee Photo" %}</h6>
                </div>
                <div class="card-body text-center">
                    {% if form.instance.photo %}
                    <img src="{{ form.instance.photo.url }}" alt="{% trans 'Employee Photo' %}"
                         class="rounded-circle mb-3" width="150" height="150" style="object-fit: cover;" id="photo-preview">
                    {% else %}
                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 150px; height: 150px;" id="photo-preview">
                        <i class="fas fa-user fa-4x text-white"></i>
                    </div>
                    {% endif %}

                    {{ form.photo|as_crispy_field }}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if form.instance.pk %}
                                {% trans "Update Employee" %}
                            {% else %}
                                {% trans "Create Employee" %}
                            {% endif %}
                        </button>
                        <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            {% trans "Cancel" %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Setup Card for New Employee -->
            {% if not form.instance.pk %}
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-1"></i>
                        {% trans "إعداد النظام" %} (System Setup)
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">{% trans "تأكد من إعداد هذه العناصر قبل إضافة الموظفين:" %}</p>

                    <div class="mb-3">
                        <h6 class="text-success">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            {% trans "البدلات" %}
                        </h6>
                        <div class="d-grid gap-1">
                            <a href="{% url 'hr:allowance_type_list' %}" class="btn btn-outline-success btn-sm" target="_blank">
                                <i class="fas fa-list me-1"></i>
                                {% trans "عرض الأنواع" %}
                            </a>
                            <a href="{% url 'hr:allowance_type_create' %}" class="btn btn-success btn-sm" target="_blank">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "إضافة نوع جديد" %}
                            </a>
                        </div>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-info">
                            <i class="fas fa-shield-alt me-1"></i>
                            {% trans "التأمينات" %}
                        </h6>
                        <div class="d-grid gap-1">
                            <a href="{% url 'hr:insurance_type_list' %}" class="btn btn-outline-info btn-sm" target="_blank">
                                <i class="fas fa-list me-1"></i>
                                {% trans "عرض الأنواع" %}
                            </a>
                            <a href="{% url 'hr:insurance_type_create' %}" class="btn btn-info btn-sm" target="_blank">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "إضافة نوع جديد" %}
                            </a>
                        </div>
                    </div>

                    <div class="alert alert-warning alert-sm">
                        <small>
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            {% trans "الروابط تفتح في نوافذ جديدة" %}
                        </small>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "Help" %}
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "Fill all required fields marked with *" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "Employee ID will be auto-generated if left empty" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "Photo should be in JPG, PNG format (max 2MB)" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "National ID must be unique" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-primary me-1"></i>
                                {% trans "Assign a work shift to define working hours" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-calendar-check text-info me-1"></i>
                                {% trans "Leave management available after employee creation" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-money-bill-wave text-success me-1"></i>
                                {% trans "Allowances and insurance can be added after saving" %}
                            </li>
                            <li>
                                <i class="fas fa-calculator text-warning me-1"></i>
                                {% trans "Salary calculations include basic + allowances - deductions" %}
                            </li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Photo preview
    document.getElementById('id_photo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('photo-preview');
                preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="rounded-circle" width="150" height="150" style="object-fit: cover;">`;
            };
            reader.readAsDataURL(file);
        }
    });

    // Auto-generate employee ID if empty
    document.getElementById('id_first_name').addEventListener('blur', generateEmployeeId);
    document.getElementById('id_last_name').addEventListener('blur', generateEmployeeId);

    function generateEmployeeId() {
        const employeeIdField = document.getElementById('id_employee_id');
        if (!employeeIdField.value) {
            const firstName = document.getElementById('id_first_name').value;
            const lastName = document.getElementById('id_last_name').value;

            if (firstName && lastName) {
                const timestamp = Date.now().toString().slice(-4);
                const initials = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                employeeIdField.value = `EMP${initials}${timestamp}`;
            }
        }
    }

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = document.querySelectorAll('input[required], select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please fill all required fields" %}');
        }
    });

    // Remove validation classes on input
    document.querySelectorAll('input, select, textarea').forEach(field => {
        field.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });
</script>
{% endblock %}
