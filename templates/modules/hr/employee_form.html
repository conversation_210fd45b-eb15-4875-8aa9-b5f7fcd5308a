{% extends 'modules/hr/base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}
{% load hr_tags %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="h3 mb-0">{{ title }}</h1>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<form method="post" enctype="multipart/form-data">
    {% csrf_token %}

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                                <i class="fas fa-user me-1"></i>{% trans "Personal Information" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#employment" role="tab">
                                <i class="fas fa-briefcase me-1"></i>{% trans "Employment Details" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#contact" role="tab">
                                <i class="fas fa-phone me-1"></i>{% trans "Contact Information" %}
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#additional" role="tab">
                                <i class="fas fa-info-circle me-1"></i>{% trans "Additional Info" %}
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <!-- Personal Information Tab -->
                        <div class="tab-pane fade show active" id="personal" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.first_name|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.last_name|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.arabic_name|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.national_id|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.passport_number|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    {{ form.birth_date|as_crispy_field }}
                                </div>
                                <div class="col-md-4">
                                    {{ form.gender|as_crispy_field }}
                                </div>
                                <div class="col-md-4">
                                    {{ form.marital_status|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <!-- Employment Details Tab -->
                        <div class="tab-pane fade" id="employment" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.employee_id|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.hire_date|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.department|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.position|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.direct_manager|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.basic_salary|as_crispy_field }}
                                </div>
                            </div>

                            <!-- Work Shift Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-clock me-2"></i>
                                            {% trans "شفت العمل" %} (Work Shift)
                                        </h6>
                                        <p class="mb-2">{% trans "Select the work shift for this employee. This determines working hours, break times, and working days." %}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    {{ form.shift|as_crispy_field }}
                                    <small class="form-text text-muted">
                                        {% trans "Choose the appropriate work shift for this employee" %}
                                    </small>
                                </div>
                                <div class="col-md-4">
                                    <label class="form-label">{% trans "Quick Actions" %}</label>
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'hr:shift_create' %}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-plus me-1"></i>
                                            {% trans "إنشاء شفت جديد" %}
                                        </a>
                                        <a href="{% url 'hr:shift_list' %}" target="_blank" class="btn btn-sm btn-outline-secondary">
                                            <i class="fas fa-list me-1"></i>
                                            {% trans "عرض جميع الشفتات" %}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% if form.user %}
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.user|as_crispy_field }}
                                    <small class="form-text text-muted">
                                        {% trans "Link this employee to a user account for system access" %}
                                    </small>
                                </div>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Contact Information Tab -->
                        <div class="tab-pane fade" id="contact" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.mobile|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.phone|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.email|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.address|as_crispy_field }}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    {{ form.emergency_contact_name|as_crispy_field }}
                                </div>
                                <div class="col-md-6">
                                    {{ form.emergency_contact_phone|as_crispy_field }}
                                </div>
                            </div>
                        </div>

                        <!-- Additional Information Tab -->
                        <div class="tab-pane fade" id="additional" role="tabpanel">
                            <div class="row">
                                <div class="col-md-12">
                                    {{ form.notes|as_crispy_field }}
                                </div>
                            </div>

                            <!-- Allowances & Insurance Management -->
                            {% if form.instance.pk %}
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-money-bill-wave me-2"></i>
                                            {% trans "إدارة البدلات" %} (Allowances)
                                        </h6>
                                        <p class="mb-2">{% trans "Manage employee allowances and benefits." %}</p>
                                        <div class="d-grid gap-2">
                                            <a href="#" class="btn btn-sm btn-success disabled">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "إضافة بدل" %} - Coming Soon
                                            </a>
                                            <a href="{% url 'hr:employee_detail' form.instance.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>
                                                {% trans "عرض البدلات" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-shield-alt me-2"></i>
                                            {% trans "إدارة التأمينات" %} (Insurance)
                                        </h6>
                                        <p class="mb-2">{% trans "Manage employee insurance and deductions." %}</p>
                                        <div class="d-grid gap-2">
                                            <a href="#" class="btn btn-sm btn-info disabled">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "إضافة تأمين" %} - Coming Soon
                                            </a>
                                            <a href="{% url 'hr:employee_detail' form.instance.pk %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-eye me-1"></i>
                                                {% trans "عرض التأمينات" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Leave Management Info -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="alert alert-success">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-calendar-check me-2"></i>
                                            {% trans "إدارة الإجازات" %} (Leave Management)
                                        </h6>
                                        <p class="mb-2">{% trans "After saving the employee, you can manage their leave requests and view leave balance." %}</p>
                                        <div class="d-flex gap-2">
                                            <a href="{% url 'hr:leave_request_create' %}?employee={{ form.instance.pk }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "طلب إجازة جديد" %}
                                            </a>
                                            <a href="{% url 'hr:employee_detail' form.instance.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>
                                                {% trans "عرض تفاصيل الموظف" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% else %}
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h6 class="alert-heading">
                                            <i class="fas fa-info-circle me-2"></i>
                                            {% trans "ما يمكن إدارته بعد الإنشاء" %} (Available After Creation)
                                        </h6>
                                        <p class="mb-0">{% trans "After creating this employee, you will be able to:" %}</p>
                                        <ul class="mb-0 mt-2">
                                            <li><i class="fas fa-money-bill-wave text-success me-1"></i>{% trans "إضافة وإدارة البدلات" %} (Add and manage allowances)</li>
                                            <li><i class="fas fa-shield-alt text-info me-1"></i>{% trans "إعداد التأمينات والخصومات" %} (Setup insurance and deductions)</li>
                                            <li><i class="fas fa-calendar-check text-primary me-1"></i>{% trans "إنشاء طلبات الإجازات" %} (Create leave requests)</li>
                                            <li><i class="fas fa-clock text-warning me-1"></i>{% trans "تتبع الحضور وساعات العمل" %} (Track attendance and working hours)</li>
                                            <li><i class="fas fa-gavel text-danger me-1"></i>{% trans "تطبيق الإجراءات التأديبية" %} (Apply disciplinary actions)</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Photo and Actions -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">{% trans "Employee Photo" %}</h6>
                </div>
                <div class="card-body text-center">
                    {% if form.instance.photo %}
                    <img src="{{ form.instance.photo.url }}" alt="{% trans 'Employee Photo' %}"
                         class="rounded-circle mb-3" width="150" height="150" style="object-fit: cover;" id="photo-preview">
                    {% else %}
                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                         style="width: 150px; height: 150px;" id="photo-preview">
                        <i class="fas fa-user fa-4x text-white"></i>
                    </div>
                    {% endif %}

                    {{ form.photo|as_crispy_field }}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            {% if form.instance.pk %}
                                {% trans "Update Employee" %}
                            {% else %}
                                {% trans "Create Employee" %}
                            {% endif %}
                        </button>
                        <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            {% trans "Cancel" %}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Help Card -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        {% trans "Help" %}
                    </h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "Fill all required fields marked with *" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "Employee ID will be auto-generated if left empty" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "Photo should be in JPG, PNG format (max 2MB)" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-1"></i>
                                {% trans "National ID must be unique" %}
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-clock text-primary me-1"></i>
                                {% trans "Assign a work shift to define working hours" %}
                            </li>
                            <li>
                                <i class="fas fa-calendar-check text-info me-1"></i>
                                {% trans "Leave management available after employee creation" %}
                            </li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
    // Photo preview
    document.getElementById('id_photo').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const preview = document.getElementById('photo-preview');
                preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="rounded-circle" width="150" height="150" style="object-fit: cover;">`;
            };
            reader.readAsDataURL(file);
        }
    });

    // Auto-generate employee ID if empty
    document.getElementById('id_first_name').addEventListener('blur', generateEmployeeId);
    document.getElementById('id_last_name').addEventListener('blur', generateEmployeeId);

    function generateEmployeeId() {
        const employeeIdField = document.getElementById('id_employee_id');
        if (!employeeIdField.value) {
            const firstName = document.getElementById('id_first_name').value;
            const lastName = document.getElementById('id_last_name').value;

            if (firstName && lastName) {
                const timestamp = Date.now().toString().slice(-4);
                const initials = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                employeeIdField.value = `EMP${initials}${timestamp}`;
            }
        }
    }

    // Form validation
    document.querySelector('form').addEventListener('submit', function(e) {
        const requiredFields = document.querySelectorAll('input[required], select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('{% trans "Please fill all required fields" %}');
        }
    });

    // Remove validation classes on input
    document.querySelectorAll('input, select, textarea').forEach(field => {
        field.addEventListener('input', function() {
            this.classList.remove('is-invalid');
        });
    });
</script>
{% endblock %}
