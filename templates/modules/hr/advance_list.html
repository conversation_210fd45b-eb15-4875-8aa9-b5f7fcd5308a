{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Advance List" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-hand-holding-usd me-2"></i>
        {% trans "Employee Advances" %}
    </h1>
    <a href="{% url 'hr:advance_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        {% trans "Request Advance" %}
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<!-- Smart Alerts for Advances -->
{% if advances %}
    {% with pending_count=advances|length %}
    {% if pending_count > 5 %}
    <div class="alert alert-warning alert-dismissible fade show">
        <div class="d-flex">
            <div class="flex-shrink-0 me-3">
                <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
            </div>
            <div>
                <h6 class="alert-heading">{% trans "High Volume of Advance Requests" %}</h6>
                <p class="mb-0">
                    {% trans "You have" %} <strong>{{ pending_count }}</strong> {% trans "advance requests." %}
                    {% trans "Consider reviewing approval policies or processing times to maintain employee satisfaction." %}
                </p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}
    {% endwith %}
{% else %}
<div class="alert alert-info alert-dismissible fade show">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-info-circle fa-lg text-info"></i>
        </div>
        <div>
            <h6 class="alert-heading">{% trans "No Advance Requests" %}</h6>
            <p class="mb-2">
                {% trans "There are currently no advance requests in the system." %}
                {% trans "This could indicate good financial planning by employees or low awareness of the advance system." %}
            </p>
            <small class="text-muted">
                <i class="fas fa-lightbulb me-1"></i>
                {% trans "Tip: Consider communicating advance policies to employees who might benefit from this service." %}
            </small>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
</div>
{% endif %}

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">{% trans "Employee" %}</label>
                <select name="employee" class="form-select">
                    <option value="">{% trans "All Employees" %}</option>
                    {% for employee in employees %}
                    <option value="{{ employee.id }}" {% if employee_filter == employee.id|stringformat:"s" %}selected{% endif %}>
                        {{ employee.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">{% trans "Status" %}</label>
                <select name="status" class="form-select">
                    <option value="">{% trans "All Statuses" %}</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>{% trans "Pending" %}</option>
                    <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>{% trans "Approved" %}</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>{% trans "Rejected" %}</option>
                    <option value="paid" {% if status_filter == 'paid' %}selected{% endif %}>{% trans "Paid" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">{% trans "Type" %}</label>
                <select name="advance_type" class="form-select">
                    <option value="">{% trans "All Types" %}</option>
                    <option value="salary" {% if type_filter == 'salary' %}selected{% endif %}>{% trans "Salary Advance" %}</option>
                    <option value="emergency" {% if type_filter == 'emergency' %}selected{% endif %}>{% trans "Emergency" %}</option>
                    <option value="travel" {% if type_filter == 'travel' %}selected{% endif %}>{% trans "Travel" %}</option>
                    <option value="other" {% if type_filter == 'other' %}selected{% endif %}>{% trans "Other" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-filter me-2"></i>
                        {% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Advances List -->
<div class="card">
    <div class="card-body">
        {% if advances %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Employee" %}</th>
                        <th>{% trans "Type" %}</th>
                        <th>{% trans "Amount" %}</th>
                        <th>{% trans "Installments" %}</th>
                        <th>{% trans "Required Date" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for advance in advances %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if advance.employee.photo %}
                                <img src="{{ advance.employee.photo.url }}" class="employee-avatar me-2" alt="{{ advance.employee.full_name }}">
                                {% else %}
                                <div class="employee-avatar bg-secondary d-flex align-items-center justify-content-center me-2">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <strong>{{ advance.employee.full_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ advance.employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ advance.get_advance_type_display }}
                            </span>
                        </td>
                        <td>
                            <strong>{{ advance.amount|floatformat:2 }}</strong>
                        </td>
                        <td>
                            {% if advance.installments > 1 %}
                                {{ advance.installments }} {% trans "installments" %}
                            {% else %}
                                {% trans "Single payment" %}
                            {% endif %}
                        </td>
                        <td>{{ advance.required_date|date:"d/m/Y" }}</td>
                        <td>
                            {% if advance.status == 'pending' %}
                                <span class="badge bg-warning">{% trans "Pending" %}</span>
                            {% elif advance.status == 'approved' %}
                                <span class="badge bg-success">{% trans "Approved" %}</span>
                            {% elif advance.status == 'rejected' %}
                                <span class="badge bg-danger">{% trans "Rejected" %}</span>
                            {% elif advance.status == 'paid' %}
                                <span class="badge bg-primary">{% trans "Paid" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'hr:advance_detail' advance.id %}" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if advance.status == 'pending' %}
                                <a href="{% url 'hr:advance_edit' advance.id %}" class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="post" action="{% url 'hr:advance_approve' advance.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-success" title="{% trans 'Approve' %}" onclick="return confirm('{% trans "Are you sure you want to approve this advance?" %}')">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <form method="post" action="{% url 'hr:advance_reject' advance.id %}" class="d-inline">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-outline-danger" title="{% trans 'Reject' %}" onclick="return confirm('{% trans "Are you sure you want to reject this advance?" %}')">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-hand-holding-usd fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No advance requests found" %}</h5>
            <p class="text-muted">{% trans "Create your first advance request to get started." %}</p>
            <a href="{% url 'hr:advance_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create Advance Request" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
