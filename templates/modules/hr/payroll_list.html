{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Payroll List" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-money-bill-wave me-2"></i>
        {% trans "Payroll List" %}
    </h1>
    <div class="btn-group">
        <a href="{% url 'hr:payroll_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            {% trans "Create Payroll" %}
        </a>
        <a href="{% url 'hr:payroll_generate' %}" class="btn btn-success">
            <i class="fas fa-calculator me-1"></i>
            {% trans "Generate Payroll" %}
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="month" class="form-label">{% trans "Month" %}</label>
                <select class="form-select" id="month" name="month">
                    <option value="">{% trans "All Months" %}</option>
                    {% for i in "123456789012"|make_list %}
                    <option value="{{ forloop.counter }}" {% if forloop.counter|stringformat:"s" == selected_month %}selected{% endif %}>
                        {% if forloop.counter == 1 %}{% trans "January" %}
                        {% elif forloop.counter == 2 %}{% trans "February" %}
                        {% elif forloop.counter == 3 %}{% trans "March" %}
                        {% elif forloop.counter == 4 %}{% trans "April" %}
                        {% elif forloop.counter == 5 %}{% trans "May" %}
                        {% elif forloop.counter == 6 %}{% trans "June" %}
                        {% elif forloop.counter == 7 %}{% trans "July" %}
                        {% elif forloop.counter == 8 %}{% trans "August" %}
                        {% elif forloop.counter == 9 %}{% trans "September" %}
                        {% elif forloop.counter == 10 %}{% trans "October" %}
                        {% elif forloop.counter == 11 %}{% trans "November" %}
                        {% elif forloop.counter == 12 %}{% trans "December" %}
                        {% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="year" class="form-label">{% trans "Year" %}</label>
                <select class="form-select" id="year" name="year">
                    <option value="">{% trans "All Years" %}</option>
                    <option value="2024" {% if selected_year == "2024" %}selected{% endif %}>2024</option>
                    <option value="2023" {% if selected_year == "2023" %}selected{% endif %}>2023</option>
                    <option value="2022" {% if selected_year == "2022" %}selected{% endif %}>2022</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        {% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Payroll List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            {% trans "Payroll Records" %}
            {% if page_obj %}
            <span class="badge bg-secondary ms-2">{{ page_obj.paginator.count }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body p-0">
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No payroll records found" %}</h5>
            <p class="text-muted">{% trans "Start by generating payroll for your employees" %}</p>
            <div class="btn-group">
                <a href="{% url 'hr:payroll_generate' %}" class="btn btn-primary">
                    <i class="fas fa-calculator me-1"></i>
                    {% trans "Generate Payroll" %}
                </a>
                <a href="{% url 'hr:payroll_create' %}" class="btn btn-outline-primary">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "Manual Entry" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
