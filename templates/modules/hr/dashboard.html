{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}الموارد البشرية{% else %}{% trans "Human Resources" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الموارد البشرية{% else %}{% trans "Human Resources" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-user-tie{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إدارة الموارد البشرية{% else %}{% trans "Human Resources Management" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة أهم أصول مؤسستك - موظفيك. تتبع معلومات الموظفين، إدارة الحضور، معالجة الرواتب، والمزيد.{% else %}{% trans "Manage your organization's most valuable asset - your people. Track employee information, manage attendance, handle payroll, and more." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-primary{% endblock %}

{% block module_alert %}
<div class="alert alert-info alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-info-circle fa-2x text-info"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to HR Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your human resources operations. Use the navigation menu to access different HR functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'hr:dashboard' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'hr:advance_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-hand-holding-usd me-2 text-warning"></i> {% trans "السلف" %} (Advances)
</a>
<a href="{% url 'hr:custody_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-box me-2 text-info"></i> {% trans "العهد" %} (Custodies)
</a>
<a href="{% url 'hr:employee_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% trans "Employees" %}
</a>
<a href="{% url 'hr:department_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-sitemap me-2"></i> {% trans "Departments" %}
</a>
<a href="{% url 'hr:contract_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-file-contract me-2"></i> {% trans "Contracts" %}
</a>
<a href="{% url 'hr:payroll_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-money-bill-wave me-2"></i> {% trans "Payroll" %}
</a>
<a href="{% url 'hr:attendance_report' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-check me-2"></i> {% trans "Attendance" %}
</a>
<a href="{% url 'hr:commission_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-percentage me-2"></i> {% trans "Commissions" %}
</a>
<a href="{% url 'hr:sales_team_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-users-cog me-2"></i> {% trans "Sales Teams" %}
</a>
<a href="{% url 'hr:reports_dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'hr:advance_create' %}" class="btn btn-warning">
        <i class="fas fa-hand-holding-usd me-2"></i> {% trans "طلب سلفة جديدة" %}
    </a>
    <a href="{% url 'hr:custody_create' %}" class="btn btn-info">
        <i class="fas fa-box me-2"></i> {% trans "إنشاء عهدة جديدة" %}
    </a>
    <a href="{% url 'hr:employee_create' %}" class="btn btn-primary">
        <i class="fas fa-user-plus me-2"></i> {% trans "Add Employee" %}
    </a>
    <a href="{% url 'hr:attendance_entry' %}" class="btn btn-outline-primary">
        <i class="fas fa-calendar-plus me-2"></i> {% trans "Record Attendance" %}
    </a>
    <a href="{% url 'hr:commission_create' %}" class="btn btn-outline-primary">
        <i class="fas fa-percentage me-2"></i> {% trans "Add Commission" %}
    </a>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Pending Advances" %}</h6>
                    <h3 class="card-title mb-0">{{ pending_advances|default:5 }}</h3>
                    <small class="text-muted">
                        <a href="{% url 'hr:advance_list' %}" class="text-decoration-none">
                            {% trans "View All" %} <i class="fas fa-arrow-right"></i>
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Active Custodies" %}</h6>
                    <h3 class="card-title mb-0">{{ active_custodies|default:14 }}</h3>
                    <small class="text-muted">
                        <a href="{% url 'hr:custody_list' %}" class="text-decoration-none">
                            {% trans "View All" %} <i class="fas fa-arrow-right"></i>
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-primary h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-primary text-white rounded p-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Total Employees" %}</h6>
                    <h3 class="card-title mb-0">{{ total_employees|default:20 }}</h3>
                    <small class="text-muted">
                        <a href="{% url 'hr:employee_list' %}" class="text-decoration-none">
                            {% trans "View All" %} <i class="fas fa-arrow-right"></i>
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Pending Commissions" %}</h6>
                    <h3 class="card-title mb-0">{{ pending_commissions|default:8 }}</h3>
                    <small class="text-muted">
                        <a href="{% url 'hr:commission_list' %}" class="text-decoration-none">
                            {% trans "View All" %} <i class="fas fa-arrow-right"></i>
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Employee Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>

        <h5 class="mt-4 mb-3">{% trans "Recent Employees" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "ID" %}</th>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Department" %}</th>
                        <th>{% trans "Position" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center">{% trans "No employees found" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Upcoming Birthdays" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No upcoming birthdays" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Upcoming Leaves" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No upcoming leaves" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('HR Module loaded');
    });
</script>
{% endblock %}
