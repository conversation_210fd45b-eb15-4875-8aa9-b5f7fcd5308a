{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Terminate Employee" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:employee_detail' employee.id %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="h3 mb-0 text-danger">
            <i class="fas fa-user-times me-2"></i>
            {% trans "Terminate Employee" %}
        </h1>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% trans "Confirm Employee Termination" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    {% if employee.photo %}
                    <img src="{{ employee.photo.url }}" alt="{{ employee.full_name }}" 
                         class="rounded-circle mb-3" width="100" height="100" style="object-fit: cover;">
                    {% else %}
                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                         style="width: 100px; height: 100px;">
                        <i class="fas fa-user fa-3x text-white"></i>
                    </div>
                    {% endif %}
                    
                    <h5>{{ employee.full_name }}</h5>
                    <p class="text-muted">{{ employee.employee_id }} - {{ employee.position.title }}</p>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>{% trans "Warning!" %}</strong>
                    {% trans "This action will terminate the employee's contract and change their status to 'Terminated'. This action cannot be undone." %}
                </div>
                
                <div class="mb-3">
                    <h6>{% trans "Employee Information:" %}</h6>
                    <ul class="list-unstyled">
                        <li><strong>{% trans "Department" %}:</strong> {{ employee.department.name }}</li>
                        <li><strong>{% trans "Hire Date" %}:</strong> {{ employee.hire_date|date:"Y-m-d" }}</li>
                        <li><strong>{% trans "Current Status" %}:</strong> 
                            <span class="badge bg-success">{{ employee.get_status_display }}</span>
                        </li>
                    </ul>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="termination_reason" class="form-label">{% trans "Termination Reason" %}</label>
                        <textarea class="form-control" id="termination_reason" name="termination_reason" 
                                  rows="3" placeholder="{% trans 'Please provide a reason for termination...' %}" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="termination_date" class="form-label">{% trans "Termination Date" %}</label>
                        <input type="date" class="form-control" id="termination_date" name="termination_date" 
                               value="{{ today|date:'Y-m-d' }}" required>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="confirm_termination" required>
                        <label class="form-check-label" for="confirm_termination">
                            {% trans "I confirm that I want to terminate this employee" %}
                        </label>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'hr:employee_detail' employee.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-user-times me-1"></i>
                            {% trans "Terminate Employee" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Confirm termination
    document.querySelector('form').addEventListener('submit', function(e) {
        const confirmed = document.getElementById('confirm_termination').checked;
        const reason = document.getElementById('termination_reason').value.trim();
        
        if (!confirmed) {
            e.preventDefault();
            alert('{% trans "Please confirm that you want to terminate this employee" %}');
            return;
        }
        
        if (!reason) {
            e.preventDefault();
            alert('{% trans "Please provide a reason for termination" %}');
            return;
        }
        
        if (!confirm('{% trans "Are you absolutely sure you want to terminate this employee? This action cannot be undone." %}')) {
            e.preventDefault();
        }
    });
</script>
{% endblock %}
