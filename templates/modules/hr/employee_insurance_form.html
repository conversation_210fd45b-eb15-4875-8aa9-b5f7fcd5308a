{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2 text-info"></i>
                    {{ title }}
                </h5>
                <div class="text-muted">
                    {% trans "الموظف" %} (Employee): <strong>{{ employee.get_full_name }}</strong> ({{ employee.employee_id }})
                </div>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="insurance_type" class="form-label">
                                    {% trans "نوع التأمين" %} (Insurance Type) <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="insurance_type" name="insurance_type" required>
                                    <option value="">{% trans "اختر نوع التأمين" %} (Select Type)</option>
                                    {% for insurance_type in insurance_types %}
                                    <option value="{{ insurance_type.id }}" 
                                            data-employee-percentage="{{ insurance_type.employee_percentage }}"
                                            data-company-percentage="{{ insurance_type.company_percentage }}"
                                            data-max-salary-limit="{{ insurance_type.max_salary_limit|default:'' }}">
                                        {{ insurance_type.name }} ({{ insurance_type.code }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="start_date" class="form-label">
                                    {% trans "تاريخ البداية" %} (Start Date) <span class="text-danger">*</span>
                                </label>
                                <input type="date" class="form-control" id="start_date" name="start_date" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employee_percentage" class="form-label">
                                    {% trans "نسبة الموظف %" %} (Employee Percentage %)
                                </label>
                                <input type="number" class="form-control" id="employee_percentage" name="employee_percentage" 
                                       step="0.01" min="0" max="100">
                                <div class="form-text">{% trans "اتركه فارغاً لاستخدام النسبة الافتراضية" %} (Leave empty to use default percentage)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_percentage" class="form-label">
                                    {% trans "نسبة الشركة %" %} (Company Percentage %)
                                </label>
                                <input type="number" class="form-control" id="company_percentage" name="company_percentage" 
                                       step="0.01" min="0" max="100">
                                <div class="form-text">{% trans "اتركه فارغاً لاستخدام النسبة الافتراضية" %} (Leave empty to use default percentage)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="end_date" class="form-label">
                                    {% trans "تاريخ النهاية" %} (End Date)
                                </label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                                <div class="form-text">{% trans "اتركه فارغاً للتأمين الدائم" %} (Leave empty for permanent insurance)</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            {% trans "ملاحظات" %} (Notes)
                        </label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Insurance Calculation Preview -->
                    <div class="card bg-light mb-3" id="insurance_preview" style="display: none;">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-calculator me-2"></i>
                                {% trans "معاينة حساب التأمين" %} (Insurance Calculation Preview)
                            </h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="text-muted">{% trans "الراتب الأساسي" %} (Basic Salary)</div>
                                    <div class="h6 text-primary">{{ employee.basic_salary|floatformat:2 }}</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-muted">{% trans "الراتب الخاضع للتأمين" %} (Insurable Salary)</div>
                                    <div class="h6 text-info" id="insurable_salary">{{ employee.basic_salary|floatformat:2 }}</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-muted">{% trans "خصم الموظف" %} (Employee Deduction)</div>
                                    <div class="h6 text-danger" id="employee_deduction">0.00</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-muted">{% trans "مساهمة الشركة" %} (Company Contribution)</div>
                                    <div class="h6 text-success" id="company_contribution">0.00</div>
                                </div>
                            </div>
                            <hr>
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="text-muted">{% trans "الراتب بعد الخصم" %} (Net Salary)</div>
                                    <div class="h5 text-primary" id="net_salary">{{ employee.basic_salary|floatformat:2 }}</div>
                                </div>
                                <div class="col-6">
                                    <div class="text-muted">{% trans "إجمالي التأمين" %} (Total Insurance)</div>
                                    <div class="h5 text-info" id="total_insurance">0.00</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:employee_detail' employee.id %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            {% trans "رجوع" %} (Back)
                        </a>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-1"></i>
                            {% trans "حفظ التأمين" %} (Save Insurance)
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const insuranceTypeSelect = document.getElementById('insurance_type');
    const employeePercentageInput = document.getElementById('employee_percentage');
    const companyPercentageInput = document.getElementById('company_percentage');
    const insurancePreview = document.getElementById('insurance_preview');
    
    const insurableSalaryDisplay = document.getElementById('insurable_salary');
    const employeeDeductionDisplay = document.getElementById('employee_deduction');
    const companyContributionDisplay = document.getElementById('company_contribution');
    const netSalaryDisplay = document.getElementById('net_salary');
    const totalInsuranceDisplay = document.getElementById('total_insurance');
    
    const basicSalary = {{ employee.basic_salary }};
    
    insuranceTypeSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const employeePercentage = selectedOption.dataset.employeePercentage;
        const companyPercentage = selectedOption.dataset.companyPercentage;
        
        if (employeePercentage) {
            employeePercentageInput.value = employeePercentage;
        }
        if (companyPercentage) {
            companyPercentageInput.value = companyPercentage;
        }
        
        updateInsurancePreview();
        insurancePreview.style.display = this.value ? 'block' : 'none';
    });
    
    employeePercentageInput.addEventListener('input', updateInsurancePreview);
    companyPercentageInput.addEventListener('input', updateInsurancePreview);
    
    function updateInsurancePreview() {
        const selectedOption = insuranceTypeSelect.options[insuranceTypeSelect.selectedIndex];
        const maxSalaryLimit = parseFloat(selectedOption.dataset.maxSalaryLimit) || 0;
        
        let insurableSalary = basicSalary;
        if (maxSalaryLimit > 0 && basicSalary > maxSalaryLimit) {
            insurableSalary = maxSalaryLimit;
        }
        
        const employeePercentage = parseFloat(employeePercentageInput.value) || 0;
        const companyPercentage = parseFloat(companyPercentageInput.value) || 0;
        
        const employeeDeduction = (insurableSalary * employeePercentage) / 100;
        const companyContribution = (insurableSalary * companyPercentage) / 100;
        const totalInsurance = employeeDeduction + companyContribution;
        const netSalary = basicSalary - employeeDeduction;
        
        insurableSalaryDisplay.textContent = insurableSalary.toFixed(2);
        employeeDeductionDisplay.textContent = employeeDeduction.toFixed(2);
        companyContributionDisplay.textContent = companyContribution.toFixed(2);
        totalInsuranceDisplay.textContent = totalInsurance.toFixed(2);
        netSalaryDisplay.textContent = netSalary.toFixed(2);
    }
});
</script>
{% endblock %}
