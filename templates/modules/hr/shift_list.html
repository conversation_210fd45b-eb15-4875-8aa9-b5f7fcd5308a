{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Work Shifts" %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% trans "Work Shifts" %}</h2>
    <a href="{% url 'hr:shift_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        {% trans "Create New Shift" %}
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="shift_type" class="form-label">{% trans "Shift Type" %}</label>
                <select name="shift_type" id="shift_type" class="form-select">
                    <option value="">{% trans "All Types" %}</option>
                    <option value="morning" {% if shift_type_filter == 'morning' %}selected{% endif %}>{% trans "Morning Shift" %}</option>
                    <option value="evening" {% if shift_type_filter == 'evening' %}selected{% endif %}>{% trans "Evening Shift" %}</option>
                    <option value="night" {% if shift_type_filter == 'night' %}selected{% endif %}>{% trans "Night Shift" %}</option>
                    <option value="rotating" {% if shift_type_filter == 'rotating' %}selected{% endif %}>{% trans "Rotating Shift" %}</option>
                    <option value="flexible" {% if shift_type_filter == 'flexible' %}selected{% endif %}>{% trans "Flexible Shift" %}</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="is_active" class="form-label">{% trans "Status" %}</label>
                <select name="is_active" id="is_active" class="form-select">
                    <option value="">{% trans "All" %}</option>
                    <option value="true" {% if is_active_filter == 'true' %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="false" {% if is_active_filter == 'false' %}selected{% endif %}>{% trans "Inactive" %}</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-filter me-1"></i>
                    {% trans "Filter" %}
                </button>
                <a href="{% url 'hr:shift_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    {% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Shifts List -->
<div class="card">
    <div class="card-body">
        {% if shifts %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Type" %}</th>
                        <th>{% trans "Working Hours" %}</th>
                        <th>{% trans "Break Duration" %}</th>
                        <th>{% trans "Working Days" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for shift in shifts %}
                    <tr>
                        <td>
                            <strong>{{ shift.name }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ shift.get_shift_type_display }}</span>
                        </td>
                        <td>
                            <i class="fas fa-clock me-1"></i>
                            {{ shift.start_time|time:"H:i" }} - {{ shift.end_time|time:"H:i" }}
                        </td>
                        <td>
                            {{ shift.break_duration }}
                        </td>
                        <td>
                            <small class="text-muted">
                                {% for day in shift.working_days_list %}
                                    {{ day }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                            </small>
                        </td>
                        <td>
                            {% if shift.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'hr:shift_detail' shift.id %}" class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'hr:shift_edit' shift.id %}" class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'hr:shift_delete' shift.id %}" class="btn btn-outline-danger" title="{% trans 'Delete' %}">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-clock fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No shifts found" %}</h5>
            <p class="text-muted">{% trans "Create your first work shift to get started." %}</p>
            <a href="{% url 'hr:shift_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create First Shift" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
