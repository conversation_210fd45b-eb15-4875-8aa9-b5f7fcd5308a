{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ employee.full_name }} - {% trans "Employee Details" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h1 class="h3 mb-0">{{ employee.full_name }}</h1>
            <small class="text-muted">{{ employee.employee_id }} - {{ employee.position.title }}</small>
        </div>
    </div>
    <div class="btn-group">
        <a href="{% url 'hr:employee_edit' employee.id %}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>
            {% trans "Edit" %}
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split"
                data-bs-toggle="dropdown">
            <span class="visually-hidden">{% trans "Toggle Dropdown" %}</span>
        </button>
        <ul class="dropdown-menu">
            <!-- Leave & Attendance -->
            <li><a class="dropdown-item" href="{% url 'hr:leave_request_create' %}?employee={{ employee.id }}">
                <i class="fas fa-calendar-check me-2 text-success"></i>{% trans "طلب إجازة" %} (Request Leave)
            </a></li>
            <li><a class="dropdown-item" href="{% url 'hr:employee_edit' employee.id %}">
                <i class="fas fa-clock me-2 text-primary"></i>{% trans "تعديل الشفت" %} (Change Shift)
            </a></li>

            <li><hr class="dropdown-divider"></li>

            <!-- Allowances & Insurance -->
            <li><h6 class="dropdown-header">
                <i class="fas fa-money-bill-wave me-1"></i>{% trans "البدلات والتأمينات" %}
            </h6></li>
            <li><a class="dropdown-item" href="{% url 'hr:employee_allowance_create' employee.id %}">
                <i class="fas fa-plus me-2 text-success"></i>{% trans "إضافة بدل" %} (Add Allowance)
            </a></li>
            <li><a class="dropdown-item" href="{% url 'hr:employee_insurance_create' employee.id %}">
                <i class="fas fa-shield-alt me-2 text-info"></i>{% trans "إضافة تأمين" %} (Add Insurance)
            </a></li>
            <li><a class="dropdown-item disabled" href="#">
                <i class="fas fa-plane me-2 text-warning"></i>{% trans "طلب بدل مأمورية" %} (Mission Request) - Coming Soon
            </a></li>

            <li><hr class="dropdown-divider"></li>

            <!-- Financial -->
            <li><h6 class="dropdown-header">
                <i class="fas fa-dollar-sign me-1"></i>{% trans "المالية" %}
            </h6></li>
            <li><a class="dropdown-item" href="{% url 'hr:contract_create' %}?employee={{ employee.id }}">
                <i class="fas fa-file-contract me-2"></i>{% trans "Create Contract" %}
            </a></li>
            <li><a class="dropdown-item" href="{% url 'hr:advance_create' %}?employee={{ employee.id }}">
                <i class="fas fa-money-bill me-2"></i>{% trans "Request Advance" %}
            </a></li>
            <li><a class="dropdown-item" href="{% url 'hr:commission_create' %}?employee={{ employee.id }}">
                <i class="fas fa-percentage me-2"></i>{% trans "Add Commission" %}
            </a></li>

            <li><hr class="dropdown-divider"></li>

            <!-- Disciplinary -->
            <li><h6 class="dropdown-header">
                <i class="fas fa-gavel me-1"></i>{% trans "الإجراءات التأديبية" %}
            </h6></li>
            <li><a class="dropdown-item disabled" href="#">
                <i class="fas fa-exclamation-triangle me-2 text-warning"></i>{% trans "تطبيق جزاء" %} (Apply Penalty) - Coming Soon
            </a></li>

            <li><hr class="dropdown-divider"></li>

            <!-- Danger Zone -->
            <li><a class="dropdown-item text-danger" href="{% url 'hr:employee_delete' employee.id %}">
                <i class="fas fa-user-times me-2"></i>{% trans "Terminate Employee" %}
            </a></li>
        </ul>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<div class="row">
    <!-- Employee Info Card -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-body text-center">
                {% if employee.photo %}
                <img src="{{ employee.photo.url }}" alt="{{ employee.full_name }}"
                     class="rounded-circle mb-3" width="120" height="120" style="object-fit: cover;">
                {% else %}
                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                     style="width: 120px; height: 120px;">
                    <i class="fas fa-user fa-3x text-white"></i>
                </div>
                {% endif %}

                <h5 class="card-title">{{ employee.full_name }}</h5>
                {% if employee.arabic_name %}
                <p class="text-muted">{{ employee.arabic_name }}</p>
                {% endif %}

                <div class="mb-3">
                    {% if employee.status == 'active' %}
                    <span class="badge bg-success fs-6">{% trans "Active" %}</span>
                    {% elif employee.status == 'inactive' %}
                    <span class="badge bg-warning fs-6">{% trans "Inactive" %}</span>
                    {% elif employee.status == 'terminated' %}
                    <span class="badge bg-danger fs-6">{% trans "Terminated" %}</span>
                    {% elif employee.status == 'suspended' %}
                    <span class="badge bg-secondary fs-6">{% trans "Suspended" %}</span>
                    {% endif %}
                </div>

                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="mb-0">{{ employee.years_of_service }}</h6>
                            <small class="text-muted">{% trans "Years" %}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="mb-0">{{ employee.age|default:"-" }}</h6>
                            <small class="text-muted">{% trans "Age" %}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="mb-0">{{ employee.basic_salary|floatformat:0 }}</h6>
                        <small class="text-muted">{% trans "Salary" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Quick Stats" %}</h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-success mb-0">{{ attendance_stats.present_days|default:0 }}</h5>
                            <small class="text-muted">{% trans "Present Days" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-warning mb-0">{{ attendance_stats.late_days|default:0 }}</h5>
                            <small class="text-muted">{% trans "Late Days" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-info mb-0">{{ attendance_stats.total_hours|default:0|floatformat:1 }}</h5>
                            <small class="text-muted">{% trans "Total Hours" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-primary mb-0">{{ attendance_stats.overtime_hours|default:0|floatformat:1 }}</h5>
                            <small class="text-muted">{% trans "Overtime" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Details Tabs -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                            <i class="fas fa-user me-1"></i>{% trans "Personal Info" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#employment" role="tab">
                            <i class="fas fa-briefcase me-1"></i>{% trans "Employment" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#financial" role="tab">
                            <i class="fas fa-money-bill me-1"></i>{% trans "Financial" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#shift-leaves" role="tab">
                            <i class="fas fa-clock me-1"></i>{% trans "شفت وإجازات" %} (Shift & Leaves)
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#allowances-insurance" role="tab">
                            <i class="fas fa-money-bill-wave me-1"></i>{% trans "بدلات وتأمينات" %} (Allowances & Insurance)
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#documents" role="tab">
                            <i class="fas fa-file me-1"></i>{% trans "Documents" %}
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Personal Info Tab -->
                    <div class="tab-pane fade show active" id="personal" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Full Name" %}:</td>
                                        <td>{{ employee.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Arabic Name" %}:</td>
                                        <td>{{ employee.arabic_name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "National ID" %}:</td>
                                        <td>{{ employee.national_id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Passport" %}:</td>
                                        <td>{{ employee.passport_number|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Birth Date" %}:</td>
                                        <td>{{ employee.birth_date|date:"Y-m-d"|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Gender" %}:</td>
                                        <td>{{ employee.get_gender_display }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Mobile" %}:</td>
                                        <td>{{ employee.mobile }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Phone" %}:</td>
                                        <td>{{ employee.phone|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Email" %}:</td>
                                        <td>{{ employee.email|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Marital Status" %}:</td>
                                        <td>{{ employee.get_marital_status_display }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Emergency Contact" %}:</td>
                                        <td>{{ employee.emergency_contact_name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Emergency Phone" %}:</td>
                                        <td>{{ employee.emergency_contact_phone|default:"-" }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        {% if employee.address %}
                        <div class="row">
                            <div class="col-12">
                                <h6>{% trans "Address" %}</h6>
                                <p class="text-muted">{{ employee.address }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Employment Tab -->
                    <div class="tab-pane fade" id="employment" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Employee ID" %}:</td>
                                        <td>{{ employee.employee_id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Department" %}:</td>
                                        <td>{{ employee.department.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Position" %}:</td>
                                        <td>{{ employee.position.title }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Direct Manager" %}:</td>
                                        <td>
                                            {% if employee.direct_manager %}
                                            <a href="{% url 'hr:employee_detail' employee.direct_manager.id %}">
                                                {{ employee.direct_manager.full_name }}
                                            </a>
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Hire Date" %}:</td>
                                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Status" %}:</td>
                                        <td>{{ employee.get_status_display }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Branch" %}:</td>
                                        <td>{{ employee.branch.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Company" %}:</td>
                                        <td>{{ employee.company.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Work Shift" %}:</td>
                                        <td>
                                            {% if employee.shift %}
                                            <a href="{% url 'hr:shift_detail' employee.shift.id %}">
                                                {{ employee.shift.name }}
                                            </a>
                                            <br>
                                            <small class="text-muted">
                                                {{ employee.shift.start_time|time:"H:i" }} - {{ employee.shift.end_time|time:"H:i" }}
                                            </small>
                                            {% else %}
                                            <span class="text-muted">{% trans "No shift assigned" %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Shift & Leaves Tab -->
                    <div class="tab-pane fade" id="shift-leaves" role="tabpanel">
                        <div class="row">
                            <!-- Current Shift Info -->
                            <div class="col-md-6">
                                <h6>{% trans "معلومات الشفت الحالي" %} (Current Shift)</h6>
                                {% if employee.shift %}
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ employee.shift.name }}</h6>
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Type" %}</small>
                                                <div>{{ employee.shift.get_shift_type_display }}</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Working Hours" %}</small>
                                                <div>{{ employee.shift.start_time|time:"H:i" }} - {{ employee.shift.end_time|time:"H:i" }}</div>
                                            </div>
                                        </div>
                                        <div class="row mt-2">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Break Time" %}</small>
                                                <div>
                                                    {% if employee.shift.break_start and employee.shift.break_end %}
                                                    {{ employee.shift.break_start|time:"H:i" }} - {{ employee.shift.break_end|time:"H:i" }}
                                                    {% else %}
                                                    {{ employee.shift.break_duration }}
                                                    {% endif %}
                                                </div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Working Days" %}</small>
                                                <div>
                                                    <small>
                                                        {% for day in employee.shift.working_days_list %}
                                                            {{ day }}{% if not forloop.last %}, {% endif %}
                                                        {% endfor %}
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <a href="{% url 'hr:shift_detail' employee.shift.id %}" class="btn btn-sm btn-outline-primary">
                                                {% trans "View Shift Details" %}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    {% trans "No shift assigned to this employee" %}
                                    <div class="mt-2">
                                        <a href="{% url 'hr:employee_edit' employee.id %}" class="btn btn-sm btn-warning">
                                            {% trans "Assign Shift" %}
                                        </a>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Leave Balance -->
                                <h6 class="mt-4">{% trans "رصيد الإجازات" %} (Leave Balance)</h6>
                                {% if leave_balances %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Leave Type" %}</th>
                                                <th>{% trans "Used" %}</th>
                                                <th>{% trans "Remaining" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for balance in leave_balances %}
                                            <tr>
                                                <td>{{ balance.leave_type.name }}</td>
                                                <td>{{ balance.used_days }}</td>
                                                <td>
                                                    <span class="badge bg-{% if balance.remaining_days > 5 %}success{% elif balance.remaining_days > 0 %}warning{% else %}danger{% endif %}">
                                                        {{ balance.remaining_days }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    {% trans "No leave types configured for this company" %}
                                    <div class="mt-2">
                                        <a href="{% url 'hr:leave_type_create' %}" class="btn btn-sm btn-primary">
                                            {% trans "Create Leave Types" %}
                                        </a>
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            <!-- Recent Leave Requests -->
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">{% trans "طلبات الإجازات الأخيرة" %} (Recent Leave Requests)</h6>
                                    <a href="{% url 'hr:leave_request_create' %}?employee={{ employee.id }}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "New Request" %}
                                    </a>
                                </div>

                                {% if recent_leave_requests %}
                                <div class="list-group">
                                    {% for leave in recent_leave_requests %}
                                    <div class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h6 class="mb-1">{{ leave.leave_type.name }}</h6>
                                                <p class="mb-1">
                                                    <small>
                                                        <i class="fas fa-calendar me-1"></i>
                                                        {{ leave.start_date|date:"d/m/Y" }} - {{ leave.end_date|date:"d/m/Y" }}
                                                        ({{ leave.total_days }} {% trans "days" %})
                                                    </small>
                                                </p>
                                                <small class="text-muted">{{ leave.reason|truncatechars:50 }}</small>
                                            </div>
                                            <div>
                                                {% if leave.status == 'pending' %}
                                                    <span class="badge bg-warning">{% trans "Pending" %}</span>
                                                {% elif leave.status == 'approved' %}
                                                    <span class="badge bg-success">{% trans "Approved" %}</span>
                                                {% elif leave.status == 'rejected' %}
                                                    <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="mt-2">
                                            <a href="{% url 'hr:leave_request_detail' leave.id %}" class="btn btn-sm btn-outline-primary">
                                                {% trans "View" %}
                                            </a>
                                            {% if leave.status == 'pending' %}
                                            <a href="{% url 'hr:leave_request_edit' leave.id %}" class="btn btn-sm btn-outline-warning">
                                                {% trans "Edit" %}
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>

                                <div class="mt-3 text-center">
                                    <a href="{% url 'hr:leave_request_list' %}?employee={{ employee.id }}" class="btn btn-sm btn-outline-secondary">
                                        {% trans "View All Leave Requests" %}
                                    </a>
                                </div>
                                {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">{% trans "No leave requests found" %}</p>
                                    <a href="{% url 'hr:leave_request_create' %}?employee={{ employee.id }}" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "Create First Leave Request" %}
                                    </a>
                                </div>
                                {% endif %}

                                <!-- Current Leave Status -->
                                {% if employee.is_currently_on_leave %}
                                <div class="alert alert-info mt-3">
                                    <h6 class="alert-heading">
                                        <i class="fas fa-calendar-check me-2"></i>
                                        {% trans "Currently on Leave" %}
                                    </h6>
                                    {% if employee.current_leave %}
                                    <p class="mb-0">
                                        <strong>{{ employee.current_leave.leave_type.name }}</strong><br>
                                        {{ employee.current_leave.start_date|date:"d/m/Y" }} - {{ employee.current_leave.end_date|date:"d/m/Y" }}
                                    </p>
                                    {% endif %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Financial Tab -->
                    <div class="tab-pane fade" id="financial" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>{% trans "Salary Information" %}</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Basic Salary" %}:</td>
                                        <td>{{ employee.basic_salary|floatformat:2 }}</td>
                                    </tr>
                                </table>

                                <h6 class="mt-4">{% trans "Recent Commissions" %}</h6>
                                {% if recent_commissions %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Type" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Status" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for commission in recent_commissions %}
                                            <tr>
                                                <td>{{ commission.get_commission_type_display }}</td>
                                                <td>{{ commission.amount|floatformat:2 }}</td>
                                                <td>
                                                    <span class="badge bg-{% if commission.status == 'approved' %}success{% elif commission.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                                        {{ commission.get_status_display }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No commissions found" %}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6>{% trans "Active Advances" %}</h6>
                                {% if active_advances %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Type" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Status" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for advance in active_advances %}
                                            <tr>
                                                <td>{{ advance.get_advance_type_display }}</td>
                                                <td>{{ advance.amount|floatformat:2 }}</td>
                                                <td>
                                                    <span class="badge bg-{% if advance.status == 'paid' %}success{% elif advance.status == 'approved' %}info{% else %}warning{% endif %}">
                                                        {{ advance.get_status_display }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No active advances" %}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Allowances & Insurance Tab -->
                    <div class="tab-pane fade" id="allowances-insurance" role="tabpanel">
                        <div class="row">
                            <!-- Employee Allowances -->
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">
                                        <i class="fas fa-money-bill-wave text-success me-2"></i>
                                        {% trans "البدلات النشطة" %} (Active Allowances)
                                    </h6>
                                    <a href="{% url 'hr:employee_allowance_create' employee.id %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "إضافة بدل" %}
                                    </a>
                                </div>

                                {% if employee.get_active_allowances %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Allowance Type" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Start Date" %}</th>
                                                <th>{% trans "Actions" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for allowance in employee.get_active_allowances %}
                                            <tr>
                                                <td>
                                                    <strong>{{ allowance.allowance_type.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ allowance.allowance_type.code }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        {{ allowance.calculated_amount|floatformat:2 }}
                                                    </span>
                                                    {% if allowance.allowance_type.calculation_type == 'percentage' %}
                                                    <br>
                                                    <small class="text-muted">{{ allowance.percentage }}%</small>
                                                    {% endif %}
                                                </td>
                                                <td>{{ allowance.start_date|date:"d/m/Y" }}</td>
                                                <td>
                                                    <a href="#" class="btn btn-sm btn-outline-primary disabled">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="#" class="btn btn-sm btn-outline-danger disabled">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-success">
                                                <th>{% trans "Total Allowances" %}</th>
                                                <th>
                                                    <strong>{{ employee.get_total_allowances|floatformat:2 }}</strong>
                                                </th>
                                                <th colspan="2"></th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-money-bill-wave fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">{% trans "No allowances assigned" %}</p>
                                    <a href="{% url 'hr:employee_allowance_create' employee.id %}" class="btn btn-success">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "Add First Allowance" %}
                                    </a>
                                </div>
                                {% endif %}

                                <!-- Salary Summary -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-calculator me-2"></i>
                                            {% trans "ملخص الراتب" %} (Salary Summary)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-4">
                                                <h6 class="text-primary mb-0">{{ employee.basic_salary|floatformat:2 }}</h6>
                                                <small class="text-muted">{% trans "Basic Salary" %}</small>
                                            </div>
                                            <div class="col-4">
                                                <h6 class="text-success mb-0">{{ employee.get_total_allowances|floatformat:2 }}</h6>
                                                <small class="text-muted">{% trans "Total Allowances" %}</small>
                                            </div>
                                            <div class="col-4">
                                                <h6 class="text-info mb-0">{{ employee.get_gross_salary|floatformat:2 }}</h6>
                                                <small class="text-muted">{% trans "Gross Salary" %}</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Employee Insurance -->
                            <div class="col-md-6">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0">
                                        <i class="fas fa-shield-alt text-info me-2"></i>
                                        {% trans "التأمينات النشطة" %} (Active Insurance)
                                    </h6>
                                    <a href="{% url 'hr:employee_insurance_create' employee.id %}" class="btn btn-sm btn-info">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "إضافة تأمين" %}
                                    </a>
                                </div>

                                {% if employee.get_active_insurances %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Insurance Type" %}</th>
                                                <th>{% trans "Employee %" %}</th>
                                                <th>{% trans "Company %" %}</th>
                                                <th>{% trans "Deduction" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for insurance in employee.get_active_insurances %}
                                            {% with calculation=insurance.calculate_deduction %}
                                            <tr>
                                                <td>
                                                    <strong>{{ insurance.insurance_type.name }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ insurance.insurance_type.code }}</small>
                                                </td>
                                                <td>
                                                    <span class="badge bg-warning">{{ insurance.get_employee_percentage }}%</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">{{ insurance.get_company_percentage }}%</span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-danger">
                                                        {{ calculation.employee_deduction|floatformat:2 }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endwith %}
                                            {% endfor %}
                                        </tbody>
                                        <tfoot>
                                            <tr class="table-warning">
                                                <th>{% trans "Total Deductions" %}</th>
                                                <th colspan="2"></th>
                                                <th>
                                                    <strong>{{ employee.get_total_insurance_deductions.employee_deduction|floatformat:2 }}</strong>
                                                </th>
                                            </tr>
                                        </tfoot>
                                    </table>
                                </div>
                                {% else %}
                                <div class="text-center py-4">
                                    <i class="fas fa-shield-alt fa-2x text-muted mb-2"></i>
                                    <p class="text-muted">{% trans "No insurance assigned" %}</p>
                                    <a href="{% url 'hr:employee_insurance_create' employee.id %}" class="btn btn-info">
                                        <i class="fas fa-plus me-1"></i>
                                        {% trans "Add First Insurance" %}
                                    </a>
                                </div>
                                {% endif %}

                                <!-- Net Salary Calculation -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-hand-holding-usd me-2"></i>
                                            {% trans "الراتب الصافي" %} (Net Salary)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Gross Salary" %}</small>
                                                <div class="h6 text-success">{{ employee.get_gross_salary|floatformat:2 }}</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">{% trans "Total Deductions" %}</small>
                                                <div class="h6 text-danger">{{ employee.get_total_insurance_deductions.employee_deduction|floatformat:2 }}</div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="text-center">
                                            <h5 class="text-primary mb-0">{{ employee.get_net_salary|floatformat:2 }}</h5>
                                            <small class="text-muted">{% trans "Net Salary (Estimated)" %}</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Mission Allowance Requests -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">
                                            <i class="fas fa-plane me-2"></i>
                                            {% trans "طلبات بدل المأمورية" %} (Mission Requests)
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="text-center">
                                            <a href="#" class="btn btn-warning disabled">
                                                <i class="fas fa-plus me-1"></i>
                                                {% trans "طلب بدل مأمورية" %}
                                            </a>
                                            <br>
                                            <small class="text-muted mt-2 d-block">{% trans "Coming Soon" %}</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div class="tab-pane fade" id="documents" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>{% trans "Active Contract" %}</h6>
                                {% if active_contract %}
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ active_contract.contract_number }}</h6>
                                        <p class="card-text">
                                            <strong>{% trans "Type" %}:</strong> {{ active_contract.get_contract_type_display }}<br>
                                            <strong>{% trans "Start Date" %}:</strong> {{ active_contract.start_date|date:"Y-m-d" }}<br>
                                            {% if active_contract.end_date %}
                                            <strong>{% trans "End Date" %}:</strong> {{ active_contract.end_date|date:"Y-m-d" }}<br>
                                            {% endif %}
                                            <strong>{% trans "Status" %}:</strong>
                                            <span class="badge bg-{% if active_contract.status == 'active' %}success{% else %}secondary{% endif %}">
                                                {{ active_contract.get_status_display }}
                                            </span>
                                        </p>
                                        <a href="{% url 'hr:contract_detail' active_contract.id %}" class="btn btn-sm btn-outline-primary">
                                            {% trans "View Contract" %}
                                        </a>
                                    </div>
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No active contract" %}</p>
                                <a href="{% url 'hr:contract_create' %}?employee={{ employee.id }}" class="btn btn-primary">
                                    {% trans "Create Contract" %}
                                </a>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6>{% trans "Notes" %}</h6>
                                {% if employee.notes %}
                                <div class="bg-light p-3 rounded">
                                    {{ employee.notes|linebreaks }}
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No notes available" %}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
