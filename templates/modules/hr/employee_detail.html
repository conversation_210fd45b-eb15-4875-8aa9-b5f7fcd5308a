{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ employee.full_name }} - {% trans "Employee Details" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:employee_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h1 class="h3 mb-0">{{ employee.full_name }}</h1>
            <small class="text-muted">{{ employee.employee_id }} - {{ employee.position.title }}</small>
        </div>
    </div>
    <div class="btn-group">
        <a href="{% url 'hr:employee_edit' employee.id %}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>
            {% trans "Edit" %}
        </a>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle dropdown-toggle-split"
                data-bs-toggle="dropdown">
            <span class="visually-hidden">{% trans "Toggle Dropdown" %}</span>
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="{% url 'hr:contract_create' %}?employee={{ employee.id }}">
                <i class="fas fa-file-contract me-2"></i>{% trans "Create Contract" %}
            </a></li>
            <li><a class="dropdown-item" href="{% url 'hr:advance_create' %}?employee={{ employee.id }}">
                <i class="fas fa-money-bill me-2"></i>{% trans "Request Advance" %}
            </a></li>
            <li><a class="dropdown-item" href="{% url 'hr:commission_create' %}?employee={{ employee.id }}">
                <i class="fas fa-percentage me-2"></i>{% trans "Add Commission" %}
            </a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="{% url 'hr:employee_delete' employee.id %}">
                <i class="fas fa-user-times me-2"></i>{% trans "Terminate Employee" %}
            </a></li>
        </ul>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<div class="row">
    <!-- Employee Info Card -->
    <div class="col-lg-4">
        <div class="card mb-4">
            <div class="card-body text-center">
                {% if employee.photo %}
                <img src="{{ employee.photo.url }}" alt="{{ employee.full_name }}"
                     class="rounded-circle mb-3" width="120" height="120" style="object-fit: cover;">
                {% else %}
                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3"
                     style="width: 120px; height: 120px;">
                    <i class="fas fa-user fa-3x text-white"></i>
                </div>
                {% endif %}

                <h5 class="card-title">{{ employee.full_name }}</h5>
                {% if employee.arabic_name %}
                <p class="text-muted">{{ employee.arabic_name }}</p>
                {% endif %}

                <div class="mb-3">
                    {% if employee.status == 'active' %}
                    <span class="badge bg-success fs-6">{% trans "Active" %}</span>
                    {% elif employee.status == 'inactive' %}
                    <span class="badge bg-warning fs-6">{% trans "Inactive" %}</span>
                    {% elif employee.status == 'terminated' %}
                    <span class="badge bg-danger fs-6">{% trans "Terminated" %}</span>
                    {% elif employee.status == 'suspended' %}
                    <span class="badge bg-secondary fs-6">{% trans "Suspended" %}</span>
                    {% endif %}
                </div>

                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="mb-0">{{ employee.years_of_service }}</h6>
                            <small class="text-muted">{% trans "Years" %}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h6 class="mb-0">{{ employee.age|default:"-" }}</h6>
                            <small class="text-muted">{% trans "Age" %}</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h6 class="mb-0">{{ employee.basic_salary|floatformat:0 }}</h6>
                        <small class="text-muted">{% trans "Salary" %}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Quick Stats" %}</h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-success mb-0">{{ attendance_stats.present_days|default:0 }}</h5>
                            <small class="text-muted">{% trans "Present Days" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-warning mb-0">{{ attendance_stats.late_days|default:0 }}</h5>
                            <small class="text-muted">{% trans "Late Days" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-info mb-0">{{ attendance_stats.total_hours|default:0|floatformat:1 }}</h5>
                            <small class="text-muted">{% trans "Total Hours" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <h5 class="text-primary mb-0">{{ attendance_stats.overtime_hours|default:0|floatformat:1 }}</h5>
                            <small class="text-muted">{% trans "Overtime" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Details Tabs -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#personal" role="tab">
                            <i class="fas fa-user me-1"></i>{% trans "Personal Info" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#employment" role="tab">
                            <i class="fas fa-briefcase me-1"></i>{% trans "Employment" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#financial" role="tab">
                            <i class="fas fa-money-bill me-1"></i>{% trans "Financial" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#documents" role="tab">
                            <i class="fas fa-file me-1"></i>{% trans "Documents" %}
                        </a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content">
                    <!-- Personal Info Tab -->
                    <div class="tab-pane fade show active" id="personal" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Full Name" %}:</td>
                                        <td>{{ employee.full_name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Arabic Name" %}:</td>
                                        <td>{{ employee.arabic_name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "National ID" %}:</td>
                                        <td>{{ employee.national_id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Passport" %}:</td>
                                        <td>{{ employee.passport_number|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Birth Date" %}:</td>
                                        <td>{{ employee.birth_date|date:"Y-m-d"|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Gender" %}:</td>
                                        <td>{{ employee.get_gender_display }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Mobile" %}:</td>
                                        <td>{{ employee.mobile }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Phone" %}:</td>
                                        <td>{{ employee.phone|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Email" %}:</td>
                                        <td>{{ employee.email|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Marital Status" %}:</td>
                                        <td>{{ employee.get_marital_status_display }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Emergency Contact" %}:</td>
                                        <td>{{ employee.emergency_contact_name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Emergency Phone" %}:</td>
                                        <td>{{ employee.emergency_contact_phone|default:"-" }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        {% if employee.address %}
                        <div class="row">
                            <div class="col-12">
                                <h6>{% trans "Address" %}</h6>
                                <p class="text-muted">{{ employee.address }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Employment Tab -->
                    <div class="tab-pane fade" id="employment" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Employee ID" %}:</td>
                                        <td>{{ employee.employee_id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Department" %}:</td>
                                        <td>{{ employee.department.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Position" %}:</td>
                                        <td>{{ employee.position.title }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Direct Manager" %}:</td>
                                        <td>
                                            {% if employee.direct_manager %}
                                            <a href="{% url 'hr:employee_detail' employee.direct_manager.id %}">
                                                {{ employee.direct_manager.full_name }}
                                            </a>
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Hire Date" %}:</td>
                                        <td>{{ employee.hire_date|date:"Y-m-d" }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Status" %}:</td>
                                        <td>{{ employee.get_status_display }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Branch" %}:</td>
                                        <td>{{ employee.branch.name }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-bold">{% trans "Company" %}:</td>
                                        <td>{{ employee.company.name }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Tab -->
                    <div class="tab-pane fade" id="financial" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>{% trans "Salary Information" %}</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-bold">{% trans "Basic Salary" %}:</td>
                                        <td>{{ employee.basic_salary|floatformat:2 }}</td>
                                    </tr>
                                </table>

                                <h6 class="mt-4">{% trans "Recent Commissions" %}</h6>
                                {% if recent_commissions %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Type" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Status" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for commission in recent_commissions %}
                                            <tr>
                                                <td>{{ commission.get_commission_type_display }}</td>
                                                <td>{{ commission.amount|floatformat:2 }}</td>
                                                <td>
                                                    <span class="badge bg-{% if commission.status == 'approved' %}success{% elif commission.status == 'pending' %}warning{% else %}secondary{% endif %}">
                                                        {{ commission.get_status_display }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No commissions found" %}</p>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6>{% trans "Active Advances" %}</h6>
                                {% if active_advances %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{% trans "Type" %}</th>
                                                <th>{% trans "Amount" %}</th>
                                                <th>{% trans "Status" %}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for advance in active_advances %}
                                            <tr>
                                                <td>{{ advance.get_advance_type_display }}</td>
                                                <td>{{ advance.amount|floatformat:2 }}</td>
                                                <td>
                                                    <span class="badge bg-{% if advance.status == 'paid' %}success{% elif advance.status == 'approved' %}info{% else %}warning{% endif %}">
                                                        {{ advance.get_status_display }}
                                                    </span>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No active advances" %}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Documents Tab -->
                    <div class="tab-pane fade" id="documents" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>{% trans "Active Contract" %}</h6>
                                {% if active_contract %}
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ active_contract.contract_number }}</h6>
                                        <p class="card-text">
                                            <strong>{% trans "Type" %}:</strong> {{ active_contract.get_contract_type_display }}<br>
                                            <strong>{% trans "Start Date" %}:</strong> {{ active_contract.start_date|date:"Y-m-d" }}<br>
                                            {% if active_contract.end_date %}
                                            <strong>{% trans "End Date" %}:</strong> {{ active_contract.end_date|date:"Y-m-d" }}<br>
                                            {% endif %}
                                            <strong>{% trans "Status" %}:</strong>
                                            <span class="badge bg-{% if active_contract.status == 'active' %}success{% else %}secondary{% endif %}">
                                                {{ active_contract.get_status_display }}
                                            </span>
                                        </p>
                                        <a href="{% url 'hr:contract_detail' active_contract.id %}" class="btn btn-sm btn-outline-primary">
                                            {% trans "View Contract" %}
                                        </a>
                                    </div>
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No active contract" %}</p>
                                <a href="{% url 'hr:contract_create' %}?employee={{ employee.id }}" class="btn btn-primary">
                                    {% trans "Create Contract" %}
                                </a>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <h6>{% trans "Notes" %}</h6>
                                {% if employee.notes %}
                                <div class="bg-light p-3 rounded">
                                    {{ employee.notes|linebreaks }}
                                </div>
                                {% else %}
                                <p class="text-muted">{% trans "No notes available" %}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
