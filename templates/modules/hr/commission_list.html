{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Commission List" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-percentage me-2"></i>
        {% trans "Commission List" %}
    </h1>
    <a href="{% url 'hr:commission_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        {% trans "Add Commission" %}
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="employee" class="form-label">{% trans "Employee" %}</label>
                <select class="form-select" id="employee" name="employee">
                    <option value="">{% trans "All Employees" %}</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id|stringformat:"s" == selected_employee %}selected{% endif %}>
                        {{ emp.full_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">{% trans "Status" %}</label>
                <select class="form-select" id="status" name="status">
                    <option value="">{% trans "All Status" %}</option>
                    <option value="pending" {% if selected_status == "pending" %}selected{% endif %}>{% trans "Pending" %}</option>
                    <option value="approved" {% if selected_status == "approved" %}selected{% endif %}>{% trans "Approved" %}</option>
                    <option value="rejected" {% if selected_status == "rejected" %}selected{% endif %}>{% trans "Rejected" %}</option>
                    <option value="paid" {% if selected_status == "paid" %}selected{% endif %}>{% trans "Paid" %}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-outline-primary">
                        <i class="fas fa-search me-1"></i>
                        {% trans "Filter" %}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Commission List -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            {% trans "Commissions" %}
            {% if page_obj %}
            <span class="badge bg-secondary ms-2">{{ page_obj.paginator.count }}</span>
            {% endif %}
        </h5>
    </div>
    <div class="card-body p-0">
        {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th>{% trans "Employee" %}</th>
                        <th>{% trans "Type" %}</th>
                        <th>{% trans "Amount" %}</th>
                        <th>{% trans "Period" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Created" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for commission in page_obj %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if commission.employee.photo %}
                                <img src="{{ commission.employee.photo.url }}" alt="{{ commission.employee.full_name }}"
                                     class="rounded-circle me-2" width="32" height="32">
                                {% else %}
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2"
                                     style="width: 32px; height: 32px;">
                                    <i class="fas fa-user text-white small"></i>
                                </div>
                                {% endif %}
                                <div>
                                    <div class="fw-bold">{{ commission.employee.full_name }}</div>
                                    <small class="text-muted">{{ commission.employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ commission.get_commission_type_display }}</td>
                        <td>{{ commission.amount|floatformat:2 }}</td>
                        <td>
                            {% if commission.period_start and commission.period_end %}
                            {{ commission.period_start|date:"M d" }} - {{ commission.period_end|date:"M d" }}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            {% if commission.status == 'pending' %}
                            <span class="badge bg-warning">{% trans "Pending" %}</span>
                            {% elif commission.status == 'approved' %}
                            <span class="badge bg-success">{% trans "Approved" %}</span>
                            {% elif commission.status == 'rejected' %}
                            <span class="badge bg-danger">{% trans "Rejected" %}</span>
                            {% elif commission.status == 'paid' %}
                            <span class="badge bg-info">{% trans "Paid" %}</span>
                            {% endif %}
                        </td>
                        <td>{{ commission.created_at|date:"M d, Y" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'hr:commission_detail' commission.id %}"
                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'hr:commission_edit' commission.id %}"
                                   class="btn btn-outline-secondary" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if commission.status == 'pending' %}
                                <a href="{% url 'hr:commission_approve' commission.id %}"
                                   class="btn btn-outline-success" title="{% trans 'Approve' %}">
                                    <i class="fas fa-check"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <i class="fas fa-percentage fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">{% trans "No commissions found" %}</h6>
                            <p class="text-muted">{% trans "Start by adding your first commission" %}</p>
                            <a href="{% url 'hr:commission_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus me-1"></i>
                                {% trans "Add Commission" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-percentage fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No commissions found" %}</h5>
            <p class="text-muted">{% trans "Start by adding your first commission" %}</p>
            <a href="{% url 'hr:commission_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                {% trans "Add Commission" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
