{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Attendance Report" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-chart-line me-2"></i>
        {% trans "Attendance Report" %}
    </h1>
    <div class="btn-group">
        <a href="{% url 'hr:attendance_entry' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            {% trans "Record Attendance" %}
        </a>
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-download me-1"></i>
            {% trans "Export" %}
        </button>
        <ul class="dropdown-menu">
            <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="fas fa-file-excel me-2"></i>{% trans "Export to Excel" %}
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf me-2"></i>{% trans "Export to PDF" %}
            </a></li>
            <li><a class="dropdown-item" href="#" onclick="printReport()">
                <i class="fas fa-print me-2"></i>{% trans "Print Report" %}
            </a></li>
        </ul>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Filters -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            {% trans "Report Filters" %}
        </h6>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">{% trans "Start Date" %}</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">{% trans "End Date" %}</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
            </div>
            <div class="col-md-3">
                <label for="employee" class="form-label">{% trans "Employee" %}</label>
                <select class="form-select" id="employee" name="employee">
                    <option value="">{% trans "All Employees" %}</option>
                    {% for emp in employees %}
                    <option value="{{ emp.id }}" {% if emp.id|stringformat:"s" == selected_employee %}selected{% endif %}>
                        {{ emp.full_name }} ({{ emp.employee_id }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="department" class="form-label">{% trans "Department" %}</label>
                <select class="form-select" id="department" name="department">
                    <option value="">{% trans "All Departments" %}</option>
                    {% for dept in departments %}
                    <option value="{{ dept.id }}" {% if dept.id|stringformat:"s" == selected_department %}selected{% endif %}>
                        {{ dept.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    {% trans "Apply Filters" %}
                </button>
                <a href="{% url 'hr:attendance_report' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-undo me-1"></i>
                    {% trans "Reset" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-subtitle text-muted">{% trans "Total Present" %}</h6>
                        <h3 class="card-title text-success mb-0" id="total-present">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card danger">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-subtitle text-muted">{% trans "Total Absent" %}</h6>
                        <h3 class="card-title text-danger mb-0" id="total-absent">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times-circle fa-2x text-danger"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-subtitle text-muted">{% trans "Total Late" %}</h6>
                        <h3 class="card-title text-warning mb-0" id="total-late">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-subtitle text-muted">{% trans "Total Hours" %}</h6>
                        <h3 class="card-title text-primary mb-0" id="total-hours">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hourglass-half fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Data -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                {% trans "Attendance Records" %}
                {% if page_obj %}
                <span class="badge bg-secondary ms-2">{{ page_obj.paginator.count }}</span>
                {% endif %}
            </h5>
            <div class="btn-group btn-group-sm">
                <button class="btn btn-outline-secondary" onclick="toggleView('table')" id="table-view-btn">
                    <i class="fas fa-table"></i>
                </button>
                <button class="btn btn-outline-secondary" onclick="toggleView('calendar')" id="calendar-view-btn">
                    <i class="fas fa-calendar"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <!-- Table View -->
        <div id="table-view">
            {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{% trans "Employee" %}</th>
                            <th>{% trans "Date" %}</th>
                            <th>{% trans "Check In" %}</th>
                            <th>{% trans "Check Out" %}</th>
                            <th>{% trans "Working Hours" %}</th>
                            <th>{% trans "Overtime" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for summary in page_obj %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if summary.employee.photo %}
                                    <img src="{{ summary.employee.photo.url }}" alt="{{ summary.employee.full_name }}"
                                         class="rounded-circle me-2" width="32" height="32">
                                    {% else %}
                                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2"
                                         style="width: 32px; height: 32px;">
                                        <i class="fas fa-user text-white small"></i>
                                    </div>
                                    {% endif %}
                                    <div>
                                        <div class="fw-bold">{{ summary.employee.full_name }}</div>
                                        <small class="text-muted">{{ summary.employee.employee_id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ summary.date|date:"Y-m-d" }}</td>
                            <td>
                                {% if summary.check_in_time %}
                                <span class="text-success">{{ summary.check_in_time|time:"H:i" }}</span>
                                {% if summary.is_late %}
                                <i class="fas fa-exclamation-triangle text-warning ms-1" title="{% trans 'Late' %}"></i>
                                {% endif %}
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if summary.check_out_time %}
                                <span class="text-danger">{{ summary.check_out_time|time:"H:i" }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if summary.working_hours %}
                                {{ summary.working_hours|floatformat:1 }}h
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if summary.overtime_hours %}
                                <span class="text-info">{{ summary.overtime_hours|floatformat:1 }}h</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if summary.is_absent %}
                                <span class="badge bg-danger">{% trans "Absent" %}</span>
                                {% elif summary.is_late %}
                                <span class="badge bg-warning">{% trans "Late" %}</span>
                                {% else %}
                                <span class="badge bg-success">{% trans "Present" %}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" onclick="viewDetails({{ summary.id }})"
                                            title="{% trans 'View Details' %}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="editAttendance({{ summary.id }})"
                                            title="{% trans 'Edit' %}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "No attendance records found" %}</h5>
                <p class="text-muted">{% trans "Try adjusting your filters or date range" %}</p>
            </div>
            {% endif %}
        </div>

        <!-- Calendar View (placeholder) -->
        <div id="calendar-view" style="display: none;">
            <div class="text-center py-5">
                <i class="fas fa-calendar fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">{% trans "Calendar View" %}</h5>
                <p class="text-muted">{% trans "Calendar view will be implemented in future updates" %}</p>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="card-footer">
        <nav aria-label="{% trans 'Attendance pagination' %}">
            <ul class="pagination justify-content-center mb-0">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}{% if selected_employee %}&employee={{ selected_employee }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}">
                        {% trans "First" %}
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}{% if selected_employee %}&employee={{ selected_employee }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}">
                        {% trans "Previous" %}
                    </a>
                </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        {% trans "Page" %} {{ page_obj.number }} {% trans "of" %} {{ page_obj.paginator.num_pages }}
                    </span>
                </li>

                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}{% if selected_employee %}&employee={{ selected_employee }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}">
                        {% trans "Next" %}
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if start_date %}&start_date={{ start_date }}{% endif %}{% if end_date %}&end_date={{ end_date }}{% endif %}{% if selected_employee %}&employee={{ selected_employee }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}">
                        {% trans "Last" %}
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Toggle between table and calendar view
    function toggleView(viewType) {
        const tableView = document.getElementById('table-view');
        const calendarView = document.getElementById('calendar-view');
        const tableBtn = document.getElementById('table-view-btn');
        const calendarBtn = document.getElementById('calendar-view-btn');

        if (viewType === 'table') {
            tableView.style.display = 'block';
            calendarView.style.display = 'none';
            tableBtn.classList.add('active');
            calendarBtn.classList.remove('active');
        } else {
            tableView.style.display = 'none';
            calendarView.style.display = 'block';
            tableBtn.classList.remove('active');
            calendarBtn.classList.add('active');
        }
    }

    // View attendance details
    function viewDetails(summaryId) {
        // This would open a modal or navigate to details page
        alert('{% trans "View details for attendance ID: " %}' + summaryId);
    }

    // Edit attendance
    function editAttendance(summaryId) {
        // This would open edit form
        alert('{% trans "Edit attendance for ID: " %}' + summaryId);
    }

    // Export functions
    function exportReport(format) {
        const params = new URLSearchParams(window.location.search);
        params.set('export', format);
        window.location.href = '?' + params.toString();
    }

    function printReport() {
        window.print();
    }

    // Calculate and display summary statistics
    function calculateSummary() {
        // Count different statuses from the table
        let presentCount = 0;
        let absentCount = 0;
        let lateCount = 0;
        let totalHours = 0;

        const rows = document.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const statusBadge = row.querySelector('.badge');
            if (statusBadge) {
                if (statusBadge.textContent.includes('Present')) presentCount++;
                else if (statusBadge.textContent.includes('Absent')) absentCount++;
                else if (statusBadge.textContent.includes('Late')) lateCount++;
            }

            const hoursCell = row.cells[4];
            if (hoursCell && hoursCell.textContent !== '-') {
                const hours = parseFloat(hoursCell.textContent.replace('h', ''));
                if (!isNaN(hours)) totalHours += hours;
            }
        });

        document.getElementById('total-present').textContent = presentCount;
        document.getElementById('total-absent').textContent = absentCount;
        document.getElementById('total-late').textContent = lateCount;
        document.getElementById('total-hours').textContent = totalHours.toFixed(1);
    }

    // Load summary on page load
    calculateSummary();

    // Set default date range (current month)
    document.addEventListener('DOMContentLoaded', function() {
        const startDate = document.getElementById('start_date');
        const endDate = document.getElementById('end_date');

        if (!startDate.value) {
            const now = new Date();
            const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
            startDate.value = firstDay.toISOString().split('T')[0];
        }

        if (!endDate.value) {
            const now = new Date();
            endDate.value = now.toISOString().split('T')[0];
        }
    });
</script>
{% endblock %}
