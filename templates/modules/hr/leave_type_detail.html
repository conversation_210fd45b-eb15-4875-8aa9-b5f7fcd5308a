{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ leave_type.name }} - {% trans "Leave Type Details" %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:leave_type_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h2>{{ leave_type.name }}</h2>
            <small class="text-muted">
                <span class="badge bg-primary">{{ leave_type.code }}</span>
                {% if leave_type.is_active %}
                    <span class="badge bg-success">{% trans "Active" %}</span>
                {% else %}
                    <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                {% endif %}
            </small>
        </div>
    </div>
    <div class="btn-group">
        <a href="{% url 'hr:leave_type_edit' leave_type.id %}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>
            {% trans "Edit" %}
        </a>
        <a href="{% url 'hr:leave_type_delete' leave_type.id %}" class="btn btn-outline-danger">
            <i class="fas fa-trash me-1"></i>
            {% trans "Delete" %}
        </a>
    </div>
</div>

<div class="row">
    <!-- Leave Type Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    {% trans "Leave Type Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{% trans "Name" %}:</td>
                                <td>{{ leave_type.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Code" %}:</td>
                                <td>
                                    <span class="badge bg-primary">{{ leave_type.code }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Max Days Per Year" %}:</td>
                                <td><strong>{{ leave_type.max_days_per_year }}</strong> {% trans "days" %}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Max Consecutive Days" %}:</td>
                                <td><strong>{{ leave_type.max_consecutive_days }}</strong> {% trans "days" %}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Minimum Notice Days" %}:</td>
                                <td><strong>{{ leave_type.min_notice_days }}</strong> {% trans "days" %}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{% trans "Paid Leave" %}:</td>
                                <td>
                                    {% if leave_type.is_paid %}
                                        <span class="badge bg-success">{% trans "Yes" %}</span>
                                    {% else %}
                                        <span class="badge bg-warning">{% trans "No" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Salary Percentage" %}:</td>
                                <td><strong>{{ leave_type.salary_percentage }}%</strong></td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Requires Approval" %}:</td>
                                <td>
                                    {% if leave_type.requires_approval %}
                                        <span class="badge bg-info">{% trans "Yes" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "No" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Medical Certificate" %}:</td>
                                <td>
                                    {% if leave_type.requires_medical_certificate %}
                                        <span class="badge bg-warning">{% trans "Required" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "Not Required" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Carry Forward" %}:</td>
                                <td>
                                    {% if leave_type.carry_forward %}
                                        <span class="badge bg-success">{% trans "Yes" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "No" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Leave Requests -->
        {% if recent_requests %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    {% trans "Recent Leave Requests" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% trans "Employee" %}</th>
                                <th>{% trans "Period" %}</th>
                                <th>{% trans "Days" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Request Date" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in recent_requests %}
                            <tr>
                                <td>
                                    <a href="{% url 'hr:employee_detail' request.employee.id %}">
                                        {{ request.employee.full_name }}
                                    </a>
                                </td>
                                <td>
                                    {{ request.start_date|date:"d/m/Y" }} - {{ request.end_date|date:"d/m/Y" }}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ request.total_days }}</span>
                                </td>
                                <td>
                                    {% if request.status == 'pending' %}
                                        <span class="badge bg-warning">{% trans "Pending" %}</span>
                                    {% elif request.status == 'approved' %}
                                        <span class="badge bg-success">{% trans "Approved" %}</span>
                                    {% elif request.status == 'rejected' %}
                                        <span class="badge bg-danger">{% trans "Rejected" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ request.created_at|date:"d/m/Y" }}</small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Statistics -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    {% trans "Statistics" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h4 class="text-primary mb-0">{{ recent_requests.count }}</h4>
                        <small class="text-muted">{% trans "Total Requests" %}</small>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <a href="{% url 'hr:leave_request_create' %}?leave_type={{ leave_type.id }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        {% trans "New Request" %}
                    </a>
                    <a href="{% url 'hr:leave_request_list' %}?leave_type={{ leave_type.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-1"></i>
                        {% trans "View All Requests" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    {% trans "Quick Info" %}
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-calendar me-2 text-primary"></i>
                        <strong>{{ leave_type.max_days_per_year }}</strong> {% trans "days per year" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-clock me-2 text-warning"></i>
                        <strong>{{ leave_type.min_notice_days }}</strong> {% trans "days notice required" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-money-bill me-2 text-success"></i>
                        <strong>{{ leave_type.salary_percentage }}%</strong> {% trans "salary percentage" %}
                    </li>
                    {% if leave_type.requires_medical_certificate %}
                    <li class="mb-2">
                        <i class="fas fa-file-medical me-2 text-danger"></i>
                        {% trans "Medical certificate required" %}
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
