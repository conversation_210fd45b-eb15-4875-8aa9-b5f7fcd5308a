{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2 text-info"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    {% trans "اسم التأمين" %} (Insurance Name) <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">{% trans "مثال: التأمينات الاجتماعية، التأمين الطبي" %} (e.g., Social Insurance, Medical Insurance)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="code" class="form-label">
                                    {% trans "كود التأمين" %} (Insurance Code) <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="code" name="code" required>
                                <div class="form-text">{% trans "مثال: SOCIAL, MEDICAL" %} (e.g., SOCIAL, MEDICAL)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="employee_percentage" class="form-label">
                                    {% trans "نسبة الموظف %" %} (Employee Percentage %) <span class="text-danger">*</span>
                                </label>
                                <input type="number" class="form-control" id="employee_percentage" name="employee_percentage" 
                                       step="0.01" min="0" max="100" required>
                                <div class="form-text">{% trans "النسبة المخصومة من راتب الموظف" %} (Percentage deducted from employee salary)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="company_percentage" class="form-label">
                                    {% trans "نسبة الشركة %" %} (Company Percentage %)
                                </label>
                                <input type="number" class="form-control" id="company_percentage" name="company_percentage" 
                                       step="0.01" min="0" max="100" value="0">
                                <div class="form-text">{% trans "النسبة التي تتحملها الشركة" %} (Percentage paid by company)</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="max_salary_limit" class="form-label">
                                    {% trans "حد الراتب الأقصى" %} (Maximum Salary Limit)
                                </label>
                                <input type="number" class="form-control" id="max_salary_limit" name="max_salary_limit" 
                                       step="0.01" min="0">
                                <div class="form-text">{% trans "الحد الأقصى للراتب الخاضع للتأمين" %} (Maximum salary subject to insurance)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        {% trans "نشط" %} (Active)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">
                            {% trans "الوصف" %} (Description)
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        <div class="form-text">{% trans "وصف اختياري للتأمين" %} (Optional description)</div>
                    </div>

                    <!-- Insurance Calculation Example -->
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-calculator me-2"></i>
                                {% trans "مثال على الحساب" %} (Calculation Example)
                            </h6>
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="text-muted">{% trans "راتب افتراضي" %} (Sample Salary)</div>
                                    <div class="h6 text-primary">5,000.00</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-muted">{% trans "خصم الموظف" %} (Employee Deduction)</div>
                                    <div class="h6 text-danger" id="employee_deduction">0.00</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-muted">{% trans "مساهمة الشركة" %} (Company Contribution)</div>
                                    <div class="h6 text-success" id="company_contribution">0.00</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-muted">{% trans "إجمالي التأمين" %} (Total Insurance)</div>
                                    <div class="h6 text-info" id="total_insurance">0.00</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:insurance_type_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            {% trans "رجوع" %} (Back)
                        </a>
                        <button type="submit" class="btn btn-info">
                            <i class="fas fa-save me-1"></i>
                            {% trans "حفظ" %} (Save)
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const employeePercentageInput = document.getElementById('employee_percentage');
    const companyPercentageInput = document.getElementById('company_percentage');
    const maxSalaryLimitInput = document.getElementById('max_salary_limit');
    
    const employeeDeductionDisplay = document.getElementById('employee_deduction');
    const companyContributionDisplay = document.getElementById('company_contribution');
    const totalInsuranceDisplay = document.getElementById('total_insurance');
    
    const sampleSalary = 5000;
    
    function updateCalculationExample() {
        const employeePercentage = parseFloat(employeePercentageInput.value) || 0;
        const companyPercentage = parseFloat(companyPercentageInput.value) || 0;
        const maxSalaryLimit = parseFloat(maxSalaryLimitInput.value) || 0;
        
        let applicableSalary = sampleSalary;
        if (maxSalaryLimit > 0 && sampleSalary > maxSalaryLimit) {
            applicableSalary = maxSalaryLimit;
        }
        
        const employeeDeduction = (applicableSalary * employeePercentage) / 100;
        const companyContribution = (applicableSalary * companyPercentage) / 100;
        const totalInsurance = employeeDeduction + companyContribution;
        
        employeeDeductionDisplay.textContent = employeeDeduction.toFixed(2);
        companyContributionDisplay.textContent = companyContribution.toFixed(2);
        totalInsuranceDisplay.textContent = totalInsurance.toFixed(2);
    }
    
    employeePercentageInput.addEventListener('input', updateCalculationExample);
    companyPercentageInput.addEventListener('input', updateCalculationExample);
    maxSalaryLimitInput.addEventListener('input', updateCalculationExample);
    
    // Initial calculation
    updateCalculationExample();
});
</script>
{% endblock %}
