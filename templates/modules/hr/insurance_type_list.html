{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "أنواع التأمينات" %} (Insurance Types){% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2 text-info"></i>
                    {% trans "أنواع التأمينات" %} (Insurance Types)
                </h5>
                <a href="{% url 'hr:insurance_type_create' %}" class="btn btn-info">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "إضافة نوع تأمين جديد" %} (Add New Type)
                </a>
            </div>
            <div class="card-body">
                {% if insurance_types %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "الاسم" %} (Name)</th>
                                <th>{% trans "الكود" %} (Code)</th>
                                <th>{% trans "نسبة الموظف" %} (Employee %)</th>
                                <th>{% trans "نسبة الشركة" %} (Company %)</th>
                                <th>{% trans "حد الراتب الأقصى" %} (Max Salary Limit)</th>
                                <th>{% trans "الحالة" %} (Status)</th>
                                <th>{% trans "الإجراءات" %} (Actions)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for insurance_type in insurance_types %}
                            <tr>
                                <td>
                                    <strong>{{ insurance_type.name }}</strong>
                                    {% if insurance_type.description %}
                                    <br>
                                    <small class="text-muted">{{ insurance_type.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ insurance_type.code }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-warning">{{ insurance_type.employee_percentage }}%</span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ insurance_type.company_percentage }}%</span>
                                </td>
                                <td>
                                    {% if insurance_type.max_salary_limit %}
                                    <span class="text-info fw-bold">{{ insurance_type.max_salary_limit|floatformat:2 }}</span>
                                    {% else %}
                                    <span class="text-muted">{% trans "بلا حد" %} (No Limit)</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if insurance_type.is_active %}
                                    <span class="badge bg-success">{% trans "نشط" %} (Active)</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% trans "غير نشط" %} (Inactive)</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" disabled>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-shield-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا توجد أنواع تأمينات" %} (No Insurance Types Found)</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة نوع تأمين جديد" %} (Start by adding a new insurance type)</p>
                    <a href="{% url 'hr:insurance_type_create' %}" class="btn btn-info">
                        <i class="fas fa-plus me-1"></i>
                        {% trans "إضافة نوع تأمين جديد" %} (Add New Type)
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
