{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <!-- Employee and Leave Type -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.employee.id_for_label }}" class="form-label">{{ form.employee.label }}</label>
                            {{ form.employee }}
                            {% if form.employee.errors %}
                                <div class="text-danger small">{{ form.employee.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.leave_type.id_for_label }}" class="form-label">{{ form.leave_type.label }}</label>
                            {{ form.leave_type }}
                            {% if form.leave_type.errors %}
                                <div class="text-danger small">{{ form.leave_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Leave Period -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.start_date.id_for_label }}" class="form-label">{{ form.start_date.label }}</label>
                            {{ form.start_date }}
                            {% if form.start_date.errors %}
                                <div class="text-danger small">{{ form.start_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                            {{ form.end_date }}
                            {% if form.end_date.errors %}
                                <div class="text-danger small">{{ form.end_date.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Reason -->
                    <div class="mb-3">
                        <label for="{{ form.reason.id_for_label }}" class="form-label">{{ form.reason.label }}</label>
                        {{ form.reason }}
                        {% if form.reason.errors %}
                            <div class="text-danger small">{{ form.reason.errors.0 }}</div>
                        {% endif %}
                    </div>

                    <!-- Emergency Contact and Replacement -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.emergency_contact.id_for_label }}" class="form-label">{{ form.emergency_contact.label }}</label>
                            {{ form.emergency_contact }}
                            {% if form.emergency_contact.errors %}
                                <div class="text-danger small">{{ form.emergency_contact.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.replacement_employee.id_for_label }}" class="form-label">{{ form.replacement_employee.label }}</label>
                            {{ form.replacement_employee }}
                            {% if form.replacement_employee.errors %}
                                <div class="text-danger small">{{ form.replacement_employee.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Attachments -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.medical_certificate.id_for_label }}" class="form-label">{{ form.medical_certificate.label }}</label>
                            {{ form.medical_certificate }}
                            {% if form.medical_certificate.errors %}
                                <div class="text-danger small">{{ form.medical_certificate.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">{% trans "Required for medical leave types" %}</small>
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.attachment.id_for_label }}" class="form-label">{{ form.attachment.label }}</label>
                            {{ form.attachment }}
                            {% if form.attachment.errors %}
                                <div class="text-danger small">{{ form.attachment.errors.0 }}</div>
                            {% endif %}
                            <small class="form-text text-muted">{% trans "Optional supporting documents" %}</small>
                        </div>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:leave_request_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            {% trans "Back to List" %}
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Submit Leave Request" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Help" %}</h6>
            </div>
            <div class="card-body">
                <h6>{% trans "Leave Request Guidelines" %}</h6>
                <ul class="small">
                    <li>{% trans "Select the appropriate leave type" %}</li>
                    <li>{% trans "Provide sufficient notice as per company policy" %}</li>
                    <li>{% trans "Attach medical certificate if required" %}</li>
                    <li>{% trans "Specify emergency contact information" %}</li>
                    <li>{% trans "Suggest a replacement employee if needed" %}</li>
                </ul>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Quick Actions" %}</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'hr:leave_type_list' %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-calendar-alt me-1"></i>
                        {% trans "View Leave Types" %}
                    </a>
                    <a href="{% url 'hr:leave_request_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-list me-1"></i>
                        {% trans "All Leave Requests" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Calculate total days when dates change
document.addEventListener('DOMContentLoaded', function() {
    const startDateField = document.getElementById('{{ form.start_date.id_for_label }}');
    const endDateField = document.getElementById('{{ form.end_date.id_for_label }}');
    
    function calculateDays() {
        if (startDateField.value && endDateField.value) {
            const startDate = new Date(startDateField.value);
            const endDate = new Date(endDateField.value);
            
            if (endDate >= startDate) {
                const timeDiff = endDate.getTime() - startDate.getTime();
                const dayDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
                
                // Show calculated days
                let daysInfo = document.getElementById('days-info');
                if (!daysInfo) {
                    daysInfo = document.createElement('div');
                    daysInfo.id = 'days-info';
                    daysInfo.className = 'alert alert-info mt-2';
                    endDateField.parentNode.appendChild(daysInfo);
                }
                daysInfo.innerHTML = `<i class="fas fa-calendar me-1"></i> {% trans "Total Days" %}: <strong>${dayDiff}</strong>`;
            }
        }
    }
    
    startDateField.addEventListener('change', calculateDays);
    endDateField.addEventListener('change', calculateDays);
});
</script>
{% endblock %}
