{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Leave Types" %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>{% trans "Leave Types" %}</h2>
    <a href="{% url 'hr:leave_type_create' %}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        {% trans "Create New Leave Type" %}
    </a>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="is_active" class="form-label">{% trans "Status" %}</label>
                <select name="is_active" id="is_active" class="form-select">
                    <option value="">{% trans "All" %}</option>
                    <option value="true" {% if is_active_filter == 'true' %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="false" {% if is_active_filter == 'false' %}selected{% endif %}>{% trans "Inactive" %}</option>
                </select>
            </div>
            <div class="col-md-8 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-filter me-1"></i>
                    {% trans "Filter" %}
                </button>
                <a href="{% url 'hr:leave_type_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    {% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Leave Types List -->
<div class="row">
    {% if leave_types %}
    {% for leave_type in leave_types %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">{{ leave_type.name }}</h6>
                {% if leave_type.is_active %}
                    <span class="badge bg-success">{% trans "Active" %}</span>
                {% else %}
                    <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                {% endif %}
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>{% trans "Code:" %}</strong> 
                    <span class="badge bg-primary">{{ leave_type.code }}</span>
                </div>
                
                <div class="row mb-2">
                    <div class="col-6">
                        <small class="text-muted">{% trans "Max Days/Year" %}</small>
                        <div><strong>{{ leave_type.max_days_per_year }}</strong></div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">{% trans "Max Consecutive" %}</small>
                        <div><strong>{{ leave_type.max_consecutive_days }}</strong></div>
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-6">
                        <small class="text-muted">{% trans "Notice Days" %}</small>
                        <div><strong>{{ leave_type.min_notice_days }}</strong></div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">{% trans "Salary %" %}</small>
                        <div><strong>{{ leave_type.salary_percentage }}%</strong></div>
                    </div>
                </div>

                <div class="mb-3">
                    <div class="d-flex flex-wrap gap-1">
                        {% if leave_type.is_paid %}
                            <span class="badge bg-success">{% trans "Paid" %}</span>
                        {% else %}
                            <span class="badge bg-warning">{% trans "Unpaid" %}</span>
                        {% endif %}
                        
                        {% if leave_type.requires_approval %}
                            <span class="badge bg-info">{% trans "Requires Approval" %}</span>
                        {% endif %}
                        
                        {% if leave_type.requires_medical_certificate %}
                            <span class="badge bg-warning">{% trans "Medical Certificate" %}</span>
                        {% endif %}
                        
                        {% if leave_type.carry_forward %}
                            <span class="badge bg-secondary">{% trans "Carry Forward" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100">
                    <a href="{% url 'hr:leave_type_detail' leave_type.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>
                        {% trans "View" %}
                    </a>
                    <a href="{% url 'hr:leave_type_edit' leave_type.id %}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-edit me-1"></i>
                        {% trans "Edit" %}
                    </a>
                    <a href="{% url 'hr:leave_type_delete' leave_type.id %}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-trash me-1"></i>
                        {% trans "Delete" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% else %}
    <div class="col-12">
        <div class="text-center py-5">
            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{% trans "No leave types found" %}</h5>
            <p class="text-muted">{% trans "Create your first leave type to get started." %}</p>
            <a href="{% url 'hr:leave_type_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create First Leave Type" %}
            </a>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
