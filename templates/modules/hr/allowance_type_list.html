{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "أنواع البدلات" %} (Allowance Types){% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2 text-success"></i>
                    {% trans "أنواع البدلات" %} (Allowance Types)
                </h5>
                <a href="{% url 'hr:allowance_type_create' %}" class="btn btn-success">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "إضافة نوع بدل جديد" %} (Add New Type)
                </a>
            </div>
            <div class="card-body">
                {% if allowance_types %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{% trans "الاسم" %} (Name)</th>
                                <th>{% trans "الكود" %} (Code)</th>
                                <th>{% trans "نوع الحساب" %} (Calculation Type)</th>
                                <th>{% trans "المبلغ الثابت" %} (Fixed Amount)</th>
                                <th>{% trans "الحالة" %} (Status)</th>
                                <th>{% trans "الإجراءات" %} (Actions)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for allowance_type in allowance_types %}
                            <tr>
                                <td>
                                    <strong>{{ allowance_type.name }}</strong>
                                    {% if allowance_type.description %}
                                    <br>
                                    <small class="text-muted">{{ allowance_type.description|truncatechars:50 }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-primary">{{ allowance_type.code }}</span>
                                </td>
                                <td>
                                    {% if allowance_type.calculation_type == 'fixed' %}
                                    <span class="badge bg-info">{% trans "ثابت" %} (Fixed)</span>
                                    {% else %}
                                    <span class="badge bg-warning">{% trans "نسبة" %} (Percentage)</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if allowance_type.default_amount %}
                                    <span class="text-success fw-bold">{{ allowance_type.default_amount|floatformat:2 }}</span>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if allowance_type.is_active %}
                                    <span class="badge bg-success">{% trans "نشط" %} (Active)</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% trans "غير نشط" %} (Inactive)</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" disabled>
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-list fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">{% trans "لا توجد أنواع بدلات" %} (No Allowance Types Found)</h5>
                    <p class="text-muted">{% trans "ابدأ بإضافة نوع بدل جديد" %} (Start by adding a new allowance type)</p>
                    <a href="{% url 'hr:allowance_type_create' %}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        {% trans "إضافة نوع بدل جديد" %} (Add New Type)
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
