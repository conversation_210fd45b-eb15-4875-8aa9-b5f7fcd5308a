{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ shift.name }} - {% trans "Shift Details" %}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div class="d-flex align-items-center">
        <a href="{% url 'hr:shift_list' %}" class="btn btn-outline-secondary me-3">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h2>{{ shift.name }}</h2>
            <small class="text-muted">{{ shift.get_shift_type_display }}</small>
        </div>
    </div>
    <div class="btn-group">
        <a href="{% url 'hr:shift_edit' shift.id %}" class="btn btn-primary">
            <i class="fas fa-edit me-1"></i>
            {% trans "Edit" %}
        </a>
        <a href="{% url 'hr:shift_delete' shift.id %}" class="btn btn-outline-danger">
            <i class="fas fa-trash me-1"></i>
            {% trans "Delete" %}
        </a>
    </div>
</div>

<div class="row">
    <!-- Shift Information -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    {% trans "Shift Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{% trans "Shift Name" %}:</td>
                                <td>{{ shift.name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Shift Type" %}:</td>
                                <td>
                                    <span class="badge bg-info">{{ shift.get_shift_type_display }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Working Hours" %}:</td>
                                <td>
                                    <strong>{{ shift.start_time|time:"H:i" }} - {{ shift.end_time|time:"H:i" }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        {% trans "Total" %}: {{ shift.total_working_hours }}
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Break Time" %}:</td>
                                <td>
                                    {% if shift.break_start and shift.break_end %}
                                    {{ shift.break_start|time:"H:i" }} - {{ shift.break_end|time:"H:i" }}
                                    <br>
                                    <small class="text-muted">
                                        {% trans "Duration" %}: {{ shift.break_duration }}
                                    </small>
                                    {% else %}
                                    {{ shift.break_duration }}
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">{% trans "Working Days" %}:</td>
                                <td>
                                    {% for day in shift.working_days_list %}
                                        <span class="badge bg-secondary me-1">{{ day }}</span>
                                    {% endfor %}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Grace Period In" %}:</td>
                                <td>{{ shift.grace_period_in }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Grace Period Out" %}:</td>
                                <td>{{ shift.grace_period_out }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">{% trans "Overtime Threshold" %}:</td>
                                <td>{{ shift.overtime_threshold }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <!-- Settings -->
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>{% trans "Settings" %}</h6>
                        <div class="d-flex flex-wrap gap-2">
                            {% if shift.allow_early_checkin %}
                                <span class="badge bg-success">{% trans "Allow Early Check-in" %}</span>
                            {% endif %}
                            {% if shift.allow_late_checkout %}
                                <span class="badge bg-success">{% trans "Allow Late Check-out" %}</span>
                            {% endif %}
                            {% if shift.auto_checkout %}
                                <span class="badge bg-info">
                                    {% trans "Auto Check-out" %} 
                                    {% if shift.auto_checkout_time %}
                                        ({{ shift.auto_checkout_time|time:"H:i" }})
                                    {% endif %}
                                </span>
                            {% endif %}
                            {% if shift.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Employees in this Shift -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    {% trans "Employees in this Shift" %} ({{ employees.count }})
                </h6>
            </div>
            <div class="card-body">
                {% if employees %}
                <div class="list-group list-group-flush">
                    {% for employee in employees %}
                    <div class="list-group-item d-flex align-items-center px-0">
                        {% if employee.photo %}
                        <img src="{{ employee.photo.url }}" alt="{{ employee.full_name }}" 
                             class="rounded-circle me-2" width="32" height="32">
                        {% else %}
                        <div class="bg-secondary rounded-circle me-2 d-flex align-items-center justify-content-center" 
                             style="width: 32px; height: 32px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        {% endif %}
                        <div class="flex-grow-1">
                            <a href="{% url 'hr:employee_detail' employee.id %}" class="text-decoration-none">
                                <strong>{{ employee.full_name }}</strong>
                            </a>
                            <br>
                            <small class="text-muted">{{ employee.position.title }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-users fa-2x text-muted mb-2"></i>
                    <p class="text-muted">{% trans "No employees assigned to this shift" %}</p>
                    <a href="{% url 'hr:employee_list' %}" class="btn btn-sm btn-outline-primary">
                        {% trans "Assign Employees" %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    {% trans "Quick Stats" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-primary mb-0">{{ employees.count }}</h5>
                        <small class="text-muted">{% trans "Total Employees" %}</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-0">{{ shift.total_working_hours }}</h5>
                        <small class="text-muted">{% trans "Working Hours" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
