{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{{ form.name.label }}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small">{{ form.name.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.shift_type.id_for_label }}" class="form-label">{{ form.shift_type.label }}</label>
                            {{ form.shift_type }}
                            {% if form.shift_type.errors %}
                                <div class="text-danger small">{{ form.shift_type.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Working Hours -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.start_time.id_for_label }}" class="form-label">{{ form.start_time.label }}</label>
                            {{ form.start_time }}
                            {% if form.start_time.errors %}
                                <div class="text-danger small">{{ form.start_time.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.end_time.id_for_label }}" class="form-label">{{ form.end_time.label }}</label>
                            {{ form.end_time }}
                            {% if form.end_time.errors %}
                                <div class="text-danger small">{{ form.end_time.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Break Times -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="{{ form.break_start.id_for_label }}" class="form-label">{{ form.break_start.label }}</label>
                            {{ form.break_start }}
                            {% if form.break_start.errors %}
                                <div class="text-danger small">{{ form.break_start.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.break_end.id_for_label }}" class="form-label">{{ form.break_end.label }}</label>
                            {{ form.break_end }}
                            {% if form.break_end.errors %}
                                <div class="text-danger small">{{ form.break_end.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.break_duration.id_for_label }}" class="form-label">{{ form.break_duration.label }}</label>
                            {{ form.break_duration }}
                            {% if form.break_duration.errors %}
                                <div class="text-danger small">{{ form.break_duration.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Working Days -->
                    <div class="mb-3">
                        <label class="form-label">{% trans "Working Days" %}</label>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.monday }}
                                    <label class="form-check-label" for="{{ form.monday.id_for_label }}">
                                        {{ form.monday.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.tuesday }}
                                    <label class="form-check-label" for="{{ form.tuesday.id_for_label }}">
                                        {{ form.tuesday.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.wednesday }}
                                    <label class="form-check-label" for="{{ form.wednesday.id_for_label }}">
                                        {{ form.wednesday.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.thursday }}
                                    <label class="form-check-label" for="{{ form.thursday.id_for_label }}">
                                        {{ form.thursday.label }}
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.friday }}
                                    <label class="form-check-label" for="{{ form.friday.id_for_label }}">
                                        {{ form.friday.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.saturday }}
                                    <label class="form-check-label" for="{{ form.saturday.id_for_label }}">
                                        {{ form.saturday.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    {{ form.sunday }}
                                    <label class="form-check-label" for="{{ form.sunday.id_for_label }}">
                                        {{ form.sunday.label }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Grace Periods -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="{{ form.grace_period_in.id_for_label }}" class="form-label">{{ form.grace_period_in.label }}</label>
                            {{ form.grace_period_in }}
                            {% if form.grace_period_in.errors %}
                                <div class="text-danger small">{{ form.grace_period_in.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.grace_period_out.id_for_label }}" class="form-label">{{ form.grace_period_out.label }}</label>
                            {{ form.grace_period_out }}
                            {% if form.grace_period_out.errors %}
                                <div class="text-danger small">{{ form.grace_period_out.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="{{ form.overtime_threshold.id_for_label }}" class="form-label">{{ form.overtime_threshold.label }}</label>
                            {{ form.overtime_threshold }}
                            {% if form.overtime_threshold.errors %}
                                <div class="text-danger small">{{ form.overtime_threshold.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Additional Settings -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-check">
                                {{ form.allow_early_checkin }}
                                <label class="form-check-label" for="{{ form.allow_early_checkin.id_for_label }}">
                                    {{ form.allow_early_checkin.label }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                {{ form.allow_late_checkout }}
                                <label class="form-check-label" for="{{ form.allow_late_checkout.id_for_label }}">
                                    {{ form.allow_late_checkout.label }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                {{ form.auto_checkout }}
                                <label class="form-check-label" for="{{ form.auto_checkout.id_for_label }}">
                                    {{ form.auto_checkout.label }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Auto Checkout Time -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.auto_checkout_time.id_for_label }}" class="form-label">{{ form.auto_checkout_time.label }}</label>
                            {{ form.auto_checkout_time }}
                            {% if form.auto_checkout_time.errors %}
                                <div class="text-danger small">{{ form.auto_checkout_time.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Form Errors -->
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:shift_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            {% trans "Back to List" %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            {% trans "Save Shift" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Help" %}</h6>
            </div>
            <div class="card-body">
                <h6>{% trans "Shift Configuration Tips" %}</h6>
                <ul class="small">
                    <li>{% trans "Choose appropriate grace periods for check-in and check-out" %}</li>
                    <li>{% trans "Set break times within working hours" %}</li>
                    <li>{% trans "Configure overtime threshold based on company policy" %}</li>
                    <li>{% trans "Enable auto-checkout for better attendance tracking" %}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
