{% extends 'base.html' %}
{% load i18n %}
{% load hr_tags %}

{% block extra_css %}
<style>
    .hr-sidebar {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-y: auto;
        max-height: 100vh;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }

    .hr-sidebar .list-group-item {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        border-radius: 0.5rem;
        margin-bottom: 0.25rem;
    }

    .hr-sidebar .list-group-item:hover,
    .hr-sidebar .list-group-item.active {
        background: rgba(255, 255, 255, 0.1);
        color: white;
        transform: translateX(5px);
        transition: all 0.3s ease;
    }

    .hr-sidebar .list-group-item i {
        width: 20px;
        text-align: center;
    }

    .stat-card {
        border-left: 4px solid;
        transition: transform 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .stat-card.primary {
        border-left-color: #007bff;
    }

    .stat-card.success {
        border-left-color: #28a745;
    }

    .stat-card.warning {
        border-left-color: #ffc107;
    }

    .stat-card.danger {
        border-left-color: #dc3545;
    }

    .employee-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }

    .badge-status {
        font-size: 0.75rem;
    }

    /* Scrollbar styling for sidebar */
    .hr-sidebar::-webkit-scrollbar {
        width: 6px;
    }

    .hr-sidebar::-webkit-scrollbar-track {
        background: rgba(255,255,255,0.1);
    }

    .hr-sidebar::-webkit-scrollbar-thumb {
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
    }

    .hr-sidebar::-webkit-scrollbar-thumb:hover {
        background: rgba(255,255,255,0.5);
    }

    /* Section headers styling */
    .hr-sidebar h6 {
        font-size: 11px;
        font-weight: 600;
        letter-spacing: 1px;
        margin-bottom: 8px;
        margin-top: 20px;
        padding: 0 20px;
    }

    .hr-sidebar h6:first-of-type {
        margin-top: 10px;
    }

    /* Improved list item styling */
    .hr-sidebar .list-group-item {
        font-size: 14px;
        padding: 10px 20px;
        margin: 1px 10px;
    }

    .hr-sidebar .list-group-item.active {
        background: rgba(255,255,255,0.2) !important;
        color: white !important;
        font-weight: 600;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    /* Smart Alerts Styling */
    .module-alert {
        border-left: 4px solid;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
    }

    .alert-info.module-alert {
        border-left-color: #17a2b8;
        background: linear-gradient(135deg, #e8f4f8 0%, #f0f9fb 100%);
    }

    .alert-warning.module-alert {
        border-left-color: #ffc107;
        background: linear-gradient(135deg, #fff8e1 0%, #fffbf0 100%);
    }

    .alert-danger.module-alert {
        border-left-color: #dc3545;
        background: linear-gradient(135deg, #ffeaea 0%, #fff5f5 100%);
    }

    .alert-success.module-alert {
        border-left-color: #28a745;
        background: linear-gradient(135deg, #eafaf1 0%, #f0fdf4 100%);
    }

    .alert-heading {
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .alert .btn-sm {
        font-size: 0.8rem;
        padding: 0.25rem 0.75rem;
    }

    /* Animation for alerts */
    .module-alert {
        animation: slideInDown 0.5s ease-out;
    }

    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
{% endblock %}

{% block sidebar %}
<div class="hr-sidebar p-3">
    <div class="text-center mb-4">
        <h4 class="text-white mb-0">
            <i class="fas fa-users me-2"></i>
            {% trans "Human Resources" %}
        </h4>
        <small class="text-white-50">{% trans "Employee Management System" %}</small>
    </div>

    <div class="list-group list-group-flush">
        <!-- Dashboard -->
        <a href="{% url 'hr:dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
            <i class="fas fa-tachometer-alt me-2"></i>
            {% trans "Dashboard" %}
        </a>

        <!-- Main Features -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Main Features" %}</h6>
            <a href="{% url 'hr:advance_list' %}" class="list-group-item list-group-item-action {% if 'advance' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-hand-holding-usd me-2 text-warning"></i>
                {% trans "السلف" %} (Advances)
            </a>
            <a href="{% url 'hr:custody_list' %}" class="list-group-item list-group-item-action {% if 'custody' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-box me-2 text-info"></i>
                {% trans "العهد" %} (Custodies)
            </a>
        </div>

        <!-- Quick Actions -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Quick Actions" %}</h6>
            <a href="{% url 'hr:employee_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-user-plus me-2"></i>
                {% trans "Add Employee" %}
            </a>
            <a href="{% url 'hr:advance_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-hand-holding-usd me-2"></i>
                {% trans "New Advance Request" %}
            </a>
            <a href="{% url 'hr:custody_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-box me-2"></i>
                {% trans "New Custody Record" %}
            </a>
            <a href="{% url 'hr:commission_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-percentage me-2"></i>
                {% trans "Add Commission" %}
            </a>
            <a href="{% url 'hr:shift_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-clock me-2 text-primary"></i>
                {% trans "إنشاء شفت جديد" %} (Create Shift)
            </a>
            <a href="{% url 'hr:leave_request_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-calendar-check me-2 text-success"></i>
                {% trans "طلب إجازة" %} (Request Leave)
            </a>
            <a href="{% url 'hr:leave_type_create' %}" class="list-group-item list-group-item-action">
                <i class="fas fa-calendar-alt me-2 text-info"></i>
                {% trans "نوع إجازة جديد" %} (New Leave Type)
            </a>
        </div>

        <!-- Employees -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Employee Management" %}</h6>
            <a href="{% url 'hr:employee_list' %}" class="list-group-item list-group-item-action {% if 'employee' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-users me-2"></i>
                {% trans "Employees" %}
            </a>
            <a href="{% url 'hr:department_list' %}" class="list-group-item list-group-item-action {% if 'department' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-sitemap me-2"></i>
                {% trans "Departments" %}
            </a>
            <a href="{% url 'hr:position_list' %}" class="list-group-item list-group-item-action {% if 'position' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-briefcase me-2"></i>
                {% trans "Positions" %}
            </a>
        </div>

        <!-- Attendance -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Attendance" %}</h6>
            <a href="{% url 'hr:attendance_entry' %}" class="list-group-item list-group-item-action {% if 'attendance' in request.resolver_match.url_name and 'entry' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-clock me-2"></i>
                {% trans "Record Attendance" %}
            </a>
            <a href="{% url 'hr:manual_attendance' %}" class="list-group-item list-group-item-action {% if 'manual_attendance' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-edit me-2"></i>
                {% trans "Manual Attendance" %}
            </a>
            <a href="{% url 'hr:gps_attendance' %}" class="list-group-item list-group-item-action {% if 'gps_attendance' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-map-marker-alt me-2"></i>
                {% trans "GPS Attendance" %}
            </a>
            <a href="{% url 'hr:attendance_summary' %}" class="list-group-item list-group-item-action {% if 'attendance_summary' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-list me-2"></i>
                {% trans "Attendance Summary" %}
            </a>
            <a href="{% url 'hr:attendance_report' %}" class="list-group-item list-group-item-action {% if 'attendance' in request.resolver_match.url_name and 'report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-chart-line me-2"></i>
                {% trans "Attendance Reports" %}
            </a>
        </div>

        <!-- Shifts & Leaves -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">
                <i class="fas fa-clock me-2"></i>
                {% trans "الشفتات والإجازات" %} (Shifts & Leaves)
            </h6>
            <a href="{% url 'hr:shift_list' %}" class="list-group-item list-group-item-action {% if 'shift' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-clock me-2 text-primary"></i>
                {% trans "شفتات العمل" %} (Work Shifts)
            </a>
            <a href="{% url 'hr:leave_type_list' %}" class="list-group-item list-group-item-action {% if 'leave_type' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-calendar-alt me-2 text-success"></i>
                {% trans "أنواع الإجازات" %} (Leave Types)
            </a>
            <a href="{% url 'hr:leave_request_list' %}" class="list-group-item list-group-item-action {% if 'leave' in request.resolver_match.url_name and 'leave_type' not in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-calendar-check me-2 text-warning"></i>
                {% trans "طلبات الإجازات" %} (Leave Requests)
            </a>
        </div>

        <!-- Advances & Custodies -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Advances & Custodies" %}</h6>
            <a href="{% url 'hr:advance_list' %}" class="list-group-item list-group-item-action {% if 'advance' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-hand-holding-usd me-2"></i>
                {% trans "Employee Advances" %}
            </a>
            <a href="{% url 'hr:custody_list' %}" class="list-group-item list-group-item-action {% if 'custody' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-box me-2"></i>
                {% trans "Employee Custodies" %}
            </a>
        </div>

        <!-- Payroll & Finance -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Payroll & Finance" %}</h6>
            <a href="{% url 'hr:payroll_list' %}" class="list-group-item list-group-item-action {% if 'payroll' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-money-bill-wave me-2"></i>
                {% trans "Payroll" %}
            </a>
            <a href="{% url 'hr:commission_list' %}" class="list-group-item list-group-item-action {% if 'commission' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-percentage me-2"></i>
                {% trans "Commissions" %}
            </a>
        </div>

        <!-- Sales Teams -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Sales Management" %}</h6>
            <a href="{% url 'hr:sales_team_list' %}" class="list-group-item list-group-item-action {% if 'sales_team' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-users me-2"></i>
                {% trans "Sales Teams" %}
            </a>
            <a href="{% url 'hr:sales_representative_list' %}" class="list-group-item list-group-item-action {% if 'sales_representative' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-user-tie me-2"></i>
                {% trans "Sales Representatives" %}
            </a>
        </div>



        <!-- Legal -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Legal & Compliance" %}</h6>
            <a href="{% url 'hr:contract_list' %}" class="list-group-item list-group-item-action {% if 'contract' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-file-contract me-2"></i>
                {% trans "Contracts" %}
            </a>
            <a href="{% url 'hr:disciplinary_list' %}" class="list-group-item list-group-item-action {% if 'disciplinary' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-gavel me-2"></i>
                {% trans "Disciplinary Actions" %}
            </a>
            <a href="{% url 'hr:investigation_list' %}" class="list-group-item list-group-item-action {% if 'investigation' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-search me-2"></i>
                {% trans "Investigations" %}
            </a>
        </div>

        <!-- Reports -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Reports" %}</h6>
            <a href="{% url 'hr:reports_dashboard' %}" class="list-group-item list-group-item-action {% if 'reports_dashboard' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-chart-bar me-2"></i>
                {% trans "Reports Dashboard" %}
            </a>
            <a href="{% url 'hr:attendance_report_detailed' %}" class="list-group-item list-group-item-action {% if 'attendance_report_detailed' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-clock me-2"></i>
                {% trans "Attendance Report" %}
            </a>
            <a href="{% url 'hr:payroll_report' %}" class="list-group-item list-group-item-action {% if 'payroll_report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-money-bill-wave me-2"></i>
                {% trans "Payroll Report" %}
            </a>
            <a href="{% url 'hr:commission_report' %}" class="list-group-item list-group-item-action {% if 'commission_report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-percentage me-2"></i>
                {% trans "Commission Report" %}
            </a>
            <a href="{% url 'hr:advance_report' %}" class="list-group-item list-group-item-action {% if 'advance_report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-hand-holding-usd me-2"></i>
                {% trans "Advance Report" %}
            </a>
            <a href="{% url 'hr:employee_report' %}" class="list-group-item list-group-item-action {% if 'employee_report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-users me-2"></i>
                {% trans "Employee Report" %}
            </a>
            <a href="{% url 'hr:contract_report' %}" class="list-group-item list-group-item-action {% if 'contract_report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-file-contract me-2"></i>
                {% trans "Contract Report" %}
            </a>
            <a href="{% url 'hr:disciplinary_report' %}" class="list-group-item list-group-item-action {% if 'disciplinary_report' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-gavel me-2"></i>
                {% trans "Disciplinary Report" %}
            </a>
        </div>

        <!-- Settings -->
        <div class="mt-3">
            <h6 class="text-white-50 text-uppercase small mb-2">{% trans "Settings" %}</h6>
            <a href="{% url 'hr:settings' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'settings' %}active{% endif %}">
                <i class="fas fa-cog me-2"></i>
                {% trans "HR Settings" %}
            </a>
            <a href="{% url 'hr:work_schedule_list' %}" class="list-group-item list-group-item-action {% if 'work_schedule' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-calendar-alt me-2"></i>
                {% trans "Work Schedules" %}
            </a>
            <a href="{% url 'hr:attendance_device_list' %}" class="list-group-item list-group-item-action {% if 'attendance_device' in request.resolver_match.url_name %}active{% endif %}">
                <i class="fas fa-fingerprint me-2"></i>
                {% trans "Attendance Devices" %}
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block main_content %}
<div class="container-fluid p-4">
    {% block page_title %}
    <h1 class="h3 mb-4">{% block title %}{% trans "Human Resources" %}{% endblock %}</h1>
    {% endblock %}

    {% if messages %}
    <div class="row mb-3">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% block content %}
    {% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Smart Alert Management
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide non-critical alerts after 10 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert-info.module-alert, .alert-success.module-alert');
            alerts.forEach(function(alert) {
                if (alert.style.display !== 'none') {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 10000);

        // Keep critical alerts (warning, danger) visible longer
        setTimeout(function() {
            const criticalAlerts = document.querySelectorAll('.alert-warning.module-alert, .alert-danger.module-alert');
            criticalAlerts.forEach(function(alert) {
                if (alert.style.display !== 'none') {
                    alert.classList.add('pulse-animation');
                }
            });
        }, 15000);

        // Alert interaction tracking
        document.querySelectorAll('.module-alert .alert-link, .module-alert .btn').forEach(function(link) {
            link.addEventListener('click', function() {
                // Track alert interactions for analytics
                const alertType = this.closest('.alert').className.match(/alert-(\w+)/)[1];
                console.log('Alert interaction:', alertType, this.textContent.trim());
            });
        });

        // Confirm delete actions
        document.querySelectorAll('.btn-danger[href*="delete"]').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                if (!confirm('{% trans "Are you sure you want to delete this item?" %}')) {
                    e.preventDefault();
                }
            });
        });

        // Tooltip initialization
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Smart form validation hints
        const forms = document.querySelectorAll('form');
        forms.forEach(function(form) {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let hasErrors = false;

                requiredFields.forEach(function(field) {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        hasErrors = true;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    // Show smart error message
                    showSmartAlert('warning', '{% trans "Please fill in all required fields" %}', '{% trans "Some required information is missing. Please check the highlighted fields." %}');
                }
            });
        });
    });

    // Smart alert function
    function showSmartAlert(type, title, message, duration = 5000) {
        const alertContainer = document.getElementById('hr-alerts-container') || document.body;
        const alertId = 'smart-alert-' + Date.now();

        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show module-alert">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-3">
                        <i class="fas fa-${type === 'warning' ? 'exclamation-triangle' : 'info-circle'} fa-lg text-${type}"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">${title}</h6>
                        <p class="mb-0">${message}</p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        alertContainer.insertAdjacentHTML('afterbegin', alertHTML);

        // Auto-remove after duration
        setTimeout(function() {
            const alert = document.getElementById(alertId);
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, duration);
    }

    // Add pulse animation CSS
    const style = document.createElement('style');
    style.textContent = `
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            50% { box-shadow: 0 4px 20px rgba(255,193,7,0.3); }
            100% { box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}
