{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-undo me-2"></i>
        {{ title }}
    </h1>
    <a href="{% url 'hr:custody_detail' custody.id %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-1"></i>
        {% trans "Back to Details" %}
    </a>
</div>
{% endblock %}

{% block content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="javascript:history.back()" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back" %}
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ title }}</h5>
            </div>
            <div class="card-body">
                <!-- Custody Summary -->
                <div class="alert alert-info">
                    <h6 class="alert-heading">{% trans "Custody Information" %}</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "Custody Number" %}:</strong> {{ custody.custody_number }}</p>
                            <p class="mb-1"><strong>{% trans "Item" %}:</strong> {{ custody.item_name }}</p>
                            <p class="mb-1"><strong>{% trans "Employee" %}:</strong> {{ custody.employee.full_name }}</p>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-1"><strong>{% trans "Delivery Date" %}:</strong> {{ custody.delivery_date|date:"d/m/Y" }}</p>
                            <p class="mb-1"><strong>{% trans "Total Value" %}:</strong> {{ custody.total_value|floatformat:2 }}</p>
                            {% if custody.expected_return_date %}
                            <p class="mb-1"><strong>{% trans "Expected Return" %}:</strong> {{ custody.expected_return_date|date:"d/m/Y" }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.actual_return_date.id_for_label }}" class="form-label">
                                    {{ form.actual_return_date.label }}
                                    <span class="text-danger">*</span>
                                </label>
                                {{ form.actual_return_date }}
                                {% if form.actual_return_date.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.actual_return_date.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.received_by.id_for_label }}" class="form-label">
                                    {{ form.received_by.label }}
                                </label>
                                {{ form.received_by }}
                                {% if form.received_by.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.received_by.errors.0 }}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.status.id_for_label }}" class="form-label">
                            {{ form.status.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.status }}
                        {% if form.status.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.status.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "Select the condition of the item upon return" %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.condition_on_return.id_for_label }}" class="form-label">
                            {{ form.condition_on_return.label }}
                            <span class="text-danger">*</span>
                        </label>
                        {{ form.condition_on_return }}
                        {% if form.condition_on_return.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.condition_on_return.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "Describe the condition of the item when returned" %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.notes.errors.0 }}
                        </div>
                        {% endif %}
                        <div class="form-text">
                            {% trans "Any additional notes about the return process" %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'hr:custody_detail' custody.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-undo me-2"></i>
                            {% trans "Return Custody" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Original Condition -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Original Condition" %}</h5>
            </div>
            <div class="card-body">
                <h6>{% trans "Condition on Delivery" %}:</h6>
                <p class="text-muted">{{ custody.condition_on_delivery }}</p>
                
                {% if custody.item_description %}
                <h6 class="mt-3">{% trans "Item Description" %}:</h6>
                <p class="text-muted">{{ custody.item_description }}</p>
                {% endif %}
                
                {% if custody.serial_number %}
                <h6 class="mt-3">{% trans "Serial Number" %}:</h6>
                <p class="text-muted">{{ custody.serial_number }}</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Return Guidelines -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% trans "Return Guidelines" %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h6 class="alert-heading">{% trans "Important Notes" %}</h6>
                    <ul class="mb-0">
                        <li>{% trans "Inspect the item carefully before marking as returned" %}</li>
                        <li>{% trans "Document any damage or missing parts" %}</li>
                        <li>{% trans "Take photos if necessary for documentation" %}</li>
                        <li>{% trans "Ensure all accessories are returned" %}</li>
                    </ul>
                </div>
                
                <div class="mt-3">
                    <h6>{% trans "Status Options" %}:</h6>
                    <ul class="list-unstyled">
                        <li><span class="badge bg-primary me-2">{% trans "Returned" %}</span> {% trans "Item returned in good condition" %}</li>
                        <li><span class="badge bg-warning me-2">{% trans "Damaged" %}</span> {% trans "Item returned with damage" %}</li>
                        <li><span class="badge bg-danger me-2">{% trans "Lost" %}</span> {% trans "Item was lost and cannot be returned" %}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add form classes
    const formControls = document.querySelectorAll('input, select, textarea');
    formControls.forEach(function(control) {
        control.classList.add('form-control');
    });
    
    // Status change handler
    const statusField = document.getElementById('{{ form.status.id_for_label }}');
    const conditionField = document.getElementById('{{ form.condition_on_return.id_for_label }}');
    
    if (statusField && conditionField) {
        statusField.addEventListener('change', function() {
            const status = this.value;
            if (status === 'lost') {
                conditionField.value = '{% trans "Item was lost and could not be returned" %}';
                conditionField.readOnly = true;
            } else if (status === 'damaged') {
                conditionField.value = '{% trans "Item returned with damage - " %}';
                conditionField.readOnly = false;
                conditionField.focus();
            } else {
                conditionField.value = '{% trans "Item returned in good condition" %}';
                conditionField.readOnly = false;
            }
        });
    }
});
</script>
{% endblock %}
