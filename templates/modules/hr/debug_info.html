{% extends 'modules/hr/base.html' %}
{% load i18n %}

{% block title %}{% trans "Debug Information" %}{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">{% trans "Debug Information" %}</h5>
    </div>
    <div class="card-body">
        <h6>{% trans "Session Information" %}:</h6>
        <ul>
            <li><strong>{% trans "Active Company ID" %}:</strong> {{ request.session.active_company_id|default:"Not Set" }}</li>
            <li><strong>{% trans "Active Branch ID" %}:</strong> {{ request.session.active_branch_id|default:"Not Set" }}</li>
            <li><strong>{% trans "User" %}:</strong> {{ request.user.username }}</li>
            <li><strong>{% trans "User ID" %}:</strong> {{ request.user.id }}</li>
        </ul>

        <h6 class="mt-4">{% trans "Available Companies" %}:</h6>
        {% if companies %}
        <ul>
            {% for company in companies %}
            <li>{{ company.name }} (ID: {{ company.id }})</li>
            {% endfor %}
        </ul>
        {% else %}
        <p class="text-muted">{% trans "No companies found" %}</p>
        {% endif %}

        <h6 class="mt-4">{% trans "URL Information" %}:</h6>
        <ul>
            <li><strong>{% trans "Current URL" %}:</strong> {{ request.get_full_path }}</li>
            <li><strong>{% trans "URL Name" %}:</strong> {{ request.resolver_match.url_name }}</li>
            <li><strong>{% trans "App Name" %}:</strong> {{ request.resolver_match.app_name }}</li>
        </ul>

        <h6 class="mt-4">{% trans "Quick Actions" %}:</h6>
        <div class="btn-group" role="group">
            <a href="{% url 'companies:company_list' %}" class="btn btn-primary">
                <i class="fas fa-building me-2"></i>
                {% trans "Select Company" %}
            </a>
            <a href="{% url 'hr:dashboard' %}" class="btn btn-secondary">
                <i class="fas fa-tachometer-alt me-2"></i>
                {% trans "HR Dashboard" %}
            </a>
            <a href="{% url 'hr:advance_list' %}" class="btn btn-info">
                <i class="fas fa-hand-holding-usd me-2"></i>
                {% trans "Advances" %}
            </a>
            <a href="{% url 'hr:custody_list' %}" class="btn btn-warning">
                <i class="fas fa-box me-2"></i>
                {% trans "Custodies" %}
            </a>
        </div>
    </div>
</div>
{% endblock %}
