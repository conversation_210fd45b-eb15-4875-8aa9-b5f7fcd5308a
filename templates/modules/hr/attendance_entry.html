{% extends 'modules/hr/base.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block title %}{% trans "Attendance Entry" %}{% endblock %}

{% block page_title %}
<div class="d-flex justify-content-between align-items-center">
    <h1 class="h3 mb-0">
        <i class="fas fa-clock me-2"></i>
        {% trans "Attendance Entry" %}
    </h1>
    <div class="btn-group">
        <a href="{% url 'hr:attendance_report' %}" class="btn btn-outline-primary">
            <i class="fas fa-chart-line me-1"></i>
            {% trans "View Reports" %}
        </a>
        <a href="{% url 'hr:manual_attendance' %}" class="btn btn-outline-secondary">
            <i class="fas fa-edit me-1"></i>
            {% trans "Manual Entry" %}
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Quick Attendance Entry -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-stopwatch me-2"></i>
                    {% trans "Quick Attendance Entry" %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" id="attendance-form">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-12">
                            {{ form.employee|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.date|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.time|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            {{ form.attendance_type|as_crispy_field }}
                        </div>
                        <div class="col-md-6">
                            {{ form.entry_method|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            {{ form.device|as_crispy_field }}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            {{ form.notes|as_crispy_field }}
                        </div>
                    </div>
                    
                    <!-- GPS Location (if enabled) -->
                    <div class="row" id="gps-section" style="display: none;">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                {% trans "GPS location will be captured automatically" %}
                                <div id="location-status" class="mt-2"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-clock me-2"></i>
                            {% trans "Record Attendance" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">{% trans "Quick Actions" %}</h6>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6">
                        <button class="btn btn-success w-100" onclick="quickEntry('check_in')">
                            <i class="fas fa-sign-in-alt me-1"></i>
                            {% trans "Check In" %}
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-danger w-100" onclick="quickEntry('check_out')">
                            <i class="fas fa-sign-out-alt me-1"></i>
                            {% trans "Check Out" %}
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-warning w-100" onclick="quickEntry('break_start')">
                            <i class="fas fa-coffee me-1"></i>
                            {% trans "Break Start" %}
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-info w-100" onclick="quickEntry('break_end')">
                            <i class="fas fa-play me-1"></i>
                            {% trans "Break End" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Attendance -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    {% trans "Recent Attendance Records" %}
                </h5>
            </div>
            <div class="card-body p-0">
                {% if recent_attendance %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>{% trans "Employee" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Time" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Method" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for attendance in recent_attendance %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if attendance.employee.photo %}
                                        <img src="{{ attendance.employee.photo.url }}" alt="{{ attendance.employee.full_name }}" 
                                             class="rounded-circle me-2" width="30" height="30">
                                        {% else %}
                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                             style="width: 30px; height: 30px;">
                                            <i class="fas fa-user text-white small"></i>
                                        </div>
                                        {% endif %}
                                        <div>
                                            <small class="fw-bold">{{ attendance.employee.full_name }}</small>
                                            <br><small class="text-muted">{{ attendance.employee.employee_id }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ attendance.date|date:"M d" }}</td>
                                <td>{{ attendance.time|time:"H:i" }}</td>
                                <td>
                                    {% if attendance.attendance_type == 'check_in' %}
                                    <span class="badge bg-success">{% trans "Check In" %}</span>
                                    {% elif attendance.attendance_type == 'check_out' %}
                                    <span class="badge bg-danger">{% trans "Check Out" %}</span>
                                    {% elif attendance.attendance_type == 'break_start' %}
                                    <span class="badge bg-warning">{% trans "Break Start" %}</span>
                                    {% elif attendance.attendance_type == 'break_end' %}
                                    <span class="badge bg-info">{% trans "Break End" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if attendance.entry_method == 'manual' %}
                                    <i class="fas fa-edit text-primary" title="{% trans 'Manual' %}"></i>
                                    {% elif attendance.entry_method == 'biometric' %}
                                    <i class="fas fa-fingerprint text-success" title="{% trans 'Biometric' %}"></i>
                                    {% elif attendance.entry_method == 'gps' %}
                                    <i class="fas fa-map-marker-alt text-info" title="{% trans 'GPS' %}"></i>
                                    {% elif attendance.entry_method == 'web' %}
                                    <i class="fas fa-globe text-secondary" title="{% trans 'Web' %}"></i>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">{% trans "No recent attendance records" %}</h6>
                    <p class="text-muted small">{% trans "Start recording attendance to see recent entries here" %}</p>
                </div>
                {% endif %}
            </div>
            {% if recent_attendance %}
            <div class="card-footer text-center">
                <a href="{% url 'hr:attendance_report' %}" class="btn btn-sm btn-outline-primary">
                    {% trans "View All Records" %}
                </a>
            </div>
            {% endif %}
        </div>
        
        <!-- Today's Summary -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    {% trans "Today's Summary" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-3">
                        <div class="border-end">
                            <h5 class="text-success mb-0" id="present-count">0</h5>
                            <small class="text-muted">{% trans "Present" %}</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border-end">
                            <h5 class="text-danger mb-0" id="absent-count">0</h5>
                            <small class="text-muted">{% trans "Absent" %}</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="border-end">
                            <h5 class="text-warning mb-0" id="late-count">0</h5>
                            <small class="text-muted">{% trans "Late" %}</small>
                        </div>
                    </div>
                    <div class="col-3">
                        <h5 class="text-info mb-0" id="break-count">0</h5>
                        <small class="text-muted">{% trans "On Break" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Current time update
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-GB', { hour12: false });
        const dateString = now.toISOString().split('T')[0];
        
        document.getElementById('id_time').value = timeString.substring(0, 5);
        document.getElementById('id_date').value = dateString;
    }
    
    // Update time every second
    setInterval(updateCurrentTime, 1000);
    updateCurrentTime();
    
    // GPS functionality
    function enableGPS() {
        const gpsSection = document.getElementById('gps-section');
        const locationStatus = document.getElementById('location-status');
        
        if (navigator.geolocation) {
            gpsSection.style.display = 'block';
            locationStatus.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>{% trans "Getting location..." %}';
            
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    document.getElementById('id_latitude').value = position.coords.latitude;
                    document.getElementById('id_longitude').value = position.coords.longitude;
                    document.getElementById('id_location_accuracy').value = position.coords.accuracy;
                    
                    locationStatus.innerHTML = '<i class="fas fa-check-circle text-success me-1"></i>{% trans "Location captured successfully" %}';
                },
                function(error) {
                    locationStatus.innerHTML = '<i class="fas fa-exclamation-triangle text-warning me-1"></i>{% trans "Could not get location" %}';
                }
            );
        } else {
            locationStatus.innerHTML = '<i class="fas fa-times-circle text-danger me-1"></i>{% trans "GPS not supported" %}';
        }
    }
    
    // Entry method change handler
    document.getElementById('id_entry_method').addEventListener('change', function() {
        if (this.value === 'gps') {
            enableGPS();
        } else {
            document.getElementById('gps-section').style.display = 'none';
        }
    });
    
    // Quick entry functions
    function quickEntry(type) {
        document.getElementById('id_attendance_type').value = type;
        document.getElementById('id_entry_method').value = 'web';
        
        // Auto-submit if employee is selected
        const employeeField = document.getElementById('id_employee');
        if (employeeField.value) {
            document.getElementById('attendance-form').submit();
        } else {
            alert('{% trans "Please select an employee first" %}');
            employeeField.focus();
        }
    }
    
    // Form submission with validation
    document.getElementById('attendance-form').addEventListener('submit', function(e) {
        const employee = document.getElementById('id_employee').value;
        const attendanceType = document.getElementById('id_attendance_type').value;
        
        if (!employee) {
            e.preventDefault();
            alert('{% trans "Please select an employee" %}');
            return;
        }
        
        if (!attendanceType) {
            e.preventDefault();
            alert('{% trans "Please select attendance type" %}');
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>{% trans "Recording..." %}';
        submitBtn.disabled = true;
        
        // Re-enable button after 3 seconds (in case of error)
        setTimeout(function() {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 3000);
    });
    
    // Load today's summary (mock data for now)
    function loadTodaySummary() {
        // This would typically be an AJAX call to get real data
        document.getElementById('present-count').textContent = '8';
        document.getElementById('absent-count').textContent = '2';
        document.getElementById('late-count').textContent = '1';
        document.getElementById('break-count').textContent = '3';
    }
    
    // Load summary on page load
    loadTodaySummary();
</script>
{% endblock %}
