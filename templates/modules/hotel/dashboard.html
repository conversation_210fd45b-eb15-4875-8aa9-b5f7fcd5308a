{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة الفنادق{% else %}{% trans "Hotel Management" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الفنادق{% else %}{% trans "Hotel" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-hotel{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إدارة الفنادق{% else %}{% trans "Hotel Management" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة عمليات الفندق بكفاءة. التعامل مع الحجوزات، وتخصيص الغرف، وخدمات الضيوف، والفواتير في مكان واحد.{% else %}{% trans "Manage your hotel operations efficiently. Handle reservations, room assignments, guest services, and billing in one place." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-info{% endblock %}
{% block quick_actions_header_class %}bg-info{% endblock %}

{% block module_alert %}
<div class="alert alert-info alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-hotel fa-2x text-info"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Hotel Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your hotel operations. Use the navigation menu to access different hotel functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% trans "Reservations" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-door-open me-2"></i> {% trans "Rooms" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% trans "Guests" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-concierge-bell me-2"></i> {% trans "Services" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice-dollar me-2"></i> {% trans "Billing" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-info">
        <i class="fas fa-calendar-plus me-2"></i> {% trans "New Reservation" %}
    </button>
    <button class="btn btn-outline-info">
        <i class="fas fa-user-plus me-2"></i> {% trans "Check-in Guest" %}
    </button>
    <button class="btn btn-outline-info">
        <i class="fas fa-user-minus me-2"></i> {% trans "Check-out Guest" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-door-open fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Available Rooms" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-bed fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Occupied Rooms" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Reservations Today" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Occupancy Rate" %}</h6>
                    <h3 class="card-title mb-0">0%</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Hotel Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>

        <h5 class="mt-4 mb-3">{% trans "Today's Check-ins" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Reservation #" %}</th>
                        <th>{% trans "Guest" %}</th>
                        <th>{% trans "Room" %}</th>
                        <th>{% trans "Check-in" %}</th>
                        <th>{% trans "Check-out" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No check-ins scheduled for today" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Room Status" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No room data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Upcoming Reservations" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No upcoming reservations" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Hotel Module loaded');
    });
</script>
{% endblock %}
