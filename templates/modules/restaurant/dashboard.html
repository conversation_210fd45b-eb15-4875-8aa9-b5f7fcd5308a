{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Restaurant & Cafe Management" %}{% endblock %}
{% block module_name %}{% trans "Restaurant" %}{% endblock %}
{% block module_icon %}fas fa-utensils{% endblock %}
{% block module_header %}{% trans "Restaurant & Cafe Management" %}{% endblock %}
{% block module_description %}{% trans "Run your food service business smoothly. Manage orders, tables, kitchen operations, and customer service all in one system." %}{% endblock %}

{% block sidebar_header_class %}bg-warning{% endblock %}
{% block quick_actions_header_class %}bg-warning{% endblock %}

{% block module_alert %}
<div class="alert alert-warning alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-utensils fa-2x text-warning"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Restaurant Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your restaurant operations. Use the navigation menu to access different restaurant functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-clipboard-list me-2"></i> {% trans "Orders" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chair me-2"></i> {% trans "Tables" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-hamburger me-2"></i> {% trans "Menu Items" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-utensils me-2"></i> {% trans "Kitchen Display" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% trans "Customers" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-warning">
        <i class="fas fa-plus me-2"></i> {% trans "New Order" %}
    </button>
    <button class="btn btn-outline-warning">
        <i class="fas fa-hamburger me-2"></i> {% trans "Manage Menu" %}
    </button>
    <button class="btn btn-outline-warning">
        <i class="fas fa-chair me-2"></i> {% trans "Table Layout" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Active Orders" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-chair fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Available Tables" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-hamburger fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Menu Items" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Today's Revenue" %}</h6>
                    <h3 class="card-title mb-0">$0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Restaurant Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Active Orders" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Order #" %}</th>
                        <th>{% trans "Table" %}</th>
                        <th>{% trans "Server" %}</th>
                        <th>{% trans "Items" %}</th>
                        <th>{% trans "Time" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No active orders" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Popular Items" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No sales data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Table Status" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No table data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Restaurant Module loaded');
    });
</script>
{% endblock %}
