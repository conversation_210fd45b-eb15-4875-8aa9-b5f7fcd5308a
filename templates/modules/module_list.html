{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة الوحدات والتراخيص{% else %}{% trans "Module & License Management" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الوحدات المتاحة{% else %}{% trans "Available Modules" %}{% endif %}</h5>
                <div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary active" id="grid-view-btn">
                            <i class="fas fa-th-large"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="list-view-btn">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                    <div class="btn-group ms-2" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-filter me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تصفية{% else %}{% trans "Filter" %}{% endif %}
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item filter-item" href="#" data-filter="all">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</a></li>
                            <li><a class="dropdown-item filter-item" href="#" data-filter="installed">{% if LANGUAGE_CODE == 'ar' %}المثبتة{% else %}{% trans "Installed" %}{% endif %}</a></li>
                            <li><a class="dropdown-item filter-item" href="#" data-filter="not-installed">{% if LANGUAGE_CODE == 'ar' %}غير المثبتة{% else %}{% trans "Not Installed" %}{% endif %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item filter-item" href="#" data-filter="core">{% if LANGUAGE_CODE == 'ar' %}الأساسية{% else %}{% trans "Core" %}{% endif %}</a></li>
                            <li><a class="dropdown-item filter-item" href="#" data-filter="extra">{% if LANGUAGE_CODE == 'ar' %}الإضافية{% else %}{% trans "Extra" %}{% endif %}</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="module-search" placeholder="{% if LANGUAGE_CODE == 'ar' %}بحث عن الوحدات...{% else %}{% trans 'Search modules...' %}{% endif %}">
                </div>
                
                <!-- Grid View (Default) -->
                <div id="grid-view" class="row">
                    {% for module in modules %}
                    <div class="col-md-4 col-lg-3 mb-4 module-card"
                         data-name="{{ module.name|lower }}"
                         data-code="{{ module.code|lower }}"
                         data-installed="{% if module.id in installed_modules %}yes{% else %}no{% endif %}"
                         data-core="{% if module.is_core %}yes{% else %}no{% endif %}">
                        <div class="card h-100 shadow-sm module-card-inner {% if module.id in installed_modules and installed_modules.module.id.is_active %}border-success{% endif %}">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">{{ module.name }}</h6>
                                {% if module.id in installed_modules %}
                                    {% if installed_modules.module.id.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفعّل{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}معطّل{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                {% endif %}
                            </div>
                            <div class="card-body text-center p-4">
                                <div class="module-icon-wrapper mb-3">
                                    <i class="{{ module.icon|default:'fas fa-puzzle-piece' }} fa-3x module-icon"></i>
                                </div>
                                <p class="card-text small">{{ module.description|truncatechars:100 }}</p>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <small class="text-muted">v{{ module.version }}</small>
                                    {% if module.is_core %}
                                    <span class="badge bg-primary">{% if LANGUAGE_CODE == 'ar' %}أساسي{% else %}{% trans "Core" %}{% endif %}</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="card-footer bg-light">
                                <div class="d-grid gap-2">
                                    {% if module.id in installed_modules %}
                                        {% if installed_modules.module.id.is_active %}
                                        <a href="{% url 'modules:module_dashboard' module.code %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-external-link-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}فتح{% else %}{% trans "Open" %}{% endif %}
                                        </a>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'modules:module_update' module.code %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-sync-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تحديث{% else %}{% trans "Update" %}{% endif %}
                                            </a>
                                            <a href="{% url 'modules:module_uninstall' module.code %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                            </a>
                                        </div>
                                        {% else %}
                                        <a href="{% url 'modules:module_install' module.code %}" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-play me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تفعيل{% else %}{% trans "Activate" %}{% endif %}
                                        </a>
                                        <a href="{% url 'modules:module_uninstall' module.code %}" class="btn btn-sm btn-outline-danger">
                                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                        </a>
                                        {% endif %}
                                    {% else %}
                                    <a href="{% url 'modules:module_install' module.code %}" class="btn btn-sm btn-success">
                                        <i class="fas fa-download me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تثبيت{% else %}{% trans "Install" %}{% endif %}
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}لا توجد وحدات متاحة حالياً.{% else %}{% trans "No modules available at the moment." %}{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- List View (Alternative) -->
                <div id="list-view" class="d-none">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الوحدة{% else %}{% trans "Module" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الإصدار{% else %}{% trans "Version" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for module in modules %}
                                <tr class="module-row"
                                    data-name="{{ module.name|lower }}"
                                    data-code="{{ module.code|lower }}"
                                    data-installed="{% if module.id in installed_modules %}yes{% else %}no{% endif %}"
                                    data-core="{% if module.is_core %}yes{% else %}no{% endif %}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="{{ module.icon|default:'fas fa-puzzle-piece' }} fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ module.name }}</h6>
                                                <small class="text-muted">{{ module.code }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ module.description|truncatechars:100 }}</td>
                                    <td>{{ module.version }}</td>
                                    <td>
                                        {% if module.id in installed_modules %}
                                            {% if installed_modules.module.id.is_active %}
                                            <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفعّل{% else %}{% trans "Active" %}{% endif %}</span>
                                            {% else %}
                                            <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}معطّل{% else %}{% trans "Inactive" %}{% endif %}</span>
                                            {% endif %}
                                        {% else %}
                                        <span class="badge bg-light text-dark">{% if LANGUAGE_CODE == 'ar' %}غير مثبت{% else %}{% trans "Not Installed" %}{% endif %}</span>
                                        {% endif %}
                                        
                                        {% if module.is_core %}
                                        <span class="badge bg-primary ms-1">{% if LANGUAGE_CODE == 'ar' %}أساسي{% else %}{% trans "Core" %}{% endif %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if module.id in installed_modules %}
                                            {% if installed_modules.module.id.is_active %}
                                            <a href="{% url 'modules:module_dashboard' module.code %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}فتح{% else %}{% trans "Open" %}{% endif %}
                                            </a>
                                            <a href="{% url 'modules:module_update' module.code %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-sync-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تحديث{% else %}{% trans "Update" %}{% endif %}
                                            </a>
                                            <a href="{% url 'modules:module_uninstall' module.code %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                            </a>
                                            {% else %}
                                            <a href="{% url 'modules:module_install' module.code %}" class="btn btn-sm btn-outline-success">
                                                <i class="fas fa-play me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تفعيل{% else %}{% trans "Activate" %}{% endif %}
                                            </a>
                                            <a href="{% url 'modules:module_uninstall' module.code %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                            </a>
                                            {% endif %}
                                        {% else %}
                                        <a href="{% url 'modules:module_install' module.code %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-download me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تثبيت{% else %}{% trans "Install" %}{% endif %}
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تراخيص الشركة{% else %}{% trans "Company Licenses" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                {% if request.user.company %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تراخيص الوحدات للشركة <strong>{{ request.user.company.name }}</strong>. يمكنك تثبيت وإدارة الوحدات المرخصة لشركتك.
                    {% else %}
                    {% trans "Module licenses for company" %} <strong>{{ request.user.company.name }}</strong>. {% trans "You can install and manage modules licensed for your company." %}
                    {% endif %}
                </div>
                
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوحدة{% else %}{% trans "Module" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ التثبيت{% else %}{% trans "Installation Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإصدار المثبت{% else %}{% trans "Installed Version" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for cm in company_modules %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <i class="{{ cm.module.icon|default:'fas fa-puzzle-piece' }}"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ cm.module.name }}</h6>
                                            <small class="text-muted">{{ cm.module.code }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ cm.installed_at|date:"Y-m-d H:i" }}</td>
                                <td>{{ cm.installed_version }}</td>
                                <td>
                                    {% if cm.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفعّل{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}معطّل{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center">
                                    {% if LANGUAGE_CODE == 'ar' %}لا توجد وحدات مثبتة حالياً.{% else %}{% trans "No modules installed yet." %}{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    أنت غير مرتبط بأي شركة حالياً. يرجى الاتصال بمسؤول النظام لتعيينك إلى شركة.
                    {% else %}
                    {% trans "You are not associated with any company. Please contact the system administrator to assign you to a company." %}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Toggle between grid and list view
        $('#grid-view-btn').click(function() {
            $('#grid-view').removeClass('d-none');
            $('#list-view').addClass('d-none');
            $(this).addClass('active');
            $('#list-view-btn').removeClass('active');
        });
        
        $('#list-view-btn').click(function() {
            $('#list-view').removeClass('d-none');
            $('#grid-view').addClass('d-none');
            $(this).addClass('active');
            $('#grid-view-btn').removeClass('active');
        });
        
        // Search functionality
        $('#module-search').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.module-card, .module-row').filter(function() {
                $(this).toggle(
                    $(this).data('name').toLowerCase().indexOf(value) > -1 ||
                    $(this).data('code').toLowerCase().indexOf(value) > -1
                );
            });
        });
        
        // Filter functionality
        $('.filter-item').click(function(e) {
            e.preventDefault();
            var filter = $(this).data('filter');
            
            $('.module-card, .module-row').show();
            
            if (filter === 'installed') {
                $('.module-card[data-installed="no"], .module-row[data-installed="no"]').hide();
            } else if (filter === 'not-installed') {
                $('.module-card[data-installed="yes"], .module-row[data-installed="yes"]').hide();
            } else if (filter === 'core') {
                $('.module-card[data-core="no"], .module-row[data-core="no"]').hide();
            } else if (filter === 'extra') {
                $('.module-card[data-core="yes"], .module-row[data-core="yes"]').hide();
            }
        });
    });
</script>
{% endblock %}
