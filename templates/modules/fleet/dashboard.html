{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Fleet Management" %}{% endblock %}
{% block module_name %}{% trans "Fleet" %}{% endblock %}
{% block module_icon %}fas fa-truck{% endblock %}
{% block module_header %}{% trans "Fleet Management" %}{% endblock %}
{% block module_description %}{% trans "Manage your vehicle fleet efficiently. Track vehicles, maintenance schedules, fuel consumption, and driver assignments." %}{% endblock %}

{% block sidebar_header_class %}bg-dark{% endblock %}
{% block quick_actions_header_class %}bg-dark{% endblock %}

{% block module_alert %}
<div class="alert alert-dark alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-truck fa-2x text-dark"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Fleet Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your vehicle fleet operations. Use the navigation menu to access different fleet functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-car me-2"></i> {% trans "Vehicles" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-tools me-2"></i> {% trans "Maintenance" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-gas-pump me-2"></i> {% trans "Fuel Logs" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-road me-2"></i> {% trans "Trips" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-user-tie me-2"></i> {% trans "Drivers" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice-dollar me-2"></i> {% trans "Costs" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-dark">
        <i class="fas fa-plus me-2"></i> {% trans "Add Vehicle" %}
    </button>
    <button class="btn btn-outline-dark">
        <i class="fas fa-tools me-2"></i> {% trans "Schedule Maintenance" %}
    </button>
    <button class="btn btn-outline-dark">
        <i class="fas fa-file-alt me-2"></i> {% trans "Generate Reports" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-dark h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-dark text-white rounded p-3">
                        <i class="fas fa-car fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Total Vehicles" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Available Vehicles" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "In Maintenance" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Maintenance Due" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Fleet Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Vehicle List" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Vehicle" %}</th>
                        <th>{% trans "License Plate" %}</th>
                        <th>{% trans "Model" %}</th>
                        <th>{% trans "Driver" %}</th>
                        <th>{% trans "Odometer" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No vehicles found" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Upcoming Maintenance" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No upcoming maintenance scheduled" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Fuel Consumption" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No fuel consumption data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Fleet Module loaded');
    });
</script>
{% endblock %}
