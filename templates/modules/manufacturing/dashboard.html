{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Manufacturing Management" %}{% endblock %}
{% block module_name %}{% trans "Manufacturing" %}{% endblock %}
{% block module_icon %}fas fa-industry{% endblock %}
{% block module_header %}{% trans "Manufacturing Management" %}{% endblock %}
{% block module_description %}{% trans "Optimize your production processes. Manage production orders, bill of materials, work centers, and manufacturing operations." %}{% endblock %}

{% block sidebar_header_class %}bg-secondary{% endblock %}
{% block quick_actions_header_class %}bg-secondary{% endblock %}

{% block module_alert %}
<div class="alert alert-secondary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-industry fa-2x text-secondary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Manufacturing Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your manufacturing operations. Use the navigation menu to access different manufacturing functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-clipboard-list me-2"></i> {% trans "Production Orders" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-list-ol me-2"></i> {% trans "Bill of Materials" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-cogs me-2"></i> {% trans "Work Centers" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-tools me-2"></i> {% trans "Maintenance" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-secondary">
        <i class="fas fa-plus me-2"></i> {% trans "New Production Order" %}
    </button>
    <button class="btn btn-outline-secondary">
        <i class="fas fa-list-ol me-2"></i> {% trans "Create Bill of Materials" %}
    </button>
    <button class="btn btn-outline-secondary">
        <i class="fas fa-file-alt me-2"></i> {% trans "Generate Reports" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-secondary h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-secondary text-white rounded p-3">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Production Orders" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Completed Orders" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Material Shortages" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Capacity Utilization" %}</h6>
                    <h3 class="card-title mb-0">0%</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Manufacturing Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Active Production Orders" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Order #" %}</th>
                        <th>{% trans "Product" %}</th>
                        <th>{% trans "Quantity" %}</th>
                        <th>{% trans "Start Date" %}</th>
                        <th>{% trans "End Date" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No active production orders" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Work Center Utilization" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No utilization data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Material Requirements" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No material data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Manufacturing Module loaded');
    });
</script>
{% endblock %}
