{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Document Management" %}{% endblock %}
{% block module_name %}{% trans "Documents" %}{% endblock %}
{% block module_icon %}fas fa-file-archive{% endblock %}
{% block module_header %}{% trans "Document Management" %}{% endblock %}
{% block module_description %}{% trans "Organize and secure your documents. Store, categorize, search, and share documents with controlled access and version tracking." %}{% endblock %}

{% block sidebar_header_class %}bg-secondary{% endblock %}
{% block quick_actions_header_class %}bg-secondary{% endblock %}

{% block module_alert %}
<div class="alert alert-secondary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-file-archive fa-2x text-secondary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Document Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your document operations. Use the navigation menu to access different document functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-folder me-2"></i> {% trans "Folders" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file me-2"></i> {% trans "Documents" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Tags" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-share-alt me-2"></i> {% trans "Shared" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-history me-2"></i> {% trans "Recent" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-trash me-2"></i> {% trans "Trash" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-cog me-2"></i> {% trans "Settings" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-secondary">
        <i class="fas fa-upload me-2"></i> {% trans "Upload Document" %}
    </button>
    <button class="btn btn-outline-secondary">
        <i class="fas fa-folder-plus me-2"></i> {% trans "Create Folder" %}
    </button>
    <button class="btn btn-outline-secondary">
        <i class="fas fa-search me-2"></i> {% trans "Advanced Search" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-secondary h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-secondary text-white rounded p-3">
                        <i class="fas fa-file fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Total Documents" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Folders" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-share-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Shared Documents" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Storage Used" %}</h6>
                    <h3 class="card-title mb-0">0 MB</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Document Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Recent Documents" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Type" %}</th>
                        <th>{% trans "Size" %}</th>
                        <th>{% trans "Owner" %}</th>
                        <th>{% trans "Modified" %}</th>
                        <th>{% trans "Tags" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No documents found" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Document Types" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No document data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Storage Usage" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No storage data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Document Module loaded');
    });
</script>
{% endblock %}
