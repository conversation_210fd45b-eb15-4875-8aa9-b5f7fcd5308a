{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% block module_title %}{% endblock %} | {% trans "ERP System" %}{% endblock %}

{% block content %}
<div class="module-dashboard">
    <!-- Header Section -->
    <div class="module-header bg-light py-4 mb-4 shadow-sm">
        <div class="container-fluid">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-2">
                    <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">{% trans "Dashboard" %}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{% block module_name %}{% endblock %}</li>
                </ol>
            </nav>
            <div class="d-flex align-items-center">
                <div class="module-icon-large me-3 rounded-circle bg-white p-3 shadow-sm">
                    <i class="{% block module_icon %}{% endblock %} fa-2x"></i>
                </div>
                <div>
                    <h2 class="module-title mb-1">
                        {% block module_header %}{% endblock %}
                    </h2>
                    <p class="module-description text-muted mb-0">
                        {% block module_description %}{% endblock %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Module Alert -->
    <div class="row mb-4">
        <div class="col-12">
            {% block module_alert %}{% endblock %}
        </div>
    </div>

    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-header {% block sidebar_header_class %}bg-primary{% endblock %} text-white">
                    <h5 class="card-title mb-0">{% trans "Navigation" %}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush module-sidebar-menu">
                        {% block sidebar_menu %}{% endblock %}
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow-sm mt-4">
                <div class="card-header {% block quick_actions_header_class %}bg-secondary{% endblock %} text-white">
                    <h5 class="card-title mb-0">{% trans "Quick Actions" %}</h5>
                </div>
                <div class="card-body">
                    {% block quick_actions %}{% endblock %}
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <!-- Stats Cards -->
            <div class="row mb-4">
                {% block stats_cards %}{% endblock %}
            </div>

            <!-- Main Content Area -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}نظرة عامة{% else %}{% trans "Overview" %}{% endif %}{% endblock %}</h5>
                </div>
                <div class="card-body">
                    {% block module_content %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}
                        هذه الوحدة قيد التطوير. سيتم توفير التنفيذ الفعلي في التحديثات المستقبلية.
                        {% else %}
                        {% trans "This module is under development. The actual implementation will be available in future updates." %}
                        {% endif %}
                    </div>
                    {% endblock %}
                </div>
            </div>

            <!-- Additional Content -->
            {% block additional_content %}{% endblock %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% block module_js %}{% endblock %}
{% endblock %}
