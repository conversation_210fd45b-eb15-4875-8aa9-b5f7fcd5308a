{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "E-Commerce Management" %}{% endblock %}
{% block module_name %}{% trans "E-Commerce" %}{% endblock %}
{% block module_icon %}fas fa-shopping-bag{% endblock %}
{% block module_header %}{% trans "E-Commerce Management" %}{% endblock %}
{% block module_description %}{% trans "Expand your business online. Manage your online store, products, orders, and customer accounts with integrated e-commerce tools." %}{% endblock %}

{% block sidebar_header_class %}bg-danger{% endblock %}
{% block quick_actions_header_class %}bg-danger{% endblock %}

{% block module_alert %}
<div class="alert alert-danger alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-shopping-bag fa-2x text-danger"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to E-Commerce Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your online store operations. Use the navigation menu to access different e-commerce functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-store me-2"></i> {% trans "Online Store" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-box-open me-2"></i> {% trans "Products" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-shopping-cart me-2"></i> {% trans "Orders" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% trans "Customers" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-percent me-2"></i> {% trans "Discounts" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-truck me-2"></i> {% trans "Shipping" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-cog me-2"></i> {% trans "Settings" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-danger">
        <i class="fas fa-plus me-2"></i> {% trans "Add Product" %}
    </button>
    <button class="btn btn-outline-danger">
        <i class="fas fa-percent me-2"></i> {% trans "Create Discount" %}
    </button>
    <button class="btn btn-outline-danger">
        <i class="fas fa-chart-bar me-2"></i> {% trans "View Reports" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Today's Orders" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Revenue (Today)" %}</h6>
                    <h3 class="card-title mb-0">$0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Visitors" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-shopping-basket fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Abandoned Carts" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "E-Commerce Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Recent Orders" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Order #" %}</th>
                        <th>{% trans "Customer" %}</th>
                        <th>{% trans "Date" %}</th>
                        <th>{% trans "Items" %}</th>
                        <th>{% trans "Total" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No recent orders" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Top Selling Products" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No product data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Traffic Sources" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No traffic data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('E-Commerce Module loaded');
    });
</script>
{% endblock %}
