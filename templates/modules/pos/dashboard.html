{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}نقاط البيع{% else %}{% trans "Point of Sale" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}نقاط البيع{% else %}{% trans "Point of Sale" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-cash-register{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}نقاط البيع{% else %}{% trans "Point of Sale" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}إدارة عمليات البيع بكفاءة باستخدام نظام نقاط بيع سهل الاستخدام. معالجة المبيعات، تتبع المخزون، وتحليل الأداء.{% else %}{% trans "Manage your retail operations with an easy-to-use point of sale system. Process sales, track inventory, and analyze performance." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-danger{% endblock %}
{% block quick_actions_header_class %}bg-danger{% endblock %}

{% block module_alert %}
<div class="alert alert-danger alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-cash-register fa-2x text-danger"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% if LANGUAGE_CODE == 'ar' %}مرحباً بك في نقاط البيع{% else %}{% trans "Welcome to Point of Sale" %}{% endif %}</h5>
            <p>{% if LANGUAGE_CODE == 'ar' %}يساعدك هذا الموديول على إدارة جميع جوانب عمليات البيع بالتجزئة. استخدم قائمة التنقل للوصول إلى وظائف نقاط البيع المختلفة.{% else %}{% trans "This module helps you manage all aspects of your retail operations. Use the navigation menu to access different POS functions." %}{% endif %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans 'Close' %}{% endif %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-cash-register me-2"></i> {% if LANGUAGE_CODE == 'ar' %}جلسات نقاط البيع{% else %}{% trans "POS Sessions" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-receipt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الطلبات{% else %}{% trans "Orders" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% if LANGUAGE_CODE == 'ar' %}المنتجات{% else %}{% trans "Products" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-money-bill-wave me-2"></i> {% if LANGUAGE_CODE == 'ar' %}المدفوعات{% else %}{% trans "Payments" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-pie me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير{% else %}{% trans "Reports" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-cog me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الإعدادات{% else %}{% trans "Configuration" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-danger">
        <i class="fas fa-play me-2"></i> {% if LANGUAGE_CODE == 'ar' %}جلسة جديدة{% else %}{% trans "New Session" %}{% endif %}
    </button>
    <button class="btn btn-outline-danger">
        <i class="fas fa-boxes me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة المنتجات{% else %}{% trans "Manage Products" %}{% endif %}
    </button>
    <button class="btn btn-outline-danger">
        <i class="fas fa-chart-bar me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التقارير{% else %}{% trans "View Reports" %}{% endif %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-cash-register fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الجلسات النشطة{% else %}{% trans "Active Sessions" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}طلبات اليوم{% else %}{% trans "Today's Orders" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}العملاء المخدومين{% else %}{% trans "Customers Served" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}إيرادات اليوم{% else %}{% trans "Today's Revenue" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">$0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}نظرة عامة على نقاط البيع{% else %}{% trans "POS Overview" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}هذه الوحدة قيد التطوير. سيتم توفير التنفيذ الفعلي في التحديثات المستقبلية.{% else %}{% trans "This module is under development. The actual implementation will be available in future updates." %}{% endif %}
        </div>

        <h5 class="mt-4 mb-3">{% if LANGUAGE_CODE == 'ar' %}الطلبات الأخيرة{% else %}{% trans "Recent Orders" %}{% endif %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}رقم الطلب{% else %}{% trans "Order #" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الجلسة{% else %}{% trans "Session" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}العميل{% else %}{% trans "Customer" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}المجموع{% else %}{% trans "Total" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% if LANGUAGE_CODE == 'ar' %}لم يتم العثور على طلبات{% else %}{% trans "No orders found" %}{% endif %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المنتجات الأكثر مبيعاً{% else %}{% trans "Top Selling Products" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات منتجات متاحة{% else %}{% trans "No product data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}طرق الدفع{% else %}{% trans "Payment Methods" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات دفع متاحة{% else %}{% trans "No payment data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('POS Module loaded');
    });
</script>
{% endblock %}
