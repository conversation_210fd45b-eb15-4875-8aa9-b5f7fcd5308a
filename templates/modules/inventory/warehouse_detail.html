{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{{ warehouse.name }}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{{ warehouse.name }}{% endblock %}
{% block module_description %}{% trans "Warehouse details and inventory" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:warehouse_edit' warehouse.id %}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i> {% trans "Edit Warehouse" %}
    </a>
    <a href="{% url 'inventory:stock_movement_create' %}" class="btn btn-outline-success">
        <i class="fas fa-exchange-alt me-2"></i> {% trans "Stock Movement" %}
    </a>
    <a href="{% url 'inventory:product_create' %}" class="btn btn-outline-success">
        <i class="fas fa-boxes me-2"></i> {% trans "Add Product" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Warehouses" %}
    </a>
</div>

<!-- Warehouse Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="card-title">{{ warehouse.name }}</h4>
                        <p class="text-muted mb-2">
                            <span class="badge bg-primary me-2">{{ warehouse.code }}</span>
                            {% if warehouse.branch %}
                                <span class="badge bg-info">{{ warehouse.branch.name }}</span>
                            {% endif %}
                        </p>
                        {% if warehouse.address %}
                            <p class="card-text">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {{ warehouse.address }}
                            </p>
                        {% endif %}
                        {% if warehouse.manager %}
                            <p class="card-text">
                                <i class="fas fa-user me-1"></i>
                                <strong>{% trans "Manager:" %}</strong> 
                                {{ warehouse.manager.get_full_name|default:warehouse.manager.username }}
                            </p>
                        {% endif %}
                    </div>
                    <div class="text-end">
                        {% if warehouse.is_active %}
                            <span class="badge bg-success fs-6">{% trans "Active" %}</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">{% trans "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ total_products }}</h4>
                        <small class="text-muted">{% trans "Products" %}</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ total_stock_value|floatformat:2 }}</h4>
                        <small class="text-muted">{% trans "Stock Value" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Warehouse Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "Warehouse Information" %}
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>{% trans "Code:" %}</strong></td>
                        <td>{{ warehouse.code }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Name:" %}</strong></td>
                        <td>{{ warehouse.name }}</td>
                    </tr>
                    {% if warehouse.branch %}
                    <tr>
                        <td><strong>{% trans "Branch:" %}</strong></td>
                        <td>{{ warehouse.branch.name }}</td>
                    </tr>
                    {% endif %}
                    {% if warehouse.manager %}
                    <tr>
                        <td><strong>{% trans "Manager:" %}</strong></td>
                        <td>{{ warehouse.manager.get_full_name|default:warehouse.manager.username }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>{% trans "Status:" %}</strong></td>
                        <td>
                            {% if warehouse.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Created:" %}</strong></td>
                        <td>{{ warehouse.created_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-1"></i>
                    {% trans "Statistics" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Total Products:" %}</span>
                            <strong class="text-info">{{ total_products }}</strong>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Total Stock Value:" %}</span>
                            <strong class="text-success">{{ total_stock_value|floatformat:2 }}</strong>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Recent Movements:" %}</span>
                            <strong class="text-primary">{{ recent_movements.count }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Stock Movements -->
{% if recent_movements %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-1"></i>
                    {% trans "Recent Stock Movements" %}
                </h6>
                <a href="{% url 'inventory:stock_movement_list' %}?warehouse={{ warehouse.id }}" class="btn btn-outline-primary btn-sm">
                    {% trans "View All Movements" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Product" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Quantity" %}</th>
                                <th>{% trans "Unit Cost" %}</th>
                                <th>{% trans "Total Cost" %}</th>
                                <th>{% trans "Reference" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_movements %}
                            <tr>
                                <td><small>{{ movement.created_at|date:"d/m/Y H:i" }}</small></td>
                                <td>
                                    <strong>{{ movement.product.name }}</strong><br>
                                    <small class="text-muted">{{ movement.product.sku }}</small>
                                </td>
                                <td>
                                    {% if movement.movement_type == 'in' %}
                                        <span class="badge bg-success">{% trans "In" %}</span>
                                    {% elif movement.movement_type == 'out' %}
                                        <span class="badge bg-danger">{% trans "Out" %}</span>
                                    {% elif movement.movement_type == 'transfer' %}
                                        <span class="badge bg-info">{% trans "Transfer" %}</span>
                                    {% else %}
                                        <span class="badge bg-warning">{% trans "Adjustment" %}</span>
                                    {% endif %}
                                </td>
                                <td class="fw-bold">{{ movement.quantity|floatformat:2 }}</td>
                                <td>{{ movement.unit_cost|floatformat:2 }}</td>
                                <td class="fw-bold">{{ movement.total_cost|floatformat:2 }}</td>
                                <td><small>{{ movement.reference_number|default:"-" }}</small></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                <h5>{% trans "No stock movements yet" %}</h5>
                <p class="text-muted">{% trans "Start by adding some stock movements to this warehouse." %}</p>
                <a href="{% url 'inventory:stock_movement_create' %}" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>
                    {% trans "Add Stock Movement" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
