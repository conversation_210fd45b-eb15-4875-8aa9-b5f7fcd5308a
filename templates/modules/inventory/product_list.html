{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Products" %}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{% trans "Products" %}{% endblock %}
{% block module_description %}{% trans "Manage your product inventory" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block module_alert %}
<div class="alert alert-success alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-boxes fa-2x text-success"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Product Management" %}</h5>
            <p>{% trans "Manage your product catalog, pricing, and inventory levels." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:product_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% trans "Add Product" %}
    </a>
    <a href="{% url 'inventory:category_create' %}" class="btn btn-outline-success">
        <i class="fas fa-tags me-2"></i> {% trans "Add Category" %}
    </a>
    <a href="{% url 'inventory:stock_movement_create' %}" class="btn btn-outline-success">
        <i class="fas fa-exchange-alt me-2"></i> {% trans "Stock Movement" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Dashboard" %}
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">{% trans "Search" %}</label>
                <input type="text" class="form-control" id="search" name="search" value="{{ search }}" 
                       placeholder="{% trans 'Search by name, SKU...' %}">
            </div>
            <div class="col-md-2">
                <label for="category" class="form-label">{% trans "Category" %}</label>
                <select class="form-select" id="category" name="category">
                    <option value="">{% trans "All Categories" %}</option>
                    {% for cat in categories %}
                        <option value="{{ cat.id }}" {% if category_id == cat.id|stringformat:"s" %}selected{% endif %}>
                            {{ cat.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="product_type" class="form-label">{% trans "Type" %}</label>
                <select class="form-select" id="product_type" name="product_type">
                    <option value="">{% trans "All Types" %}</option>
                    {% for type_code, type_name in product_types %}
                        <option value="{{ type_code }}" {% if product_type == type_code %}selected{% endif %}>
                            {{ type_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="is_active" class="form-label">{% trans "Status" %}</label>
                <select class="form-select" id="is_active" name="is_active">
                    <option value="">{% trans "All" %}</option>
                    <option value="true" {% if is_active == 'true' %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="false" {% if is_active == 'false' %}selected{% endif %}>{% trans "Inactive" %}</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>{% trans "Search" %}
                </button>
                <a href="{% url 'inventory:product_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>{% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Products Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-boxes me-2"></i>
            {% trans "Products" %}
        </h5>
        <a href="{% url 'inventory:product_create' %}" class="btn btn-success btn-sm">
            <i class="fas fa-plus me-1"></i>
            {% trans "Add Product" %}
        </a>
    </div>
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "SKU" %}</th>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Category" %}</th>
                        <th>{% trans "Type" %}</th>
                        <th>{% trans "Cost Price" %}</th>
                        <th>{% trans "Selling Price" %}</th>
                        <th>{% trans "Unit" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ product.sku }}</span>
                        </td>
                        <td>
                            <strong>{{ product.name }}</strong>
                            {% if product.description %}
                                <br><small class="text-muted">{{ product.description|truncatechars:50 }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ product.category.name }}</span>
                            {% if product.subcategory %}
                                <br><small class="text-muted">{{ product.subcategory.name }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ product.get_product_type_display }}</span>
                        </td>
                        <td>
                            <span class="text-success fw-bold">{{ product.cost_price|floatformat:2 }}</span>
                        </td>
                        <td>
                            <span class="text-primary fw-bold">{{ product.selling_price|floatformat:2 }}</span>
                        </td>
                        <td>
                            <small class="text-muted">{{ product.unit_of_measure.symbol }}</small>
                        </td>
                        <td>
                            {% if product.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'inventory:product_detail' product.id %}" 
                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'inventory:product_edit' product.id %}" 
                                   class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if products.has_other_pages %}
        <nav aria-label="{% trans 'Products pagination' %}">
            <ul class="pagination justify-content-center">
                {% if products.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if product_type %}&product_type={{ product_type }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                            {% trans "Previous" %}
                        </a>
                    </li>
                {% endif %}
                
                {% for num in products.paginator.page_range %}
                    {% if products.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if product_type %}&product_type={{ product_type }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if products.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ products.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if category_id %}&category={{ category_id }}{% endif %}{% if product_type %}&product_type={{ product_type }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                            {% trans "Next" %}
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h5>{% trans "No products found" %}</h5>
            <p class="text-muted">{% trans "Start by creating your first product." %}</p>
            <a href="{% url 'inventory:product_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create First Product" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
