{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block module_title %}{{ title }}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{{ title }}{% endblock %}
{% block module_description %}{% trans "Create or edit warehouse" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-outline-success">
        <i class="fas fa-list me-2"></i> {% trans "View Warehouses" %}
    </a>
    <a href="{% url 'inventory:product_create' %}" class="btn btn-outline-success">
        <i class="fas fa-boxes me-2"></i> {% trans "Add Product" %}
    </a>
    <a href="{% url 'inventory:stock_movement_create' %}" class="btn btn-outline-success">
        <i class="fas fa-exchange-alt me-2"></i> {% trans "Stock Movement" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Warehouses" %}
    </a>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-warehouse me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form|crispy }}
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            {% trans "Save Warehouse" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "Help" %}
                </h6>
            </div>
            <div class="card-body">
                <h6>{% trans "Warehouse Guidelines" %}</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Use unique warehouse codes" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Assign to appropriate branch" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Set a responsible manager" %}
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Provide complete address" %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
