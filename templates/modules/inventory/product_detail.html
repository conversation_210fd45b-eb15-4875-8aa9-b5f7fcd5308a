{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{{ product.name }}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{{ product.name }}{% endblock %}
{% block module_description %}{% trans "Product details and information" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:product_edit' product.id %}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i> {% trans "Edit Product" %}
    </a>
    <a href="{% url 'inventory:product_barcode_list' product.id %}" class="btn btn-outline-success">
        <i class="fas fa-barcode me-2"></i> {% trans "Manage Barcodes" %}
    </a>
    <a href="{% url 'inventory:stock_movement_create' %}" class="btn btn-outline-success">
        <i class="fas fa-exchange-alt me-2"></i> {% trans "Stock Movement" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:product_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Products" %}
    </a>
</div>

<!-- Product Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="card-title">{{ product.name }}</h4>
                        <p class="text-muted mb-2">
                            <span class="badge bg-primary me-2">{{ product.sku }}</span>
                            <span class="badge bg-info">{{ product.category.name }}</span>
                            {% if product.subcategory %}
                                <span class="badge bg-secondary">{{ product.subcategory.name }}</span>
                            {% endif %}
                        </p>
                        {% if product.description %}
                            <p class="card-text">{{ product.description }}</p>
                        {% endif %}
                    </div>
                    <div class="text-end">
                        {% if product.is_active %}
                            <span class="badge bg-success fs-6">{% trans "Active" %}</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">{% trans "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="card-title">{% trans "Current Stock" %}</h6>
                {% if product.track_stock %}
                    <h2 class="{% if is_low_stock %}text-danger{% else %}text-success{% endif %}">
                        {{ current_stock|floatformat:2 }}
                    </h2>
                    <small class="text-muted">{{ product.unit_of_measure.symbol }}</small>
                    {% if is_low_stock %}
                        <div class="mt-2">
                            <span class="badge bg-warning">{% trans "Low Stock" %}</span>
                        </div>
                    {% endif %}
                {% else %}
                    <h5 class="text-muted">{% trans "Not Tracked" %}</h5>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Product Details -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "Product Information" %}
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>{% trans "SKU:" %}</strong></td>
                        <td>{{ product.sku }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Type:" %}</strong></td>
                        <td>{{ product.get_product_type_display }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Category:" %}</strong></td>
                        <td>{{ product.category.name }}</td>
                    </tr>
                    {% if product.subcategory %}
                    <tr>
                        <td><strong>{% trans "Subcategory:" %}</strong></td>
                        <td>{{ product.subcategory.name }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>{% trans "Unit:" %}</strong></td>
                        <td>{{ product.unit_of_measure.name }} ({{ product.unit_of_measure.symbol }})</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Created:" %}</strong></td>
                        <td>{{ product.created_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                    {% if product.created_by %}
                    <tr>
                        <td><strong>{% trans "Created By:" %}</strong></td>
                        <td>{{ product.created_by.get_full_name|default:product.created_by.username }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-dollar-sign me-1"></i>
                    {% trans "Pricing & Stock" %}
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>{% trans "Cost Price:" %}</strong></td>
                        <td class="text-success fw-bold">{{ product.cost_price|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Selling Price:" %}</strong></td>
                        <td class="text-primary fw-bold">{{ product.selling_price|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Track Stock:" %}</strong></td>
                        <td>
                            {% if product.track_stock %}
                                <span class="badge bg-success">{% trans "Yes" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "No" %}</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% if product.track_stock %}
                    <tr>
                        <td><strong>{% trans "Min Stock:" %}</strong></td>
                        <td>{{ product.min_stock_level|floatformat:2 }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Max Stock:" %}</strong></td>
                        <td>{{ product.max_stock_level|floatformat:2 }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td><strong>{% trans "Can be Sold:" %}</strong></td>
                        <td>
                            {% if product.can_be_sold %}
                                <span class="badge bg-success">{% trans "Yes" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "No" %}</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Can be Purchased:" %}</strong></td>
                        <td>
                            {% if product.can_be_purchased %}
                                <span class="badge bg-success">{% trans "Yes" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "No" %}</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Barcodes -->
{% if barcodes %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-barcode me-1"></i>
                    {% trans "Barcodes" %}
                </h6>
                <a href="{% url 'inventory:product_barcode_create' product.id %}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "Add Barcode" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "Barcode" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Primary" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for barcode in barcodes %}
                            <tr>
                                <td><code>{{ barcode.barcode }}</code></td>
                                <td><span class="badge bg-info">{{ barcode.barcode_type }}</span></td>
                                <td>
                                    {% if barcode.is_primary %}
                                        <span class="badge bg-success">{% trans "Primary" %}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'inventory:barcode_print' barcode.id %}" 
                                           class="btn btn-outline-primary" title="{% trans 'Print' %}">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        <a href="{% url 'inventory:product_barcode_edit' barcode.id %}" 
                                           class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Stock Movements -->
{% if recent_movements %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exchange-alt me-1"></i>
                    {% trans "Recent Stock Movements" %}
                </h6>
                <a href="{% url 'inventory:stock_movement_list' %}?product={{ product.id }}" class="btn btn-outline-primary btn-sm">
                    {% trans "View All" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Warehouse" %}</th>
                                <th>{% trans "Quantity" %}</th>
                                <th>{% trans "Unit Cost" %}</th>
                                <th>{% trans "Reference" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in recent_movements %}
                            <tr>
                                <td><small>{{ movement.created_at|date:"d/m/Y H:i" }}</small></td>
                                <td>
                                    {% if movement.movement_type == 'in' %}
                                        <span class="badge bg-success">{% trans "In" %}</span>
                                    {% elif movement.movement_type == 'out' %}
                                        <span class="badge bg-danger">{% trans "Out" %}</span>
                                    {% elif movement.movement_type == 'transfer' %}
                                        <span class="badge bg-info">{% trans "Transfer" %}</span>
                                    {% else %}
                                        <span class="badge bg-warning">{% trans "Adjustment" %}</span>
                                    {% endif %}
                                </td>
                                <td><small>{{ movement.warehouse.name }}</small></td>
                                <td class="fw-bold">{{ movement.quantity|floatformat:2 }}</td>
                                <td>{{ movement.unit_cost|floatformat:2 }}</td>
                                <td><small>{{ movement.reference_number|default:"-" }}</small></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
