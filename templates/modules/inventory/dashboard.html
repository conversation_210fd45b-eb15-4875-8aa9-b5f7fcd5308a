{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة المخزون{% else %}{% trans "Inventory Management" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}المخزون{% else %}{% trans "Inventory" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إدارة المخزون{% else %}{% trans "Inventory Management" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}تتبع مستويات المخزون، إدارة المستودعات، وتحسين عمليات المخزون لتحقيق أقصى قدر من الكفاءة.{% else %}{% trans "Keep track of your stock levels, manage warehouses, and optimize inventory operations for maximum efficiency." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block module_alert %}
<div class="alert alert-success alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-boxes fa-2x text-success"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Inventory Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your inventory operations. Use the navigation menu to access different inventory functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
<a href="{% url 'inventory:stock_movement_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-exchange-alt me-2"></i> {% trans "Stock Movements" %}
</a>
<a href="{% url 'inventory:reports' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:product_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% trans "Add Product" %}
    </a>
    <a href="{% url 'inventory:warehouse_create' %}" class="btn btn-outline-success">
        <i class="fas fa-warehouse me-2"></i> {% trans "Add Warehouse" %}
    </a>
    <a href="{% url 'inventory:category_create' %}" class="btn btn-outline-success">
        <i class="fas fa-tags me-2"></i> {% trans "Add Category" %}
    </a>
    <a href="{% url 'inventory:stock_movement_create' %}" class="btn btn-outline-success">
        <i class="fas fa-exchange-alt me-2"></i> {% trans "New Movement" %}
    </a>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Total Products" %}</h6>
                    <h3 class="card-title mb-0">{{ total_products }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Low Stock Items" %}</h6>
                    <h3 class="card-title mb-0">{{ low_stock_count }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-warehouse fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Warehouses" %}</h6>
                    <h3 class="card-title mb-0">{{ total_warehouses }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Categories" %}</h6>
                    <h3 class="card-title mb-0">{{ total_categories }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Inventory Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>

        <h5 class="mt-4 mb-3">{% trans "Low Stock Products" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "SKU" %}</th>
                        <th>{% trans "Product" %}</th>
                        <th>{% trans "Warehouse" %}</th>
                        <th>{% trans "Current Stock" %}</th>
                        <th>{% trans "Min. Stock" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center">{% trans "No low stock products found" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Recent Stock Movements" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No recent stock movements" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Pending Transfers" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No pending transfers" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Inventory Module loaded');
    });
</script>
{% endblock %}
