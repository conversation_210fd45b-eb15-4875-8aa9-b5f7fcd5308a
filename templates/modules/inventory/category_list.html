{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Product Categories" %}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{% trans "Product Categories" %}{% endblock %}
{% block module_description %}{% trans "Manage product categories and classifications" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block module_alert %}
<div class="alert alert-success alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-tags fa-2x text-success"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Product Categories Management" %}</h5>
            <p>{% trans "Organize your products into categories for better management and reporting." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:category_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% trans "Add Category" %}
    </a>
    <a href="{% url 'inventory:subcategory_create' %}" class="btn btn-outline-success">
        <i class="fas fa-tag me-2"></i> {% trans "Add Subcategory" %}
    </a>
    <a href="{% url 'inventory:product_create' %}" class="btn btn-outline-success">
        <i class="fas fa-boxes me-2"></i> {% trans "Add Product" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Dashboard" %}
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">{% trans "Search" %}</label>
                <input type="text" class="form-control" id="search" name="search" value="{{ search }}" 
                       placeholder="{% trans 'Search by name or code...' %}">
            </div>
            <div class="col-md-3">
                <label for="is_active" class="form-label">{% trans "Status" %}</label>
                <select class="form-select" id="is_active" name="is_active">
                    <option value="">{% trans "All" %}</option>
                    <option value="true" {% if is_active == 'true' %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="false" {% if is_active == 'false' %}selected{% endif %}>{% trans "Inactive" %}</option>
                </select>
            </div>
            <div class="col-md-5 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>{% trans "Search" %}
                </button>
                <a href="{% url 'inventory:category_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>{% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Categories Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-tags me-2"></i>
            {% trans "Product Categories" %}
        </h5>
        <a href="{% url 'inventory:category_create' %}" class="btn btn-success btn-sm">
            <i class="fas fa-plus me-1"></i>
            {% trans "Add Category" %}
        </a>
    </div>
    <div class="card-body">
        {% if categories %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Code" %}</th>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Description" %}</th>
                        <th>{% trans "Subcategories" %}</th>
                        <th>{% trans "Products" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Created" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in categories %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ category.code }}</span>
                        </td>
                        <td>
                            <strong>{{ category.name }}</strong>
                        </td>
                        <td>
                            <small class="text-muted">{{ category.description|truncatechars:50 }}</small>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ category.subcategories_count }}</span>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ category.products_count }}</span>
                        </td>
                        <td>
                            {% if category.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ category.created_at|date:"d/m/Y" }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'inventory:category_detail' category.id %}" 
                                   class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'inventory:category_edit' category.id %}" 
                                   class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if categories.has_other_pages %}
        <nav aria-label="{% trans 'Categories pagination' %}">
            <ul class="pagination justify-content-center">
                {% if categories.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ categories.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                            {% trans "Previous" %}
                        </a>
                    </li>
                {% endif %}
                
                {% for num in categories.paginator.page_range %}
                    {% if categories.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if categories.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ categories.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                            {% trans "Next" %}
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5>{% trans "No categories found" %}</h5>
            <p class="text-muted">{% trans "Start by creating your first product category." %}</p>
            <a href="{% url 'inventory:category_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create First Category" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
