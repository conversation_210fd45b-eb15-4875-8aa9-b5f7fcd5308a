{% extends 'modules/base_module.html' %}
{% load i18n %}
{% load crispy_forms_tags %}

{% block module_title %}{{ title }}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{{ title }}{% endblock %}
{% block module_description %}{% trans "Create or edit product" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:product_list' %}" class="btn btn-outline-success">
        <i class="fas fa-list me-2"></i> {% trans "View Products" %}
    </a>
    <a href="{% url 'inventory:category_create' %}" class="btn btn-outline-success">
        <i class="fas fa-tags me-2"></i> {% trans "Add Category" %}
    </a>
    <a href="{% url 'inventory:unit_create' %}" class="btn btn-outline-success">
        <i class="fas fa-ruler me-2"></i> {% trans "Add Unit" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:product_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Products" %}
    </a>
</div>

<div class="row">
    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    {{ title }}
                </h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    {{ form|crispy }}
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'inventory:product_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-1"></i>
                            {% trans "Save Product" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "Help" %}
                </h6>
            </div>
            <div class="card-body">
                <h6>{% trans "Product Guidelines" %}</h6>
                <ul class="list-unstyled small">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Use unique SKU codes" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Select appropriate category" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Set accurate pricing" %}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Define stock levels" %}
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-1"></i>
                        {% trans "Choose correct unit" %}
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-link me-1"></i>
                    {% trans "Quick Links" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'inventory:category_list' %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-tags me-1"></i>
                        {% trans "Manage Categories" %}
                    </a>
                    <a href="{% url 'inventory:unit_list' %}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-ruler me-1"></i>
                        {% trans "Manage Units" %}
                    </a>
                    <a href="{% url 'inventory:warehouse_list' %}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-warehouse me-1"></i>
                        {% trans "View Warehouses" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Dynamic subcategory loading
document.addEventListener('DOMContentLoaded', function() {
    const categorySelect = document.getElementById('id_category');
    const subcategorySelect = document.getElementById('id_subcategory');
    
    if (categorySelect && subcategorySelect) {
        categorySelect.addEventListener('change', function() {
            const categoryId = this.value;
            
            // Clear subcategory options
            subcategorySelect.innerHTML = '<option value="">{% trans "Select subcategory..." %}</option>';
            
            if (categoryId) {
                // Fetch subcategories via AJAX
                fetch(`{% url 'inventory:ajax_subcategories' %}?category=${categoryId}`)
                    .then(response => response.json())
                    .then(data => {
                        data.subcategories.forEach(function(subcategory) {
                            const option = document.createElement('option');
                            option.value = subcategory.id;
                            option.textContent = subcategory.name;
                            subcategorySelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error:', error));
            }
        });
    }
});
</script>
{% endblock %}
