{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{{ category.name }}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{{ category.name }}{% endblock %}
{% block module_description %}{% trans "Category details and products" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:category_edit' category.id %}" class="btn btn-warning">
        <i class="fas fa-edit me-2"></i> {% trans "Edit Category" %}
    </a>
    <a href="{% url 'inventory:subcategory_create' %}" class="btn btn-outline-success">
        <i class="fas fa-tag me-2"></i> {% trans "Add Subcategory" %}
    </a>
    <a href="{% url 'inventory:product_create' %}" class="btn btn-outline-success">
        <i class="fas fa-boxes me-2"></i> {% trans "Add Product" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:category_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Categories" %}
    </a>
</div>

<!-- Category Header -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="card-title">{{ category.name }}</h4>
                        <p class="text-muted mb-2">
                            <span class="badge bg-primary me-2">{{ category.code }}</span>
                        </p>
                        {% if category.description %}
                            <p class="card-text">{{ category.description }}</p>
                        {% endif %}
                    </div>
                    <div class="text-end">
                        {% if category.is_active %}
                            <span class="badge bg-success fs-6">{% trans "Active" %}</span>
                        {% else %}
                            <span class="badge bg-secondary fs-6">{% trans "Inactive" %}</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-info">{{ subcategories_count }}</h4>
                        <small class="text-muted">{% trans "Subcategories" %}</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ products_count }}</h4>
                        <small class="text-muted">{% trans "Products" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Category Information -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-1"></i>
                    {% trans "Category Information" %}
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>{% trans "Code:" %}</strong></td>
                        <td>{{ category.code }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Name:" %}</strong></td>
                        <td>{{ category.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Status:" %}</strong></td>
                        <td>
                            {% if category.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Created:" %}</strong></td>
                        <td>{{ category.created_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                    <tr>
                        <td><strong>{% trans "Last Updated:" %}</strong></td>
                        <td>{{ category.updated_at|date:"d/m/Y H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-1"></i>
                    {% trans "Statistics" %}
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Total Subcategories:" %}</span>
                            <strong class="text-info">{{ subcategories_count }}</strong>
                        </div>
                    </div>
                    <div class="col-12 mb-3">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Total Products:" %}</span>
                            <strong class="text-success">{{ products_count }}</strong>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <span>{% trans "Active Subcategories:" %}</span>
                            <strong class="text-primary">{{ subcategories.count }}</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Subcategories -->
{% if subcategories %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-tag me-1"></i>
                    {% trans "Subcategories" %}
                </h6>
                <a href="{% url 'inventory:subcategory_create' %}" class="btn btn-success btn-sm">
                    <i class="fas fa-plus me-1"></i>
                    {% trans "Add Subcategory" %}
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for subcategory in subcategories %}
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="card-title">{{ subcategory.name }}</h6>
                                        <p class="card-text">
                                            <span class="badge bg-primary">{{ subcategory.code }}</span>
                                        </p>
                                        {% if subcategory.description %}
                                            <p class="card-text small text-muted">{{ subcategory.description|truncatechars:60 }}</p>
                                        {% endif %}
                                    </div>
                                    <div class="text-end">
                                        {% if subcategory.is_active %}
                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-2">
                                    <small class="text-muted">
                                        {% trans "Products:" %} {{ subcategory.products.count }}
                                    </small>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'inventory:subcategory_detail' subcategory.id %}" 
                                           class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:subcategory_edit' subcategory.id %}" 
                                           class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Recent Products -->
{% if products %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-boxes me-1"></i>
                    {% trans "Recent Products" %}
                </h6>
                <a href="{% url 'inventory:product_list' %}?category={{ category.id }}" class="btn btn-outline-primary btn-sm">
                    {% trans "View All Products" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>{% trans "SKU" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Subcategory" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Price" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td><span class="badge bg-primary">{{ product.sku }}</span></td>
                                <td><strong>{{ product.name }}</strong></td>
                                <td>
                                    {% if product.subcategory %}
                                        <small>{{ product.subcategory.name }}</small>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td><span class="badge bg-secondary">{{ product.get_product_type_display }}</span></td>
                                <td class="text-primary fw-bold">{{ product.selling_price|floatformat:2 }}</td>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'inventory:product_detail' product.id %}" 
                                           class="btn btn-outline-primary" title="{% trans 'View Details' %}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'inventory:product_edit' product.id %}" 
                                           class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
