{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Units of Measure" %}{% endblock %}
{% block module_name %}{% trans "Inventory" %}{% endblock %}
{% block module_icon %}fas fa-warehouse{% endblock %}
{% block module_header %}{% trans "Units of Measure" %}{% endblock %}
{% block module_description %}{% trans "Manage measurement units for products" %}{% endblock %}

{% block sidebar_header_class %}bg-success{% endblock %}
{% block quick_actions_header_class %}bg-success{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'inventory:dashboard' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="{% url 'inventory:warehouse_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-warehouse me-2"></i> {% trans "Warehouses" %}
</a>
<a href="{% url 'inventory:product_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-boxes me-2"></i> {% trans "Products" %}
</a>
<a href="{% url 'inventory:category_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tags me-2"></i> {% trans "Categories" %}
</a>
<a href="{% url 'inventory:subcategory_list' %}" class="list-group-item list-group-item-action">
    <i class="fas fa-tag me-2"></i> {% trans "Subcategories" %}
</a>
<a href="{% url 'inventory:unit_list' %}" class="list-group-item list-group-item-action active">
    <i class="fas fa-ruler me-2"></i> {% trans "Units" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'inventory:unit_create' %}" class="btn btn-success">
        <i class="fas fa-plus me-2"></i> {% trans "Add Unit" %}
    </a>
    <a href="{% url 'inventory:product_create' %}" class="btn btn-outline-success">
        <i class="fas fa-boxes me-2"></i> {% trans "Add Product" %}
    </a>
    <a href="{% url 'inventory:category_create' %}" class="btn btn-outline-success">
        <i class="fas fa-tags me-2"></i> {% trans "Add Category" %}
    </a>
</div>
{% endblock %}

{% block module_content %}
<!-- Back Button -->
<div class="mb-3">
    <a href="{% url 'inventory:dashboard' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        {% trans "Back to Dashboard" %}
    </a>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">{% trans "Search" %}</label>
                <input type="text" class="form-control" id="search" name="search" value="{{ search }}" 
                       placeholder="{% trans 'Search by name, code, or symbol...' %}">
            </div>
            <div class="col-md-3">
                <label for="is_active" class="form-label">{% trans "Status" %}</label>
                <select class="form-select" id="is_active" name="is_active">
                    <option value="">{% trans "All" %}</option>
                    <option value="true" {% if is_active == 'true' %}selected{% endif %}>{% trans "Active" %}</option>
                    <option value="false" {% if is_active == 'false' %}selected{% endif %}>{% trans "Inactive" %}</option>
                </select>
            </div>
            <div class="col-md-5 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>{% trans "Search" %}
                </button>
                <a href="{% url 'inventory:unit_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>{% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Units Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-ruler me-2"></i>
            {% trans "Units of Measure" %}
        </h5>
        <a href="{% url 'inventory:unit_create' %}" class="btn btn-success btn-sm">
            <i class="fas fa-plus me-1"></i>
            {% trans "Add Unit" %}
        </a>
    </div>
    <div class="card-body">
        {% if units %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Code" %}</th>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Symbol" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Created" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for unit in units %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ unit.code }}</span>
                        </td>
                        <td>
                            <strong>{{ unit.name }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ unit.symbol }}</span>
                        </td>
                        <td>
                            {% if unit.is_active %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                                <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ unit.created_at|date:"d/m/Y" }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{% url 'inventory:unit_edit' unit.id %}" 
                                   class="btn btn-outline-warning" title="{% trans 'Edit' %}">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if units.has_other_pages %}
        <nav aria-label="{% trans 'Units pagination' %}">
            <ul class="pagination justify-content-center">
                {% if units.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ units.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                            {% trans "Previous" %}
                        </a>
                    </li>
                {% endif %}
                
                {% for num in units.paginator.page_range %}
                    {% if units.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search %}&search={{ search }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if units.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ units.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if is_active %}&is_active={{ is_active }}{% endif %}">
                            {% trans "Next" %}
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-ruler fa-3x text-muted mb-3"></i>
            <h5>{% trans "No units found" %}</h5>
            <p class="text-muted">{% trans "Start by creating your first unit of measure." %}</p>
            <a href="{% url 'inventory:unit_create' %}" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>
                {% trans "Create First Unit" %}
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Common Units Reference -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-info-circle me-1"></i>
            {% trans "Common Units Reference" %}
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <h6>{% trans "Weight" %}</h6>
                <ul class="list-unstyled small">
                    <li>KG - {% trans "Kilogram" %}</li>
                    <li>G - {% trans "Gram" %}</li>
                    <li>LB - {% trans "Pound" %}</li>
                    <li>OZ - {% trans "Ounce" %}</li>
                </ul>
            </div>
            <div class="col-md-3">
                <h6>{% trans "Length" %}</h6>
                <ul class="list-unstyled small">
                    <li>M - {% trans "Meter" %}</li>
                    <li>CM - {% trans "Centimeter" %}</li>
                    <li>FT - {% trans "Foot" %}</li>
                    <li>IN - {% trans "Inch" %}</li>
                </ul>
            </div>
            <div class="col-md-3">
                <h6>{% trans "Volume" %}</h6>
                <ul class="list-unstyled small">
                    <li>L - {% trans "Liter" %}</li>
                    <li>ML - {% trans "Milliliter" %}</li>
                    <li>GAL - {% trans "Gallon" %}</li>
                    <li>QT - {% trans "Quart" %}</li>
                </ul>
            </div>
            <div class="col-md-3">
                <h6>{% trans "Count" %}</h6>
                <ul class="list-unstyled small">
                    <li>PCS - {% trans "Pieces" %}</li>
                    <li>BOX - {% trans "Box" %}</li>
                    <li>SET - {% trans "Set" %}</li>
                    <li>PACK - {% trans "Pack" %}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
