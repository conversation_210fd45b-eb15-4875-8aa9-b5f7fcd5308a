{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تفاصيل الوحدة{% else %}{% trans "Module Details" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ module.name }}</h5>
                <div>
                    {% if module_installed %}
                        {% if module_active %}
                        <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفعّل{% else %}{% trans "Active" %}{% endif %}</span>
                        {% else %}
                        <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}معطّل{% else %}{% trans "Inactive" %}{% endif %}</span>
                        {% endif %}
                    {% else %}
                    <span class="badge bg-light text-dark">{% if LANGUAGE_CODE == 'ar' %}غير مثبت{% else %}{% trans "Not Installed" %}{% endif %}</span>
                    {% endif %}
                    
                    {% if module.is_core %}
                    <span class="badge bg-primary ms-1">{% if LANGUAGE_CODE == 'ar' %}أساسي{% else %}{% trans "Core" %}{% endif %}</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="module-icon-wrapper mb-3">
                            <i class="{{ module.icon|default:'fas fa-puzzle-piece' }} fa-5x module-icon"></i>
                        </div>
                        <h4>{{ module.name }}</h4>
                        <p class="text-muted">{{ module.code }}</p>
                        <p><strong>{% if LANGUAGE_CODE == 'ar' %}الإصدار:{% else %}{% trans "Version:" %}{% endif %}</strong> {{ module.version }}</p>
                        
                        <div class="d-grid gap-2 mt-4">
                            {% if module_installed %}
                                {% if module_active %}
                                <a href="{% url 'modules:module_dashboard' module.code %}" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}فتح الوحدة{% else %}{% trans "Open Module" %}{% endif %}
                                </a>
                                <a href="{% url 'modules:module_update' module.code %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-sync-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تحديث{% else %}{% trans "Update" %}{% endif %}
                                </a>
                                <a href="{% url 'modules:module_uninstall' module.code %}" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                </a>
                                {% else %}
                                <a href="{% url 'modules:module_install' module.code %}" class="btn btn-success">
                                    <i class="fas fa-play me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تفعيل{% else %}{% trans "Activate" %}{% endif %}
                                </a>
                                <a href="{% url 'modules:module_uninstall' module.code %}" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                </a>
                                {% endif %}
                            {% else %}
                            <a href="{% url 'modules:module_install' module.code %}" class="btn btn-success">
                                <i class="fas fa-download me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تثبيت{% else %}{% trans "Install" %}{% endif %}
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-9">
                        <h5>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</h5>
                        <p>{{ module.description }}</p>
                        
                        <h5 class="mt-4">{% if LANGUAGE_CODE == 'ar' %}الاعتماديات{% else %}{% trans "Dependencies" %}{% endif %}</h5>
                        {% if module.dependencies.exists %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}الوحدة{% else %}{% trans "Module" %}{% endif %}</th>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}الإصدار{% else %}{% trans "Version" %}{% endif %}</th>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for dependency in module.dependencies.all %}
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    <i class="{{ dependency.icon|default:'fas fa-puzzle-piece' }}"></i>
                                                </div>
                                                <div>
                                                    <h6 class="mb-0">{{ dependency.name }}</h6>
                                                    <small class="text-muted">{{ dependency.code }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ dependency.version }}</td>
                                        <td>
                                            {% if dependency.id in installed_modules %}
                                                {% if installed_modules.dependency.id.is_active %}
                                                <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفعّل{% else %}{% trans "Active" %}{% endif %}</span>
                                                {% else %}
                                                <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}معطّل{% else %}{% trans "Inactive" %}{% endif %}</span>
                                                {% endif %}
                                            {% else %}
                                            <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}غير مثبت{% else %}{% trans "Not Installed" %}{% endif %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <p class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد اعتماديات لهذه الوحدة.{% else %}{% trans "This module has no dependencies." %}{% endif %}</p>
                        {% endif %}
                        
                        {% if module_installed %}
                        <h5 class="mt-4">{% if LANGUAGE_CODE == 'ar' %}معلومات التثبيت{% else %}{% trans "Installation Information" %}{% endif %}</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tr>
                                    <th width="30%">{% if LANGUAGE_CODE == 'ar' %}تاريخ التثبيت{% else %}{% trans "Installation Date" %}{% endif %}</th>
                                    <td>{{ company_module.installed_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الإصدار المثبت{% else %}{% trans "Installed Version" %}{% endif %}</th>
                                    <td>{{ company_module.installed_version }}</td>
                                </tr>
                                <tr>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}آخر تحديث{% else %}{% trans "Last Update" %}{% endif %}</th>
                                    <td>{{ company_module.updated_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                    <td>
                                        {% if company_module.is_active %}
                                        <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}مفعّل{% else %}{% trans "Active" %}{% endif %}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}معطّل{% else %}{% trans "Inactive" %}{% endif %}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if module_installed and module_active %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الوظائف الرئيسية{% else %}{% trans "Key Features" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if module.code == 'hr' %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-3x mb-3 text-primary"></i>
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}إدارة الموظفين{% else %}{% trans "Employee Management" %}{% endif %}</h5>
                                <p class="card-text small">{% if LANGUAGE_CODE == 'ar' %}إدارة معلومات الموظفين، العقود، والمستندات.{% else %}{% trans "Manage employee information, contracts, and documents." %}{% endif %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-money-check-alt fa-3x mb-3 text-success"></i>
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الرواتب والمكافآت{% else %}{% trans "Payroll & Compensation" %}{% endif %}</h5>
                                <p class="card-text small">{% if LANGUAGE_CODE == 'ar' %}إدارة الرواتب، المكافآت، والبدلات.{% else %}{% trans "Manage salaries, bonuses, and allowances." %}{% endif %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check fa-3x mb-3 text-info"></i>
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الحضور والإجازات{% else %}{% trans "Attendance & Leave" %}{% endif %}</h5>
                                <p class="card-text small">{% if LANGUAGE_CODE == 'ar' %}تتبع الحضور والانصراف وإدارة الإجازات.{% else %}{% trans "Track attendance and manage leave requests." %}{% endif %}</p>
                            </div>
                        </div>
                    </div>
                    {% elif module.code == 'inventory' %}
                    <!-- Similar feature cards for inventory module -->
                    {% elif module.code == 'accounting' %}
                    <!-- Similar feature cards for accounting module -->
                    {% else %}
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            استكشف الوحدة للوصول إلى جميع الميزات والوظائف المتاحة.
                            {% else %}
                            {% trans "Explore the module to access all available features and functionalities." %}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
