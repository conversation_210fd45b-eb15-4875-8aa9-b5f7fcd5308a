{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if action == 'install' %}
        {% if LANGUAGE_CODE == 'ar' %}تثبيت الوحدة{% else %}{% trans "Install Module" %}{% endif %}
    {% elif action == 'uninstall' %}
        {% if LANGUAGE_CODE == 'ar' %}إلغاء تثبيت الوحدة{% else %}{% trans "Uninstall Module" %}{% endif %}
    {% elif action == 'update' %}
        {% if LANGUAGE_CODE == 'ar' %}تحديث الوحدة{% else %}{% trans "Update Module" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if action == 'install' %}
                        {% if LANGUAGE_CODE == 'ar' %}تأكيد تثبيت الوحدة{% else %}{% trans "Confirm Module Installation" %}{% endif %}
                    {% elif action == 'uninstall' %}
                        {% if LANGUAGE_CODE == 'ar' %}تأكيد إلغاء تثبيت الوحدة{% else %}{% trans "Confirm Module Uninstallation" %}{% endif %}
                    {% elif action == 'update' %}
                        {% if LANGUAGE_CODE == 'ar' %}تأكيد تحديث الوحدة{% else %}{% trans "Confirm Module Update" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center mb-4">
                        <div class="module-icon-wrapper mb-3">
                            <i class="{{ module.icon|default:'fas fa-puzzle-piece' }} fa-5x module-icon"></i>
                        </div>
                        <h4>{{ module.name }}</h4>
                        <p class="text-muted">{{ module.code }}</p>
                        <p><strong>{% if LANGUAGE_CODE == 'ar' %}الإصدار:{% else %}{% trans "Version:" %}{% endif %}</strong> {{ module.version }}</p>
                    </div>
                    
                    <div class="col-md-9">
                        {% if action == 'install' %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            أنت على وشك تثبيت وحدة "{{ module.name }}". هذا سيضيف وظائف جديدة إلى النظام.
                            {% else %}
                            {% trans "You are about to install the" %} "{{ module.name }}" {% trans "module. This will add new functionality to your system." %}
                            {% endif %}
                        </div>
                        
                        {% if module.dependencies.exists %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            هذه الوحدة تعتمد على الوحدات التالية، والتي سيتم تثبيتها تلقائياً إذا لم تكن مثبتة بالفعل:
                            {% else %}
                            {% trans "This module depends on the following modules, which will be automatically installed if not already installed:" %}
                            {% endif %}
                            <ul class="mt-2 mb-0">
                                {% for dependency in module.dependencies.all %}
                                <li><strong>{{ dependency.name }}</strong> ({{ dependency.version }})</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                        
                        {% elif action == 'uninstall' %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            أنت على وشك إلغاء تثبيت وحدة "{{ module.name }}". هذا سيؤدي إلى إزالة الوظائف المرتبطة بهذه الوحدة من النظام.
                            {% else %}
                            {% trans "You are about to uninstall the" %} "{{ module.name }}" {% trans "module. This will remove the functionality associated with this module from your system." %}
                            {% endif %}
                        </div>
                        
                        {% if dependent_modules %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            لا يمكن إلغاء تثبيت هذه الوحدة لأن الوحدات التالية تعتمد عليها:
                            {% else %}
                            {% trans "This module cannot be uninstalled because the following modules depend on it:" %}
                            {% endif %}
                            <ul class="mt-2 mb-0">
                                {% for dep_module in dependent_modules %}
                                <li><strong>{{ dep_module.name }}</strong> ({{ dep_module.version }})</li>
                                {% endfor %}
                            </ul>
                            {% if LANGUAGE_CODE == 'ar' %}
                            يجب عليك إلغاء تثبيت هذه الوحدات أولاً قبل أن تتمكن من إلغاء تثبيت "{{ module.name }}".
                            {% else %}
                            {% trans "You must uninstall these modules first before you can uninstall" %} "{{ module.name }}".
                            {% endif %}
                        </div>
                        {% endif %}
                        
                        {% elif action == 'update' %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            أنت على وشك تحديث وحدة "{{ module.name }}" من الإصدار {{ installed_version }} إلى الإصدار {{ module.version }}.
                            {% else %}
                            {% trans "You are about to update the" %} "{{ module.name }}" {% trans "module from version" %} {{ installed_version }} {% trans "to version" %} {{ module.version }}.
                            {% endif %}
                        </div>
                        
                        {% if update_notes %}
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}ملاحظات التحديث{% else %}{% trans "Update Notes" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                {{ update_notes|linebreaks }}
                            </div>
                        </div>
                        {% endif %}
                        {% endif %}
                        
                        <form method="post" class="mt-4">
                            {% csrf_token %}
                            
                            {% if action == 'install' or action == 'update' %}
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmCheck" name="confirm" required>
                                <label class="form-check-label" for="confirmCheck">
                                    {% if action == 'install' %}
                                        {% if LANGUAGE_CODE == 'ar' %}
                                        أؤكد أنني أرغب في تثبيت وحدة "{{ module.name }}" وأفهم أن هذا سيضيف وظائف جديدة إلى النظام.
                                        {% else %}
                                        {% trans "I confirm that I want to install the" %} "{{ module.name }}" {% trans "module and understand that this will add new functionality to the system." %}
                                        {% endif %}
                                    {% elif action == 'update' %}
                                        {% if LANGUAGE_CODE == 'ar' %}
                                        أؤكد أنني أرغب في تحديث وحدة "{{ module.name }}" وأفهم أن هذا قد يغير سلوك الوحدة.
                                        {% else %}
                                        {% trans "I confirm that I want to update the" %} "{{ module.name }}" {% trans "module and understand that this may change the behavior of the module." %}
                                        {% endif %}
                                    {% endif %}
                                </label>
                            </div>
                            {% elif action == 'uninstall' and not dependent_modules %}
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmCheck" name="confirm" required>
                                <label class="form-check-label" for="confirmCheck">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    أؤكد أنني أرغب في إلغاء تثبيت وحدة "{{ module.name }}" وأفهم أن هذا سيؤدي إلى إزالة الوظائف المرتبطة بهذه الوحدة من النظام.
                                    {% else %}
                                    {% trans "I confirm that I want to uninstall the" %} "{{ module.name }}" {% trans "module and understand that this will remove the functionality associated with this module from the system." %}
                                    {% endif %}
                                </label>
                            </div>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between mt-4">
                                <a href="{% url 'modules:module_list' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                                </a>
                                
                                {% if action == 'install' %}
                                <button type="submit" class="btn btn-success" {% if not request.user.company %}disabled{% endif %}>
                                    <i class="fas fa-download me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تثبيت{% else %}{% trans "Install" %}{% endif %}
                                </button>
                                {% elif action == 'uninstall' %}
                                <button type="submit" class="btn btn-danger" {% if dependent_modules %}disabled{% endif %}>
                                    <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء التثبيت{% else %}{% trans "Uninstall" %}{% endif %}
                                </button>
                                {% elif action == 'update' %}
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sync-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تحديث{% else %}{% trans "Update" %}{% endif %}
                                </button>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if action == 'install' %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الوظائف الرئيسية{% else %}{% trans "Key Features" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if module.code == 'hr' %}
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-3x mb-3 text-primary"></i>
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}إدارة الموظفين{% else %}{% trans "Employee Management" %}{% endif %}</h5>
                                <p class="card-text small">{% if LANGUAGE_CODE == 'ar' %}إدارة معلومات الموظفين، العقود، والمستندات.{% else %}{% trans "Manage employee information, contracts, and documents." %}{% endif %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-money-check-alt fa-3x mb-3 text-success"></i>
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الرواتب والمكافآت{% else %}{% trans "Payroll & Compensation" %}{% endif %}</h5>
                                <p class="card-text small">{% if LANGUAGE_CODE == 'ar' %}إدارة الرواتب، المكافآت، والبدلات.{% else %}{% trans "Manage salaries, bonuses, and allowances." %}{% endif %}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check fa-3x mb-3 text-info"></i>
                                <h5 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الحضور والإجازات{% else %}{% trans "Attendance & Leave" %}{% endif %}</h5>
                                <p class="card-text small">{% if LANGUAGE_CODE == 'ar' %}تتبع الحضور والانصراف وإدارة الإجازات.{% else %}{% trans "Track attendance and manage leave requests." %}{% endif %}</p>
                            </div>
                        </div>
                    </div>
                    {% elif module.code == 'inventory' %}
                    <!-- Similar feature cards for inventory module -->
                    {% elif module.code == 'accounting' %}
                    <!-- Similar feature cards for accounting module -->
                    {% else %}
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            ستتمكن من استكشاف جميع ميزات ووظائف هذه الوحدة بعد التثبيت.
                            {% else %}
                            {% trans "You will be able to explore all features and functionalities of this module after installation." %}
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
