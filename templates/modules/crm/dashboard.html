{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Customer Relationship Management" %}{% endblock %}
{% block module_name %}{% trans "CRM" %}{% endblock %}
{% block module_icon %}fas fa-handshake{% endblock %}
{% block module_header %}{% trans "Customer Relationship Management" %}{% endblock %}
{% block module_description %}{% trans "Build stronger customer relationships. Manage leads, opportunities, customer communications, and sales pipeline in one integrated system." %}{% endblock %}

{% block sidebar_header_class %}bg-warning{% endblock %}
{% block quick_actions_header_class %}bg-warning{% endblock %}

{% block module_alert %}
<div class="alert alert-warning alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-handshake fa-2x text-warning"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to CRM" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your customer relationships. Use the navigation menu to access different CRM functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-user-tie me-2"></i> {% trans "Leads" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-lightbulb me-2"></i> {% trans "Opportunities" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% trans "Customers" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-phone-alt me-2"></i> {% trans "Calls" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% trans "Meetings" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-envelope me-2"></i> {% trans "Email Campaigns" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-warning">
        <i class="fas fa-plus me-2"></i> {% trans "New Lead" %}
    </button>
    <button class="btn btn-outline-warning">
        <i class="fas fa-calendar-plus me-2"></i> {% trans "Schedule Meeting" %}
    </button>
    <button class="btn btn-outline-warning">
        <i class="fas fa-envelope me-2"></i> {% trans "Send Campaign" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-user-tie fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "New Leads" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-lightbulb fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Opportunities" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Won Deals" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Lost Deals" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Sales Pipeline" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Recent Leads" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Company" %}</th>
                        <th>{% trans "Email" %}</th>
                        <th>{% trans "Phone" %}</th>
                        <th>{% trans "Source" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No leads found" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Lead Sources" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No lead source data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Upcoming Activities" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No upcoming activities" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('CRM Module loaded');
    });
</script>
{% endblock %}
