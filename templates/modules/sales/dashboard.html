{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة المبيعات{% else %}{% trans "Sales Management" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}المبيعات{% else %}{% trans "Sales" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-chart-line{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إدارة المبيعات{% else %}{% trans "Sales Management" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}تعزيز أداء المبيعات باستخدام أدوات شاملة لعروض الأسعار والطلبات والفواتير وإدارة العملاء.{% else %}{% trans "Boost your sales performance with comprehensive tools for quotations, orders, invoicing, and customer management." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-warning{% endblock %}
{% block quick_actions_header_class %}bg-warning{% endblock %}

{% block module_alert %}
<div class="alert alert-warning alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-chart-line fa-2x text-warning"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% if LANGUAGE_CODE == 'ar' %}مرحباً بك في إدارة المبيعات{% else %}{% trans "Welcome to Sales Management" %}{% endif %}</h5>
            <p>{% if LANGUAGE_CODE == 'ar' %}يساعدك هذا الموديول على إدارة جميع جوانب عمليات المبيعات الخاصة بك. استخدم قائمة التنقل للوصول إلى وظائف المبيعات المختلفة.{% else %}{% trans "This module helps you manage all aspects of your sales operations. Use the navigation menu to access different sales functions." %}{% endif %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% if LANGUAGE_CODE == 'ar' %}العملاء{% else %}{% trans "Customers" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice-dollar me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عروض الأسعار{% else %}{% trans "Quotations" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-shopping-cart me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الطلبات{% else %}{% trans "Orders" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الفواتير{% else %}{% trans "Invoices" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-pie me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التقارير{% else %}{% trans "Reports" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-warning">
        <i class="fas fa-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}عرض سعر جديد{% else %}{% trans "New Quotation" %}{% endif %}
    </button>
    <button class="btn btn-outline-warning">
        <i class="fas fa-user-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة عميل{% else %}{% trans "Add Customer" %}{% endif %}
    </button>
    <button class="btn btn-outline-warning">
        <i class="fas fa-file-invoice me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء فاتورة{% else %}{% trans "Create Invoice" %}{% endif %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}عروض الأسعار{% else %}{% trans "Quotations" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الطلبات{% else %}{% trans "Orders" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}العملاء{% else %}{% trans "Customers" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الإيرادات{% else %}{% trans "Revenue" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}0 ريال{% else %}$0{% endif %}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}نظرة عامة على المبيعات{% else %}{% trans "Sales Overview" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}هذه الوحدة قيد التطوير. سيتم توفير التنفيذ الفعلي في التحديثات المستقبلية.{% else %}{% trans "This module is under development. The actual implementation will be available in future updates." %}{% endif %}
        </div>

        <h5 class="mt-4 mb-3">{% if LANGUAGE_CODE == 'ar' %}الطلبات الأخيرة{% else %}{% trans "Recent Orders" %}{% endif %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}رقم الطلب{% else %}{% trans "Order #" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}العميل{% else %}{% trans "Customer" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}المجموع{% else %}{% trans "Total" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center">{% if LANGUAGE_CODE == 'ar' %}لم يتم العثور على طلبات{% else %}{% trans "No orders found" %}{% endif %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}أهم العملاء{% else %}{% trans "Top Customers" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات عملاء متاحة{% else %}{% trans "No customer data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المبيعات حسب المنتج{% else %}{% trans "Sales by Product" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات مبيعات متاحة{% else %}{% trans "No sales data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Sales Module loaded');
    });
</script>
{% endblock %}
