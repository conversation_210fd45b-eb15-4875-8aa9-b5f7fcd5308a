{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Front Desk Management" %}{% endblock %}
{% block module_name %}{% trans "Front Desk" %}{% endblock %}
{% block module_icon %}fas fa-desktop{% endblock %}
{% block module_header %}{% trans "Front Desk Management" %}{% endblock %}
{% block module_description %}{% trans "Streamline your reception operations. Manage visitor check-ins, appointments, and front office tasks efficiently." %}{% endblock %}

{% block sidebar_header_class %}bg-primary{% endblock %}
{% block quick_actions_header_class %}bg-primary{% endblock %}

{% block module_alert %}
<div class="alert alert-primary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-desktop fa-2x text-primary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Front Desk Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your reception operations. Use the navigation menu to access different front desk functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-user-check me-2"></i> {% trans "Visitor Check-in" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% trans "Appointments" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-bell me-2"></i> {% trans "Notifications" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-box me-2"></i> {% trans "Packages" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-phone-alt me-2"></i> {% trans "Call Log" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-cog me-2"></i> {% trans "Settings" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-primary">
        <i class="fas fa-user-plus me-2"></i> {% trans "New Visitor" %}
    </button>
    <button class="btn btn-outline-primary">
        <i class="fas fa-calendar-plus me-2"></i> {% trans "Schedule Appointment" %}
    </button>
    <button class="btn btn-outline-primary">
        <i class="fas fa-bell me-2"></i> {% trans "Send Notification" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-primary h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-primary text-white rounded p-3">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Today's Visitors" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Appointments" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-box fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Pending Packages" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-phone-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Today's Calls" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Front Desk Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Current Visitors" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Visitor" %}</th>
                        <th>{% trans "Company" %}</th>
                        <th>{% trans "Host" %}</th>
                        <th>{% trans "Purpose" %}</th>
                        <th>{% trans "Check-in Time" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No visitors currently checked in" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Today's Appointments" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No appointments scheduled for today" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Recent Notifications" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No recent notifications" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Front Desk Module loaded');
    });
</script>
{% endblock %}
