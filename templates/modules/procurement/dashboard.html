{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة المشتريات{% else %}{% trans "Procurement Management" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}المشتريات{% else %}{% trans "Procurement" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-shopping-cart{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إدارة المشتريات{% else %}{% trans "Procurement Management" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}تبسيط عملية الشراء من الطلب إلى الدفع. إدارة الموردين، تتبع أوامر الشراء، والتحكم في التكاليف.{% else %}{% trans "Streamline your purchasing process from requisition to payment. Manage vendors, track purchase orders, and control costs." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-primary{% endblock %}
{% block quick_actions_header_class %}bg-primary{% endblock %}

{% block module_alert %}
<div class="alert alert-primary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-shopping-cart fa-2x text-primary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Procurement Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your procurement operations. Use the navigation menu to access different procurement functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% trans "Purchase Requests" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice-dollar me-2"></i> {% trans "Quotations" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-shopping-cart me-2"></i> {% trans "Purchase Orders" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-truck me-2"></i> {% trans "Vendors" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-primary">
        <i class="fas fa-plus me-2"></i> {% trans "New Purchase Request" %}
    </button>
    <button class="btn btn-outline-primary">
        <i class="fas fa-file-invoice-dollar me-2"></i> {% trans "Request Quotation" %}
    </button>
    <button class="btn btn-outline-primary">
        <i class="fas fa-user-plus me-2"></i> {% trans "Add Vendor" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-primary h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-primary text-white rounded p-3">
                        <i class="fas fa-file-alt fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Purchase Requests" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-file-invoice-dollar fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Quotations" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Purchase Orders" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-truck fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Vendors" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Procurement Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>

        <h5 class="mt-4 mb-3">{% trans "Recent Purchase Orders" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "PO Number" %}</th>
                        <th>{% trans "Vendor" %}</th>
                        <th>{% trans "Date" %}</th>
                        <th>{% trans "Total" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="6" class="text-center">{% trans "No purchase orders found" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Pending Approvals" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No pending approvals" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Top Vendors" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No vendor data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Procurement Module loaded');
    });
</script>
{% endblock %}
