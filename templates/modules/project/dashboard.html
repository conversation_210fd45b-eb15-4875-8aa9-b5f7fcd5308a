{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Project Management" %}{% endblock %}
{% block module_name %}{% trans "Projects" %}{% endblock %}
{% block module_icon %}fas fa-project-diagram{% endblock %}
{% block module_header %}{% trans "Project Management" %}{% endblock %}
{% block module_description %}{% trans "Deliver projects on time and within budget. Plan, execute, and monitor projects with comprehensive tools for task management, resource allocation, and progress tracking." %}{% endblock %}

{% block sidebar_header_class %}bg-info{% endblock %}
{% block quick_actions_header_class %}bg-info{% endblock %}

{% block module_alert %}
<div class="alert alert-info alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-project-diagram fa-2x text-info"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Project Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your project operations. Use the navigation menu to access different project functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-clipboard-list me-2"></i> {% trans "Projects" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-tasks me-2"></i> {% trans "Tasks" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-users me-2"></i> {% trans "Teams" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-calendar-alt me-2"></i> {% trans "Timesheet" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice-dollar me-2"></i> {% trans "Billing" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-info">
        <i class="fas fa-plus me-2"></i> {% trans "New Project" %}
    </button>
    <button class="btn btn-outline-info">
        <i class="fas fa-tasks me-2"></i> {% trans "Create Task" %}
    </button>
    <button class="btn btn-outline-info">
        <i class="fas fa-file-alt me-2"></i> {% trans "Generate Reports" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-clipboard-list fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Active Projects" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Open Tasks" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Overdue Tasks" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Completed Tasks" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Project Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Active Projects" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Project Name" %}</th>
                        <th>{% trans "Client" %}</th>
                        <th>{% trans "Start Date" %}</th>
                        <th>{% trans "Deadline" %}</th>
                        <th>{% trans "Progress" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No active projects" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Recent Activities" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No recent activities" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Upcoming Deadlines" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No upcoming deadlines" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Project Module loaded');
    });
</script>
{% endblock %}
