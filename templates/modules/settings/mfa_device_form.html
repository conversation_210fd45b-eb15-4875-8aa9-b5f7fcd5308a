{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إضافة جهاز مصادقة متعددة العوامل{% else %}{% trans "Add MFA Device" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إضافة جهاز مصادقة متعددة العوامل{% else %}{% trans "Add MFA Device" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.device_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع الجهاز{% else %}{% trans "Device Type" %}{% endif %}</label>
                            {{ form.device_type }}
                            {% if form.device_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.device_type.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.device_type.help_text %}
                            <div class="form-text">{{ form.device_type.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6">
                            <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم الجهاز{% else %}{% trans "Device Name" %}{% endif %}</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.name.help_text %}
                            <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-check mb-3">
                        {{ form.is_primary }}
                        <label class="form-check-label" for="{{ form.is_primary.id_for_label }}">
                            {% if LANGUAGE_CODE == 'ar' %}تعيين كجهاز أساسي{% else %}{% trans "Set as primary device" %}{% endif %}
                        </label>
                        {% if form.is_primary.help_text %}
                        <div class="form-text">{{ form.is_primary.help_text }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="device-type-info totp-info">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            ستحتاج إلى تطبيق مصادقة مثل Google Authenticator أو Microsoft Authenticator أو Authy. بعد إنشاء الجهاز، سيتم عرض رمز QR لمسحه ضوئيًا باستخدام تطبيق المصادقة الخاص بك.
                            {% else %}
                            {% trans "You will need an authenticator app like Google Authenticator, Microsoft Authenticator, or Authy. After creating the device, a QR code will be displayed for you to scan with your authenticator app." %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="device-type-info sms-info d-none">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            ستتلقى رموز التحقق عبر الرسائل النصية على رقم هاتفك المحمول المسجل في ملف التعريف الخاص بك.
                            {% else %}
                            {% trans "You will receive verification codes via text messages on your mobile phone number registered in your profile." %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="device-type-info email-info d-none">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            ستتلقى رموز التحقق عبر البريد الإلكتروني على عنوان بريدك الإلكتروني المسجل.
                            {% else %}
                            {% trans "You will receive verification codes via email to your registered email address." %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'core:security:mfa_devices' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة جهاز{% else %}{% trans "Add Device" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات المصادقة متعددة العوامل{% else %}{% trans "MFA Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <h6>{% if LANGUAGE_CODE == 'ar' %}ما هي المصادقة متعددة العوامل؟{% else %}{% trans "What is Multi-Factor Authentication?" %}{% endif %}</h6>
                <p>
                    {% if LANGUAGE_CODE == 'ar' %}
                    المصادقة متعددة العوامل (MFA) هي طريقة أمان تتطلب من المستخدمين تقديم دليلين أو أكثر للتحقق من هويتهم قبل منح الوصول. تضيف MFA طبقة إضافية من الحماية لحسابك، مما يجعل من الصعب على المتسللين الوصول إلى بياناتك حتى لو تمكنوا من معرفة كلمة المرور الخاصة بك.
                    {% else %}
                    {% trans "Multi-Factor Authentication (MFA) is a security method that requires users to provide two or more verification factors to gain access. MFA adds an extra layer of protection to your account, making it harder for attackers to access your data even if they know your password." %}
                    {% endif %}
                </p>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}أنواع أجهزة المصادقة متعددة العوامل{% else %}{% trans "Types of MFA Devices" %}{% endif %}</h6>
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-mobile-alt me-2 text-info"></i>
                                    {% if LANGUAGE_CODE == 'ar' %}تطبيق المصادقة{% else %}{% trans "Authenticator App" %}{% endif %}
                                </h6>
                                <p class="card-text small">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    تطبيقات مثل Google Authenticator أو Microsoft Authenticator أو Authy تولد رموز تحقق تتغير كل 30 ثانية. هذه هي الطريقة الأكثر أمانًا وموثوقية للمصادقة متعددة العوامل.
                                    {% else %}
                                    {% trans "Apps like Google Authenticator, Microsoft Authenticator, or Authy generate verification codes that change every 30 seconds. This is the most secure and reliable method of MFA." %}
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-sms me-2 text-success"></i>
                                    {% if LANGUAGE_CODE == 'ar' %}رسالة نصية{% else %}{% trans "SMS" %}{% endif %}
                                </h6>
                                <p class="card-text small">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    تلقي رموز التحقق عبر الرسائل النصية على هاتفك المحمول. هذه الطريقة سهلة الاستخدام ولكنها قد تكون عرضة لهجمات تبديل بطاقة SIM.
                                    {% else %}
                                    {% trans "Receive verification codes via text messages on your mobile phone. This method is easy to use but may be vulnerable to SIM swapping attacks." %}
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-envelope me-2 text-warning"></i>
                                    {% if LANGUAGE_CODE == 'ar' %}بريد إلكتروني{% else %}{% trans "Email" %}{% endif %}
                                </h6>
                                <p class="card-text small">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    تلقي رموز التحقق عبر البريد الإلكتروني. هذه الطريقة مناسبة عندما لا تكون الخيارات الأخرى متاحة، ولكنها ليست آمنة مثل تطبيق المصادقة.
                                    {% else %}
                                    {% trans "Receive verification codes via email. This method is suitable when other options are not available, but it's not as secure as an authenticator app." %}
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Show/hide device type info based on selection
        $('#id_device_type').change(function() {
            var deviceType = $(this).val();
            $('.device-type-info').addClass('d-none');
            $('.' + deviceType + '-info').removeClass('d-none');
        });
        
        // Initial state
        var initialDeviceType = $('#id_device_type').val();
        $('.device-type-info').addClass('d-none');
        $('.' + initialDeviceType + '-info').removeClass('d-none');
    });
</script>
{% endblock %}
