{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}سجلات النظام{% else %}{% trans "System Logs" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}سجلات النظام{% else %}{% trans "System Logs" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تتيح لك سجلات النظام مراقبة أنشطة النظام وتشخيص المشكلات. اختر نوع السجل من القائمة أدناه لعرض محتواه.
                    {% else %}
                    {% trans "System logs allow you to monitor system activities and diagnose issues. Select a log type from the list below to view its contents." %}
                    {% endif %}
                </div>

                <div class="list-group mt-3">
                    {% for log_type in log_types %}
                    <a href="{% url 'core:settings:view_log' log_type.name %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        <div>
                            {% if log_type.name == 'application' %}
                            <i class="fas fa-cogs me-2 text-primary"></i>
                            {% elif log_type.name == 'error' %}
                            <i class="fas fa-exclamation-triangle me-2 text-danger"></i>
                            {% elif log_type.name == 'access' %}
                            <i class="fas fa-user-shield me-2 text-success"></i>
                            {% else %}
                            <i class="fas fa-file-alt me-2 text-secondary"></i>
                            {% endif %}
                            {{ log_type.description }}
                        </div>
                        <div class="d-flex align-items-center">
                            {% if log_type.exists %}
                            <span class="badge bg-success me-2">
                                {{ log_type.size_formatted }}
                            </span>
                            <span class="text-muted small me-3">
                                {{ log_type.modified|date:"Y-m-d H:i" }}
                            </span>
                            {% else %}
                            <span class="badge bg-secondary me-2">
                                {% if LANGUAGE_CODE == 'ar' %}غير موجود{% else %}{% trans "Not Found" %}{% endif %}
                            </span>
                            {% endif %}
                            <span class="badge bg-primary">
                                {% if log_type.name == 'application' %}
                                {% if LANGUAGE_CODE == 'ar' %}سجل التطبيق{% else %}{% trans "Application Log" %}{% endif %}
                                {% elif log_type.name == 'error' %}
                                {% if LANGUAGE_CODE == 'ar' %}سجل الأخطاء{% else %}{% trans "Error Log" %}{% endif %}
                                {% elif log_type.name == 'access' %}
                                {% if LANGUAGE_CODE == 'ar' %}سجل الوصول{% else %}{% trans "Access Log" %}{% endif %}
                                {% else %}
                                {{ log_type.name }}
                                {% endif %}
                            </span>
                        </div>
                    </a>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
