{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% if LANGUAGE_CODE == 'ar' %}إعدادات النظام{% else %}{% trans "System Settings" %}{% endif %}{% endblock %}
{% block module_name %}{% if LANGUAGE_CODE == 'ar' %}الإعدادات{% else %}{% trans "Settings" %}{% endif %}{% endblock %}
{% block module_icon %}fas fa-cogs{% endblock %}
{% block module_header %}{% if LANGUAGE_CODE == 'ar' %}إعدادات النظام{% else %}{% trans "System Settings" %}{% endif %}{% endblock %}
{% block module_description %}{% if LANGUAGE_CODE == 'ar' %}تكوين النظام الخاص بك ليتناسب مع احتياجات عملك. إدارة المستخدمين والأذونات ومعلومات الشركة وتفضيلات النظام.{% else %}{% trans "Configure your system to match your business needs. Manage users, permissions, company information, and system preferences." %}{% endif %}{% endblock %}

{% block sidebar_header_class %}bg-dark{% endblock %}
{% block quick_actions_header_class %}bg-dark{% endblock %}

{% block module_alert %}
<div class="alert alert-dark alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-cogs fa-2x text-dark"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% if LANGUAGE_CODE == 'ar' %}مرحباً بك في إعدادات النظام{% else %}{% trans "Welcome to System Settings" %}{% endif %}</h5>
            <p>{% if LANGUAGE_CODE == 'ar' %}يساعدك هذا الموديول على تكوين جميع جوانب نظام تخطيط موارد المؤسسات الخاص بك. استخدم قائمة التنقل للوصول إلى فئات الإعدادات المختلفة.{% else %}{% trans "This module helps you configure all aspects of your ERP system. Use the navigation menu to access different settings categories." %}{% endif %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="{% url 'core:settings:dashboard' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
    <i class="fas fa-tachometer-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}لوحة التحكم{% else %}{% trans "Dashboard" %}{% endif %}
</a>

<a href="{% url 'users:user_list' %}" class="list-group-item list-group-item-action {% if 'users:user' in request.resolver_match.view_name %}active{% endif %}">
    <i class="fas fa-users me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}{% trans "User Management" %}{% endif %}
</a>
<a href="{% url 'users:group_list' %}" class="list-group-item list-group-item-action {% if 'users:group' in request.resolver_match.view_name %}active{% endif %}">
    <i class="fas fa-users-cog me-2"></i> {% if LANGUAGE_CODE == 'ar' %}المجموعات والصلاحيات{% else %}{% trans "Groups & Permissions" %}{% endif %}
</a>
<a href="{% url 'companies:company_list' %}" class="list-group-item list-group-item-action {% if 'companies:company' in request.resolver_match.view_name %}active{% endif %}">
    <i class="fas fa-building me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الشركات{% else %}{% trans "Company Management" %}{% endif %}
</a>
<a href="{% url 'companies:branch_list' %}" class="list-group-item list-group-item-action {% if 'companies:branch' in request.resolver_match.view_name %}active{% endif %}">
    <i class="fas fa-sitemap me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الفروع{% else %}{% trans "Branch Management" %}{% endif %}
</a>
<a href="{% url 'modules:module_list' %}" class="list-group-item list-group-item-action {% if 'modules:module_list' == request.resolver_match.view_name %}active{% endif %}">
    <i class="fas fa-puzzle-piece me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الوحدات{% else %}{% trans "Module Management" %}{% endif %}
</a>
<a href="{% url 'modules:license_management' %}" class="list-group-item list-group-item-action {% if 'modules:license_management' == request.resolver_match.view_name %}active{% endif %}">
    <i class="fas fa-key me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة التراخيص{% else %}{% trans "License Management" %}{% endif %}
</a>


<a href="{% url 'core:settings:system_settings_list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'system_settings_list' or request.resolver_match.url_name == 'system_setting_create' or request.resolver_match.url_name == 'system_setting_edit' or request.resolver_match.url_name == 'system_setting_delete' %}active{% endif %}">
    <i class="fas fa-cogs me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إعدادات النظام{% else %}{% trans "System Settings" %}{% endif %}
</a>
<a href="{% url 'core:settings:company_settings_list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'company_settings_list' or request.resolver_match.url_name == 'company_setting_create' or request.resolver_match.url_name == 'company_setting_edit' or request.resolver_match.url_name == 'company_setting_delete' %}active{% endif %}">
    <i class="fas fa-sliders-h me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إعدادات الشركة{% else %}{% trans "Company Settings" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-globe me-2"></i> {% if LANGUAGE_CODE == 'ar' %}التوطين{% else %}{% trans "Localization" %}{% endif %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-envelope me-2"></i> {% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}
</a>
<a href="{% url 'core:security:dashboard' %}" class="list-group-item list-group-item-action {% if 'core:security' in request.resolver_match.namespace %}active{% endif %}">
    <i class="fas fa-shield-alt me-2"></i> {% if LANGUAGE_CODE == 'ar' %}الأمان{% else %}{% trans "Security" %}{% endif %}
</a>
<a href="{% url 'core:settings:backup_list' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'backup_list' or request.resolver_match.url_name == 'backup_create' or request.resolver_match.url_name == 'backup_restore' %}active{% endif %}">
    <i class="fas fa-database me-2"></i> {% if LANGUAGE_CODE == 'ar' %}النسخ الاحتياطي والاستعادة{% else %}{% trans "Backup & Restore" %}{% endif %}
</a>
<a href="{% url 'core:settings:system_logs' %}" class="list-group-item list-group-item-action {% if request.resolver_match.url_name == 'system_logs' or request.resolver_match.url_name == 'view_log' %}active{% endif %}">
    <i class="fas fa-history me-2"></i> {% if LANGUAGE_CODE == 'ar' %}سجلات النظام{% else %}{% trans "System Logs" %}{% endif %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <a href="{% url 'users:user_create' %}" class="btn btn-dark">
        <i class="fas fa-user-plus me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة مستخدم{% else %}{% trans "Add User" %}{% endif %}
    </a>
    <a href="{% url 'core:settings:system_settings_list' %}" class="btn btn-outline-dark">
        <i class="fas fa-cogs me-2"></i> {% if LANGUAGE_CODE == 'ar' %}إعدادات النظام{% else %}{% trans "System Settings" %}{% endif %}
    </a>
    <a href="{% url 'core:settings:backup_create' %}" class="btn btn-outline-dark">
        <i class="fas fa-database me-2"></i> {% if LANGUAGE_CODE == 'ar' %}نسخ احتياطي للنظام{% else %}{% trans "Backup System" %}{% endif %}
    </a>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-dark h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-dark text-white rounded p-3">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}المستخدمين النشطين{% else %}{% trans "Active Users" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">{{ stats.users_count }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-puzzle-piece fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الوحدات المثبتة{% else %}{% trans "Installed Modules" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">{{ stats.modules_count }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-sitemap fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}الفروع{% else %}{% trans "Branches" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">{{ stats.branches_count }}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-warning h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-warning text-white rounded p-3">
                        <i class="fas fa-database fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% if LANGUAGE_CODE == 'ar' %}آخر نسخة احتياطية{% else %}{% trans "Last Backup" %}{% endif %}</h6>
                    <h3 class="card-title mb-0">{% if stats.last_backup %}{{ stats.last_backup|date:"Y-m-d" }}{% else %}-{% endif %}</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}نظرة عامة على النظام{% else %}{% trans "System Overview" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% if LANGUAGE_CODE == 'ar' %}هذه الوحدة قيد التطوير. سيتم توفير التنفيذ الفعلي في التحديثات المستقبلية.{% else %}{% trans "This module is under development. The actual implementation will be available in future updates." %}{% endif %}
        </div>

        <h5 class="mt-4 mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات النظام{% else %}{% trans "System Information" %}{% endif %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <tbody>
                    <tr>
                        <th width="30%">{% if LANGUAGE_CODE == 'ar' %}إصدار النظام{% else %}{% trans "System Version" %}{% endif %}</th>
                        <td>{{ system_info.version }}</td>
                    </tr>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}قاعدة البيانات{% else %}{% trans "Database" %}{% endif %}</th>
                        <td>{{ system_info.database }}</td>
                    </tr>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}آخر تحديث{% else %}{% trans "Last Update" %}{% endif %}</th>
                        <td>{% if system_info.last_update %}{{ system_info.last_update|date:"Y-m-d H:i" }}{% else %}-{% endif %}</td>
                    </tr>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}وقت الخادم{% else %}{% trans "Server Time" %}{% endif %}</th>
                        <td>{{ system_info.server_time|date:"Y-m-d H:i:s" }}</td>
                    </tr>
                    <tr>
                        <th>{% if LANGUAGE_CODE == 'ar' %}الترخيص{% else %}{% trans "License" %}{% endif %}</th>
                        <td>-</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الأنشطة الأخيرة{% else %}{% trans "Recent Activities" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد أنشطة حديثة{% else %}{% trans "No recent activities" %}{% endif %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}صحة النظام{% else %}{% trans "System Health" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% if LANGUAGE_CODE == 'ar' %}لا توجد بيانات متاحة عن صحة النظام{% else %}{% trans "No system health data available" %}{% endif %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Settings Module loaded');
    });
</script>
{% endblock %}
