{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}أجهزة المصادقة متعددة العوامل{% else %}{% trans "Multi-Factor Authentication Devices" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}أجهزة المصادقة متعددة العوامل{% else %}{% trans "MFA Devices" %}{% endif %}</h5>
                <a href="{% url 'core:security:mfa_device_create' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة جهاز جديد{% else %}{% trans "Add New Device" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if security_settings and security_settings.mfa_type == 'required' and not mfa_devices %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    المصادقة متعددة العوامل مطلوبة. يرجى إعداد جهاز المصادقة متعددة العوامل.
                    {% else %}
                    {% trans "Multi-factor authentication is required. Please set up an MFA device." %}
                    {% endif %}
                </div>
                {% endif %}
                
                {% if mfa_devices %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم{% else %}{% trans "Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}آخر استخدام{% else %}{% trans "Last Used" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for device in mfa_devices %}
                            <tr>
                                <td>
                                    {{ device.name }}
                                    {% if device.is_primary %}
                                    <span class="badge bg-primary ms-2">{% if LANGUAGE_CODE == 'ar' %}أساسي{% else %}{% trans "Primary" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if device.device_type == 'totp' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}تطبيق المصادقة{% else %}{% trans "Authenticator App" %}{% endif %}</span>
                                    {% elif device.device_type == 'sms' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}رسالة نصية{% else %}{% trans "SMS" %}{% endif %}</span>
                                    {% elif device.device_type == 'email' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}بريد إلكتروني{% else %}{% trans "Email" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if device.last_used %}
                                    {{ device.last_used|date:"Y-m-d H:i" }}
                                    {% else %}
                                    <span class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لم يستخدم بعد{% else %}{% trans "Never used" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if device.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'core:security:mfa_device_verify' device.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}التحقق{% else %}{% trans 'Verify' %}{% endif %}">
                                        <i class="fas fa-check"></i>
                                    </a>
                                    <a href="{% url 'core:security:mfa_device_delete' device.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    لا توجد أجهزة مصادقة متعددة العوامل مضبوطة. انقر على "إضافة جهاز جديد" لإعداد المصادقة متعددة العوامل.
                    {% else %}
                    {% trans "No MFA devices set up. Click 'Add New Device' to set up multi-factor authentication." %}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات المصادقة متعددة العوامل{% else %}{% trans "MFA Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    المصادقة متعددة العوامل (MFA) تضيف طبقة إضافية من الأمان لحسابك. عند تسجيل الدخول، ستحتاج إلى توفير رمز تحقق بالإضافة إلى كلمة المرور الخاصة بك.
                    {% else %}
                    {% trans "Multi-Factor Authentication (MFA) adds an extra layer of security to your account. When logging in, you'll need to provide a verification code in addition to your password." %}
                    {% endif %}
                </div>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}أنواع أجهزة المصادقة متعددة العوامل{% else %}{% trans "MFA Device Types" %}{% endif %}</h6>
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}تطبيق المصادقة{% else %}{% trans "Authenticator App" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}استخدم تطبيق مصادقة مثل Google Authenticator أو Microsoft Authenticator أو Authy للحصول على رموز تحقق.{% else %}{% trans "Use an authenticator app like Google Authenticator, Microsoft Authenticator, or Authy to get verification codes." %}{% endif %}</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}رسالة نصية{% else %}{% trans "SMS" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}تلقي رموز التحقق عبر الرسائل النصية على هاتفك المحمول.{% else %}{% trans "Receive verification codes via text messages on your mobile phone." %}{% endif %}</td>
                            </tr>
                            <tr>
                                <td><span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}بريد إلكتروني{% else %}{% trans "Email" %}{% endif %}</span></td>
                                <td>{% if LANGUAGE_CODE == 'ar' %}تلقي رموز التحقق عبر البريد الإلكتروني.{% else %}{% trans "Receive verification codes via email." %}{% endif %}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}نصائح أمان{% else %}{% trans "Security Tips" %}{% endif %}</h6>
                <ul>
                    <li>{% if LANGUAGE_CODE == 'ar' %}قم بإعداد أكثر من جهاز مصادقة متعددة العوامل كنسخة احتياطية.{% else %}{% trans "Set up more than one MFA device as a backup." %}{% endif %}</li>
                    <li>{% if LANGUAGE_CODE == 'ar' %}احتفظ برموز النسخ الاحتياطي في مكان آمن.{% else %}{% trans "Keep backup codes in a safe place." %}{% endif %}</li>
                    <li>{% if LANGUAGE_CODE == 'ar' %}لا تشارك رموز التحقق الخاصة بك مع أي شخص.{% else %}{% trans "Never share your verification codes with anyone." %}{% endif %}</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
