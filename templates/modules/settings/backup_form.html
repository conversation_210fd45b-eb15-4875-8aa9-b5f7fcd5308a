{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إنشاء نسخة احتياطية جديدة{% else %}{% trans "Create New Backup" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إنشاء نسخة احتياطية جديدة{% else %}{% trans "Create New Backup" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.backup_name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم النسخة الاحتياطية{% else %}{% trans "Backup Name" %}{% endif %}</label>
                        {{ form.backup_name }}
                        {% if form.backup_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.backup_name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">{% if LANGUAGE_CODE == 'ar' %}اختياري. إذا تركت فارغًا، سيتم استخدام التاريخ والوقت الحاليين.{% else %}{% trans "Optional. If left blank, current date and time will be used." %}{% endif %}</div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.include_media }}
                        <label class="form-check-label" for="{{ form.include_media.id_for_label }}">
                            {% if LANGUAGE_CODE == 'ar' %}تضمين ملفات الوسائط{% else %}{% trans "Include Media Files" %}{% endif %}
                        </label>
                        {% if form.include_media.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.include_media.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">{% if LANGUAGE_CODE == 'ar' %}تضمين ملفات الوسائط (الصور، المستندات، إلخ) في النسخة الاحتياطية.{% else %}{% trans "Include media files (images, documents, etc.) in the backup." %}{% endif %}</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}
                        سيتم إنشاء نسخة احتياطية كاملة من قاعدة البيانات وإعدادات النظام. قد يستغرق هذا بعض الوقت اعتمادًا على حجم البيانات.
                        {% else %}
                        {% trans "A full backup of the database and system settings will be created. This may take some time depending on the size of the data." %}
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'core:settings:backup_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء نسخة احتياطية{% else %}{% trans "Create Backup" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
