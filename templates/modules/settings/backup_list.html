{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}النسخ الاحتياطي والاستعادة{% else %}{% trans "Backup & Restore" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة النسخ الاحتياطية{% else %}{% trans "Backup List" %}{% endif %}</h5>
                <a href="{% url 'core:settings:backup_create' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إنشاء نسخة احتياطية جديدة{% else %}{% trans "Create New Backup" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if backups %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم الملف{% else %}{% trans "Filename" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ الإنشاء{% else %}{% trans "Created Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحجم{% else %}{% trans "Size" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for backup in backups %}
                            <tr>
                                <td>{{ backup.filename }}</td>
                                <td>{{ backup.created_at|date:"Y-m-d H:i" }}</td>
                                <td>{{ backup.size }}</td>
                                <td>{{ backup.type }}</td>
                                <td>
                                    <a href="{% url 'core:settings:backup_download' backup.filename %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <a href="{% url 'core:settings:backup_restore' backup.filename %}" class="btn btn-sm btn-outline-warning">
                                        <i class="fas fa-undo"></i>
                                    </a>
                                    <a href="{% url 'core:settings:backup_delete' backup.filename %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد نسخ احتياطية حتى الآن. انقر على "إنشاء نسخة احتياطية جديدة" لإنشاء أول نسخة احتياطية.{% else %}{% trans "No backups yet. Click 'Create New Backup' to create your first backup." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}استعادة من نسخة احتياطية{% else %}{% trans "Restore from Backup" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <form method="post" action="{% url 'core:settings:backup_restore' 'upload' %}" enctype="multipart/form-data">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="backup_file" class="form-label">{% if LANGUAGE_CODE == 'ar' %}ملف النسخة الاحتياطية{% else %}{% trans "Backup File" %}{% endif %}</label>
                        <input type="file" class="form-control" id="backup_file" name="backup_file" required>
                        <div class="form-text">{% if LANGUAGE_CODE == 'ar' %}اختر ملف نسخة احتياطية (.zip) لاستعادته.{% else %}{% trans "Select a backup file (.zip) to restore." %}{% endif %}</div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="confirm" name="confirm" required>
                        <label class="form-check-label" for="confirm">
                            {% if LANGUAGE_CODE == 'ar' %}أفهم أن هذا سيؤدي إلى استبدال البيانات الحالية{% else %}{% trans "I understand this will overwrite current data" %}{% endif %}
                        </label>
                    </div>
                    
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}
                        تحذير: ستؤدي استعادة النسخة الاحتياطية إلى استبدال جميع البيانات الحالية. تأكد من أن لديك نسخة احتياطية من البيانات الحالية قبل المتابعة.
                        {% else %}
                        {% trans "Warning: Restoring a backup will replace all current data. Make sure you have a backup of the current data before proceeding." %}
                        {% endif %}
                    </div>
                    
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-undo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}استعادة{% else %}{% trans "Restore" %}{% endif %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
