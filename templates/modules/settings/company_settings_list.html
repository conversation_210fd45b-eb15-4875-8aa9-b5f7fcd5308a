{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إعدادات الشركة{% else %}{% trans "Company Settings" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة إعدادات الشركة{% else %}{% trans "Company Settings List" %}{% endif %}</h5>
                <a href="{% url 'core:settings:company_setting_create' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة إعداد جديد{% else %}{% trans "Add New Setting" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if settings %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                {% if user.is_superuser %}
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                {% endif %}
                                <th>{% if LANGUAGE_CODE == 'ar' %}المفتاح{% else %}{% trans "Key" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}القيمة{% else %}{% trans "Value" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}آخر تحديث{% else %}{% trans "Last Updated" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for setting in settings %}
                            <tr>
                                {% if user.is_superuser %}
                                <td>{{ setting.company.name }}</td>
                                {% endif %}
                                <td><code>{{ setting.key }}</code></td>
                                <td>{{ setting.value|truncatechars:50 }}</td>
                                <td>{{ setting.description|truncatechars:50 }}</td>
                                <td>
                                    {% if setting.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ setting.updated_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <a href="{% url 'core:settings:company_setting_edit' setting.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'core:settings:company_setting_delete' setting.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد إعدادات شركة حتى الآن. انقر على "إضافة إعداد جديد" لإنشاء أول إعداد.{% else %}{% trans "No company settings yet. Click 'Add New Setting' to create your first setting." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
