{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}سجلات الأمان{% else %}{% trans "Security Logs" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}سجلات الأمان{% else %}{% trans "Security Logs" %}{% endif %}</h5>
                <div>
                    <a href="?{% if filters.log_type %}log_type={{ filters.log_type }}&{% endif %}{% if filters.user_id %}user_id={{ filters.user_id }}&{% endif %}{% if filters.company_id %}company_id={{ filters.company_id }}&{% endif %}{% if filters.date_from %}date_from={{ filters.date_from }}&{% endif %}{% if filters.date_to %}date_to={{ filters.date_to }}&{% endif %}export=csv" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-download me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تصدير CSV{% else %}{% trans "Export CSV" %}{% endif %}
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                        <i class="fas fa-filter me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تصفية{% else %}{% trans "Filter" %}{% endif %}
                    </button>
                </div>
            </div>
            
            <!-- Filter Section -->
            <div class="collapse {% if filters.log_type or filters.user_id or filters.company_id or filters.date_from or filters.date_to %}show{% endif %}" id="filterCollapse">
                <div class="card-body border-bottom">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label for="log_type" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع السجل{% else %}{% trans "Log Type" %}{% endif %}</label>
                            <select name="log_type" id="log_type" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                {% for key, value in log_types.items %}
                                <option value="{{ key }}" {% if filters.log_type == key %}selected{% endif %}>{{ value }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="user_id" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المستخدم{% else %}{% trans "User" %}{% endif %}</label>
                            <select name="user_id" id="user_id" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                {% for user in users %}
                                <option value="{{ user.id }}" {% if filters.user_id == user.id|stringformat:"i" %}selected{% endif %}>{{ user.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="company_id" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</label>
                            <select name="company_id" id="company_id" class="form-select">
                                <option value="">{% if LANGUAGE_CODE == 'ar' %}الكل{% else %}{% trans "All" %}{% endif %}</option>
                                {% for company in companies %}
                                <option value="{{ company.id }}" {% if filters.company_id == company.id|stringformat:"i" %}selected{% endif %}>{{ company.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_from" class="form-label">{% if LANGUAGE_CODE == 'ar' %}من تاريخ{% else %}{% trans "From Date" %}{% endif %}</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ filters.date_from|default:'' }}">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="date_to" class="form-label">{% if LANGUAGE_CODE == 'ar' %}إلى تاريخ{% else %}{% trans "To Date" %}{% endif %}</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ filters.date_to|default:'' }}">
                        </div>
                        
                        <div class="col-md-6 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i> {% if LANGUAGE_CODE == 'ar' %}بحث{% else %}{% trans "Search" %}{% endif %}
                            </button>
                            <a href="{% url 'core:security:logs' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين{% else %}{% trans "Reset" %}{% endif %}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Logs Table -->
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المستخدم{% else %}{% trans "User" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}عنوان IP{% else %}{% trans "IP Address" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التفاصيل{% else %}{% trans "Details" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in logs %}
                            <tr>
                                <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                                <td>
                                    {% if log.log_type == 'login' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}تسجيل دخول{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'login_failed' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}فشل تسجيل الدخول{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'logout' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}تسجيل خروج{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'password_change' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}تغيير كلمة المرور{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'password_reset' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}إعادة تعيين كلمة المرور{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'account_locked' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}قفل الحساب{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'mfa_verification_failed' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}فشل التحقق من MFA{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}{{ log.get_log_type_display }}{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.user.username|default:"Anonymous" }}</td>
                                <td>{{ log.company.name|default:"N/A" }}</td>
                                <td>{{ log.ip_address|default:"N/A" }}</td>
                                <td>
                                    {% if log.details %}
                                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#logDetailsModal{{ log.id }}">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                    
                                    <!-- Log Details Modal -->
                                    <div class="modal fade" id="logDetailsModal{{ log.id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">{% if LANGUAGE_CODE == 'ar' %}تفاصيل السجل{% else %}{% trans "Log Details" %}{% endif %}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <pre class="bg-light p-3 rounded"><code>{{ log.details|pprint }}</code></pre>
                                                    {% if log.user_agent %}
                                                    <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}وكيل المستخدم{% else %}{% trans "User Agent" %}{% endif %}</h6>
                                                    <pre class="bg-light p-3 rounded"><code>{{ log.user_agent }}</code></pre>
                                                    {% endif %}
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans "Close" %}{% endif %}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا توجد تفاصيل{% else %}{% trans "No details" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center py-3">
                                    {% if LANGUAGE_CODE == 'ar' %}لا توجد سجلات أمان مطابقة للمعايير المحددة.{% else %}{% trans "No security logs matching the specified criteria." %}{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات سجلات الأمان{% else %}{% trans "Security Logs Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تسجل سجلات الأمان جميع الأنشطة المتعلقة بالأمان في النظام. يمكن استخدام هذه السجلات لتتبع محاولات الوصول غير المصرح بها، وتحليل أنماط الاستخدام، والامتثال لمتطلبات التدقيق.
                    {% else %}
                    {% trans "Security logs record all security-related activities in the system. These logs can be used to track unauthorized access attempts, analyze usage patterns, and comply with audit requirements." %}
                    {% endif %}
                </p>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}أنواع السجلات{% else %}{% trans "Log Types" %}{% endif %}</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}تسجيل دخول{% else %}{% trans "Login" %}{% endif %}
                                <span class="badge bg-success rounded-pill">{% if LANGUAGE_CODE == 'ar' %}نجاح{% else %}{% trans "Success" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}فشل تسجيل الدخول{% else %}{% trans "Login Failed" %}{% endif %}
                                <span class="badge bg-danger rounded-pill">{% if LANGUAGE_CODE == 'ar' %}فشل{% else %}{% trans "Failure" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}تسجيل خروج{% else %}{% trans "Logout" %}{% endif %}
                                <span class="badge bg-secondary rounded-pill">{% if LANGUAGE_CODE == 'ar' %}معلومات{% else %}{% trans "Info" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}تغيير كلمة المرور{% else %}{% trans "Password Change" %}{% endif %}
                                <span class="badge bg-info rounded-pill">{% if LANGUAGE_CODE == 'ar' %}تغيير{% else %}{% trans "Change" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين كلمة المرور{% else %}{% trans "Password Reset" %}{% endif %}
                                <span class="badge bg-warning rounded-pill">{% if LANGUAGE_CODE == 'ar' %}تغيير{% else %}{% trans "Change" %}{% endif %}</span>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}إعداد المصادقة متعددة العوامل{% else %}{% trans "MFA Setup" %}{% endif %}
                                <span class="badge bg-info rounded-pill">{% if LANGUAGE_CODE == 'ar' %}تغيير{% else %}{% trans "Change" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}التحقق من المصادقة متعددة العوامل{% else %}{% trans "MFA Verification" %}{% endif %}
                                <span class="badge bg-success rounded-pill">{% if LANGUAGE_CODE == 'ar' %}نجاح{% else %}{% trans "Success" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}فشل التحقق من المصادقة متعددة العوامل{% else %}{% trans "MFA Verification Failed" %}{% endif %}
                                <span class="badge bg-warning rounded-pill">{% if LANGUAGE_CODE == 'ar' %}فشل{% else %}{% trans "Failure" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}قفل الحساب{% else %}{% trans "Account Locked" %}{% endif %}
                                <span class="badge bg-danger rounded-pill">{% if LANGUAGE_CODE == 'ar' %}أمان{% else %}{% trans "Security" %}{% endif %}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                {% if LANGUAGE_CODE == 'ar' %}تغيير إعدادات الأمان{% else %}{% trans "Security Setting Change" %}{% endif %}
                                <span class="badge bg-primary rounded-pill">{% if LANGUAGE_CODE == 'ar' %}تكوين{% else %}{% trans "Config" %}{% endif %}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add any JavaScript for the security logs page here
    });
</script>
{% endblock %}
