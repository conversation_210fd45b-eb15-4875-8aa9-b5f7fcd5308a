{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إعدادات الأمان{% else %}{% trans "Security Settings" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إعدادات الأمان{% else %}{% trans "Security Settings" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <!-- Password Policy Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}سياسة كلمة المرور{% else %}{% trans "Password Policy" %}{% endif %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="{{ form.password_policy.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}سياسة كلمة المرور{% else %}{% trans "Password Policy" %}{% endif %}</label>
                                    {{ form.password_policy }}
                                    {% if form.password_policy.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.password_policy.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">{% if LANGUAGE_CODE == 'ar' %}اختر مستوى سياسة كلمة المرور أو اختر "مخصص" لتعيين الإعدادات يدويًا{% else %}{% trans "Choose a password policy level or select 'Custom' to set settings manually" %}{% endif %}</div>
                                </div>
                            </div>
                            
                            <div id="custom-password-settings">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <label for="{{ form.password_min_length.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحد الأدنى لطول كلمة المرور{% else %}{% trans "Minimum Password Length" %}{% endif %}</label>
                                        {{ form.password_min_length }}
                                        {% if form.password_min_length.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.password_min_length.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        {% if form.password_min_length.help_text %}
                                        <div class="form-text">{{ form.password_min_length.help_text }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="{{ form.password_expiry_days.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}انتهاء صلاحية كلمة المرور (أيام){% else %}{% trans "Password Expiry (Days)" %}{% endif %}</label>
                                        {{ form.password_expiry_days }}
                                        {% if form.password_expiry_days.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.password_expiry_days.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        {% if form.password_expiry_days.help_text %}
                                        <div class="form-text">{{ form.password_expiry_days.help_text }}</div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="{{ form.password_history_count.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}عدد كلمات المرور السابقة{% else %}{% trans "Password History Count" %}{% endif %}</label>
                                        {{ form.password_history_count }}
                                        {% if form.password_history_count.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.password_history_count.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        {% if form.password_history_count.help_text %}
                                        <div class="form-text">{{ form.password_history_count.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            {{ form.password_require_uppercase }}
                                            <label class="form-check-label" for="{{ form.password_require_uppercase.id_for_label }}">
                                                {% if LANGUAGE_CODE == 'ar' %}يتطلب أحرف كبيرة{% else %}{% trans "Require Uppercase Letters" %}{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            {{ form.password_require_lowercase }}
                                            <label class="form-check-label" for="{{ form.password_require_lowercase.id_for_label }}">
                                                {% if LANGUAGE_CODE == 'ar' %}يتطلب أحرف صغيرة{% else %}{% trans "Require Lowercase Letters" %}{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            {{ form.password_require_numbers }}
                                            <label class="form-check-label" for="{{ form.password_require_numbers.id_for_label }}">
                                                {% if LANGUAGE_CODE == 'ar' %}يتطلب أرقام{% else %}{% trans "Require Numbers" %}{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            {{ form.password_require_special_chars }}
                                            <label class="form-check-label" for="{{ form.password_require_special_chars.id_for_label }}">
                                                {% if LANGUAGE_CODE == 'ar' %}يتطلب رموز خاصة{% else %}{% trans "Require Special Characters" %}{% endif %}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Multi-Factor Authentication Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}المصادقة متعددة العوامل{% else %}{% trans "Multi-Factor Authentication" %}{% endif %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="{{ form.mfa_type.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}نوع المصادقة متعددة العوامل{% else %}{% trans "MFA Type" %}{% endif %}</label>
                                    {{ form.mfa_type }}
                                    {% if form.mfa_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.mfa_type.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">
                                        {% if LANGUAGE_CODE == 'ar' %}
                                        اختر "مطلوب" لإلزام جميع المستخدمين بإعداد المصادقة متعددة العوامل، أو "اختياري" للسماح للمستخدمين باختيار استخدامها، أو "لا شيء" لتعطيلها.
                                        {% else %}
                                        {% trans "Choose 'Required' to force all users to set up MFA, 'Optional' to allow users to choose, or 'None' to disable it." %}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Session Security Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}أمان الجلسة{% else %}{% trans "Session Security" %}{% endif %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="{{ form.session_timeout_value.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}قيمة مهلة الجلسة{% else %}{% trans "Session Timeout Value" %}{% endif %}</label>
                                    {{ form.session_timeout_value }}
                                    {% if form.session_timeout_value.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.session_timeout_value.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="{{ form.session_timeout_unit.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}وحدة مهلة الجلسة{% else %}{% trans "Session Timeout Unit" %}{% endif %}</label>
                                    {{ form.session_timeout_unit }}
                                    {% if form.session_timeout_unit.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.session_timeout_unit.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="{{ form.max_login_attempts.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الحد الأقصى لمحاولات تسجيل الدخول{% else %}{% trans "Maximum Login Attempts" %}{% endif %}</label>
                                    {{ form.max_login_attempts }}
                                    {% if form.max_login_attempts.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.max_login_attempts.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="col-md-4">
                                    <label for="{{ form.lockout_duration_minutes.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}مدة القفل (دقائق){% else %}{% trans "Lockout Duration (Minutes)" %}{% endif %}</label>
                                    {{ form.lockout_duration_minutes }}
                                    {% if form.lockout_duration_minutes.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.lockout_duration_minutes.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- IP Restrictions Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}قيود عنوان IP{% else %}{% trans "IP Restrictions" %}{% endif %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                {{ form.ip_restriction_enabled }}
                                <label class="form-check-label" for="{{ form.ip_restriction_enabled.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}تمكين قيود عنوان IP{% else %}{% trans "Enable IP Restrictions" %}{% endif %}
                                </label>
                            </div>
                            
                            <div id="ip-restrictions-settings" class="{% if not security_settings.ip_restriction_enabled %}d-none{% endif %}">
                                <div class="row mb-3">
                                    <div class="col-md-12">
                                        <label for="{{ form.allowed_ips.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}عناوين IP المسموح بها{% else %}{% trans "Allowed IP Addresses" %}{% endif %}</label>
                                        {{ form.allowed_ips }}
                                        {% if form.allowed_ips.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.allowed_ips.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        {% if form.allowed_ips.help_text %}
                                        <div class="form-text">{{ form.allowed_ips.help_text }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Audit Logging Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}تسجيل التدقيق{% else %}{% trans "Audit Logging" %}{% endif %}</h5>
                        </div>
                        <div class="card-body">
                            <div class="form-check mb-3">
                                {{ form.enable_audit_logging }}
                                <label class="form-check-label" for="{{ form.enable_audit_logging.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}تمكين تسجيل التدقيق{% else %}{% trans "Enable Audit Logging" %}{% endif %}
                                </label>
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    تسجيل جميع الأنشطة المتعلقة بالأمان مثل تسجيل الدخول، وتغيير كلمة المرور، وإعداد المصادقة متعددة العوامل، وما إلى ذلك.
                                    {% else %}
                                    {% trans "Log all security-related activities such as logins, password changes, MFA setup, etc." %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'core:security:dashboard' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حفظ الإعدادات{% else %}{% trans "Save Settings" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Toggle custom password settings based on policy selection
        $('#id_password_policy').change(function() {
            if ($(this).val() === 'custom') {
                $('#custom-password-settings').show();
            } else {
                $('#custom-password-settings').hide();
            }
        });
        
        // Initial state
        if ($('#id_password_policy').val() !== 'custom') {
            $('#custom-password-settings').hide();
        }
        
        // Toggle IP restrictions settings
        $('#id_ip_restriction_enabled').change(function() {
            if ($(this).is(':checked')) {
                $('#ip-restrictions-settings').removeClass('d-none');
            } else {
                $('#ip-restrictions-settings').addClass('d-none');
            }
        });
    });
</script>
{% endblock %}
