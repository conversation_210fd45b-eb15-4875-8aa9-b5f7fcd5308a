{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}معلومات أمان المستخدمين{% else %}{% trans "User Security Information" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات أمان المستخدمين{% else %}{% trans "User Security Information" %}{% endif %}</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المستخدم{% else %}{% trans "User" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}آخر تغيير لكلمة المرور{% else %}{% trans "Password Last Changed" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تنتهي صلاحية كلمة المرور{% else %}{% trans "Password Expires" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}محاولات تسجيل الدخول الفاشلة{% else %}{% trans "Failed Login Attempts" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}حالة الحساب{% else %}{% trans "Account Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}آخر عنوان IP{% else %}{% trans "Last IP" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for info in users_info %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            {% if info.user.profile_image %}
                                            <img src="{{ info.user.profile_image.url }}" alt="{{ info.user.username }}" class="rounded-circle">
                                            {% else %}
                                            <div class="avatar-initial rounded-circle bg-secondary">{{ info.user.username|first|upper }}</div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <div class="fw-bold">{{ info.user.get_full_name|default:info.user.username }}</div>
                                            <small class="text-muted">{{ info.user.email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>{{ info.password_last_changed|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% if info.password_expires_at %}
                                    <span class="{% if info.is_password_expired %}text-danger{% else %}text-success{% endif %}">
                                        {{ info.password_expires_at|date:"Y-m-d" }}
                                        {% if info.is_password_expired %}
                                        <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}منتهية الصلاحية{% else %}{% trans "Expired" %}{% endif %}</span>
                                        {% endif %}
                                    </span>
                                    {% else %}
                                    <span class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا تنتهي{% else %}{% trans "Never" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if info.failed_login_attempts > 0 %}
                                    <span class="badge bg-warning">{{ info.failed_login_attempts }}</span>
                                    {% else %}
                                    <span class="badge bg-success">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if info.is_account_locked %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مقفل{% else %}{% trans "Locked" %}{% endif %}</span>
                                    <small class="d-block text-muted">{% if LANGUAGE_CODE == 'ar' %}حتى{% else %}{% trans "Until" %}{% endif %} {{ info.account_locked_until|date:"Y-m-d H:i" }}</small>
                                    {% else %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if info.last_login_ip %}
                                    <code>{{ info.last_login_ip }}</code>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'core:security:user_security_info_edit' info.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'core:security:password_history_list' info.user.id %}" class="btn btn-sm btn-outline-info" title="{% if LANGUAGE_CODE == 'ar' %}تاريخ كلمة المرور{% else %}{% trans 'Password History' %}{% endif %}">
                                            <i class="fas fa-history"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    {% if LANGUAGE_CODE == 'ar' %}لا توجد معلومات أمان للمستخدمين.{% else %}{% trans "No user security information found." %}{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات أمان المستخدم{% else %}{% trans "User Security Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تعرض هذه الصفحة معلومات الأمان لجميع المستخدمين في النظام. يمكنك مراقبة حالة حسابات المستخدمين، وإدارة قفل الحسابات، وعرض تاريخ كلمات المرور.
                    {% else %}
                    {% trans "This page displays security information for all users in the system. You can monitor user account status, manage account lockouts, and view password history." %}
                    {% endif %}
                </p>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}الإجراءات المتاحة{% else %}{% trans "Available Actions" %}{% endif %}</h6>
                <ul>
                    <li>
                        <strong>{% if LANGUAGE_CODE == 'ar' %}تعديل معلومات الأمان{% else %}{% trans "Edit Security Information" %}{% endif %}</strong>: 
                        {% if LANGUAGE_CODE == 'ar' %}
                        يمكنك إلغاء قفل الحسابات، وإعادة تعيين محاولات تسجيل الدخول الفاشلة، وإعادة تعيين تاريخ انتهاء صلاحية كلمة المرور.
                        {% else %}
                        {% trans "You can unlock accounts, reset failed login attempts, and reset password expiry dates." %}
                        {% endif %}
                    </li>
                    <li>
                        <strong>{% if LANGUAGE_CODE == 'ar' %}عرض تاريخ كلمة المرور{% else %}{% trans "View Password History" %}{% endif %}</strong>: 
                        {% if LANGUAGE_CODE == 'ar' %}
                        يمكنك عرض تاريخ كلمات المرور السابقة للمستخدم.
                        {% else %}
                        {% trans "You can view a user's previous password history." %}
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
