{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تعديل معلومات أمان المستخدم{% else %}{% trans "Edit User Security Information" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}تعديل معلومات أمان المستخدم: {{ security_info.user.get_full_name|default:security_info.user.username }}{% else %}{% trans "Edit User Security Information" %}: {{ security_info.user.get_full_name|default:security_info.user.username }}{% endif %}
                </h5>
                <a href="{% url 'core:security:user_security_info_list' %}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات المستخدم{% else %}{% trans "User Information" %}{% endif %}</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th width="40%">{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                        <td>{{ security_info.user.username }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                        <td>{{ security_info.user.get_full_name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                        <td>{{ security_info.user.email|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}آخر تسجيل دخول{% else %}{% trans "Last Login" %}{% endif %}</th>
                                        <td>{{ security_info.user.last_login|date:"Y-m-d H:i"|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}آخر عنوان IP{% else %}{% trans "Last IP Address" %}{% endif %}</th>
                                        <td>{{ security_info.last_login_ip|default:"-" }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات الأمان{% else %}{% trans "Security Information" %}{% endif %}</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th width="40%">{% if LANGUAGE_CODE == 'ar' %}آخر تغيير لكلمة المرور{% else %}{% trans "Password Last Changed" %}{% endif %}</th>
                                        <td>{{ security_info.password_last_changed|date:"Y-m-d H:i" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}تنتهي صلاحية كلمة المرور{% else %}{% trans "Password Expires" %}{% endif %}</th>
                                        <td>
                                            {% if security_info.password_expires_at %}
                                            <span class="{% if security_info.is_password_expired %}text-danger{% else %}text-success{% endif %}">
                                                {{ security_info.password_expires_at|date:"Y-m-d H:i" }}
                                                {% if security_info.is_password_expired %}
                                                <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}منتهية الصلاحية{% else %}{% trans "Expired" %}{% endif %}</span>
                                                {% endif %}
                                            </span>
                                            {% else %}
                                            <span class="text-muted">{% if LANGUAGE_CODE == 'ar' %}لا تنتهي{% else %}{% trans "Never" %}{% endif %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}محاولات تسجيل الدخول الفاشلة{% else %}{% trans "Failed Login Attempts" %}{% endif %}</th>
                                        <td>
                                            {% if security_info.failed_login_attempts > 0 %}
                                            <span class="badge bg-warning">{{ security_info.failed_login_attempts }}</span>
                                            {% else %}
                                            <span class="badge bg-success">0</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}آخر محاولة فاشلة{% else %}{% trans "Last Failed Login" %}{% endif %}</th>
                                        <td>{{ security_info.last_failed_login|date:"Y-m-d H:i"|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}حالة الحساب{% else %}{% trans "Account Status" %}{% endif %}</th>
                                        <td>
                                            {% if security_info.is_account_locked %}
                                            <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مقفل{% else %}{% trans "Locked" %}{% endif %}</span>
                                            <small class="d-block text-muted">{% if LANGUAGE_CODE == 'ar' %}حتى{% else %}{% trans "Until" %}{% endif %} {{ security_info.account_locked_until|date:"Y-m-d H:i" }}</small>
                                            {% else %}
                                            <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</h6>
                        <div class="d-flex gap-2">
                            {% if security_info.is_account_locked %}
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="unlock_account">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-unlock me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء قفل الحساب{% else %}{% trans "Unlock Account" %}{% endif %}
                                </button>
                            </form>
                            {% endif %}
                            
                            {% if security_info.failed_login_attempts > 0 %}
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="reset_failed_attempts">
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-redo me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين محاولات تسجيل الدخول{% else %}{% trans "Reset Login Attempts" %}{% endif %}
                                </button>
                            </form>
                            {% endif %}
                            
                            <form method="post" class="d-inline">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="reset_password_expiry">
                                <button type="submit" class="btn btn-secondary">
                                    <i class="fas fa-clock me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعادة تعيين انتهاء صلاحية كلمة المرور{% else %}{% trans "Reset Password Expiry" %}{% endif %}
                                </button>
                            </form>
                            
                            <a href="{% url 'core:security:password_history_list' security_info.user.id %}" class="btn btn-outline-primary">
                                <i class="fas fa-history me-1"></i> {% if LANGUAGE_CODE == 'ar' %}عرض تاريخ كلمة المرور{% else %}{% trans "View Password History" %}{% endif %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}سجلات الأمان الأخيرة{% else %}{% trans "Recent Security Logs" %}{% endif %}</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}عنوان IP{% else %}{% trans "IP Address" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التفاصيل{% else %}{% trans "Details" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in security_info.user.security_logs.all|slice:":10" %}
                            <tr>
                                <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% if log.log_type == 'login' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}تسجيل دخول{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'login_failed' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}فشل تسجيل الدخول{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'logout' %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}تسجيل خروج{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'password_change' %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}تغيير كلمة المرور{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'account_locked' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}قفل الحساب{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'account_unlocked' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}إلغاء قفل الحساب{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}{{ log.get_log_type_display }}{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.ip_address|default:"N/A" }}</td>
                                <td>
                                    {% if log.details %}
                                    <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="modal" data-bs-target="#logDetailsModal{{ log.id }}">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                    
                                    <!-- Log Details Modal -->
                                    <div class="modal fade" id="logDetailsModal{{ log.id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">{% if LANGUAGE_CODE == 'ar' %}تفاصيل السجل{% else %}{% trans "Log Details" %}{% endif %}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <pre class="bg-light p-3 rounded"><code>{{ log.details|pprint }}</code></pre>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans "Close" %}{% endif %}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center py-3">
                                    {% if LANGUAGE_CODE == 'ar' %}لا توجد سجلات أمان لهذا المستخدم.{% else %}{% trans "No security logs for this user." %}{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'core:security:logs' %}?user_id={{ security_info.user.id }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i> {% if LANGUAGE_CODE == 'ar' %}عرض جميع السجلات{% else %}{% trans "View All Logs" %}{% endif %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
