{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if log_type == 'application' %}
    {% if LANGUAGE_CODE == 'ar' %}سجل التطبيق{% else %}{% trans "Application Log" %}{% endif %}
    {% elif log_type == 'error' %}
    {% if LANGUAGE_CODE == 'ar' %}سجل الأخطاء{% else %}{% trans "Error Log" %}{% endif %}
    {% elif log_type == 'access' %}
    {% if LANGUAGE_CODE == 'ar' %}سجل الوصول{% else %}{% trans "Access Log" %}{% endif %}
    {% else %}
    {{ log_type }}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {% if log_type == 'application' %}
                    {% if LANGUAGE_CODE == 'ar' %}سجل التطبيق{% else %}{% trans "Application Log" %}{% endif %}
                    {% elif log_type == 'error' %}
                    {% if LANGUAGE_CODE == 'ar' %}سجل الأخطاء{% else %}{% trans "Error Log" %}{% endif %}
                    {% elif log_type == 'access' %}
                    {% if LANGUAGE_CODE == 'ar' %}سجل الوصول{% else %}{% trans "Access Log" %}{% endif %}
                    {% else %}
                    {{ log_type }}
                    {% endif %}

                    {% if log_info %}
                    <small class="text-muted ms-2">
                        {% if log_info.exists %}
                        ({{ log_info.size_formatted }}, {% if LANGUAGE_CODE == 'ar' %}آخر تعديل{% else %}{% trans "Last Modified" %}{% endif %}: {{ log_info.modified|date:"Y-m-d H:i" }})
                        {% else %}
                        ({% if LANGUAGE_CODE == 'ar' %}غير موجود{% else %}{% trans "Not Found" %}{% endif %})
                        {% endif %}
                    </small>
                    {% endif %}
                </h5>
                <div>
                    <a href="{% url 'core:settings:download_log' log_type %}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-download me-1"></i> {% if LANGUAGE_CODE == 'ar' %}تنزيل{% else %}{% trans "Download" %}{% endif %}
                    </a>
                    <a href="{% url 'core:settings:clear_log' log_type %}" class="btn btn-sm btn-outline-danger">
                        <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}مسح{% else %}{% trans "Clear" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="bg-dark text-light p-3 rounded log-container">
                    <pre class="mb-0 log-content">{{ log_content }}</pre>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'core:settings:system_logs' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة إلى قائمة السجلات{% else %}{% trans "Back to Logs List" %}{% endif %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
