{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if setting %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل إعداد النظام{% else %}{% trans "Edit System Setting" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة إعداد نظام جديد{% else %}{% trans "Add New System Setting" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if setting %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل إعداد النظام: {{ setting.key }}{% else %}{% trans "Edit System Setting" %}: {{ setting.key }}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إضافة إعداد نظام جديد{% else %}{% trans "Add New System Setting" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.key.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المفتاح{% else %}{% trans "Key" %}{% endif %}</label>
                        {{ form.key }}
                        {% if form.key.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.key.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <div class="form-text">{% if LANGUAGE_CODE == 'ar' %}مفتاح فريد لتحديد هذا الإعداد. يجب أن يكون بتنسيق snake_case.{% else %}{% trans "A unique key to identify this setting. Should be in snake_case format." %}{% endif %}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.value.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}القيمة{% else %}{% trans "Value" %}{% endif %}</label>
                        {{ form.value }}
                        {% if form.value.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.value.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.is_active }}
                        <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                            {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                        </label>
                        {% if form.is_active.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.is_active.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'core:settings:system_settings_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
