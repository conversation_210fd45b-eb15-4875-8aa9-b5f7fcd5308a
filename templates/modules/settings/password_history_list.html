{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}تاريخ كلمة المرور{% else %}{% trans "Password History" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    {% if LANGUAGE_CODE == 'ar' %}تاريخ كلمة المرور: {{ user_obj.get_full_name|default:user_obj.username }}{% else %}{% trans "Password History" %}: {{ user_obj.get_full_name|default:user_obj.username }}{% endif %}
                </h5>
                <a href="{% url 'core:security:user_security_info_list' %}" class="btn btn-sm btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات المستخدم{% else %}{% trans "User Information" %}{% endif %}</h6>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th width="40%">{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                        <td>{{ user_obj.username }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                        <td>{{ user_obj.get_full_name|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                        <td>{{ user_obj.email|default:"-" }}</td>
                                    </tr>
                                    <tr>
                                        <th>{% if LANGUAGE_CODE == 'ar' %}آخر تغيير لكلمة المرور{% else %}{% trans "Password Last Changed" %}{% endif %}</th>
                                        <td>{{ user_obj.security_info.password_last_changed|date:"Y-m-d H:i" }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <h6>{% if LANGUAGE_CODE == 'ar' %}تاريخ كلمة المرور{% else %}{% trans "Password History" %}{% endif %}</h6>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تاريخ التغيير{% else %}{% trans "Change Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}تجزئة كلمة المرور{% else %}{% trans "Password Hash" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in history %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ entry.created_at|date:"Y-m-d H:i:s" }}</td>
                                <td>
                                    <code class="small">{{ entry.password|truncatechars:50 }}</code>
                                    <button type="button" class="btn btn-sm btn-outline-info ms-2" data-bs-toggle="modal" data-bs-target="#hashModal{{ entry.id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    
                                    <!-- Hash Modal -->
                                    <div class="modal fade" id="hashModal{{ entry.id }}" tabindex="-1" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title">{% if LANGUAGE_CODE == 'ar' %}تجزئة كلمة المرور{% else %}{% trans "Password Hash" %}{% endif %}</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                                        {% if LANGUAGE_CODE == 'ar' %}
                                                        هذه هي تجزئة كلمة المرور المشفرة. لا يمكن استعادة كلمة المرور الأصلية من هذه التجزئة.
                                                        {% else %}
                                                        {% trans "This is the encrypted password hash. The original password cannot be recovered from this hash." %}
                                                        {% endif %}
                                                    </div>
                                                    <div class="bg-light p-3 rounded">
                                                        <code class="small">{{ entry.password }}</code>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% if LANGUAGE_CODE == 'ar' %}إغلاق{% else %}{% trans "Close" %}{% endif %}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="text-center py-3">
                                    {% if LANGUAGE_CODE == 'ar' %}لا يوجد تاريخ لكلمة المرور.{% else %}{% trans "No password history found." %}{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}معلومات تاريخ كلمة المرور{% else %}{% trans "Password History Information" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <p>
                    {% if LANGUAGE_CODE == 'ar' %}
                    يحتفظ النظام بتاريخ كلمات المرور السابقة للمستخدمين. يتم استخدام هذا التاريخ لمنع المستخدمين من إعادة استخدام كلمات المرور القديمة عند تغيير كلمة المرور.
                    {% else %}
                    {% trans "The system maintains a history of users' previous passwords. This history is used to prevent users from reusing old passwords when changing their password." %}
                    {% endif %}
                </p>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    يتم تخزين كلمات المرور بشكل آمن باستخدام خوارزميات تجزئة قوية. لا يمكن استعادة كلمات المرور الأصلية من هذه التجزئات.
                    {% else %}
                    {% trans "Passwords are securely stored using strong hashing algorithms. The original passwords cannot be recovered from these hashes." %}
                    {% endif %}
                </div>
                
                <h6 class="mt-3">{% if LANGUAGE_CODE == 'ar' %}سياسة إعادة استخدام كلمة المرور{% else %}{% trans "Password Reuse Policy" %}{% endif %}</h6>
                <p>
                    {% if user_obj.company and user_obj.company.security_settings %}
                        {% with settings=user_obj.company.security_settings %}
                            {% if settings.password_history_count > 0 %}
                                {% if LANGUAGE_CODE == 'ar' %}
                                لا يمكن للمستخدم إعادة استخدام آخر {{ settings.password_history_count }} كلمات مرور.
                                {% else %}
                                {% trans "The user cannot reuse their last" %} {{ settings.password_history_count }} {% trans "passwords." %}
                                {% endif %}
                            {% else %}
                                {% if LANGUAGE_CODE == 'ar' %}
                                لا توجد قيود على إعادة استخدام كلمات المرور السابقة.
                                {% else %}
                                {% trans "There are no restrictions on reusing previous passwords." %}
                                {% endif %}
                            {% endif %}
                        {% endwith %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}
                        يتم تطبيق سياسة إعادة استخدام كلمة المرور على مستوى النظام.
                        {% else %}
                        {% trans "Password reuse policy is applied at the system level." %}
                        {% endif %}
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
