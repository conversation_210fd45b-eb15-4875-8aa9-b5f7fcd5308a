{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}التحقق من جهاز المصادقة متعددة العوامل{% else %}{% trans "Verify MFA Device" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}التحقق من جهاز المصادقة متعددة العوامل{% else %}{% trans "Verify MFA Device" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}معلومات الجهاز{% else %}{% trans "Device Information" %}{% endif %}</h6>
                        <p>
                            <strong>{% if LANGUAGE_CODE == 'ar' %}الاسم:{% else %}{% trans "Name:" %}{% endif %}</strong> {{ device.name }}<br>
                            <strong>{% if LANGUAGE_CODE == 'ar' %}النوع:{% else %}{% trans "Type:" %}{% endif %}</strong> 
                            {% if device.device_type == 'totp' %}
                                {% if LANGUAGE_CODE == 'ar' %}تطبيق المصادقة{% else %}{% trans "Authenticator App" %}{% endif %}
                            {% elif device.device_type == 'sms' %}
                                {% if LANGUAGE_CODE == 'ar' %}رسالة نصية{% else %}{% trans "SMS" %}{% endif %}
                            {% elif device.device_type == 'email' %}
                                {% if LANGUAGE_CODE == 'ar' %}بريد إلكتروني{% else %}{% trans "Email" %}{% endif %}
                            {% endif %}
                        </p>
                        
                        {% if device.device_type == 'totp' %}
                        <div class="mb-4">
                            <h6>{% if LANGUAGE_CODE == 'ar' %}امسح رمز QR{% else %}{% trans "Scan QR Code" %}{% endif %}</h6>
                            <p class="text-muted small">
                                {% if LANGUAGE_CODE == 'ar' %}
                                امسح رمز QR هذا باستخدام تطبيق المصادقة الخاص بك (مثل Google Authenticator أو Microsoft Authenticator أو Authy).
                                {% else %}
                                {% trans "Scan this QR code with your authenticator app (like Google Authenticator, Microsoft Authenticator, or Authy)." %}
                                {% endif %}
                            </p>
                            <div class="qr-code-container border p-3 text-center bg-white">
                                {{ qr_code|safe }}
                            </div>
                            
                            <div class="mt-3">
                                <p class="mb-1">{% if LANGUAGE_CODE == 'ar' %}أو أدخل المفتاح السري يدويًا:{% else %}{% trans "Or enter the secret key manually:" %}{% endif %}</p>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ device.secret_key }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-clipboard-text="{{ device.secret_key }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    إذا لم تتمكن من مسح رمز QR، يمكنك إدخال هذا المفتاح السري في تطبيق المصادقة الخاص بك.
                                    {% else %}
                                    {% trans "If you can't scan the QR code, you can enter this secret key into your authenticator app." %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% elif device.device_type == 'sms' %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            تم إرسال رمز التحقق إلى رقم هاتفك المحمول المسجل. يرجى إدخال الرمز أدناه.
                            {% else %}
                            {% trans "A verification code has been sent to your registered mobile phone number. Please enter the code below." %}
                            {% endif %}
                        </div>
                        {% elif device.device_type == 'email' %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            تم إرسال رمز التحقق إلى عنوان بريدك الإلكتروني المسجل. يرجى إدخال الرمز أدناه.
                            {% else %}
                            {% trans "A verification code has been sent to your registered email address. Please enter the code below." %}
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-6">
                        <h6>{% if LANGUAGE_CODE == 'ar' %}أدخل رمز التحقق{% else %}{% trans "Enter Verification Code" %}{% endif %}</h6>
                        <form method="post">
                            {% csrf_token %}
                            
                            {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            
                            <div class="mb-3">
                                <label for="{{ form.token.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}رمز التحقق{% else %}{% trans "Verification Code" %}{% endif %}</label>
                                {{ form.token }}
                                {% if form.token.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.token.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    أدخل الرمز المكون من 6 أرقام من تطبيق المصادقة الخاص بك.
                                    {% else %}
                                    {% trans "Enter the 6-digit code from your authenticator app." %}
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'core:security:mfa_devices' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check me-1"></i> {% if LANGUAGE_CODE == 'ar' %}التحقق{% else %}{% trans "Verify" %}{% endif %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تعليمات{% else %}{% trans "Instructions" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                {% if device.device_type == 'totp' %}
                <h6>{% if LANGUAGE_CODE == 'ar' %}كيفية إعداد تطبيق المصادقة{% else %}{% trans "How to Set Up Authenticator App" %}{% endif %}</h6>
                <ol>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        قم بتنزيل وتثبيت تطبيق مصادقة على هاتفك الذكي:
                        {% else %}
                        {% trans "Download and install an authenticator app on your smartphone:" %}
                        {% endif %}
                        <ul class="mt-2">
                            <li><a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank">Google Authenticator (Android)</a></li>
                            <li><a href="https://apps.apple.com/app/google-authenticator/id388497605" target="_blank">Google Authenticator (iOS)</a></li>
                            <li><a href="https://play.google.com/store/apps/details?id=com.azure.authenticator" target="_blank">Microsoft Authenticator (Android)</a></li>
                            <li><a href="https://apps.apple.com/app/microsoft-authenticator/id983156458" target="_blank">Microsoft Authenticator (iOS)</a></li>
                            <li><a href="https://authy.com/download/" target="_blank">Authy (Multiple platforms)</a></li>
                        </ul>
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        افتح تطبيق المصادقة وأضف حساب جديد (عادة عن طريق النقر على زر "+" أو "إضافة").
                        {% else %}
                        {% trans "Open the authenticator app and add a new account (usually by tapping the '+' or 'Add' button)." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        اختر خيار مسح رمز QR وامسح الرمز المعروض على هذه الصفحة.
                        {% else %}
                        {% trans "Choose the option to scan a QR code and scan the code displayed on this page." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        بمجرد إضافة الحساب، سيبدأ التطبيق في إنشاء رموز تحقق مكونة من 6 أرقام تتغير كل 30 ثانية.
                        {% else %}
                        {% trans "Once the account is added, the app will start generating 6-digit verification codes that change every 30 seconds." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        أدخل الرمز الحالي في النموذج أعلاه للتحقق من إعداد جهازك.
                        {% else %}
                        {% trans "Enter the current code in the form above to verify your device setup." %}
                        {% endif %}
                    </li>
                </ol>
                {% elif device.device_type == 'sms' %}
                <h6>{% if LANGUAGE_CODE == 'ar' %}كيفية التحقق من رمز الرسالة النصية{% else %}{% trans "How to Verify SMS Code" %}{% endif %}</h6>
                <ol>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        تم إرسال رمز تحقق مكون من 6 أرقام إلى رقم هاتفك المحمول المسجل.
                        {% else %}
                        {% trans "A 6-digit verification code has been sent to your registered mobile phone number." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        تحقق من رسائلك النصية للحصول على الرمز.
                        {% else %}
                        {% trans "Check your text messages for the code." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        أدخل الرمز في النموذج أعلاه للتحقق من إعداد جهازك.
                        {% else %}
                        {% trans "Enter the code in the form above to verify your device setup." %}
                        {% endif %}
                    </li>
                </ol>
                {% elif device.device_type == 'email' %}
                <h6>{% if LANGUAGE_CODE == 'ar' %}كيفية التحقق من رمز البريد الإلكتروني{% else %}{% trans "How to Verify Email Code" %}{% endif %}</h6>
                <ol>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        تم إرسال رمز تحقق مكون من 6 أرقام إلى عنوان بريدك الإلكتروني المسجل.
                        {% else %}
                        {% trans "A 6-digit verification code has been sent to your registered email address." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        تحقق من بريدك الإلكتروني للحصول على الرمز.
                        {% else %}
                        {% trans "Check your email for the code." %}
                        {% endif %}
                    </li>
                    <li>
                        {% if LANGUAGE_CODE == 'ar' %}
                        أدخل الرمز في النموذج أعلاه للتحقق من إعداد جهازك.
                        {% else %}
                        {% trans "Enter the code in the form above to verify your device setup." %}
                        {% endif %}
                    </li>
                </ol>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize clipboard.js
        var clipboard = new ClipboardJS('.copy-btn');
        
        clipboard.on('success', function(e) {
            var button = $(e.trigger);
            button.html('<i class="fas fa-check"></i>');
            setTimeout(function() {
                button.html('<i class="fas fa-copy"></i>');
            }, 2000);
            e.clearSelection();
        });
        
        // Focus on token input
        $('#id_token').focus();
    });
</script>
{% endblock %}
