{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}مسح السجل{% else %}{% trans "Clear Log" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تأكيد مسح السجل{% else %}{% trans "Confirm Log Clearing" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد مسح هذا السجل؟ سيتم حذف جميع المعلومات في هذا السجل بشكل دائم.
                    {% else %}
                    {% trans "Are you sure you want to clear this log? All information in this log will be permanently deleted." %}
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}تفاصيل السجل:{% else %}{% trans "Log Details:" %}{% endif %}</h6>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">{% if LANGUAGE_CODE == 'ar' %}نوع السجل{% else %}{% trans "Log Type" %}{% endif %}</th>
                            <td>
                                {% if log_type == 'application' %}
                                {% if LANGUAGE_CODE == 'ar' %}سجل التطبيق{% else %}{% trans "Application Log" %}{% endif %}
                                {% elif log_type == 'error' %}
                                {% if LANGUAGE_CODE == 'ar' %}سجل الأخطاء{% else %}{% trans "Error Log" %}{% endif %}
                                {% elif log_type == 'access' %}
                                {% if LANGUAGE_CODE == 'ar' %}سجل الوصول{% else %}{% trans "Access Log" %}{% endif %}
                                {% else %}
                                {{ log_type }}
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'core:settings:view_log' log_type %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}مسح السجل{% else %}{% trans "Clear Log" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
