{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}لوحة الأمان{% else %}{% trans "Security Dashboard" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}حالة الأمان{% else %}{% trans "Security Status" %}{% endif %}</h5>
                <a href="{% url 'core:security:settings' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-cog me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إعدادات الأمان{% else %}{% trans "Security Settings" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Password Policy -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-{% if security_settings.password_policy == 'high' %}success{% elif security_settings.password_policy == 'medium' %}warning{% else %}danger{% endif %}">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-circle bg-{% if security_settings.password_policy == 'high' %}success{% elif security_settings.password_policy == 'medium' %}warning{% else %}danger{% endif %} me-3">
                                        <i class="fas fa-key text-white"></i>
                                    </div>
                                    <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}سياسة كلمة المرور{% else %}{% trans "Password Policy" %}{% endif %}</h5>
                                </div>
                                <p class="card-text">
                                    {% if security_settings.password_policy == 'high' %}
                                        {% if LANGUAGE_CODE == 'ar' %}سياسة كلمة مرور قوية{% else %}{% trans "Strong password policy" %}{% endif %}
                                    {% elif security_settings.password_policy == 'medium' %}
                                        {% if LANGUAGE_CODE == 'ar' %}سياسة كلمة مرور متوسطة{% else %}{% trans "Medium password policy" %}{% endif %}
                                    {% elif security_settings.password_policy == 'low' %}
                                        {% if LANGUAGE_CODE == 'ar' %}سياسة كلمة مرور ضعيفة{% else %}{% trans "Weak password policy" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}سياسة كلمة مرور مخصصة{% else %}{% trans "Custom password policy" %}{% endif %}
                                    {% endif %}
                                </p>
                                <ul class="list-unstyled small">
                                    <li>
                                        <i class="fas fa-check-circle text-{% if security_settings.password_min_length >= 8 %}success{% else %}danger{% endif %} me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}الحد الأدنى للطول: {{ security_settings.password_min_length }}{% else %}{% trans "Minimum length" %}: {{ security_settings.password_min_length }}{% endif %}
                                    </li>
                                    <li>
                                        <i class="fas {% if security_settings.password_require_uppercase %}fa-check-circle text-success{% else %}fa-times-circle text-danger{% endif %} me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}يتطلب أحرف كبيرة{% else %}{% trans "Requires uppercase" %}{% endif %}
                                    </li>
                                    <li>
                                        <i class="fas {% if security_settings.password_require_numbers %}fa-check-circle text-success{% else %}fa-times-circle text-danger{% endif %} me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}يتطلب أرقام{% else %}{% trans "Requires numbers" %}{% endif %}
                                    </li>
                                    <li>
                                        <i class="fas {% if security_settings.password_require_special_chars %}fa-check-circle text-success{% else %}fa-times-circle text-danger{% endif %} me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}يتطلب رموز خاصة{% else %}{% trans "Requires special characters" %}{% endif %}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Multi-Factor Authentication -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-{% if security_settings.mfa_type == 'required' %}success{% elif security_settings.mfa_type == 'optional' %}warning{% else %}danger{% endif %}">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-circle bg-{% if security_settings.mfa_type == 'required' %}success{% elif security_settings.mfa_type == 'optional' %}warning{% else %}danger{% endif %} me-3">
                                        <i class="fas fa-shield-alt text-white"></i>
                                    </div>
                                    <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}المصادقة متعددة العوامل{% else %}{% trans "Multi-Factor Authentication" %}{% endif %}</h5>
                                </div>
                                <p class="card-text">
                                    {% if security_settings.mfa_type == 'required' %}
                                        {% if LANGUAGE_CODE == 'ar' %}المصادقة متعددة العوامل مطلوبة{% else %}{% trans "MFA is required" %}{% endif %}
                                    {% elif security_settings.mfa_type == 'optional' %}
                                        {% if LANGUAGE_CODE == 'ar' %}المصادقة متعددة العوامل اختيارية{% else %}{% trans "MFA is optional" %}{% endif %}
                                    {% else %}
                                        {% if LANGUAGE_CODE == 'ar' %}المصادقة متعددة العوامل معطلة{% else %}{% trans "MFA is disabled" %}{% endif %}
                                    {% endif %}
                                </p>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <span>
                                        {% if mfa_devices %}
                                            <i class="fas fa-check-circle text-success me-1"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}{{ mfa_devices.count }} جهاز مفعل{% else %}{{ mfa_devices.count }} device{{ mfa_devices.count|pluralize }} enabled{% endif %}
                                        {% else %}
                                            <i class="fas fa-times-circle text-danger me-1"></i>
                                            {% if LANGUAGE_CODE == 'ar' %}لا توجد أجهزة مفعلة{% else %}{% trans "No devices enabled" %}{% endif %}
                                        {% endif %}
                                    </span>
                                    <a href="{% url 'core:security:mfa_devices' %}" class="btn btn-sm btn-outline-primary">
                                        {% if LANGUAGE_CODE == 'ar' %}إدارة{% else %}{% trans "Manage" %}{% endif %}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Session Security -->
                    <div class="col-md-4 mb-4">
                        <div class="card h-100 border-{% if security_settings.session_timeout_value <= 30 %}success{% elif security_settings.session_timeout_value <= 60 %}warning{% else %}danger{% endif %}">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-circle bg-{% if security_settings.session_timeout_value <= 30 %}success{% elif security_settings.session_timeout_value <= 60 %}warning{% else %}danger{% endif %} me-3">
                                        <i class="fas fa-clock text-white"></i>
                                    </div>
                                    <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}أمان الجلسة{% else %}{% trans "Session Security" %}{% endif %}</h5>
                                </div>
                                <p class="card-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    مهلة الجلسة: {{ security_settings.session_timeout_value }} {{ security_settings.get_session_timeout_unit_display }}
                                    {% else %}
                                    {% trans "Session timeout" %}: {{ security_settings.session_timeout_value }} {{ security_settings.get_session_timeout_unit_display }}
                                    {% endif %}
                                </p>
                                <ul class="list-unstyled small">
                                    <li>
                                        <i class="fas {% if security_settings.max_login_attempts <= 5 %}fa-check-circle text-success{% else %}fa-exclamation-circle text-warning{% endif %} me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}الحد الأقصى لمحاولات تسجيل الدخول: {{ security_settings.max_login_attempts }}{% else %}{% trans "Max login attempts" %}: {{ security_settings.max_login_attempts }}{% endif %}
                                    </li>
                                    <li>
                                        <i class="fas {% if security_settings.lockout_duration_minutes >= 15 %}fa-check-circle text-success{% else %}fa-exclamation-circle text-warning{% endif %} me-1"></i>
                                        {% if LANGUAGE_CODE == 'ar' %}مدة القفل: {{ security_settings.lockout_duration_minutes }} دقيقة{% else %}{% trans "Lockout duration" %}: {{ security_settings.lockout_duration_minutes }} {% trans "minutes" %}{% endif %}
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mb-4">
    <!-- User Security Management -->
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إدارة أمان المستخدمين{% else %}{% trans "User Security Management" %}{% endif %}</h5>
                <a href="{% url 'core:security:user_security_info_list' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-users-cog me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}{% trans "Manage Users" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-primary">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-circle bg-primary me-3">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}إدارة أمان المستخدمين{% else %}{% trans "User Security Management" %}{% endif %}</h5>
                                </div>
                                <p class="card-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    إدارة حالة حسابات المستخدمين، وإلغاء قفل الحسابات، وإعادة تعيين محاولات تسجيل الدخول الفاشلة.
                                    {% else %}
                                    {% trans "Manage user account status, unlock accounts, and reset failed login attempts." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'core:security:user_security_info_list' %}" class="btn btn-sm btn-outline-primary mt-2">
                                    <i class="fas fa-users-cog me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}{% trans "Manage Users" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-info">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-circle bg-info me-3">
                                        <i class="fas fa-history text-white"></i>
                                    </div>
                                    <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}تاريخ كلمات المرور{% else %}{% trans "Password History" %}{% endif %}</h5>
                                </div>
                                <p class="card-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    عرض تاريخ كلمات المرور السابقة للمستخدمين ومنع إعادة استخدام كلمات المرور القديمة.
                                    {% else %}
                                    {% trans "View users' previous password history and prevent reuse of old passwords." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'core:security:user_security_info_list' %}" class="btn btn-sm btn-outline-info mt-2">
                                    <i class="fas fa-history me-1"></i> {% if LANGUAGE_CODE == 'ar' %}عرض التاريخ{% else %}{% trans "View History" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="icon-circle bg-warning me-3">
                                        <i class="fas fa-lock text-white"></i>
                                    </div>
                                    <h5 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}الحسابات المقفلة{% else %}{% trans "Locked Accounts" %}{% endif %}</h5>
                                </div>
                                <p class="card-text">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    عرض وإدارة الحسابات المقفلة بسبب محاولات تسجيل الدخول الفاشلة المتكررة.
                                    {% else %}
                                    {% trans "View and manage accounts that are locked due to repeated failed login attempts." %}
                                    {% endif %}
                                </p>
                                <a href="{% url 'core:security:user_security_info_list' %}" class="btn btn-sm btn-outline-warning mt-2">
                                    <i class="fas fa-unlock-alt me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة الحسابات المقفلة{% else %}{% trans "Manage Locked Accounts" %}{% endif %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Security Logs -->
    <div class="col-md-8 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}سجلات الأمان الأخيرة{% else %}{% trans "Recent Security Logs" %}{% endif %}</h5>
                <a href="{% url 'core:security:logs' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-list me-1"></i> {% if LANGUAGE_CODE == 'ar' %}عرض الكل{% else %}{% trans "View All" %}{% endif %}
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}التاريخ{% else %}{% trans "Date" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}النوع{% else %}{% trans "Type" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}المستخدم{% else %}{% trans "User" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}عنوان IP{% else %}{% trans "IP Address" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for log in recent_logs %}
                            <tr>
                                <td>{{ log.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    {% if log.log_type == 'login' %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}تسجيل دخول{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'login_failed' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}فشل تسجيل الدخول{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'account_locked' %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}قفل الحساب{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% elif log.log_type == 'mfa_verification_failed' %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}فشل التحقق من MFA{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}{{ log.get_log_type_display }}{% else %}{{ log.get_log_type_display }}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>{{ log.user.username|default:"Anonymous" }}</td>
                                <td>{{ log.ip_address|default:"N/A" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="text-center py-3">
                                    {% if LANGUAGE_CODE == 'ar' %}لا توجد سجلات أمان حديثة.{% else %}{% trans "No recent security logs." %}{% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Security Statistics -->
    <div class="col-md-4 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إحصائيات الأمان{% else %}{% trans "Security Statistics" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}تسجيلات الدخول الناجحة{% else %}{% trans "Successful Logins" %}{% endif %}</h6>
                                <h2 class="mb-0 text-success">{{ login_stats.successful_logins }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 mb-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}محاولات تسجيل الدخول الفاشلة{% else %}{% trans "Failed Login Attempts" %}{% endif %}</h6>
                                <h2 class="mb-0 text-danger">{{ login_stats.failed_logins }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{% if LANGUAGE_CODE == 'ar' %}الحسابات المقفلة{% else %}{% trans "Locked Accounts" %}{% endif %}</h6>
                                <h2 class="mb-0 text-warning">{{ login_stats.locked_accounts }}</h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add any JavaScript for the security dashboard here
    });
</script>
{% endblock %}
