{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}حذف إعداد النظام{% else %}{% trans "Delete System Setting" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تأكيد الحذف{% else %}{% trans "Confirm Deletion" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد حذف إعداد النظام هذا؟ هذا الإجراء لا يمكن التراجع عنه.
                    {% else %}
                    {% trans "Are you sure you want to delete this system setting? This action cannot be undone." %}
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}تفاصيل الإعداد:{% else %}{% trans "Setting Details:" %}{% endif %}</h6>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">{% if LANGUAGE_CODE == 'ar' %}المفتاح{% else %}{% trans "Key" %}{% endif %}</th>
                            <td><code>{{ setting.key }}</code></td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}القيمة{% else %}{% trans "Value" %}{% endif %}</th>
                            <td>{{ setting.value }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الوصف{% else %}{% trans "Description" %}{% endif %}</th>
                            <td>{{ setting.description }}</td>
                        </tr>
                    </table>
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'core:settings:system_settings_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
