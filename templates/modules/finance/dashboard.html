{% extends 'modules/base_module.html' %}
{% load i18n %}

{% block module_title %}{% trans "Financial Management" %}{% endblock %}
{% block module_name %}{% trans "Finance" %}{% endblock %}
{% block module_icon %}fas fa-money-bill-wave{% endblock %}
{% block module_header %}{% trans "Financial Management" %}{% endblock %}
{% block module_description %}{% trans "Take control of your financial operations. Manage budgets, financial planning, cash flow, and financial analysis." %}{% endblock %}

{% block sidebar_header_class %}bg-primary{% endblock %}
{% block quick_actions_header_class %}bg-primary{% endblock %}

{% block module_alert %}
<div class="alert alert-primary alert-dismissible fade show module-alert">
    <div class="d-flex">
        <div class="flex-shrink-0 me-3">
            <i class="fas fa-money-bill-wave fa-2x text-primary"></i>
        </div>
        <div>
            <h5 class="alert-heading">{% trans "Welcome to Financial Management" %}</h5>
            <p>{% trans "This module helps you manage all aspects of your financial operations. Use the navigation menu to access different financial functions." %}</p>
        </div>
    </div>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
</div>
{% endblock %}

{% block sidebar_menu %}
<a href="#" class="list-group-item list-group-item-action active">
    <i class="fas fa-tachometer-alt me-2"></i> {% trans "Dashboard" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-coins me-2"></i> {% trans "Budgets" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-pie me-2"></i> {% trans "Financial Planning" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-exchange-alt me-2"></i> {% trans "Cash Flow" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-chart-line me-2"></i> {% trans "Financial Analysis" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-invoice-dollar me-2"></i> {% trans "Investments" %}
</a>
<a href="#" class="list-group-item list-group-item-action">
    <i class="fas fa-file-alt me-2"></i> {% trans "Reports" %}
</a>
{% endblock %}

{% block quick_actions %}
<div class="d-grid gap-2">
    <button class="btn btn-primary">
        <i class="fas fa-plus me-2"></i> {% trans "New Budget" %}
    </button>
    <button class="btn btn-outline-primary">
        <i class="fas fa-chart-line me-2"></i> {% trans "Financial Analysis" %}
    </button>
    <button class="btn btn-outline-primary">
        <i class="fas fa-file-alt me-2"></i> {% trans "Generate Reports" %}
    </button>
</div>
{% endblock %}

{% block stats_cards %}
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-primary h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-primary text-white rounded p-3">
                        <i class="fas fa-coins fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Active Budgets" %}</h6>
                    <h3 class="card-title mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-success h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-success text-white rounded p-3">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Revenue (YTD)" %}</h6>
                    <h3 class="card-title mb-0">$0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-danger h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-danger text-white rounded p-3">
                        <i class="fas fa-chart-line fa-flip-vertical fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Expenses (YTD)" %}</h6>
                    <h3 class="card-title mb-0">$0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="col-md-3 col-sm-6 mb-4">
    <div class="card border-info h-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div class="flex-shrink-0 me-3">
                    <div class="bg-info text-white rounded p-3">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                </div>
                <div>
                    <h6 class="card-subtitle text-muted">{% trans "Profit Margin" %}</h6>
                    <h3 class="card-title mb-0">0%</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content_title %}{% trans "Financial Overview" %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {% trans "This module is under development. The actual implementation will be available in future updates." %}
        </div>
        
        <h5 class="mt-4 mb-3">{% trans "Budget Performance" %}</h5>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>{% trans "Budget Name" %}</th>
                        <th>{% trans "Period" %}</th>
                        <th>{% trans "Planned" %}</th>
                        <th>{% trans "Actual" %}</th>
                        <th>{% trans "Variance" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td colspan="7" class="text-center">{% trans "No budget data available" %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Revenue vs Expenses" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No financial data available" %}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% trans "Cash Flow Forecast" %}</h5>
            </div>
            <div class="card-body">
                <p class="text-muted text-center">{% trans "No forecast data available" %}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block module_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Finance Module loaded');
    });
</script>
{% endblock %}
