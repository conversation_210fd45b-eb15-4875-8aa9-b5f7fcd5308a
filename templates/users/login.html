{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Login" %} | ERP System{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h4 class="mb-0">
                    <i class="fas fa-cubes me-2"></i>ERP System
                </h4>
            </div>
            <div class="card-body p-4">
                <h5 class="card-title text-center mb-4">{% trans "Login to your account" %}</h5>
                
                {% if messages %}
                <div class="messages mb-4">
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <form method="post" action="{% url 'users:login' %}">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.username.id_for_label }}" class="form-label">
                            <i class="fas fa-user me-1"></i>{% trans "Username" %}
                        </label>
                        {{ form.username }}
                        {% if form.username.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.username.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-4">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock me-1"></i>{% trans "Password" %}
                        </label>
                        {{ form.password }}
                        {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {{ form.password.errors }}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-sign-in-alt me-1"></i>{% trans "Login" %}
                        </button>
                    </div>
                    
                    {% if next %}
                    <input type="hidden" name="next" value="{{ next }}">
                    {% endif %}
                </form>
            </div>
            <div class="card-footer bg-light text-center py-3">
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown">
                        <i class="fas fa-globe me-1"></i>{% trans "Language" %}
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                        {% get_available_languages as LANGUAGES %}
                        {% get_language_info_list for LANGUAGES as languages %}
                        {% for language in languages %}
                        <li>
                            <a class="dropdown-item {% if language.code == LANGUAGE_CODE %}active{% endif %}" 
                               href="{% url 'set_language' %}" 
                               data-language="{{ language.code }}">
                                {{ language.name_local }}
                            </a>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Language selector
        $('.dropdown-item[data-language]').click(function(e) {
            e.preventDefault();
            var language = $(this).data('language');
            var form = $('<form>', {
                'action': '{% url "set_language" %}',
                'method': 'post'
            }).append(
                $('<input>', {
                    'name': 'language',
                    'value': language,
                    'type': 'hidden'
                }),
                $('<input>', {
                    'name': 'csrfmiddlewaretoken',
                    'value': '{{ csrf_token }}',
                    'type': 'hidden'
                })
            );
            $('body').append(form);
            form.submit();
        });
    });
</script>
{% endblock %}
