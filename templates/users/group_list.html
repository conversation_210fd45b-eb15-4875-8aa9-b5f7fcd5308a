{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة المجموعات والصلاحيات{% else %}{% trans "Groups & Permissions Management" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة المجموعات{% else %}{% trans "Group List" %}{% endif %}</h5>
                <a href="{% url 'users:group_create' %}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة مجموعة جديدة{% else %}{% trans "Add New Group" %}{% endif %}
                </a>
            </div>
            <div class="card-body">
                {% if groups %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المجموعة{% else %}{% trans "Group Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}عدد المستخدمين{% else %}{% trans "User Count" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}عدد الصلاحيات{% else %}{% trans "Permission Count" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for group in groups %}
                            <tr>
                                <td>{{ group.name }}</td>
                                <td>{{ group.user_set.count }}</td>
                                <td>{{ group.permissions.count }}</td>
                                <td>
                                    <a href="{% url 'users:group_edit' group.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'users:group_delete' group.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا توجد مجموعات حتى الآن. انقر على "إضافة مجموعة جديدة" لإنشاء أول مجموعة.{% else %}{% trans "No groups yet. Click 'Add New Group' to create your first group." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}نظرة عامة على الصلاحيات{% else %}{% trans "Permissions Overview" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    المجموعات هي طريقة لتنظيم المستخدمين وتعيين صلاحيات متعددة لهم دفعة واحدة. يمكنك إنشاء مجموعات مختلفة لأدوار مختلفة في مؤسستك.
                    {% else %}
                    {% trans "Groups are a way to organize users and assign multiple permissions to them at once. You can create different groups for different roles in your organization." %}
                    {% endif %}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المجموعات الشائعة{% else %}{% trans "Common Groups" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}المديرين{% else %}{% trans "Managers" %}{% endif %}
                                        <span class="badge bg-primary rounded-pill">{% trans "Full access to most modules" %}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}المحاسبين{% else %}{% trans "Accountants" %}{% endif %}
                                        <span class="badge bg-primary rounded-pill">{% trans "Access to financial modules" %}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}موظفي المبيعات{% else %}{% trans "Sales Staff" %}{% endif %}
                                        <span class="badge bg-primary rounded-pill">{% trans "Access to sales and CRM" %}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}أنواع الصلاحيات{% else %}{% trans "Permission Types" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}عرض{% else %}{% trans "View" %}{% endif %}
                                        <span class="badge bg-success rounded-pill">{% trans "Read-only access" %}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}إضافة{% else %}{% trans "Add" %}{% endif %}
                                        <span class="badge bg-info rounded-pill">{% trans "Create new records" %}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans "Change" %}{% endif %}
                                        <span class="badge bg-warning rounded-pill">{% trans "Update existing records" %}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}
                                        <span class="badge bg-danger rounded-pill">{% trans "Remove records" %}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
