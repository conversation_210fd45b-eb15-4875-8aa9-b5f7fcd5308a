{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if user %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل المستخدم{% else %}{% trans "Edit User" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة مستخدم جديد{% else %}{% trans "Add New User" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if user %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل المستخدم: {{ user.username }}{% else %}{% trans "Edit User" %}: {{ user.username }}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إضافة مستخدم جديد{% else %}{% trans "Add New User" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات الحساب{% else %}{% trans "Account Information" %}{% endif %}</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if not user %}
                            <div class="mb-3">
                                <label for="{{ form.password1.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}كلمة المرور{% else %}{% trans "Password" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.password1 }}
                                {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password1.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">{{ form.password1.help_text }}</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.password2.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}تأكيد كلمة المرور{% else %}{% trans "Confirm Password" %}{% endif %} <span class="text-danger">*</span></label>
                                {{ form.password2 }}
                                {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            {% else %}
                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    لتغيير كلمة المرور، استخدم خيار "تغيير كلمة المرور" المنفصل.
                                    {% else %}
                                    {% trans "To change the password, use the separate 'Change Password' option." %}
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}
                            
                            <div class="mb-3 form-check">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}
                                </label>
                                {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_active.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3 form-check">
                                {{ form.is_staff }}
                                <label class="form-check-label" for="{{ form.is_staff.id_for_label }}">
                                    {% if LANGUAGE_CODE == 'ar' %}موظف (يمكنه الوصول إلى لوحة الإدارة){% else %}{% trans "Staff (Can access admin panel)" %}{% endif %}
                                </label>
                                {% if form.is_staff.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.is_staff.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}المعلومات الشخصية{% else %}{% trans "Personal Information" %}{% endif %}</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.first_name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم الأول{% else %}{% trans "First Name" %}{% endif %}</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.last_name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الاسم الأخير{% else %}{% trans "Last Name" %}{% endif %}</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الهاتف{% else %}{% trans "Phone" %}{% endif %}</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.address.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}العنوان{% else %}{% trans "Address" %}{% endif %}</label>
                                {{ form.address }}
                                {% if form.address.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.address.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.profile_picture.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صورة الملف الشخصي{% else %}{% trans "Profile Picture" %}{% endif %}</label>
                                {{ form.profile_picture }}
                                {% if form.profile_picture.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.profile_picture.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}معلومات الشركة{% else %}{% trans "Company Information" %}{% endif %}</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.company.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</label>
                                {{ form.company }}
                                {% if form.company.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.company.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.branch.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}</label>
                                {{ form.branch }}
                                {% if form.branch.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.branch.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6 class="mb-3">{% if LANGUAGE_CODE == 'ar' %}الصلاحيات{% else %}{% trans "Permissions" %}{% endif %}</h6>
                            
                            <div class="mb-3">
                                <label for="{{ form.groups.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}المجموعات{% else %}{% trans "Groups" %}{% endif %}</label>
                                {{ form.groups }}
                                {% if form.groups.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.groups.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.user_permissions.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}صلاحيات المستخدم{% else %}{% trans "User Permissions" %}{% endif %}</label>
                                {{ form.user_permissions }}
                                {% if form.user_permissions.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.user_permissions.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'users:user_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize Select2 for dropdowns
        $('.select2').select2({
            theme: 'bootstrap-5'
        });
        
        // Company-Branch dependency
        $('#{{ form.company.id_for_label }}').change(function() {
            var companyId = $(this).val();
            if (companyId) {
                // AJAX call to get branches for the selected company
                $.ajax({
                    url: '/companies/get_branches/',
                    data: {
                        'company_id': companyId
                    },
                    dataType: 'json',
                    success: function(data) {
                        var branchSelect = $('#{{ form.branch.id_for_label }}');
                        branchSelect.empty();
                        branchSelect.append('<option value="">---------</option>');
                        $.each(data, function(index, branch) {
                            branchSelect.append('<option value="' + branch.id + '">' + branch.name + '</option>');
                        });
                    }
                });
            } else {
                $('#{{ form.branch.id_for_label }}').empty().append('<option value="">---------</option>');
            }
        });
    });
</script>
{% endblock %}
