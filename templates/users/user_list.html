{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}إدارة المستخدمين{% else %}{% trans "User Management" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}قائمة المستخدمين{% else %}{% trans "User List" %}{% endif %}</h5>
                <div>
                    <a href="{% url 'users:group_list' %}" class="btn btn-sm btn-outline-secondary me-1">
                        <i class="fas fa-users-cog me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إدارة المجموعات{% else %}{% trans "Manage Groups" %}{% endif %}
                    </a>
                    <a href="{% url 'users:user_create' %}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة مستخدم جديد{% else %}{% trans "Add New User" %}{% endif %}
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الحالة{% else %}{% trans "Status" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الصلاحيات{% else %}{% trans "Permissions" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.get_full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.company.name|default:"-" }}</td>
                                <td>{{ user.branch.name|default:"-" }}</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">{% if LANGUAGE_CODE == 'ar' %}نشط{% else %}{% trans "Active" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% if LANGUAGE_CODE == 'ar' %}غير نشط{% else %}{% trans "Inactive" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_superuser %}
                                    <span class="badge bg-danger">{% if LANGUAGE_CODE == 'ar' %}مدير النظام{% else %}{% trans "Superuser" %}{% endif %}</span>
                                    {% elif user.is_staff %}
                                    <span class="badge bg-warning">{% if LANGUAGE_CODE == 'ar' %}موظف{% else %}{% trans "Staff" %}{% endif %}</span>
                                    {% else %}
                                    <span class="badge bg-info">{% if LANGUAGE_CODE == 'ar' %}مستخدم{% else %}{% trans "User" %}{% endif %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'users:user_edit' user.id %}" class="btn btn-sm btn-outline-primary" title="{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}">
                                        <i class="fas fa-edit"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}تعديل{% else %}{% trans 'Edit' %}{% endif %}</span>
                                    </a>
                                    {% if user != request.user %}
                                    <a href="{% url 'users:user_delete' user.id %}" class="btn btn-sm btn-outline-danger" title="{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}">
                                        <i class="fas fa-trash"></i>
                                        <span class="visually-hidden">{% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans 'Delete' %}{% endif %}</span>
                                    </a>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا يوجد مستخدمين حتى الآن. انقر على "إضافة مستخدم جديد" لإنشاء أول مستخدم.{% else %}{% trans "No users yet. Click 'Add New User' to create your first user." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}إدارة المجموعات والصلاحيات{% else %}{% trans "Groups & Permissions Management" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المجموعات{% else %}{% trans "Groups" %}{% endif %}</h6>
                                <a href="#" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إضافة{% else %}{% trans "Add" %}{% endif %}
                                </a>
                            </div>
                            <div class="card-body">
                                <div class="list-group">
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}المديرين{% else %}{% trans "Managers" %}{% endif %}
                                        <span class="badge bg-primary rounded-pill">3</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}المحاسبين{% else %}{% trans "Accountants" %}{% endif %}
                                        <span class="badge bg-primary rounded-pill">5</span>
                                    </a>
                                    <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        {% if LANGUAGE_CODE == 'ar' %}المبيعات{% else %}{% trans "Sales" %}{% endif %}
                                        <span class="badge bg-primary rounded-pill">8</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}الصلاحيات{% else %}{% trans "Permissions" %}{% endif %}</h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    يمكنك إدارة صلاحيات المستخدمين من خلال المجموعات. انقر على مجموعة لعرض وتعديل الصلاحيات الخاصة بها.
                                    {% else %}
                                    {% trans "You can manage user permissions through groups. Click on a group to view and edit its permissions." %}
                                    {% endif %}
                                </p>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    {% if LANGUAGE_CODE == 'ar' %}
                                    تغيير الصلاحيات قد يؤثر على قدرة المستخدمين على الوصول إلى أجزاء معينة من النظام. يرجى توخي الحذر.
                                    {% else %}
                                    {% trans "Changing permissions may affect users' ability to access certain parts of the system. Please proceed with caution." %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
