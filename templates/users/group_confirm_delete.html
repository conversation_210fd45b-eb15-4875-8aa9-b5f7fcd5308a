{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}حذف المجموعة{% else %}{% trans "Delete Group" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تأكيد الحذف{% else %}{% trans "Confirm Deletion" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد حذف المجموعة "{{ group.name }}"؟ هذا الإجراء لا يمكن التراجع عنه.
                    {% else %}
                    {% trans "Are you sure you want to delete the group" %} "{{ group.name }}"? {% trans "This action cannot be undone." %}
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}تفاصيل المجموعة:{% else %}{% trans "Group Details:" %}{% endif %}</h6>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">{% if LANGUAGE_CODE == 'ar' %}اسم المجموعة{% else %}{% trans "Group Name" %}{% endif %}</th>
                            <td>{{ group.name }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}عدد المستخدمين{% else %}{% trans "User Count" %}{% endif %}</th>
                            <td>{{ group.user_set.count }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}عدد الصلاحيات{% else %}{% trans "Permission Count" %}{% endif %}</th>
                            <td>{{ group.permissions.count }}</td>
                        </tr>
                    </table>
                </div>
                
                {% if group.user_set.exists %}
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    تحذير: هناك {{ group.user_set.count }} مستخدم في هذه المجموعة. حذف المجموعة سيؤدي إلى إزالة هذه الصلاحيات من هؤلاء المستخدمين.
                    {% else %}
                    {% trans "Warning: There are" %} {{ group.user_set.count }} {% trans "users in this group. Deleting the group will remove these permissions from those users." %}
                    {% endif %}
                </div>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'users:group_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
{% if group.user_set.exists %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المستخدمون المتأثرون{% else %}{% trans "Affected Users" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in group.user_set.all %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.get_full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.company.name|default:"-" }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
