{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}
    {% if group %}
        {% if LANGUAGE_CODE == 'ar' %}تعديل المجموعة{% else %}{% trans "Edit Group" %}{% endif %}
    {% else %}
        {% if LANGUAGE_CODE == 'ar' %}إضافة مجموعة جديدة{% else %}{% trans "Add New Group" %}{% endif %}
    {% endif %}
{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">
                    {% if group %}
                        {% if LANGUAGE_CODE == 'ar' %}تعديل المجموعة: {{ group.name }}{% else %}{% trans "Edit Group" %}: {{ group.name }}{% endif %}
                    {% else %}
                        {% if LANGUAGE_CODE == 'ar' %}إضافة مجموعة جديدة{% else %}{% trans "Add New Group" %}{% endif %}
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">{% if LANGUAGE_CODE == 'ar' %}اسم المجموعة{% else %}{% trans "Group Name" %}{% endif %} <span class="text-danger">*</span></label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{% if LANGUAGE_CODE == 'ar' %}الصلاحيات{% else %}{% trans "Permissions" %}{% endif %}</label>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}
                            حدد الصلاحيات التي ترغب في منحها لهذه المجموعة. يمكنك استخدام مربع البحث للعثور على صلاحيات محددة.
                            {% else %}
                            {% trans "Select the permissions you want to grant to this group. You can use the search box to find specific permissions." %}
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <input type="text" class="form-control" id="permissionSearch" placeholder="{% if LANGUAGE_CODE == 'ar' %}بحث عن الصلاحيات...{% else %}{% trans 'Search permissions...' %}{% endif %}">
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">{% if LANGUAGE_CODE == 'ar' %}جميع الصلاحيات{% else %}{% trans "All Permissions" %}{% endif %}</h6>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">{% if LANGUAGE_CODE == 'ar' %}تحديد الكل{% else %}{% trans "Select All" %}{% endif %}</button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAll">{% if LANGUAGE_CODE == 'ar' %}إلغاء تحديد الكل{% else %}{% trans "Deselect All" %}{% endif %}</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                        <div class="row">
                                            {% for permission in form.permissions %}
                                            <div class="col-md-4 permission-item">
                                                <div class="form-check mb-2">
                                                    {{ permission.tag }}
                                                    <label class="form-check-label" for="{{ permission.id_for_label }}">
                                                        {{ permission.choice_label }}
                                                    </label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if form.permissions.errors %}
                        <div class="invalid-feedback d-block mt-2">
                            {% for error in form.permissions.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'users:group_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i> {% if LANGUAGE_CODE == 'ar' %}العودة{% else %}{% trans "Back" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حفظ{% else %}{% trans "Save" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}المستخدمون في هذه المجموعة{% else %}{% trans "Users in this Group" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                {% if group and group.user_set.exists %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                                <th>{% if LANGUAGE_CODE == 'ar' %}الإجراءات{% else %}{% trans "Actions" %}{% endif %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in group.user_set.all %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.get_full_name }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.company.name|default:"-" }}</td>
                                <td>
                                    <a href="{% url 'users:user_edit' user.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}لا يوجد مستخدمون في هذه المجموعة حتى الآن.{% else %}{% trans "No users in this group yet." %}{% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Permission search
        $('#permissionSearch').on('keyup', function() {
            var value = $(this).val().toLowerCase();
            $('.permission-item').filter(function() {
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        });
        
        // Select all permissions
        $('#selectAll').click(function() {
            $('.permission-item input[type="checkbox"]').prop('checked', true);
        });
        
        // Deselect all permissions
        $('#deselectAll').click(function() {
            $('.permission-item input[type="checkbox"]').prop('checked', false);
        });
    });
</script>
{% endblock %}
