{% extends 'modules/settings/dashboard.html' %}
{% load i18n %}

{% block content_title %}{% if LANGUAGE_CODE == 'ar' %}حذف المستخدم{% else %}{% trans "Delete User" %}{% endif %}{% endblock %}

{% block module_content %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-light">
                <h5 class="card-title mb-0">{% if LANGUAGE_CODE == 'ar' %}تأكيد الحذف{% else %}{% trans "Confirm Deletion" %}{% endif %}</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    هل أنت متأكد من أنك تريد حذف المستخدم "{{ user.username }}"؟ هذا الإجراء لا يمكن التراجع عنه.
                    {% else %}
                    {% trans "Are you sure you want to delete the user" %} "{{ user.username }}"? {% trans "This action cannot be undone." %}
                    {% endif %}
                </div>
                
                <div class="mb-4">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}تفاصيل المستخدم:{% else %}{% trans "User Details:" %}{% endif %}</h6>
                    <table class="table table-bordered">
                        <tr>
                            <th width="30%">{% if LANGUAGE_CODE == 'ar' %}اسم المستخدم{% else %}{% trans "Username" %}{% endif %}</th>
                            <td>{{ user.username }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الاسم الكامل{% else %}{% trans "Full Name" %}{% endif %}</th>
                            <td>{{ user.get_full_name|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}البريد الإلكتروني{% else %}{% trans "Email" %}{% endif %}</th>
                            <td>{{ user.email|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الشركة{% else %}{% trans "Company" %}{% endif %}</th>
                            <td>{{ user.company.name|default:"-" }}</td>
                        </tr>
                        <tr>
                            <th>{% if LANGUAGE_CODE == 'ar' %}الفرع{% else %}{% trans "Branch" %}{% endif %}</th>
                            <td>{{ user.branch.name|default:"-" }}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle me-2"></i>
                    {% if LANGUAGE_CODE == 'ar' %}
                    حذف المستخدم سيؤدي إلى حذف جميع البيانات المرتبطة به، بما في ذلك السجلات والإعدادات الشخصية.
                    {% else %}
                    {% trans "Deleting the user will remove all associated data, including records and personal settings." %}
                    {% endif %}
                </div>
                
                <form method="post">
                    {% csrf_token %}
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'users:user_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i> {% if LANGUAGE_CODE == 'ar' %}إلغاء{% else %}{% trans "Cancel" %}{% endif %}
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> {% if LANGUAGE_CODE == 'ar' %}حذف{% else %}{% trans "Delete" %}{% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_content %}{% endblock %}
