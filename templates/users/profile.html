{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "My Profile" %} | ERP System{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-md-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">{% trans "Profile Information" %}</h5>
                </div>
                <div class="card-body text-center">
                    {% if user.profile_picture %}
                    <img src="{{ user.profile_picture.url }}" class="rounded-circle img-thumbnail mb-3" width="150" height="150" alt="{% trans 'Profile picture' %}">
                    {% else %}
                    <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 150px; height: 150px;">
                        <i class="fas fa-user-circle fa-5x text-secondary"></i>
                    </div>
                    {% endif %}
                    
                    <h4>{{ user.get_full_name|default:user.username }}</h4>
                    <p class="text-muted">{{ user.email }}</p>
                    
                    <div class="d-grid gap-2 mt-3">
                        <button class="btn btn-outline-primary" id="changeProfilePicture">
                            <i class="fas fa-camera me-1"></i> {% trans "Change Picture" %}
                        </button>
                        <button class="btn btn-outline-secondary" id="changePassword">
                            <i class="fas fa-key me-1"></i> {% trans "Change Password" %}
                        </button>
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between">
                        <div>
                            <i class="fas fa-building me-1"></i> {{ user.company.name|default:"-" }}
                        </div>
                        <div>
                            <i class="fas fa-map-marker-alt me-1"></i> {{ user.branch.name|default:"-" }}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% trans "Account Information" %}</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{% trans "Username" %}</span>
                            <span class="text-primary">{{ user.username }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{% trans "Status" %}</span>
                            {% if user.is_active %}
                            <span class="badge bg-success">{% trans "Active" %}</span>
                            {% else %}
                            <span class="badge bg-secondary">{% trans "Inactive" %}</span>
                            {% endif %}
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{% trans "Role" %}</span>
                            {% if user.is_superuser %}
                            <span class="badge bg-danger">{% trans "Superuser" %}</span>
                            {% elif user.is_staff %}
                            <span class="badge bg-warning">{% trans "Staff" %}</span>
                            {% else %}
                            <span class="badge bg-info">{% trans "User" %}</span>
                            {% endif %}
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{% trans "Last Login" %}</span>
                            <span>{{ user.last_login|date:"Y-m-d H:i"|default:"-" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>{% trans "Date Joined" %}</span>
                            <span>{{ user.date_joined|date:"Y-m-d" }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{% trans "Edit Profile" %}</h5>
                    <button class="btn btn-sm btn-light" id="toggleEdit">
                        <i class="fas fa-edit me-1"></i> {% trans "Edit" %}
                    </button>
                </div>
                <div class="card-body">
                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <form method="post" enctype="multipart/form-data" id="profileForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">{% trans "First Name" %}</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.first_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">{% trans "Last Name" %}</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.last_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.email.id_for_label }}" class="form-label">{% trans "Email" %}</label>
                            {{ form.email }}
                            {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.phone.id_for_label }}" class="form-label">{% trans "Phone" %}</label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">{% trans "Address" %}</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 d-none">
                            <label for="{{ form.profile_picture.id_for_label }}" class="form-label">{% trans "Profile Picture" %}</label>
                            {{ form.profile_picture }}
                            {% if form.profile_picture.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.profile_picture.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% trans "Save Changes" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">{% trans "Recent Activity" %}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        {% trans "Your recent activity will be displayed here." %}
                    </div>
                    
                    <ul class="list-group">
                        <li class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{% trans "Logged in" %}</h6>
                                <small>{{ user.last_login|date:"Y-m-d H:i"|default:"-" }}</small>
                            </div>
                            <p class="mb-1 text-muted">{% trans "You logged in to the system." %}</p>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Change Password" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="passwordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">{% trans "Current Password" %}</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">{% trans "New Password" %}</label>
                        <input type="password" class="form-control" id="newPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">{% trans "Confirm New Password" %}</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="savePassword">{% trans "Save Changes" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Change Profile Picture Modal -->
<div class="modal fade" id="changeProfilePictureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Change Profile Picture" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="pictureForm">
                    <div class="mb-3">
                        <label for="profilePicture" class="form-label">{% trans "Upload New Picture" %}</label>
                        <input type="file" class="form-control" id="profilePicture" accept="image/*">
                    </div>
                    <div class="text-center mt-3">
                        <div id="imagePreview" class="d-none">
                            <img src="" class="img-thumbnail mb-3" style="max-height: 200px;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="savePicture">{% trans "Save Changes" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Toggle form fields edit state
        $('#toggleEdit').click(function() {
            var formFields = $('#profileForm input, #profileForm textarea');
            formFields.prop('readonly', function(i, v) { return !v; });
            
            if ($(this).find('i').hasClass('fa-edit')) {
                $(this).html('<i class="fas fa-times me-1"></i> {% trans "Cancel" %}');
            } else {
                $(this).html('<i class="fas fa-edit me-1"></i> {% trans "Edit" %}');
            }
        });
        
        // Set form fields to readonly by default
        $('#profileForm input, #profileForm textarea').prop('readonly', true);
        
        // Change Password Modal
        $('#changePassword').click(function() {
            $('#changePasswordModal').modal('show');
        });
        
        // Change Profile Picture Modal
        $('#changeProfilePicture').click(function() {
            $('#changeProfilePictureModal').modal('show');
        });
        
        // Image Preview
        $('#profilePicture').change(function() {
            if (this.files && this.files[0]) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#imagePreview').removeClass('d-none');
                    $('#imagePreview img').attr('src', e.target.result);
                }
                reader.readAsDataURL(this.files[0]);
            }
        });
        
        // Save Profile Picture
        $('#savePicture').click(function() {
            var fileInput = $('#profilePicture')[0];
            if (fileInput.files && fileInput.files[0]) {
                // Transfer the selected file to the main form
                var mainFileInput = $('#{{ form.profile_picture.id_for_label }}')[0];
                
                // Create a DataTransfer object and add the file
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(fileInput.files[0]);
                mainFileInput.files = dataTransfer.files;
                
                // Submit the form
                $('#profileForm').submit();
            }
            $('#changeProfilePictureModal').modal('hide');
        });
    });
</script>
{% endblock %}
