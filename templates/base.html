{% load i18n %}
{% load static %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "ERP System" %}{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Tajawal for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/style.css' %}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'core:dashboard' %}">
                <i class="fas fa-cubes me-2"></i>{% trans "ERP System" %}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-label="{% trans 'Toggle navigation' %}" title="{% trans 'Toggle navigation' %}">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'core:dashboard' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>{% trans "Dashboard" %}
                        </a>
                    </li>
                    {% if perms.users.view_user %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'users:user_list' %}">
                            <i class="fas fa-users me-1"></i>{% trans "Users" %}
                        </a>
                    </li>
                    {% endif %}
                    {% if perms.companies.view_company %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="companiesDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building me-1"></i>{% trans "Companies" %}
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{% url 'companies:company_list' %}">
                                    {% trans "Companies" %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'companies:branch_list' %}">
                                    {% trans "Branches" %}
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% endif %}
                    {% if perms.modules.view_module %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'modules:module_list' %}">
                            <i class="fas fa-puzzle-piece me-1"></i>{% trans "Modules" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Search Form -->
                <form class="d-flex me-2" id="searchForm">
                    <div class="input-group">
                        <input type="text" id="searchInput" class="form-control" placeholder="{% trans 'Search...' %}">
                        <button class="btn btn-outline-light" type="submit" aria-label="{% trans 'Search' %}" title="{% trans 'Search' %}">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <!-- Company/Branch Selector -->
                <ul class="navbar-nav me-2">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="companyDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building me-1"></i>
                            {% if user.company %}
                                {{ user.company.name }} / {{ user.branch.name }}
                            {% else %}
                                {% trans "Select Company/Branch" %}
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu">
                            {% if user.is_superuser %}
                                {% for company in companies %}
                                    <li class="dropdown-header">{{ company.name }}</li>
                                    {% for branch in company.branches.all %}
                                        <li>
                                            <a class="dropdown-item {% if user.company == company and user.branch == branch %}active{% endif %}"
                                               href="{% url 'companies:set_active_company_branch' company_id=company.id branch_id=branch.id %}?next={{ request.path }}">
                                                <i class="fas fa-code-branch me-1"></i>{{ branch.name }}
                                            </a>
                                        </li>
                                    {% endfor %}
                                    {% if not forloop.last %}<li><hr class="dropdown-divider"></li>{% endif %}
                                {% endfor %}
                            {% endif %}
                        </ul>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            {% if user.profile_picture %}
                                <img src="{{ user.profile_picture.url }}" class="rounded-circle me-1" width="24" height="24" alt="{% trans 'Profile picture' %}" title="{{ user.get_full_name|default:user.username }}">
                            {% else %}
                                <i class="fas fa-user-circle me-1"></i>
                            {% endif %}
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{% url 'users:user_profile' %}">
                                    <i class="fas fa-user me-1"></i>{% trans "Profile" %}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'users:logout' %}">
                                    <i class="fas fa-sign-out-alt me-1"></i>{% trans "Logout" %}
                                </a>
                            </li>
                        </ul>
                    </li>
                    <!-- Language Selector -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            {% if LANGUAGE_CODE == 'ar' %}العربية{% else %}English{% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% get_current_language as CURRENT_LANGUAGE %}
                            {% get_available_languages as AVAILABLE_LANGUAGES %}
                            {% for lang_code, lang_name in LANGUAGES %}
                                <li>
                                    <form action="{% url 'set_language' %}" method="post" class="language-form">
                                        {% csrf_token %}
                                        <input name="next" type="hidden" value="{{ request.path }}">
                                        <input name="language" type="hidden" value="{{ lang_code }}">
                                        <button type="submit" class="dropdown-item {% if lang_code == CURRENT_LANGUAGE %}active{% endif %}">
                                            {% if lang_code == 'ar' %}العربية{% else %}English{% endif %}
                                        </button>
                                    </form>
                                </li>
                            {% endfor %}
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <div class="container-fluid py-4">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="{% trans 'Close' %}"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">© {% now "Y" %} {% trans "ERP System" %}. {% trans "All rights reserved." %}</span>
        </div>
    </footer>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>

    <!-- AJAX Search -->
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2();

            // AJAX Search
            $('#searchForm').on('submit', function(e) {
                e.preventDefault();
                var query = $('#searchInput').val();

                if (query.length < 2) return;

                $.ajax({
                    url: '{% url "core:ajax_search" %}',
                    data: {
                        'q': query,
                        {% if user.company %}
                        'company_id': {{ user.company.id }},
                        {% endif %}
                    },
                    dataType: 'json',
                    success: function(data) {
                        // Display search results in a modal
                        var resultsHtml = '';

                        if (data.results.length === 0) {
                            resultsHtml = '<p class="text-center">{% trans "No results found" %}</p>';
                        } else {
                            resultsHtml = '<div class="list-group">';
                            $.each(data.results, function(index, result) {
                                var icon = '';
                                switch(result.type) {
                                    case 'user': icon = 'fas fa-user'; break;
                                    case 'company': icon = 'fas fa-building'; break;
                                    case 'branch': icon = 'fas fa-code-branch'; break;
                                    case 'module': icon = 'fas fa-puzzle-piece'; break;
                                    default: icon = 'fas fa-file';
                                }

                                resultsHtml += '<a href="' + result.url + '" class="list-group-item list-group-item-action">' +
                                               '<i class="' + icon + ' me-2"></i>' + result.text +
                                               '</a>';
                            });
                            resultsHtml += '</div>';
                        }

                        // Create or update modal
                        var modal = $('#searchResultsModal');
                        if (modal.length === 0) {
                            $('body').append(
                                '<div class="modal fade" id="searchResultsModal" tabindex="-1">' +
                                '<div class="modal-dialog">' +
                                '<div class="modal-content">' +
                                '<div class="modal-header">' +
                                '<h5 class="modal-title">{% trans "Search Results" %}</h5>' +
                                '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                                '</div>' +
                                '<div class="modal-body" id="searchResultsContent"></div>' +
                                '</div></div></div>'
                            );
                            modal = $('#searchResultsModal');
                        }

                        $('#searchResultsContent').html(resultsHtml);
                        var bsModal = new bootstrap.Modal(modal);
                        bsModal.show();
                    }
                });
            });

            // Language selector
            $('.dropdown-item[data-language]').click(function(e) {
                e.preventDefault();
                var language = $(this).data('language');
                var form = $('<form>', {
                    'action': '{% url "set_language" %}',
                    'method': 'post'
                }).append(
                    $('<input>', {
                        'name': 'language',
                        'value': language,
                        'type': 'hidden'
                    }),
                    $('<input>', {
                        'name': 'csrfmiddlewaretoken',
                        'value': '{{ csrf_token }}',
                        'type': 'hidden'
                    })
                );
                $('body').append(form);
                form.submit();
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
