{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% if LANGUAGE_CODE == 'ar' %}انتهت صلاحية كلمة المرور{% else %}{% trans "Password Expired" %}{% endif %}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}انتهت صلاحية كلمة المرور{% else %}{% trans "Password Expired" %}{% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-clock fa-5x text-warning mb-3"></i>
                        <h5>{% if LANGUAGE_CODE == 'ar' %}انتهت صلاحية كلمة المرور الخاصة بك{% else %}{% trans "Your password has expired" %}{% endif %}</h5>
                    </div>
                    
                    <div class="alert alert-info">
                        <p>
                            {% if LANGUAGE_CODE == 'ar' %}
                            لقد انتهت صلاحية كلمة المرور الخاصة بك وفقًا لسياسة الأمان. يجب عليك تغيير كلمة المرور الخاصة بك للمتابعة.
                            {% else %}
                            {% trans "Your password has expired according to the security policy. You must change your password to continue." %}
                            {% endif %}
                        </p>
                        
                        <p class="mb-0">
                            {% if LANGUAGE_CODE == 'ar' %}
                            سيتم توجيهك إلى صفحة تغيير كلمة المرور. يرجى اختيار كلمة مرور قوية تلبي متطلبات السياسة.
                            {% else %}
                            {% trans "You will be redirected to the password change page. Please choose a strong password that meets the policy requirements." %}
                            {% endif %}
                        </p>
                    </div>
                    
                    <div class="d-grid gap-2 mt-4">
                        <a href="{% url 'password_change' %}" class="btn btn-primary">
                            <i class="fas fa-key me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}تغيير كلمة المرور{% else %}{% trans "Change Password" %}{% endif %}
                        </a>
                        <a href="{% url 'logout' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}تسجيل الخروج{% else %}{% trans "Logout" %}{% endif %}
                        </a>
                    </div>
                </div>
                <div class="card-footer">
                    <h6>{% if LANGUAGE_CODE == 'ar' %}متطلبات كلمة المرور:{% else %}{% trans "Password Requirements:" %}{% endif %}</h6>
                    <ul class="mb-0 small">
                        {% if security_settings.password_min_length > 0 %}
                        <li>{% if LANGUAGE_CODE == 'ar' %}على الأقل {{ security_settings.password_min_length }} أحرف{% else %}{% trans "At least" %} {{ security_settings.password_min_length }} {% trans "characters" %}{% endif %}</li>
                        {% endif %}
                        
                        {% if security_settings.password_require_uppercase %}
                        <li>{% if LANGUAGE_CODE == 'ar' %}على الأقل حرف كبير واحد (A-Z){% else %}{% trans "At least one uppercase letter (A-Z)" %}{% endif %}</li>
                        {% endif %}
                        
                        {% if security_settings.password_require_lowercase %}
                        <li>{% if LANGUAGE_CODE == 'ar' %}على الأقل حرف صغير واحد (a-z){% else %}{% trans "At least one lowercase letter (a-z)" %}{% endif %}</li>
                        {% endif %}
                        
                        {% if security_settings.password_require_numbers %}
                        <li>{% if LANGUAGE_CODE == 'ar' %}على الأقل رقم واحد (0-9){% else %}{% trans "At least one number (0-9)" %}{% endif %}</li>
                        {% endif %}
                        
                        {% if security_settings.password_require_special_chars %}
                        <li>{% if LANGUAGE_CODE == 'ar' %}على الأقل رمز خاص واحد (مثل !@#$%^&*){% else %}{% trans "At least one special character (like !@#$%^&*)" %}{% endif %}</li>
                        {% endif %}
                        
                        {% if security_settings.password_history_count > 0 %}
                        <li>{% if LANGUAGE_CODE == 'ar' %}يجب أن تكون مختلفة عن آخر {{ security_settings.password_history_count }} كلمات مرور{% else %}{% trans "Must be different from your last" %} {{ security_settings.password_history_count }} {% trans "passwords" %}{% endif %}</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto-redirect to password change page after 5 seconds
    setTimeout(function() {
        window.location.href = "{% url 'password_change' %}";
    }, 5000);
</script>
{% endblock %}
