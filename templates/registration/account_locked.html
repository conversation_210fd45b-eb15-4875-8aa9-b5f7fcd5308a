{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% if LANGUAGE_CODE == 'ar' %}الحساب مقفل{% else %}{% trans "Account Locked" %}{% endif %}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-lock me-2"></i>
                        {% if LANGUAGE_CODE == 'ar' %}الحساب مقفل{% else %}{% trans "Account Locked" %}{% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-user-lock fa-5x text-danger mb-3"></i>
                        <h5>{% if LANGUAGE_CODE == 'ar' %}تم قفل حسابك مؤقتًا{% else %}{% trans "Your account has been temporarily locked" %}{% endif %}</h5>
                    </div>
                    
                    <div class="alert alert-warning">
                        <p>
                            {% if LANGUAGE_CODE == 'ar' %}
                            تم قفل حسابك بسبب وجود عدد كبير من محاولات تسجيل الدخول الفاشلة. يرجى الانتظار {{ lockout_minutes }} دقيقة قبل محاولة تسجيل الدخول مرة أخرى.
                            {% else %}
                            {% trans "Your account has been locked due to too many failed login attempts. Please wait" %} {{ lockout_minutes }} {% trans "minutes before trying to log in again." %}
                            {% endif %}
                        </p>
                        
                        <p class="mb-0">
                            {% if LANGUAGE_CODE == 'ar' %}
                            إذا كنت تعتقد أن هذا خطأ أو نسيت كلمة المرور الخاصة بك، يمكنك استخدام خيار "نسيت كلمة المرور" أدناه.
                            {% else %}
                            {% trans "If you believe this is an error or you've forgotten your password, you can use the 'Forgot Password' option below." %}
                            {% endif %}
                        </p>
                    </div>
                    
                    <div class="d-grid gap-2 mt-4">
                        <a href="{% url 'password_reset' %}" class="btn btn-outline-primary">
                            <i class="fas fa-key me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}نسيت كلمة المرور{% else %}{% trans "Forgot Password" %}{% endif %}
                        </a>
                        <a href="{% url 'login' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            {% if LANGUAGE_CODE == 'ar' %}العودة إلى صفحة تسجيل الدخول{% else %}{% trans "Back to Login Page" %}{% endif %}
                        </a>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        {% if LANGUAGE_CODE == 'ar' %}
                        إذا كنت بحاجة إلى مساعدة، يرجى الاتصال بمسؤول النظام.
                        {% else %}
                        {% trans "If you need assistance, please contact your system administrator." %}
                        {% endif %}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
