// Initialize components when the document is ready
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap4',
        width: '100%'
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
    
    // Confirm delete actions
    $('.confirm-delete').on('click', function(e) {
        if (!confirm($(this).data('confirm-message') || 'Are you sure you want to delete this item?')) {
            e.preventDefault();
        }
    });
    
    // Dynamic form fields
    $('.add-form-row').on('click', function() {
        var formRow = $(this).closest('.form-row');
        var newRow = formRow.clone(true);
        
        // Clear values in the new row
        newRow.find('input, select, textarea').val('');
        newRow.find('input[type=checkbox], input[type=radio]').prop('checked', false);
        
        // Insert the new row after the current one
        formRow.after(newRow);
        
        // Reinitialize Select2 in the new row
        newRow.find('.select2').select2({
            theme: 'bootstrap4',
            width: '100%'
        });
        
        return false;
    });
    
    $('.remove-form-row').on('click', function() {
        var formRow = $(this).closest('.form-row');
        var formRows = $('.form-row').length;
        
        // Don't remove if it's the only row
        if (formRows > 1) {
            formRow.remove();
        } else {
            // Clear values instead of removing
            formRow.find('input, select, textarea').val('');
            formRow.find('input[type=checkbox], input[type=radio]').prop('checked', false);
            
            // Reinitialize Select2
            formRow.find('.select2').select2({
                theme: 'bootstrap4',
                width: '100%'
            });
        }
        
        return false;
    });
});
