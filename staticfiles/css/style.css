/* Main Styles */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --info-color: #3498db;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --body-bg: #f8f9fa;
    --card-bg: #ffffff;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --card-hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    --border-radius: 0.5rem;
    --transition-speed: 0.3s;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: var(--body-bg);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer {
    margin-top: auto;
    padding: 1.5rem 0;
    background-color: var(--light-color);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Dashboard */
.card {
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    box-shadow: var(--card-shadow);
    border-radius: var(--border-radius);
    border: none;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-hover-shadow);
}

.card-header {
    border-bottom: none;
    padding: 1rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    border-top: none;
    background-color: transparent;
}

/* Module Cards */
.module-card-inner {
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: none;
    transition: all var(--transition-speed);
}

.module-card-inner::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 0 0 0 100%;
    z-index: -1;
    transition: all var(--transition-speed);
}

.module-card-inner:hover::before {
    width: 150px;
    height: 150px;
}

.module-card-inner::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--primary-color);
    transition: all var(--transition-speed);
    opacity: 0.7;
}

.module-card-inner:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.module-icon-wrapper {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    transition: all var(--transition-speed);
}

.module-icon {
    font-size: 2rem;
    transition: all var(--transition-speed);
}

.module-card-inner:hover .module-icon {
    transform: scale(1.2);
}

.module-btn {
    border-radius: 50px;
    padding: 0.5rem 1.5rem;
    transition: all var(--transition-speed);
}

.module-btn:hover {
    transform: scale(1.05);
}

/* Module Colors */
.module-hr .module-icon-wrapper {
    background-color: rgba(41, 128, 185, 0.1);
}
.module-hr .module-icon {
    color: #2980b9;
}
.module-hr .module-btn {
    background-color: #2980b9;
    border-color: #2980b9;
}
.module-hr.module-card-inner::after {
    background-color: #2980b9;
}

.module-inventory .module-icon-wrapper {
    background-color: rgba(46, 204, 113, 0.1);
}
.module-inventory .module-icon {
    color: #2ecc71;
}
.module-inventory .module-btn {
    background-color: #2ecc71;
    border-color: #2ecc71;
}
.module-inventory.module-card-inner::after {
    background-color: #2ecc71;
}

.module-procurement .module-icon-wrapper {
    background-color: rgba(155, 89, 182, 0.1);
}
.module-procurement .module-icon {
    color: #9b59b6;
}
.module-procurement .module-btn {
    background-color: #9b59b6;
    border-color: #9b59b6;
}
.module-procurement.module-card-inner::after {
    background-color: #9b59b6;
}

.module-sales .module-icon-wrapper {
    background-color: rgba(230, 126, 34, 0.1);
}
.module-sales .module-icon {
    color: #e67e22;
}
.module-sales .module-btn {
    background-color: #e67e22;
    border-color: #e67e22;
}
.module-sales.module-card-inner::after {
    background-color: #e67e22;
}

.module-pos .module-icon-wrapper {
    background-color: rgba(231, 76, 60, 0.1);
}
.module-pos .module-icon {
    color: #e74c3c;
}
.module-pos .module-btn {
    background-color: #e74c3c;
    border-color: #e74c3c;
}
.module-pos.module-card-inner::after {
    background-color: #e74c3c;
}

.module-hotel .module-icon-wrapper {
    background-color: rgba(52, 152, 219, 0.1);
}
.module-hotel .module-icon {
    color: #3498db;
}
.module-hotel .module-btn {
    background-color: #3498db;
    border-color: #3498db;
}
.module-hotel.module-card-inner::after {
    background-color: #3498db;
}

.module-restaurant .module-icon-wrapper {
    background-color: rgba(241, 196, 15, 0.1);
}
.module-restaurant .module-icon {
    color: #f1c40f;
}
.module-restaurant .module-btn {
    background-color: #f1c40f;
    border-color: #f1c40f;
}
.module-restaurant.module-card-inner::after {
    background-color: #f1c40f;
}

.module-accounting .module-icon-wrapper {
    background-color: rgba(22, 160, 133, 0.1);
}
.module-accounting .module-icon {
    color: #16a085;
}
.module-accounting .module-btn {
    background-color: #16a085;
    border-color: #16a085;
}
.module-accounting.module-card-inner::after {
    background-color: #16a085;
}

.module-finance .module-icon-wrapper {
    background-color: rgba(39, 174, 96, 0.1);
}
.module-finance .module-icon {
    color: #27ae60;
}
.module-finance .module-btn {
    background-color: #27ae60;
    border-color: #27ae60;
}
.module-finance.module-card-inner::after {
    background-color: #27ae60;
}

.module-manufacturing .module-icon-wrapper {
    background-color: rgba(44, 62, 80, 0.1);
}
.module-manufacturing .module-icon {
    color: #2c3e50;
}
.module-manufacturing .module-btn {
    background-color: #2c3e50;
    border-color: #2c3e50;
}
.module-manufacturing.module-card-inner::after {
    background-color: #2c3e50;
}

.module-project .module-icon-wrapper {
    background-color: rgba(142, 68, 173, 0.1);
}
.module-project .module-icon {
    color: #8e44ad;
}
.module-project .module-btn {
    background-color: #8e44ad;
    border-color: #8e44ad;
}
.module-project.module-card-inner::after {
    background-color: #8e44ad;
}

.module-fleet .module-icon-wrapper {
    background-color: rgba(52, 73, 94, 0.1);
}
.module-fleet .module-icon {
    color: #34495e;
}
.module-fleet .module-btn {
    background-color: #34495e;
    border-color: #34495e;
}
.module-fleet.module-card-inner::after {
    background-color: #34495e;
}

.module-document .module-icon-wrapper {
    background-color: rgba(127, 140, 141, 0.1);
}
.module-document .module-icon {
    color: #7f8c8d;
}
.module-document .module-btn {
    background-color: #7f8c8d;
    border-color: #7f8c8d;
}
.module-document.module-card-inner::after {
    background-color: #7f8c8d;
}

.module-crm .module-icon-wrapper {
    background-color: rgba(211, 84, 0, 0.1);
}
.module-crm .module-icon {
    color: #d35400;
}
.module-crm .module-btn {
    background-color: #d35400;
    border-color: #d35400;
}
.module-crm.module-card-inner::after {
    background-color: #d35400;
}

.module-frontdesk .module-icon-wrapper {
    background-color: rgba(41, 128, 185, 0.1);
}
.module-frontdesk .module-icon {
    color: #2980b9;
}
.module-frontdesk .module-btn {
    background-color: #2980b9;
    border-color: #2980b9;
}
.module-frontdesk.module-card-inner::after {
    background-color: #2980b9;
}

.module-ecommerce .module-icon-wrapper {
    background-color: rgba(230, 126, 34, 0.1);
}
.module-ecommerce .module-icon {
    color: #e67e22;
}
.module-ecommerce .module-btn {
    background-color: #e67e22;
    border-color: #e67e22;
}
.module-ecommerce.module-card-inner::after {
    background-color: #e67e22;
}

.module-settings .module-icon-wrapper {
    background-color: rgba(52, 73, 94, 0.1);
}
.module-settings .module-icon {
    color: #34495e;
}
.module-settings .module-btn {
    background-color: #34495e;
    border-color: #34495e;
}
.module-settings.module-card-inner::after {
    background-color: #34495e;
}

/* Navigation */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.75rem 1rem;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1rem;
    transition: color var(--transition-speed);
}

.navbar-dark .navbar-nav .nav-link:hover {
    color: #ffffff;
}

/* Language Selector */
.language-form {
    margin: 0;
    padding: 0;
}

.language-form button.dropdown-item {
    text-align: left;
    background: none;
    border: none;
    width: 100%;
    padding: 0.5rem 1rem;
}

.language-form button.dropdown-item.active {
    background-color: var(--primary-color);
    color: white;
}

.language-form button.dropdown-item:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.05);
}

/* Forms */
.form-control, .form-select {
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.input-group-text {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    padding: 0.75rem 1rem;
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple {
    border-radius: var(--border-radius);
    border: 1px solid #ced4da;
    min-height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 38px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 38px;
}

/* Module Dashboard */
.module-dashboard .breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.module-dashboard .module-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.module-dashboard .module-description {
    font-size: 1rem;
    margin-bottom: 2rem;
}

/* Module Header */
.module-header {
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.module-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 0 0 0 200px;
    z-index: 0;
}

.module-icon-large {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    color: var(--primary-color);
    position: relative;
    z-index: 1;
}

html[lang="ar"] .module-icon-large {
    margin-left: 1rem;
    margin-right: 0;
}

/* Log Viewer */
.log-container {
    max-height: 500px;
    overflow-y: auto;
}

.log-content {
    white-space: pre-wrap;
}

.module-sidebar-menu .list-group-item {
    border: none;
    border-radius: 0;
    padding: 0.75rem 1.5rem;
    transition: all var(--transition-speed);
}

.module-sidebar-menu .list-group-item:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--primary-color);
}

/* Module Alerts */
.module-alert {
    border-left-width: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.module-alert .alert-heading {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.module-alert p {
    margin-bottom: 0;
}

.module-alert .btn-close {
    opacity: 0.5;
    transition: opacity var(--transition-speed);
}

.module-alert .btn-close:hover {
    opacity: 1;
}

/* RTL Support for Arabic */
html[lang="ar"] {
    direction: rtl;
    text-align: right;
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Margin adjustments for RTL */
html[lang="ar"] .me-1,
html[lang="ar"] .me-2,
html[lang="ar"] .me-3,
html[lang="ar"] .me-4 {
    margin-right: 0 !important;
}

html[lang="ar"] .me-1 {
    margin-left: 0.25rem !important;
}

html[lang="ar"] .me-2 {
    margin-left: 0.5rem !important;
}

html[lang="ar"] .me-3 {
    margin-left: 1rem !important;
}

html[lang="ar"] .me-4 {
    margin-left: 1.5rem !important;
}

html[lang="ar"] .ms-1 {
    margin-right: 0.25rem !important;
    margin-left: 0 !important;
}

html[lang="ar"] .ms-2 {
    margin-right: 0.5rem !important;
    margin-left: 0 !important;
}

html[lang="ar"] .ms-3 {
    margin-right: 1rem !important;
    margin-left: 0 !important;
}

html[lang="ar"] .ms-4 {
    margin-right: 1.5rem !important;
    margin-left: 0 !important;
}

html[lang="ar"] .ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

html[lang="ar"] .me-auto {
    margin-left: auto !important;
    margin-right: 0 !important;
}

/* Dropdown menu adjustments */
html[lang="ar"] .dropdown-menu-end {
    right: auto;
    left: 0;
}

html[lang="ar"] .dropdown-menu {
    text-align: right;
}

/* Input group adjustments */
html[lang="ar"] .input-group > .input-group-text:first-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

html[lang="ar"] .input-group > .form-control:last-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

/* Navbar adjustments */
html[lang="ar"] .navbar-nav {
    padding-right: 0;
}

html[lang="ar"] .navbar-nav .nav-link {
    text-align: right;
}

/* Card and module adjustments */
html[lang="ar"] .card-title,
html[lang="ar"] .card-subtitle,
html[lang="ar"] .module-title,
html[lang="ar"] .module-description {
    text-align: right;
}

html[lang="ar"] .module-card-inner::before {
    left: 0;
    right: auto;
    border-radius: 0 0 100% 0;
}

html[lang="ar"] .module-card-inner::after {
    right: 0;
    left: auto;
}

/* Table adjustments */
html[lang="ar"] .table th,
html[lang="ar"] .table td {
    text-align: right;
}

/* Alert adjustments */
html[lang="ar"] .alert {
    text-align: right;
}

html[lang="ar"] .alert .btn-close {
    margin-right: auto;
    margin-left: 0;
}

html[lang="ar"] .module-alert {
    border-right-width: 5px;
    border-left-width: 1px;
}

/* List group adjustments */
html[lang="ar"] .list-group {
    padding-right: 0;
}

html[lang="ar"] .list-group-item {
    text-align: right;
}

/* Form adjustments */
html[lang="ar"] .form-label {
    text-align: right;
}

html[lang="ar"] .form-check {
    padding-right: 1.5em;
    padding-left: 0;
}

html[lang="ar"] .form-check .form-check-input {
    float: right;
    margin-right: -1.5em;
    margin-left: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }

    .module-card {
        width: 50%;
    }
}

@media (max-width: 576px) {
    .module-card {
        width: 100%;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .module-dashboard .module-title {
        font-size: 1.5rem;
    }
}
