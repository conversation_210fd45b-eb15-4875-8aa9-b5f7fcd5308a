"""
Standalone script to create depreciation records for assets in the accounting module.
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

# Now you can import Django models
from users.models import User
from companies.models import Company, Branch
from modules.accounting.models import FiscalYear, AccountType, AccountChart, Account
from modules.accounting.models import CashRegister, Bank, BankAccount
from modules.accounting.models import Journal, CheckBook, PaymentMethod
from modules.accounting.models import JournalEntry, JournalEntryLine
from modules.accounting.models import Voucher, Check
from modules.accounting.models import AssetCategory, Asset, DepreciationMethod, Depreciation
from django.utils import timezone
import datetime
import decimal

def main():
    """Main function to run the script."""
    print("Starting depreciation creation script...")
    
    # Get existing data
    company = Company.objects.first()
    branch = Branch.objects.filter(company=company).first()
    fiscal_year = FiscalYear.objects.filter(company=company).first()
    
    print(f"Company: {company}")
    print(f"Branch: {branch}")
    print(f"Fiscal Year: {fiscal_year}")
    
    # Get account types
    asset_type = AccountType.objects.get(code="ASSET", company=company)
    expense_type = AccountType.objects.get(code="EXPENSE", company=company)
    
    # Get chart of accounts
    chart = AccountChart.objects.get(code="STANDARD", company=company)
    print(f"Using existing chart of accounts: {chart}")
    
    # Get fixed assets account
    fixed_assets = Account.objects.get(chart=chart, code="1200")
    print(f"Using existing fixed assets account: {fixed_assets}")
    
    # Get existing accounts
    try:
        office_equipment = Account.objects.get(chart=chart, code="1201001")
        print(f"Using existing office equipment account: {office_equipment}")
    except Account.DoesNotExist:
        print("Office equipment account not found")
        office_equipment = Account.objects.create(
            name="Office Equipment",
            code="1201001",
            chart=chart,
            account_type=asset_type,
            parent=fixed_assets,
            company=company,
            is_active=True,
            created_by=User.objects.first()
        )
        print(f"Created office equipment account: {office_equipment}")
    
    try:
        depreciation_expense = Account.objects.get(chart=chart, code="5100001")
        print(f"Using existing depreciation expense account: {depreciation_expense}")
    except Account.DoesNotExist:
        print("Depreciation expense account not found")
        depreciation_expense = Account.objects.create(
            name="Depreciation Expense",
            code="5100001",
            chart=chart,
            account_type=expense_type,
            company=company,
            is_active=True,
            created_by=User.objects.first()
        )
        print(f"Created depreciation expense account: {depreciation_expense}")
    
    try:
        accumulated_depreciation = Account.objects.get(chart=chart, code="1202001")
        print(f"Using existing accumulated depreciation account: {accumulated_depreciation}")
    except Account.DoesNotExist:
        print("Accumulated depreciation account not found")
        accumulated_depreciation = Account.objects.create(
            name="Accumulated Depreciation",
            code="1202001",
            chart=chart,
            account_type=asset_type,
            company=company,
            is_active=True,
            created_by=User.objects.first()
        )
        print(f"Created accumulated depreciation account: {accumulated_depreciation}")
    
    # Create depreciation method
    try:
        straight_line = DepreciationMethod.objects.get(code="SL", company=company)
        print(f"Using existing straight line depreciation method: {straight_line}")
    except DepreciationMethod.DoesNotExist:
        straight_line = DepreciationMethod.objects.create(
            name="Straight Line",
            code="SL",
            method_type="straight_line",
            company=company,
            factor=1.0,
            is_active=True,
            notes="Straight line depreciation method",
            created_by=User.objects.first()
        )
        print(f"Created straight line depreciation method: {straight_line}")
    
    # Create asset category
    try:
        asset_category = AssetCategory.objects.get(code="OE", company=company)
        print(f"Using existing asset category: {asset_category}")
    except AssetCategory.DoesNotExist:
        asset_category = AssetCategory.objects.create(
            name="Office Equipment",
            code="OE",
            company=company,
            asset_account=office_equipment,
            depreciation_account=depreciation_expense,
            accumulated_depreciation_account=accumulated_depreciation,
            depreciation_rate=20.0,  # 20% per year
            useful_life_years=5,
            is_active=True,
            notes="Office equipment category",
            created_by=User.objects.first()
        )
        print(f"Created asset category: {asset_category}")
    
    # Create a new asset
    try:
        asset = Asset.objects.get(code="COMP001", company=company)
        print(f"Using existing asset: {asset}")
    except Asset.DoesNotExist:
        print("Asset not found, creating new asset...")
        asset = Asset.objects.create(
            name="Office Computer",
            code="COMP001",
            company=company,
            branch=branch,
            category=asset_category,
            depreciation_method=straight_line,
            purchase_date=timezone.now().date(),
            in_service_date=timezone.now().date(),
            purchase_value=decimal.Decimal('1500.00'),
            salvage_value=decimal.Decimal('300.00'),
            useful_life_years=5,
            depreciation_frequency="monthly",
            asset_account=office_equipment,
            depreciation_account=depreciation_expense,
            accumulated_depreciation_account=accumulated_depreciation,
            state="active",
            is_active=True,
            description="Dell XPS 15 laptop for accounting department",
            location="Accounting Office",
            serial_number="XPS15-2023-001",
            model="XPS 15",
            manufacturer="Dell",
            warranty_expiry=timezone.now().date() + datetime.timedelta(days=365),
            created_by=User.objects.first()
        )
        print(f"Created new asset: {asset}")
    
    # Calculate depreciation for the asset
    calculate_depreciation(asset)
    
    print("Script completed successfully.")

def calculate_depreciation(asset):
    """Calculate depreciation for an asset."""
    print(f"Calculating depreciation for asset: {asset}")
    
    # Check if depreciation already exists
    existing_depreciation = Depreciation.objects.filter(asset=asset).count()
    if existing_depreciation > 0:
        print(f"Depreciation already exists for asset {asset}. Skipping...")
        return
    
    # Calculate depreciation amount
    depreciable_value = asset.purchase_value - asset.salvage_value
    monthly_depreciation = depreciable_value / (asset.useful_life_years * 12)
    
    # Create depreciation records for the next 12 months
    start_date = asset.in_service_date
    for i in range(12):
        depreciation_date = start_date + datetime.timedelta(days=30 * (i + 1))
        
        # Create depreciation record
        depreciation = Depreciation.objects.create(
            asset=asset,
            date=depreciation_date,
            amount=monthly_depreciation,
            accumulated_amount=monthly_depreciation * (i + 1),
            remaining_value=asset.purchase_value - (monthly_depreciation * (i + 1)),
            period=f"{depreciation_date.year}-{depreciation_date.month:02d}",
            notes=f"Monthly depreciation for {asset.name}",
            created_by=User.objects.first()
        )
        print(f"Created depreciation record for {depreciation_date}: {depreciation}")

if __name__ == "__main__":
    main()
