from modules.models import Module, ModuleLicense, CompanyModule
from companies.models import Company
from users.models import User
from django.utils import timezone
import datetime

# Get user and company
user = User.objects.get(username='admin2')
company = user.company
print(f'User: {user.username}, Company: {company.name}')

# Create licenses and install modules
for module in Module.objects.all():
    if not module.is_core:
        # Create license
        try:
            license = ModuleLicense.objects.create(
                company=company,
                module=module,
                license_type='enterprise',
                start_date=timezone.now().date(),
                end_date=timezone.now().date() + datetime.timedelta(days=365),
                max_users=10,
                is_active=True
            )
            print(f'Created license for {module.name}')
        except Exception as e:
            print(f'Error creating license for {module.name}: {e}')
            continue
    
    # Install module
    try:
        company_module = CompanyModule.objects.create(
            company=company,
            module=module,
            is_active=True,
            installed_version=module.version
        )
        print(f'Installed module {module.name}')
    except Exception as e:
        print(f'Error installing module {module.name}: {e}')
