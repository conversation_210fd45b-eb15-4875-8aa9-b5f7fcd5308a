import random
import string
import ipaddress
from datetime import timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password
from faker import Faker

from companies.models import Company
from core.security_models import (
    SecuritySetting, UserMFADevice, SecurityLog,
    PasswordHistory, UserSecurityInfo
)

User = get_user_model()
fake = Faker(['ar_SA', 'en_US'])

class Command(BaseCommand):
    help = 'Generate test data for security module'

    def add_arguments(self, parser):
        parser.add_argument(
            '--companies',
            type=int,
            default=20,
            help='Number of companies to create'
        )
        parser.add_argument(
            '--users_per_company',
            type=int,
            default=5,
            help='Number of users per company'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before generating new data'
        )

    def handle(self, *args, **options):
        num_companies = options['companies']
        users_per_company = options['users_per_company']
        clear_data = options['clear']

        if clear_data:
            self.clear_existing_data()

        self.stdout.write(self.style.SUCCESS(f'Generating data for {num_companies} companies with {users_per_company} users each'))

        # Create companies
        companies = self.create_companies(num_companies)

        # Create users for each company
        all_users = []
        for company in companies:
            company_users = self.create_users(company, users_per_company)
            all_users.extend(company_users)

            # Create security settings for company
            self.create_security_settings(company)

            # Create security logs for company
            self.create_security_logs(company, company_users)

        # Create MFA devices for some users
        self.create_mfa_devices(all_users)

        # Create password history for users
        self.create_password_history(all_users)

        # Create user security info
        self.create_user_security_info(all_users)

        # Verify data was created
        self.stdout.write(f"Verification - Companies: {Company.objects.count()}")
        self.stdout.write(f"Verification - Users: {User.objects.count()}")
        self.stdout.write(f"Verification - Security Logs: {SecurityLog.objects.count()}")

        self.stdout.write(self.style.SUCCESS('Successfully generated security test data'))

    def clear_existing_data(self):
        """Clear existing data"""
        self.stdout.write('Clearing existing data...')

        # Delete security-related data
        SecurityLog.objects.all().delete()
        UserMFADevice.objects.all().delete()
        PasswordHistory.objects.all().delete()
        UserSecurityInfo.objects.all().delete()
        SecuritySetting.objects.all().delete()

        # Delete users and companies
        User.objects.filter(is_superuser=False).delete()
        Company.objects.all().delete()

        self.stdout.write(self.style.SUCCESS('Existing data cleared'))

    def create_companies(self, num_companies):
        """Create test companies"""
        companies = []

        for i in range(1, num_companies + 1):
            # Alternate between Arabic and English names
            if i % 2 == 0:
                name = fake['ar_SA'].company()
                address = fake['ar_SA'].address()
                phone = fake['ar_SA'].phone_number()
            else:
                name = fake['en_US'].company()
                address = fake['en_US'].address()
                phone = fake['en_US'].phone_number()

            # Generate a unique code
            code = f"C{i:03d}"

            company = Company.objects.create(
                name=f"{name} {i}",
                code=code,
                address=address,
                phone=phone,
                email=fake.company_email(),
                website=fake.domain_name(),
                tax_number=fake.numerify(text='#########'),
                active=random.choice([True, True, True, False]),  # 75% active
            )
            companies.append(company)

            # Create branches for each company
            num_branches = random.randint(1, 3)
            for j in range(1, num_branches + 1):
                branch_code = f"B{j:03d}"
                branch_name = f"Branch {j}"

                if i % 2 == 0:  # Arabic
                    branch_name = f"فرع {j}"

                company.branches.create(
                    name=branch_name,
                    code=branch_code,
                    address=fake.address(),
                    phone=fake.phone_number(),
                    email=fake.company_email(),
                    manager=fake.name(),
                    active=True
                )

            self.stdout.write(f'Created company: {company.name} with {num_branches} branches')

        return companies

    def create_users(self, company, num_users):
        """Create test users for a company"""
        users = []

        # Get branches for this company
        branches = list(company.branches.all())

        for i in range(1, num_users + 1):
            # Alternate between Arabic and English names
            if i % 2 == 0:
                first_name = fake['ar_SA'].first_name()
                last_name = fake['ar_SA'].last_name()
                address = fake['ar_SA'].address()
                phone = fake['ar_SA'].phone_number()
            else:
                first_name = fake['en_US'].first_name()
                last_name = fake['en_US'].last_name()
                address = fake['en_US'].address()
                phone = fake['en_US'].phone_number()

            # Create a unique username
            username = f"{first_name.lower()}.{last_name.lower()}{i}"
            email = f"{username}@{company.name.lower().replace(' ', '')}.com"

            # Assign to a random branch if available
            branch = random.choice(branches) if branches else None

            user = User.objects.create(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                is_active=random.choice([True, True, True, False]),  # 75% active
                company=company,
                branch=branch,
                phone=phone,
                address=address,
                password=make_password('Password123!'),  # Default password
            )
            users.append(user)

            branch_info = f" (Branch: {branch.name})" if branch else ""
            self.stdout.write(f'Created user: {user.username} for company: {company.name}{branch_info}')

        return users

    def create_security_settings(self, company):
        """Create security settings for a company"""
        # Choose random security policy
        policy = random.choice(['low', 'medium', 'high', 'custom'])

        # Base settings
        settings = {
            'company': company,
            'password_policy': policy,
            'mfa_type': random.choice(['none', 'optional', 'required']),
            'session_timeout_value': random.randint(15, 120),
            'session_timeout_unit': random.choice(['minutes', 'hours']),
            'max_login_attempts': random.randint(3, 10),
            'lockout_duration_minutes': random.randint(15, 60),
            'ip_restriction_enabled': random.choice([True, False]),
            'enable_audit_logging': random.choice([True, True, False]),  # 66% enabled
        }

        # If IP restrictions enabled, generate some random IPs
        if settings['ip_restriction_enabled']:
            num_ips = random.randint(1, 5)
            ips = []

            for _ in range(num_ips):
                # 50% chance of single IP, 50% chance of CIDR range
                if random.choice([True, False]):
                    # Single IP
                    ip = str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))
                    ips.append(ip)
                else:
                    # CIDR range
                    ip = str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))
                    prefix = random.randint(16, 30)
                    ips.append(f"{ip}/{prefix}")

            settings['allowed_ips'] = '\n'.join(ips)

        # If custom policy, set custom values
        if policy == 'custom':
            settings.update({
                'password_min_length': random.randint(6, 14),
                'password_require_uppercase': random.choice([True, False]),
                'password_require_lowercase': random.choice([True, False]),
                'password_require_numbers': random.choice([True, False]),
                'password_require_special_chars': random.choice([True, False]),
                'password_expiry_days': random.choice([0, 30, 60, 90, 180]),
                'password_history_count': random.randint(0, 10),
            })

        security_setting = SecuritySetting.objects.create(**settings)
        self.stdout.write(f'Created security settings for company: {company.name}')

        return security_setting

    def create_security_logs(self, company, users):
        """Create security logs for a company and its users"""
        # Log types with their relative frequencies
        log_types = {
            'login': 40,
            'logout': 30,
            'login_failed': 15,
            'password_change': 5,
            'password_reset': 3,
            'mfa_setup': 2,
            'mfa_verification': 3,
            'mfa_verification_failed': 1,
            'account_locked': 1,
            'account_unlocked': 1,
            'security_setting_change': 1,
            'permission_change': 1,
        }

        # Generate weighted log types
        weighted_log_types = []
        for log_type, weight in log_types.items():
            weighted_log_types.extend([log_type] * weight)

        # Generate random number of logs (10-50 per company)
        num_logs = random.randint(10, 50)

        for _ in range(num_logs):
            # Select random user (or None for anonymous)
            user = random.choice(users + [None])

            # Select random log type
            log_type = random.choice(weighted_log_types)

            # Generate random IP
            ip_address = str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))

            # Generate random user agent
            user_agent = fake.user_agent()

            # Generate random timestamp in the last 30 days
            created_at = timezone.now() - timedelta(
                days=random.randint(0, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )

            # Generate details based on log type
            details = None
            if log_type == 'login_failed':
                details = {'failed_attempts': random.randint(1, 10)}
            elif log_type == 'password_change':
                details = {'password_strength': random.choice(['weak', 'medium', 'strong'])}
            elif log_type == 'security_setting_change':
                details = {'changed_by': user.username if user else 'admin'}

            # Create log
            SecurityLog.objects.create(
                user=user,
                company=company,
                log_type=log_type,
                ip_address=ip_address,
                user_agent=user_agent,
                details=details,
                created_at=created_at
            )

        self.stdout.write(f'Created {num_logs} security logs for company: {company.name}')

    def create_mfa_devices(self, users):
        """Create MFA devices for some users"""
        # Create MFA devices for about 30% of users
        mfa_users = random.sample(users, k=int(len(users) * 0.3))

        for user in mfa_users:
            # Determine number of devices (1-3)
            num_devices = random.randint(1, 3)

            # Device type names to ensure uniqueness
            totp_names = ['Google Authenticator', 'Microsoft Authenticator', 'Authy', 'Work Phone TOTP', 'Personal Phone TOTP']
            sms_names = ['Work Phone SMS', 'Personal Phone SMS', 'Mobile SMS']
            email_names = ['Work Email', 'Personal Email', 'Backup Email']

            # Shuffle the names to get random ones
            random.shuffle(totp_names)
            random.shuffle(sms_names)
            random.shuffle(email_names)

            # Track created devices
            created_devices = 0

            # Try to create devices
            for i in range(num_devices):
                try:
                    # Device type
                    device_type = random.choice(['totp', 'sms', 'email'])

                    # Device name (ensure uniqueness)
                    if device_type == 'totp':
                        name = f"{totp_names[i % len(totp_names)]} - {user.username}"
                    elif device_type == 'sms':
                        name = f"{sms_names[i % len(sms_names)]} - {user.username}"
                    else:
                        name = f"{email_names[i % len(email_names)]} - {user.username}"

                    # Is primary (first device is primary)
                    is_primary = (i == 0)

                    # Generate random secret key
                    secret_key = ''.join(random.choices(string.ascii_uppercase + string.digits, k=16))

                    # Last used (some devices might never have been used)
                    if random.random() < 0.8:  # 80% chance of being used
                        last_used = timezone.now() - timedelta(
                            days=random.randint(0, 30),
                            hours=random.randint(0, 23)
                        )
                    else:
                        last_used = None

                    # Create device
                    UserMFADevice.objects.create(
                        user=user,
                        device_type=device_type,
                        name=name,
                        secret_key=secret_key,
                        is_active=random.choice([True, True, False]),  # 66% active
                        is_primary=is_primary,
                        last_used=last_used
                    )

                    created_devices += 1
                except Exception as e:
                    self.stdout.write(self.style.WARNING(f"Failed to create MFA device for user {user.username}: {str(e)}"))

            if created_devices > 0:
                self.stdout.write(f'Created {created_devices} MFA devices for user: {user.username}')

    def create_password_history(self, users):
        """Create password history for users"""
        for user in users:
            # Generate random number of password changes (0-10)
            num_changes = random.randint(0, 10)

            for i in range(num_changes):
                # Generate random password hash
                password = f"OldPassword{i}_{user.username}!"
                password_hash = make_password(password)

                # Generate random timestamp (older as i increases)
                created_at = timezone.now() - timedelta(
                    days=i * random.randint(10, 30),
                    hours=random.randint(0, 23)
                )

                # Create password history entry
                PasswordHistory.objects.create(
                    user=user,
                    password=password_hash,
                    created_at=created_at
                )

            if num_changes > 0:
                self.stdout.write(f'Created {num_changes} password history entries for user: {user.username}')

    def create_user_security_info(self, users):
        """Create user security info for all users"""
        for user in users:
            # Determine if account is locked (5% chance)
            is_locked = random.random() < 0.05

            # Failed login attempts
            if is_locked:
                failed_attempts = random.randint(3, 10)
                account_locked_until = timezone.now() + timedelta(minutes=random.randint(15, 60))
                last_failed_login = timezone.now() - timedelta(minutes=random.randint(1, 10))
            else:
                # 20% chance of some failed attempts but not locked
                if random.random() < 0.2:
                    failed_attempts = random.randint(1, 3)
                    last_failed_login = timezone.now() - timedelta(hours=random.randint(1, 24))
                else:
                    failed_attempts = 0
                    last_failed_login = None
                account_locked_until = None

            # Password last changed
            password_last_changed = timezone.now() - timedelta(days=random.randint(0, 180))

            # Password expiry
            if random.random() < 0.3:  # 30% chance of password expiry
                days_until_expiry = random.randint(-30, 60)  # Negative means already expired
                password_expires_at = timezone.now() + timedelta(days=days_until_expiry)
            else:
                password_expires_at = None

            # Last login IP
            last_login_ip = str(ipaddress.IPv4Address(random.randint(0, 2**32-1)))

            # Create user security info
            UserSecurityInfo.objects.create(
                user=user,
                password_last_changed=password_last_changed,
                failed_login_attempts=failed_attempts,
                last_failed_login=last_failed_login,
                account_locked_until=account_locked_until,
                last_login_ip=last_login_ip,
                password_expires_at=password_expires_at
            )

            self.stdout.write(f'Created security info for user: {user.username}')
