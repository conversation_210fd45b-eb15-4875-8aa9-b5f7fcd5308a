from django import forms
from django.utils.translation import gettext_lazy as _
from .models import SystemSetting, CompanySetting
from companies.models import Company

class SystemSettingForm(forms.ModelForm):
    """
    Form for creating/editing system settings
    """
    class Meta:
        model = SystemSetting
        fields = ['key', 'value', 'description', 'is_active']
        widgets = {
            'key': forms.TextInput(attrs={'class': 'form-control', 'dir': 'ltr'}),
            'value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

class CompanySettingForm(forms.ModelForm):
    """
    Form for creating/editing company settings
    """
    class Meta:
        model = CompanySetting
        fields = ['company', 'key', 'value', 'description', 'is_active']
        widgets = {
            'company': forms.Select(attrs={'class': 'form-control select2'}),
            'key': forms.TextInput(attrs={'class': 'form-control', 'dir': 'ltr'}),
            'value': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Limit company choices for non-superusers
        if user and not user.is_superuser and user.company:
            self.fields['company'].queryset = Company.objects.filter(pk=user.company.pk)
            self.fields['company'].initial = user.company
            self.fields['company'].widget.attrs['disabled'] = 'disabled'
            self.fields['company'].required = False
    
    def clean(self):
        cleaned_data = super().clean()
        user = getattr(self, 'user', None)
        
        # If company field is disabled, use the user's company
        if user and not user.is_superuser and user.company:
            cleaned_data['company'] = user.company
        
        return cleaned_data

class BackupForm(forms.Form):
    """
    Form for creating a backup
    """
    backup_name = forms.CharField(
        label=_('Backup Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control'})
    )
    include_media = forms.BooleanField(
        label=_('Include Media Files'),
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

class RestoreForm(forms.Form):
    """
    Form for restoring from a backup
    """
    backup_file = forms.FileField(
        label=_('Backup File'),
        widget=forms.FileInput(attrs={'class': 'form-control'})
    )
    confirm = forms.BooleanField(
        label=_('I understand this will overwrite current data'),
        required=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
