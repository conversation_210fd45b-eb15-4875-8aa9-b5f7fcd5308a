from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .security_models import SecuritySetting, UserMFADevice, SecurityLog, PasswordHistory, UserSecurityInfo

@admin.register(SecuritySetting)
class SecuritySettingAdmin(admin.ModelAdmin):
    """
    Admin for SecuritySetting model
    """
    list_display = ('company', 'password_policy', 'mfa_type', 'session_timeout_value', 'session_timeout_unit', 'updated_at')
    list_filter = ('password_policy', 'mfa_type', 'ip_restriction_enabled', 'enable_audit_logging')
    search_fields = ('company__name',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('company',)
        }),
        (_('Password Policy'), {
            'fields': (
                'password_policy', 'password_min_length', 'password_require_uppercase',
                'password_require_lowercase', 'password_require_numbers',
                'password_require_special_chars', 'password_expiry_days',
                'password_history_count'
            )
        }),
        (_('Multi-Factor Authentication'), {
            'fields': ('mfa_type',)
        }),
        (_('Session Security'), {
            'fields': (
                'session_timeout_value', 'session_timeout_unit',
                'max_login_attempts', 'lockout_duration_minutes'
            )
        }),
        (_('IP Restrictions'), {
            'fields': ('ip_restriction_enabled', 'allowed_ips')
        }),
        (_('Audit Logging'), {
            'fields': ('enable_audit_logging',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(UserMFADevice)
class UserMFADeviceAdmin(admin.ModelAdmin):
    """
    Admin for UserMFADevice model
    """
    list_display = ('user', 'device_type', 'name', 'is_active', 'is_primary', 'last_used', 'created_at')
    list_filter = ('device_type', 'is_active', 'is_primary')
    search_fields = ('user__username', 'user__email', 'name')
    readonly_fields = ('secret_key', 'last_used', 'created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('user', 'device_type', 'name')
        }),
        (_('Status'), {
            'fields': ('is_active', 'is_primary')
        }),
        (_('Security'), {
            'fields': ('secret_key',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('last_used', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(SecurityLog)
class SecurityLogAdmin(admin.ModelAdmin):
    """
    Admin for SecurityLog model
    """
    list_display = ('created_at', 'log_type', 'user', 'company', 'ip_address')
    list_filter = ('log_type', 'created_at')
    search_fields = ('user__username', 'user__email', 'company__name', 'ip_address')
    readonly_fields = ('user', 'company', 'log_type', 'ip_address', 'user_agent', 'details', 'created_at')
    fieldsets = (
        (None, {
            'fields': ('log_type', 'user', 'company')
        }),
        (_('Connection Information'), {
            'fields': ('ip_address', 'user_agent')
        }),
        (_('Details'), {
            'fields': ('details',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at',)
        }),
    )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(PasswordHistory)
class PasswordHistoryAdmin(admin.ModelAdmin):
    """
    Admin for PasswordHistory model
    """
    list_display = ('user', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('user', 'password', 'created_at')
    fieldsets = (
        (None, {
            'fields': ('user', 'password')
        }),
        (_('Timestamps'), {
            'fields': ('created_at',)
        }),
    )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(UserSecurityInfo)
class UserSecurityInfoAdmin(admin.ModelAdmin):
    """
    Admin for UserSecurityInfo model
    """
    list_display = ('user', 'failed_login_attempts', 'account_locked_until', 'password_last_changed', 'password_expires_at')
    list_filter = ('failed_login_attempts', 'account_locked_until', 'password_last_changed', 'password_expires_at')
    search_fields = ('user__username', 'user__email', 'last_login_ip')
    readonly_fields = ('user', 'password_last_changed', 'failed_login_attempts', 'last_failed_login',
                      'account_locked_until', 'last_login_ip', 'password_expires_at')
    fieldsets = (
        (None, {
            'fields': ('user',)
        }),
        (_('Password Information'), {
            'fields': ('password_last_changed', 'password_expires_at')
        }),
        (_('Login Security'), {
            'fields': ('failed_login_attempts', 'last_failed_login', 'account_locked_until', 'last_login_ip')
        }),
    )

    actions = ['unlock_accounts', 'reset_failed_attempts']

    def unlock_accounts(self, request, queryset):
        """
        Action to unlock selected accounts
        """
        updated = queryset.update(account_locked_until=None)

        # Log the action
        for security_info in queryset:
            SecurityLog.objects.create(
                user=security_info.user,
                company=security_info.user.company if hasattr(security_info.user, 'company') else None,
                log_type='account_unlocked',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={
                    'unlocked_by': request.user.username,
                    'unlocked_by_id': request.user.id
                }
            )

        self.message_user(request, _(f"{updated} accounts have been unlocked."))
    unlock_accounts.short_description = _("Unlock selected accounts")

    def reset_failed_attempts(self, request, queryset):
        """
        Action to reset failed login attempts for selected accounts
        """
        updated = queryset.update(failed_login_attempts=0)
        self.message_user(request, _(f"Failed login attempts have been reset for {updated} accounts."))
    reset_failed_attempts.short_description = _("Reset failed login attempts")
