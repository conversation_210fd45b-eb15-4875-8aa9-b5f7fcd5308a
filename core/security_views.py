from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.views.decorators.http import require_POST
from django.urls import reverse
from django.db.models import Q
import json
import csv
import datetime

from .security_models import SecuritySetting, UserMFADevice, SecurityLog, UserSecurityInfo, PasswordHistory
from .security_forms import SecuritySettingForm, UserMFADeviceForm, MFAVerificationForm
from companies.models import Company

User = get_user_model()

@login_required
@permission_required('core.view_securitysetting')
def security_dashboard(request):
    """
    Security dashboard view
    """
    # Get security settings for the user's company or system-wide
    security_settings = None
    if request.user.company:
        security_settings, created = SecuritySetting.objects.get_or_create(
            company=request.user.company,
            defaults={
                'password_policy': 'medium',
                'mfa_type': 'optional',
            }
        )
    else:
        # For superusers without a company, get or create system-wide settings
        if request.user.is_superuser:
            security_settings, created = SecuritySetting.objects.get_or_create(
                company=None,
                defaults={
                    'password_policy': 'high',
                    'mfa_type': 'required',
                }
            )

    # Get MFA devices for the user
    mfa_devices = UserMFADevice.objects.filter(user=request.user, is_active=True)

    # Get recent security logs
    if request.user.is_superuser:
        recent_logs = SecurityLog.objects.all().order_by('-created_at')[:10]
    else:
        recent_logs = SecurityLog.objects.filter(
            Q(user=request.user) | Q(company=request.user.company)
        ).order_by('-created_at')[:10]

    # Get login statistics
    login_stats = {
        'successful_logins': SecurityLog.objects.filter(log_type='login').count(),
        'failed_logins': SecurityLog.objects.filter(log_type='login_failed').count(),
        'locked_accounts': SecurityLog.objects.filter(log_type='account_locked').count(),
    }

    context = {
        'security_settings': security_settings,
        'mfa_devices': mfa_devices,
        'recent_logs': recent_logs,
        'login_stats': login_stats,
    }

    return render(request, 'modules/settings/security_dashboard.html', context)

@login_required
@permission_required('core.change_securitysetting')
def security_settings(request):
    """
    Edit security settings
    """
    # Get security settings for the user's company or system-wide
    if request.user.company and not request.user.is_superuser:
        security_settings, created = SecuritySetting.objects.get_or_create(
            company=request.user.company,
            defaults={
                'password_policy': 'medium',
                'mfa_type': 'optional',
            }
        )
    else:
        # For superusers, get or create system-wide settings
        security_settings, created = SecuritySetting.objects.get_or_create(
            company=None,
            defaults={
                'password_policy': 'high',
                'mfa_type': 'required',
            }
        )

    if request.method == 'POST':
        form = SecuritySettingForm(request.POST, instance=security_settings, user=request.user)
        if form.is_valid():
            form.save()

            # Log the change
            SecurityLog.objects.create(
                user=request.user,
                company=request.user.company,
                log_type='security_setting_change',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={
                    'changes': {k: v for k, v in form.changed_data.items()}
                }
            )

            messages.success(request, _('Security settings updated successfully.'))
            return redirect('core:security:dashboard')
    else:
        form = SecuritySettingForm(instance=security_settings, user=request.user)

    context = {
        'form': form,
        'security_settings': security_settings,
    }

    return render(request, 'modules/settings/security_settings_form.html', context)

@login_required
def mfa_devices(request):
    """
    List user's MFA devices
    """
    # Get MFA devices for the user
    mfa_devices = UserMFADevice.objects.filter(user=request.user)

    # Get security settings to check if MFA is required
    security_settings = None
    if request.user.company:
        try:
            security_settings = SecuritySetting.objects.get(company=request.user.company)
        except SecuritySetting.DoesNotExist:
            pass

    if not security_settings and request.user.is_superuser:
        try:
            security_settings = SecuritySetting.objects.get(company=None)
        except SecuritySetting.DoesNotExist:
            pass

    context = {
        'mfa_devices': mfa_devices,
        'security_settings': security_settings,
    }

    return render(request, 'modules/settings/mfa_devices.html', context)

@login_required
def mfa_device_create(request):
    """
    Create a new MFA device
    """
    if request.method == 'POST':
        form = UserMFADeviceForm(request.POST, user=request.user)
        if form.is_valid():
            device = form.save(commit=False)
            device.user = request.user
            device.generate_secret_key()
            device.save()

            # Log the change
            SecurityLog.objects.create(
                user=request.user,
                company=request.user.company,
                log_type='mfa_setup',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={
                    'device_type': device.device_type,
                    'device_name': device.name,
                }
            )

            messages.success(request, _('MFA device created successfully. Please verify it.'))
            return redirect('core:security:mfa_device_verify', device_id=device.id)
    else:
        form = UserMFADeviceForm(user=request.user)

    context = {
        'form': form,
    }

    return render(request, 'modules/settings/mfa_device_form.html', context)

@login_required
def mfa_device_verify(request, device_id):
    """
    Verify a new MFA device
    """
    device = get_object_or_404(UserMFADevice, id=device_id, user=request.user)

    if request.method == 'POST':
        form = MFAVerificationForm(request.POST, user=request.user, device=device)
        if form.is_valid():
            # Mark device as verified
            device.last_used = timezone.now()
            device.save()

            # Log the verification
            SecurityLog.objects.create(
                user=request.user,
                company=request.user.company,
                log_type='mfa_verification',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={
                    'device_type': device.device_type,
                    'device_name': device.name,
                }
            )

            messages.success(request, _('MFA device verified successfully.'))
            return redirect('core:security:mfa_devices')
        else:
            # Log the failed verification
            SecurityLog.objects.create(
                user=request.user,
                company=request.user.company,
                log_type='mfa_verification_failed',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={
                    'device_type': device.device_type,
                    'device_name': device.name,
                }
            )
    else:
        form = MFAVerificationForm(user=request.user, device=device)

    # Generate QR code for TOTP devices
    qr_code = None
    if device.device_type == 'totp':
        qr_code = device.get_qr_code()

    context = {
        'form': form,
        'device': device,
        'qr_code': qr_code,
    }

    return render(request, 'modules/settings/mfa_device_verify.html', context)

@login_required
def mfa_device_delete(request, device_id):
    """
    Delete an MFA device
    """
    device = get_object_or_404(UserMFADevice, id=device_id, user=request.user)

    if request.method == 'POST':
        # Log the deletion
        SecurityLog.objects.create(
            user=request.user,
            company=request.user.company,
            log_type='mfa_setup',
            ip_address=request.META.get('REMOTE_ADDR'),
            user_agent=request.META.get('HTTP_USER_AGENT'),
            details={
                'action': 'delete',
                'device_type': device.device_type,
                'device_name': device.name,
            }
        )

        device.delete()
        messages.success(request, _('MFA device deleted successfully.'))
        return redirect('core:security:mfa_devices')

    context = {
        'device': device,
    }

    return render(request, 'modules/settings/mfa_device_confirm_delete.html', context)

@login_required
@permission_required('core.view_securitylog')
def security_logs(request):
    """
    View security logs
    """
    # Get filter parameters
    log_type = request.GET.get('log_type')
    user_id = request.GET.get('user_id')
    company_id = request.GET.get('company_id')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # Base queryset
    logs = SecurityLog.objects.all()

    # Apply filters
    if log_type:
        logs = logs.filter(log_type=log_type)

    if user_id:
        logs = logs.filter(user_id=user_id)

    if company_id:
        logs = logs.filter(company_id=company_id)

    if date_from:
        try:
            date_from = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
            logs = logs.filter(created_at__date__gte=date_from)
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.datetime.strptime(date_to, '%Y-%m-%d').date()
            logs = logs.filter(created_at__date__lte=date_to)
        except ValueError:
            pass

    # For non-superusers, limit to their own logs and company logs
    if not request.user.is_superuser:
        logs = logs.filter(
            Q(user=request.user) | Q(company=request.user.company)
        )

    # Order by created_at descending
    logs = logs.order_by('-created_at')

    # Get users and companies for filters
    users = User.objects.all()
    companies = Company.objects.all()

    context = {
        'logs': logs,
        'users': users,
        'companies': companies,
        'log_types': dict(SecurityLog.LOG_TYPES),
        'filters': {
            'log_type': log_type,
            'user_id': user_id,
            'company_id': company_id,
            'date_from': date_from,
            'date_to': date_to,
        }
    }

    # Handle export
    if request.GET.get('export') == 'csv':
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="security_logs.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'ID', 'Date', 'Type', 'User', 'Company', 'IP Address', 'Details'
        ])

        for log in logs:
            writer.writerow([
                log.id,
                log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                log.get_log_type_display(),
                log.user.username if log.user else 'Anonymous',
                log.company.name if log.company else 'N/A',
                log.ip_address or 'N/A',
                json.dumps(log.details) if log.details else ''
            ])

        return response

    return render(request, 'modules/settings/security_logs.html', context)


def account_locked(request):
    """
    View for locked accounts
    """
    # Get the username from the session if available
    username = request.session.get('locked_username', '')
    lockout_minutes = 30  # Default

    # Try to get the user and their security info
    if username:
        try:
            user = User.objects.get(username=username)
            security_info = UserSecurityInfo.objects.get(user=user)

            # Calculate remaining lockout time
            if security_info.account_locked_until:
                remaining_time = security_info.account_locked_until - timezone.now()
                lockout_minutes = max(1, int(remaining_time.total_seconds() / 60))

                # Get lockout duration from security settings
                try:
                    if user.company:
                        security_settings = SecuritySetting.objects.get(company=user.company)
                    else:
                        security_settings = SecuritySetting.objects.get(company=None)

                    lockout_minutes = security_settings.lockout_duration_minutes
                except SecuritySetting.DoesNotExist:
                    pass
        except (User.DoesNotExist, UserSecurityInfo.DoesNotExist):
            pass

    context = {
        'lockout_minutes': lockout_minutes
    }

    return render(request, 'registration/account_locked.html', context)


@login_required
def password_expired(request):
    """
    View for expired passwords
    """
    # Get security settings
    security_settings = None

    if request.user.company:
        try:
            security_settings = SecuritySetting.objects.get(company=request.user.company)
        except SecuritySetting.DoesNotExist:
            pass

    if not security_settings and request.user.is_superuser:
        try:
            security_settings = SecuritySetting.objects.get(company=None)
        except SecuritySetting.DoesNotExist:
            pass

    # If no security settings found, create default ones
    if not security_settings:
        security_settings = SecuritySetting(
            password_policy='medium',
            password_min_length=8,
            password_require_uppercase=True,
            password_require_lowercase=True,
            password_require_numbers=True,
            password_require_special_chars=False,
            password_expiry_days=90,
            password_history_count=5
        )

    context = {
        'security_settings': security_settings
    }

    return render(request, 'registration/password_expired.html', context)


@login_required
@permission_required('core.view_usersecurityinfo')
def user_security_info_list(request):
    """
    List all user security info
    """
    # For superusers, show all users
    # For regular users, show only their company's users
    if request.user.is_superuser:
        users_info = UserSecurityInfo.objects.all().select_related('user')
    else:
        users_info = UserSecurityInfo.objects.filter(
            user__company=request.user.company
        ).select_related('user') if request.user.company else UserSecurityInfo.objects.none()

    context = {
        'users_info': users_info
    }

    return render(request, 'modules/settings/user_security_info_list.html', context)


@login_required
@permission_required('core.change_usersecurityinfo')
def user_security_info_edit(request, pk):
    """
    Edit user security info
    """
    security_info = get_object_or_404(UserSecurityInfo, pk=pk)

    # Check if user has permission to edit this security info
    if not request.user.is_superuser and request.user.company != getattr(security_info.user, 'company', None):
        messages.error(request, _('You do not have permission to edit this user\'s security information.'))
        return redirect('core:security:user_security_info_list')

    if request.method == 'POST':
        # Handle form submission
        action = request.POST.get('action')

        if action == 'unlock_account':
            # Unlock the account
            security_info.account_locked_until = None
            security_info.failed_login_attempts = 0
            security_info.save()

            # Log the action
            SecurityLog.objects.create(
                user=security_info.user,
                company=getattr(security_info.user, 'company', None),
                log_type='account_unlocked',
                ip_address=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT'),
                details={
                    'unlocked_by': request.user.username,
                    'unlocked_by_id': request.user.id
                }
            )

            messages.success(request, _('Account unlocked successfully.'))

        elif action == 'reset_failed_attempts':
            # Reset failed login attempts
            security_info.failed_login_attempts = 0
            security_info.save()

            messages.success(request, _('Failed login attempts reset successfully.'))

        elif action == 'reset_password_expiry':
            # Reset password expiry
            security_info.update_password_expiry()

            messages.success(request, _('Password expiry reset successfully.'))

        return redirect('core:security:user_security_info_edit', pk=pk)

    context = {
        'security_info': security_info
    }

    return render(request, 'modules/settings/user_security_info_form.html', context)


@login_required
@permission_required('core.view_passwordhistory')
def password_history_list(request, user_id):
    """
    View password history for a user
    """
    user = get_object_or_404(User, pk=user_id)

    # Check if user has permission to view this user's password history
    if not request.user.is_superuser and request.user.company != getattr(user, 'company', None):
        messages.error(request, _('You do not have permission to view this user\'s password history.'))
        return redirect('core:security:user_security_info_list')

    # Get password history
    history = PasswordHistory.objects.filter(user=user).order_by('-created_at')

    context = {
        'user_obj': user,
        'history': history
    }

    return render(request, 'modules/settings/password_history_list.html', context)
