from django.urls import path
from . import security_views

app_name = 'security'

urlpatterns = [
    # Security dashboard
    path('', security_views.security_dashboard, name='dashboard'),
    path('settings/', security_views.security_settings, name='settings'),

    # MFA devices
    path('mfa/', security_views.mfa_devices, name='mfa_devices'),
    path('mfa/create/', security_views.mfa_device_create, name='mfa_device_create'),
    path('mfa/verify/<int:device_id>/', security_views.mfa_device_verify, name='mfa_device_verify'),
    path('mfa/delete/<int:device_id>/', security_views.mfa_device_delete, name='mfa_device_delete'),

    # Security logs
    path('logs/', security_views.security_logs, name='logs'),

    # Account security
    path('account/locked/', security_views.account_locked, name='account_locked'),
    path('account/password-expired/', security_views.password_expired, name='password_expired'),

    # User security info
    path('users/', security_views.user_security_info_list, name='user_security_info_list'),
    path('users/<int:pk>/edit/', security_views.user_security_info_edit, name='user_security_info_edit'),
    path('users/<int:user_id>/password-history/', security_views.password_history_list, name='password_history_list'),
]
