from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.core.exceptions import PermissionDenied
from django.utils.translation import gettext_lazy as _
import ipaddress

from .security_models import SecuritySetting, UserSecurityInfo

User = get_user_model()

class SecurityEnhancedBackend(ModelBackend):
    """
    Custom authentication backend that enforces security policies
    """

    def authenticate(self, request, username=None, password=None, **kwargs):
        """
        Authenticate a user and enforce security policies
        """
        # First, check if the IP is allowed
        if request and not self.is_ip_allowed(request):
            raise PermissionDenied(_("Access from your IP address is not allowed."))

        # Try to get the user
        try:
            user = User.objects.get(username=username)

            # Check if the account is locked
            security_info, created = UserSecurityInfo.objects.get_or_create(user=user)

            if security_info.is_account_locked():
                # Calculate remaining lockout time
                remaining_time = security_info.account_locked_until - timezone.now()
                minutes = int(remaining_time.total_seconds() / 60)

                if minutes > 0:
                    raise PermissionDenied(
                        _("Your account is locked due to too many failed login attempts. "
                          "Please try again in {minutes} minutes.").format(minutes=minutes)
                    )

            # Check if the password is expired
            if security_info.is_password_expired():
                # We'll still authenticate the user but will redirect them to change their password
                # This is handled in middleware, so we just set a flag on the user object
                user.password_expired = True

            # Authenticate with the parent class
            user = super().authenticate(request, username=username, password=password, **kwargs)

            if user:
                return user

        except User.DoesNotExist:
            # Use default behavior for non-existent users
            return super().authenticate(request, username=username, password=password, **kwargs)

    def is_ip_allowed(self, request):
        """
        Check if the request IP is allowed based on security settings
        """
        client_ip = request.META.get('REMOTE_ADDR')

        if not client_ip:
            return True  # If we can't determine the IP, allow access

        # Get system-wide security settings
        try:
            security_settings = SecuritySetting.objects.get(company=None)
        except SecuritySetting.DoesNotExist:
            return True  # If no security settings exist, allow access

        # If IP restrictions are not enabled, allow access
        if not security_settings.ip_restriction_enabled:
            return True

        # If no allowed IPs are specified, allow access
        if not security_settings.allowed_ips:
            return True

        # Parse the allowed IPs
        allowed_ips = [ip.strip() for ip in security_settings.allowed_ips.split('\n') if ip.strip()]

        # Check if the client IP matches any of the allowed IPs or ranges
        client_ip_obj = ipaddress.ip_address(client_ip)

        for allowed_ip in allowed_ips:
            try:
                if '/' in allowed_ip:
                    # This is a CIDR range
                    network = ipaddress.ip_network(allowed_ip, strict=False)
                    if client_ip_obj in network:
                        return True
                else:
                    # This is a single IP
                    if client_ip == allowed_ip:
                        return True
            except ValueError:
                # Invalid IP format, skip it
                continue

        # If we get here, the IP is not allowed
        return False


class PasswordExpiryMiddleware:
    """
    Middleware to redirect users to change their password if it has expired
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # Check if the user is authenticated and has an expired password
        if request.user.is_authenticated:
            try:
                security_info = UserSecurityInfo.objects.get(user=request.user)

                if security_info.is_password_expired():
                    # Check if we're already on the password change page
                    if not request.path.startswith('/accounts/password_change/'):
                        from django.shortcuts import redirect
                        return redirect('password_change')
            except UserSecurityInfo.DoesNotExist:
                pass

        response = self.get_response(request)
        return response


class SessionSecurityMiddleware:
    """
    Middleware to enforce session timeout based on security settings
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        if request.user.is_authenticated:
            # Get the last activity time from the session
            last_activity = request.session.get('last_activity')

            if last_activity:
                # Convert to datetime
                last_activity = timezone.datetime.fromtimestamp(last_activity, tz=timezone.get_current_timezone())

                # Get timeout from security settings
                timeout_seconds = self.get_session_timeout(request.user)

                # Check if the session has timed out
                if (timezone.now() - last_activity).total_seconds() > timeout_seconds:
                    # Log the user out
                    from django.contrib.auth import logout
                    logout(request)

                    # Redirect to login page with a message
                    from django.shortcuts import redirect
                    from django.contrib import messages
                    messages.info(request, _("Your session has expired due to inactivity. Please log in again."))
                    return redirect('users:login')

            # Update the last activity time
            request.session['last_activity'] = timezone.now().timestamp()

        response = self.get_response(request)
        return response

    def get_session_timeout(self, user):
        """
        Get the session timeout in seconds for a user
        """
        try:
            # Try to get company-specific settings first
            if hasattr(user, 'company') and user.company:
                security_settings = SecuritySetting.objects.get(company=user.company)
            else:
                # Fall back to system-wide settings
                security_settings = SecuritySetting.objects.get(company=None)

            return security_settings.get_session_timeout_seconds()
        except SecuritySetting.DoesNotExist:
            # Default: 30 minutes
            return 30 * 60
