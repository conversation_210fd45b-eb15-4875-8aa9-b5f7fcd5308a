from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.auth.signals import user_logged_in, user_login_failed, user_logged_out
from .security_models import UserSecurityInfo, PasswordHistory, SecurityLog

User = get_user_model()

@receiver(post_save, sender=User)
def create_user_security_info(sender, instance, created, **kwargs):
    """
    Create UserSecurityInfo for new users
    """
    if created:
        UserSecurityInfo.objects.create(user=instance)


@receiver(pre_save, sender=User)
def handle_password_change(sender, instance, **kwargs):
    """
    Handle password changes
    """
    if instance.pk:
        try:
            old_instance = User.objects.get(pk=instance.pk)
            
            # Check if password has changed
            if instance.password != old_instance.password:
                # Add the new password to history
                PasswordHistory.add_password_to_history(instance, instance.password)
                
                # Update security info
                security_info, created = UserSecurityInfo.objects.get_or_create(user=instance)
                security_info.password_last_changed = timezone.now()
                security_info.update_password_expiry()
                security_info.save()
        except User.DoesNotExist:
            pass


@receiver(user_logged_in)
def handle_user_logged_in(sender, request, user, **kwargs):
    """
    Handle successful login
    """
    # Reset failed login attempts
    security_info, created = UserSecurityInfo.objects.get_or_create(user=user)
    security_info.reset_failed_login_attempts()
    
    # Update last login IP
    if request:
        security_info.last_login_ip = request.META.get('REMOTE_ADDR')
        security_info.save(update_fields=['last_login_ip'])
    
    # Log the login
    SecurityLog.objects.create(
        user=user,
        company=user.company if hasattr(user, 'company') else None,
        log_type='login',
        ip_address=request.META.get('REMOTE_ADDR') if request else None,
        user_agent=request.META.get('HTTP_USER_AGENT') if request else None
    )


@receiver(user_login_failed)
def handle_user_login_failed(sender, credentials, request, **kwargs):
    """
    Handle failed login attempts
    """
    username = credentials.get('username', '')
    
    try:
        user = User.objects.get(username=username)
        
        # Increment failed login attempts
        security_info, created = UserSecurityInfo.objects.get_or_create(user=user)
        security_info.increment_failed_login_attempts(request)
        
        # Log the failed login
        SecurityLog.objects.create(
            user=user,
            company=user.company if hasattr(user, 'company') else None,
            log_type='login_failed',
            ip_address=request.META.get('REMOTE_ADDR') if request else None,
            user_agent=request.META.get('HTTP_USER_AGENT') if request else None,
            details={
                'failed_attempts': security_info.failed_login_attempts
            }
        )
    except User.DoesNotExist:
        # Log the failed login for non-existent user
        SecurityLog.objects.create(
            log_type='login_failed',
            ip_address=request.META.get('REMOTE_ADDR') if request else None,
            user_agent=request.META.get('HTTP_USER_AGENT') if request else None,
            details={
                'attempted_username': username
            }
        )


@receiver(user_logged_out)
def handle_user_logged_out(sender, request, user, **kwargs):
    """
    Handle user logout
    """
    if user:
        # Log the logout
        SecurityLog.objects.create(
            user=user,
            company=user.company if hasattr(user, 'company') else None,
            log_type='logout',
            ip_address=request.META.get('REMOTE_ADDR') if request else None,
            user_agent=request.META.get('HTTP_USER_AGENT') if request else None
        )
