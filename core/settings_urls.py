from django.urls import path
from . import settings_views

app_name = 'settings'

urlpatterns = [
    # Dashboard
    path('', settings_views.settings_dashboard, name='dashboard'),
    
    # System Settings
    path('system/', settings_views.system_settings_list, name='system_settings_list'),
    path('system/create/', settings_views.system_setting_create, name='system_setting_create'),
    path('system/edit/<int:pk>/', settings_views.system_setting_edit, name='system_setting_edit'),
    path('system/delete/<int:pk>/', settings_views.system_setting_delete, name='system_setting_delete'),
    
    # Company Settings
    path('company/', settings_views.company_settings_list, name='company_settings_list'),
    path('company/create/', settings_views.company_setting_create, name='company_setting_create'),
    path('company/edit/<int:pk>/', settings_views.company_setting_edit, name='company_setting_edit'),
    path('company/delete/<int:pk>/', settings_views.company_setting_delete, name='company_setting_delete'),
    
    # Backup & Restore
    path('backup/', settings_views.backup_list, name='backup_list'),
    path('backup/create/', settings_views.backup_create, name='backup_create'),
    path('backup/restore/<str:filename>/', settings_views.backup_restore, name='backup_restore'),
    path('backup/download/<str:filename>/', settings_views.backup_download, name='backup_download'),
    path('backup/delete/<str:filename>/', settings_views.backup_delete, name='backup_delete'),
    
    # System Logs
    path('logs/', settings_views.system_logs, name='system_logs'),
    path('logs/view/<str:log_type>/', settings_views.view_log, name='view_log'),
    path('logs/download/<str:log_type>/', settings_views.download_log, name='download_log'),
    path('logs/clear/<str:log_type>/', settings_views.clear_log, name='clear_log'),
]
