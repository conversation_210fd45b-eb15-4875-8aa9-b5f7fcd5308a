# Generated by Django 5.2.1 on 2025-05-21 14:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='Key')),
                ('value', models.TextField(blank=True, null=True, verbose_name='Value')),
                ('value_en', models.TextField(blank=True, null=True, verbose_name='Value')),
                ('value_ar', models.TextField(blank=True, null=True, verbose_name='Value')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_en', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'System Setting',
                'verbose_name_plural': 'System Settings',
                'ordering': ['key'],
            },
        ),
        migrations.CreateModel(
            name='CompanySetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, verbose_name='Key')),
                ('value', models.TextField(blank=True, null=True, verbose_name='Value')),
                ('value_en', models.TextField(blank=True, null=True, verbose_name='Value')),
                ('value_ar', models.TextField(blank=True, null=True, verbose_name='Value')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_en', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='settings', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Company Setting',
                'verbose_name_plural': 'Company Settings',
                'ordering': ['company', 'key'],
                'unique_together': {('company', 'key')},
            },
        ),
    ]
