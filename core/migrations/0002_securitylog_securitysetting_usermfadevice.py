# Generated by Django 5.2.1 on 2025-05-21 19:44

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0001_initial'),
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SecurityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_type', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('login_failed', 'Login Failed'), ('password_change', 'Password Change'), ('password_reset', 'Password Reset'), ('mfa_setup', 'MFA Setup'), ('mfa_verification', 'MFA Verification'), ('mfa_verification_failed', 'MFA Verification Failed'), ('account_locked', 'Account Locked'), ('account_unlocked', 'Account Unlocked'), ('security_setting_change', 'Security Setting Change'), ('permission_change', 'Permission Change'), ('other', 'Other')], max_length=30, verbose_name='Log Type')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='User Agent')),
                ('details', models.JSONField(blank=True, null=True, verbose_name='Details')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='security_logs', to='companies.company', verbose_name='Company')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='security_logs', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Security Log',
                'verbose_name_plural': 'Security Logs',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SecuritySetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password_policy', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('custom', 'Custom')], default='medium', max_length=20, verbose_name='Password Policy')),
                ('password_min_length', models.PositiveIntegerField(default=8, verbose_name='Minimum Password Length')),
                ('password_require_uppercase', models.BooleanField(default=True, verbose_name='Require Uppercase Letters')),
                ('password_require_lowercase', models.BooleanField(default=True, verbose_name='Require Lowercase Letters')),
                ('password_require_numbers', models.BooleanField(default=True, verbose_name='Require Numbers')),
                ('password_require_special_chars', models.BooleanField(default=True, verbose_name='Require Special Characters')),
                ('password_expiry_days', models.PositiveIntegerField(default=90, verbose_name='Password Expiry (Days)')),
                ('password_history_count', models.PositiveIntegerField(default=5, verbose_name='Password History Count')),
                ('mfa_type', models.CharField(choices=[('none', 'None'), ('optional', 'Optional'), ('required', 'Required')], default='optional', max_length=20, verbose_name='Multi-Factor Authentication')),
                ('session_timeout_value', models.PositiveIntegerField(default=30, verbose_name='Session Timeout Value')),
                ('session_timeout_unit', models.CharField(choices=[('minutes', 'Minutes'), ('hours', 'Hours'), ('days', 'Days')], default='minutes', max_length=20, verbose_name='Session Timeout Unit')),
                ('max_login_attempts', models.PositiveIntegerField(default=5, verbose_name='Maximum Login Attempts')),
                ('lockout_duration_minutes', models.PositiveIntegerField(default=30, verbose_name='Lockout Duration (Minutes)')),
                ('ip_restriction_enabled', models.BooleanField(default=False, verbose_name='Enable IP Restrictions')),
                ('allowed_ips', models.TextField(blank=True, help_text='Enter one IP address or range per line (e.g., *********** or ***********/24)', null=True, verbose_name='Allowed IP Addresses')),
                ('enable_audit_logging', models.BooleanField(default=True, verbose_name='Enable Audit Logging')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='security_settings', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Security Setting',
                'verbose_name_plural': 'Security Settings',
            },
        ),
        migrations.CreateModel(
            name='UserMFADevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_type', models.CharField(choices=[('totp', 'Time-based One-Time Password (TOTP)'), ('sms', 'SMS'), ('email', 'Email')], default='totp', max_length=20, verbose_name='Device Type')),
                ('name', models.CharField(max_length=100, verbose_name='Device Name')),
                ('secret_key', models.CharField(max_length=100, verbose_name='Secret Key')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is Primary')),
                ('last_used', models.DateTimeField(blank=True, null=True, verbose_name='Last Used')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mfa_devices', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User MFA Device',
                'verbose_name_plural': 'User MFA Devices',
                'unique_together': {('user', 'name')},
            },
        ),
    ]
