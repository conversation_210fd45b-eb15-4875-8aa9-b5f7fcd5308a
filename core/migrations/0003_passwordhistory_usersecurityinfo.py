# Generated by Django 5.2.1 on 2025-05-21 19:59

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_securitylog_securitysetting_usermfadevice'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PasswordHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=255, verbose_name='Password Hash')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='password_history', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Password History',
                'verbose_name_plural': 'Password History',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='UserSecurityInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password_last_changed', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Password Last Changed')),
                ('failed_login_attempts', models.PositiveIntegerField(default=0, verbose_name='Failed Login Attempts')),
                ('last_failed_login', models.DateTimeField(blank=True, null=True, verbose_name='Last Failed Login')),
                ('account_locked_until', models.DateTimeField(blank=True, null=True, verbose_name='Account Locked Until')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='Last Login IP')),
                ('password_expires_at', models.DateTimeField(blank=True, null=True, verbose_name='Password Expires At')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='security_info', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Security Info',
                'verbose_name_plural': 'User Security Info',
            },
        ),
    ]
