from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone
from django.contrib.auth.hashers import make_password, check_password
import pyotp
import qrcode
import qrcode.image.svg
from io import BytesIO
import base64
import uuid
import json
import datetime
from companies.models import Company

User = get_user_model()

class SecuritySetting(models.Model):
    """
    Model for security settings
    """
    # Password policy settings
    PASSWORD_POLICY_CHOICES = (
        ('low', _('Low')),
        ('medium', _('Medium')),
        ('high', _('High')),
        ('custom', _('Custom')),
    )

    # MFA types
    MFA_TYPE_CHOICES = (
        ('none', _('None')),
        ('optional', _('Optional')),
        ('required', _('Required')),
    )

    # Session timeout units
    SESSION_TIMEOUT_UNIT_CHOICES = (
        ('minutes', _('Minutes')),
        ('hours', _('Hours')),
        ('days', _('Days')),
    )

    company = models.OneToOneField(
        Company,
        on_delete=models.CASCADE,
        related_name='security_settings',
        null=True,
        blank=True,
        verbose_name=_('Company')
    )

    # Password policy
    password_policy = models.CharField(
        max_length=20,
        choices=PASSWORD_POLICY_CHOICES,
        default='medium',
        verbose_name=_('Password Policy')
    )
    password_min_length = models.PositiveIntegerField(
        default=8,
        verbose_name=_('Minimum Password Length')
    )
    password_require_uppercase = models.BooleanField(
        default=True,
        verbose_name=_('Require Uppercase Letters')
    )
    password_require_lowercase = models.BooleanField(
        default=True,
        verbose_name=_('Require Lowercase Letters')
    )
    password_require_numbers = models.BooleanField(
        default=True,
        verbose_name=_('Require Numbers')
    )
    password_require_special_chars = models.BooleanField(
        default=True,
        verbose_name=_('Require Special Characters')
    )
    password_expiry_days = models.PositiveIntegerField(
        default=90,
        verbose_name=_('Password Expiry (Days)')
    )
    password_history_count = models.PositiveIntegerField(
        default=5,
        verbose_name=_('Password History Count')
    )

    # Multi-factor authentication
    mfa_type = models.CharField(
        max_length=20,
        choices=MFA_TYPE_CHOICES,
        default='optional',
        verbose_name=_('Multi-Factor Authentication')
    )

    # Session settings
    session_timeout_value = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Session Timeout Value')
    )
    session_timeout_unit = models.CharField(
        max_length=20,
        choices=SESSION_TIMEOUT_UNIT_CHOICES,
        default='minutes',
        verbose_name=_('Session Timeout Unit')
    )

    # Login attempt settings
    max_login_attempts = models.PositiveIntegerField(
        default=5,
        verbose_name=_('Maximum Login Attempts')
    )
    lockout_duration_minutes = models.PositiveIntegerField(
        default=30,
        verbose_name=_('Lockout Duration (Minutes)')
    )

    # IP restrictions
    ip_restriction_enabled = models.BooleanField(
        default=False,
        verbose_name=_('Enable IP Restrictions')
    )
    allowed_ips = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Allowed IP Addresses'),
        help_text=_('Enter one IP address or range per line (e.g., *********** or ***********/24)')
    )

    # Audit logging
    enable_audit_logging = models.BooleanField(
        default=True,
        verbose_name=_('Enable Audit Logging')
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Security Setting')
        verbose_name_plural = _('Security Settings')

    def __str__(self):
        if self.company:
            return f"{self.company.name} - Security Settings"
        return "System Security Settings"

    def get_session_timeout_seconds(self):
        """
        Get session timeout in seconds
        """
        if self.session_timeout_unit == 'minutes':
            return self.session_timeout_value * 60
        elif self.session_timeout_unit == 'hours':
            return self.session_timeout_value * 3600
        elif self.session_timeout_unit == 'days':
            return self.session_timeout_value * 86400
        return 1800  # Default: 30 minutes


class UserMFADevice(models.Model):
    """
    Model for user MFA devices
    """
    MFA_DEVICE_TYPES = (
        ('totp', _('Time-based One-Time Password (TOTP)')),
        ('sms', _('SMS')),
        ('email', _('Email')),
    )

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='mfa_devices',
        verbose_name=_('User')
    )
    device_type = models.CharField(
        max_length=20,
        choices=MFA_DEVICE_TYPES,
        default='totp',
        verbose_name=_('Device Type')
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_('Device Name')
    )
    secret_key = models.CharField(
        max_length=100,
        verbose_name=_('Secret Key')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name=_('Is Primary')
    )
    last_used = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Used')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('User MFA Device')
        verbose_name_plural = _('User MFA Devices')
        unique_together = ['user', 'name']

    def __str__(self):
        return f"{self.user.username} - {self.name}"

    def generate_secret_key(self):
        """
        Generate a new secret key
        """
        self.secret_key = pyotp.random_base32()
        return self.secret_key

    def get_totp(self):
        """
        Get TOTP object
        """
        return pyotp.TOTP(self.secret_key)

    def verify_token(self, token):
        """
        Verify a token
        """
        totp = self.get_totp()
        result = totp.verify(token)
        if result:
            self.last_used = timezone.now()
            self.save(update_fields=['last_used'])
        return result

    def get_qr_code(self):
        """
        Get QR code for TOTP setup
        """
        if self.device_type != 'totp':
            return None

        totp = self.get_totp()
        uri = totp.provisioning_uri(
            name=self.user.email,
            issuer_name="ERP System"
        )

        # Generate QR code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(uri)
        qr.make(fit=True)

        # Create SVG image
        img = qr.make_image(fill_color="black", back_color="white", image_factory=qrcode.image.svg.SvgImage)
        stream = BytesIO()
        img.save(stream)
        svg_data = stream.getvalue().decode()

        return svg_data


class SecurityLog(models.Model):
    """
    Model for security logs
    """
    LOG_TYPES = (
        ('login', _('Login')),
        ('logout', _('Logout')),
        ('login_failed', _('Login Failed')),
        ('password_change', _('Password Change')),
        ('password_reset', _('Password Reset')),
        ('mfa_setup', _('MFA Setup')),
        ('mfa_verification', _('MFA Verification')),
        ('mfa_verification_failed', _('MFA Verification Failed')),
        ('account_locked', _('Account Locked')),
        ('account_unlocked', _('Account Unlocked')),
        ('security_setting_change', _('Security Setting Change')),
        ('permission_change', _('Permission Change')),
        ('other', _('Other')),
    )

    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='security_logs',
        verbose_name=_('User')
    )
    company = models.ForeignKey(
        Company,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='security_logs',
        verbose_name=_('Company')
    )
    log_type = models.CharField(
        max_length=30,
        choices=LOG_TYPES,
        verbose_name=_('Log Type')
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('IP Address')
    )
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name=_('User Agent')
    )
    details = models.JSONField(
        null=True,
        blank=True,
        verbose_name=_('Details')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Security Log')
        verbose_name_plural = _('Security Logs')
        ordering = ['-created_at']

    def __str__(self):
        username = self.user.username if self.user else 'Anonymous'
        return f"{username} - {self.get_log_type_display()} - {self.created_at}"


class PasswordHistory(models.Model):
    """
    Model for storing password history
    """
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='password_history',
        verbose_name=_('User')
    )
    password = models.CharField(
        max_length=255,
        verbose_name=_('Password Hash')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )

    class Meta:
        verbose_name = _('Password History')
        verbose_name_plural = _('Password History')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.created_at}"

    @classmethod
    def check_password_history(cls, user, new_password, count=5):
        """
        Check if the new password exists in the user's password history

        Args:
            user: User object
            new_password: Plain text new password
            count: Number of previous passwords to check

        Returns:
            bool: True if password is not in history, False otherwise
        """
        if count <= 0:
            return True

        # Get the most recent password history entries
        history_entries = cls.objects.filter(user=user).order_by('-created_at')[:count]

        # Check if the new password matches any of the stored hashes
        for entry in history_entries:
            if check_password(new_password, entry.password):
                return False

        return True

    @classmethod
    def add_password_to_history(cls, user, password):
        """
        Add a password to the user's password history

        Args:
            user: User object
            password: Plain text or hashed password
        """
        # Check if the password is already hashed
        if not password.startswith(('pbkdf2_sha256$', 'bcrypt$', 'argon2')):
            password = make_password(password)

        # Create a new password history entry
        cls.objects.create(user=user, password=password)

        # Enforce history limit if specified in security settings
        try:
            if user.company:
                security_settings = SecuritySetting.objects.get(company=user.company)
            else:
                security_settings = SecuritySetting.objects.get(company=None)

            history_count = security_settings.password_history_count

            if history_count > 0:
                # Keep only the most recent 'history_count' entries
                old_entries = cls.objects.filter(user=user).order_by('-created_at')[history_count:]
                if old_entries.exists():
                    old_entries.delete()
        except SecuritySetting.DoesNotExist:
            pass  # No security settings defined, use default behavior


class UserSecurityInfo(models.Model):
    """
    Model for storing additional security information for users
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='security_info',
        verbose_name=_('User')
    )
    password_last_changed = models.DateTimeField(
        default=timezone.now,
        verbose_name=_('Password Last Changed')
    )
    failed_login_attempts = models.PositiveIntegerField(
        default=0,
        verbose_name=_('Failed Login Attempts')
    )
    last_failed_login = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Last Failed Login')
    )
    account_locked_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Account Locked Until')
    )
    last_login_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name=_('Last Login IP')
    )
    password_expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name=_('Password Expires At')
    )

    class Meta:
        verbose_name = _('User Security Info')
        verbose_name_plural = _('User Security Info')

    def __str__(self):
        return f"{self.user.username} - Security Info"

    def is_password_expired(self):
        """
        Check if the user's password is expired
        """
        if not self.password_expires_at:
            return False

        return timezone.now() >= self.password_expires_at

    def is_account_locked(self):
        """
        Check if the user's account is locked
        """
        if not self.account_locked_until:
            return False

        return timezone.now() < self.account_locked_until

    def reset_failed_login_attempts(self):
        """
        Reset the failed login attempts counter
        """
        self.failed_login_attempts = 0
        self.save(update_fields=['failed_login_attempts'])

    def increment_failed_login_attempts(self, request=None):
        """
        Increment the failed login attempts counter and lock the account if necessary
        """
        self.failed_login_attempts += 1
        self.last_failed_login = timezone.now()

        # Check if we should lock the account
        try:
            if self.user.company:
                security_settings = SecuritySetting.objects.get(company=self.user.company)
            else:
                security_settings = SecuritySetting.objects.get(company=None)

            max_attempts = security_settings.max_login_attempts
            lockout_duration = security_settings.lockout_duration_minutes

            if max_attempts > 0 and self.failed_login_attempts >= max_attempts:
                self.account_locked_until = timezone.now() + datetime.timedelta(minutes=lockout_duration)

                # Log the account lock
                if request:
                    SecurityLog.objects.create(
                        user=self.user,
                        company=self.user.company,
                        log_type='account_locked',
                        ip_address=request.META.get('REMOTE_ADDR'),
                        user_agent=request.META.get('HTTP_USER_AGENT'),
                        details={
                            'failed_attempts': self.failed_login_attempts,
                            'lockout_duration': lockout_duration
                        }
                    )
        except SecuritySetting.DoesNotExist:
            pass  # No security settings defined, use default behavior

        self.save(update_fields=['failed_login_attempts', 'last_failed_login', 'account_locked_until'])

    def update_password_expiry(self):
        """
        Update the password expiry date based on security settings
        """
        try:
            if self.user.company:
                security_settings = SecuritySetting.objects.get(company=self.user.company)
            else:
                security_settings = SecuritySetting.objects.get(company=None)

            expiry_days = security_settings.password_expiry_days

            if expiry_days > 0:
                self.password_expires_at = timezone.now() + datetime.timedelta(days=expiry_days)
            else:
                self.password_expires_at = None

            self.save(update_fields=['password_expires_at'])
        except SecuritySetting.DoesNotExist:
            pass  # No security settings defined, use default behavior
