from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import SystemSetting, CompanySetting

# Import security admin
from .security_admin import *

@admin.register(SystemSetting)
class SystemSettingAdmin(admin.ModelAdmin):
    """
    Admin for SystemSetting model
    """
    list_display = ('key', 'value', 'is_active', 'updated_at')
    list_filter = ('is_active',)
    search_fields = ('key', 'value', 'description')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('key', 'value', 'description', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(CompanySetting)
class CompanySettingAdmin(admin.ModelAdmin):
    """
    Admin for CompanySetting model
    """
    list_display = ('company', 'key', 'value', 'is_active', 'updated_at')
    list_filter = ('is_active', 'company')
    search_fields = ('key', 'value', 'description', 'company__name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('company', 'key', 'value', 'description', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
