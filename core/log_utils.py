import os
import logging
import datetime
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Get logger
logger = logging.getLogger(__name__)

class LogManager:
    """
    Utility class for managing system logs
    """
    
    @staticmethod
    def get_log_types():
        """
        Get available log types
        
        Returns:
            list: List of log type dictionaries
        """
        log_types = [
            {
                'name': 'application',
                'description': _('Application Logs'),
                'file': getattr(settings, 'APPLICATION_LOG_FILE', None) or os.path.join(settings.BASE_DIR, 'logs', 'app.log'),
            },
            {
                'name': 'error',
                'description': _('Error Logs'),
                'file': getattr(settings, 'ERROR_LOG_FILE', None) or os.path.join(settings.BASE_DIR, 'logs', 'error.log'),
            },
            {
                'name': 'access',
                'description': _('Access Logs'),
                'file': getattr(settings, 'ACCESS_LOG_FILE', None) or os.path.join(settings.BASE_DIR, 'logs', 'access.log'),
            },
        ]
        
        # Add file existence and size information
        for log_type in log_types:
            log_file = log_type['file']
            if os.path.exists(log_file):
                log_type['exists'] = True
                log_type['size'] = os.path.getsize(log_file)
                log_type['size_formatted'] = LogManager.format_size(log_type['size'])
                log_type['modified'] = datetime.datetime.fromtimestamp(
                    os.path.getmtime(log_file),
                    tz=timezone.get_current_timezone()
                )
            else:
                log_type['exists'] = False
                log_type['size'] = 0
                log_type['size_formatted'] = '0 B'
                log_type['modified'] = None
        
        return log_types
    
    @staticmethod
    def get_log_content(log_type):
        """
        Get the content of a log file
        
        Args:
            log_type (str): The type of log to get
            
        Returns:
            str: The log content
        """
        log_types = LogManager.get_log_types()
        log_file = None
        
        # Find the log file path
        for lt in log_types:
            if lt['name'] == log_type:
                log_file = lt['file']
                break
        
        if not log_file:
            raise ValueError(_("Invalid log type"))
        
        # Check if the log file exists
        if not os.path.exists(log_file):
            return _("Log file does not exist")
        
        # Read the log file content
        try:
            with open(log_file, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
            return content
        except Exception as e:
            logger.error(f"Error reading log file: {str(e)}")
            return _("Error reading log file: {}").format(str(e))
    
    @staticmethod
    def clear_log(log_type):
        """
        Clear a log file
        
        Args:
            log_type (str): The type of log to clear
            
        Returns:
            bool: True if successful
        """
        log_types = LogManager.get_log_types()
        log_file = None
        
        # Find the log file path
        for lt in log_types:
            if lt['name'] == log_type:
                log_file = lt['file']
                break
        
        if not log_file:
            raise ValueError(_("Invalid log type"))
        
        # Check if the log file exists
        if not os.path.exists(log_file):
            return True  # Nothing to clear
        
        # Clear the log file
        try:
            with open(log_file, 'w') as f:
                f.write('')
            logger.info(f"Log file cleared: {log_file}")
            return True
        except Exception as e:
            logger.error(f"Error clearing log file: {str(e)}")
            raise
    
    @staticmethod
    def format_size(size_bytes):
        """
        Format a file size in bytes to a human-readable format
        
        Args:
            size_bytes (int): Size in bytes
            
        Returns:
            str: Formatted size
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ("B", "KB", "MB", "GB", "TB")
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024
            i += 1
        
        return f"{size_bytes:.2f} {size_names[i]}"
