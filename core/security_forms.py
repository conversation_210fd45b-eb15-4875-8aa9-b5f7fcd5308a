from django import forms
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import PasswordChangeForm
from django.core.exceptions import ValidationError
from django.utils import timezone
import re
from .security_models import SecuritySetting, UserMFADevice, SecurityLog
from companies.models import Company

User = get_user_model()

class SecuritySettingForm(forms.ModelForm):
    """
    Form for security settings
    """
    class Meta:
        model = SecuritySetting
        fields = [
            'password_policy', 'password_min_length', 'password_require_uppercase',
            'password_require_lowercase', 'password_require_numbers',
            'password_require_special_chars', 'password_expiry_days',
            'password_history_count', 'mfa_type', 'session_timeout_value',
            'session_timeout_unit', 'max_login_attempts', 'lockout_duration_minutes',
            'ip_restriction_enabled', 'allowed_ips', 'enable_audit_logging'
        ]
        widgets = {
            'password_policy': forms.Select(attrs={'class': 'form-select'}),
            'password_min_length': forms.NumberInput(attrs={'class': 'form-control', 'min': 6, 'max': 32}),
            'password_require_uppercase': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'password_require_lowercase': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'password_require_numbers': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'password_require_special_chars': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'password_expiry_days': forms.NumberInput(attrs={'class': 'form-control', 'min': 0, 'max': 365}),
            'password_history_count': forms.NumberInput(attrs={'class': 'form-control', 'min': 0, 'max': 24}),
            'mfa_type': forms.Select(attrs={'class': 'form-select'}),
            'session_timeout_value': forms.NumberInput(attrs={'class': 'form-control', 'min': 1}),
            'session_timeout_unit': forms.Select(attrs={'class': 'form-select'}),
            'max_login_attempts': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 10}),
            'lockout_duration_minutes': forms.NumberInput(attrs={'class': 'form-control', 'min': 1, 'max': 1440}),
            'ip_restriction_enabled': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'allowed_ips': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'enable_audit_logging': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Add help texts
        self.fields['password_min_length'].help_text = _('Minimum number of characters required for passwords')
        self.fields['password_expiry_days'].help_text = _('Number of days before passwords expire (0 for never)')
        self.fields['password_history_count'].help_text = _('Number of previous passwords to remember (0 to disable)')
        self.fields['allowed_ips'].help_text = _('Enter one IP address or range per line (e.g., *********** or ***********/24)')
        
        # Set initial values based on policy
        if self.instance.pk and self.instance.password_policy != 'custom':
            self.set_policy_defaults()
    
    def set_policy_defaults(self):
        """
        Set default values based on selected policy
        """
        policy = self.instance.password_policy
        
        if policy == 'low':
            self.instance.password_min_length = 6
            self.instance.password_require_uppercase = False
            self.instance.password_require_lowercase = True
            self.instance.password_require_numbers = True
            self.instance.password_require_special_chars = False
            self.instance.password_expiry_days = 0
            self.instance.password_history_count = 0
        
        elif policy == 'medium':
            self.instance.password_min_length = 8
            self.instance.password_require_uppercase = True
            self.instance.password_require_lowercase = True
            self.instance.password_require_numbers = True
            self.instance.password_require_special_chars = False
            self.instance.password_expiry_days = 90
            self.instance.password_history_count = 3
        
        elif policy == 'high':
            self.instance.password_min_length = 12
            self.instance.password_require_uppercase = True
            self.instance.password_require_lowercase = True
            self.instance.password_require_numbers = True
            self.instance.password_require_special_chars = True
            self.instance.password_expiry_days = 60
            self.instance.password_history_count = 5
    
    def clean_allowed_ips(self):
        """
        Validate IP addresses and ranges
        """
        ip_restriction_enabled = self.cleaned_data.get('ip_restriction_enabled')
        allowed_ips = self.cleaned_data.get('allowed_ips')
        
        if not ip_restriction_enabled or not allowed_ips:
            return allowed_ips
        
        # Split by newline and validate each IP/range
        ip_list = [ip.strip() for ip in allowed_ips.split('\n') if ip.strip()]
        
        for ip in ip_list:
            # Check if it's a CIDR range
            if '/' in ip:
                try:
                    # Basic validation - a more thorough validation would use ipaddress module
                    network, bits = ip.split('/')
                    if not re.match(r'^(\d{1,3}\.){3}\d{1,3}$', network):
                        raise ValidationError(f"Invalid IP address format: {network}")
                    
                    bits = int(bits)
                    if bits < 0 or bits > 32:
                        raise ValidationError(f"Invalid CIDR prefix: {bits}")
                except ValueError:
                    raise ValidationError(f"Invalid IP range format: {ip}")
            else:
                # Single IP address
                if not re.match(r'^(\d{1,3}\.){3}\d{1,3}$', ip):
                    raise ValidationError(f"Invalid IP address format: {ip}")
        
        return allowed_ips
    
    def clean(self):
        cleaned_data = super().clean()
        password_policy = cleaned_data.get('password_policy')
        
        # If policy is not custom, set the default values
        if password_policy != 'custom':
            self.instance.password_policy = password_policy
            self.set_policy_defaults()
            
            # Update cleaned_data with the new values
            for field in self.Meta.fields:
                if hasattr(self.instance, field):
                    cleaned_data[field] = getattr(self.instance, field)
        
        return cleaned_data


class UserMFADeviceForm(forms.ModelForm):
    """
    Form for user MFA devices
    """
    class Meta:
        model = UserMFADevice
        fields = ['device_type', 'name', 'is_primary']
        widgets = {
            'device_type': forms.Select(attrs={'class': 'form-select'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'is_primary': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        
        # Add help texts
        self.fields['device_type'].help_text = _('Type of MFA device')
        self.fields['name'].help_text = _('A name to identify this device')
        self.fields['is_primary'].help_text = _('Set as primary MFA device')
    
    def clean_is_primary(self):
        """
        If this device is set as primary, unset other devices
        """
        is_primary = self.cleaned_data.get('is_primary')
        
        if is_primary and self.user and not self.instance.pk:
            # If creating a new primary device, unset other devices
            UserMFADevice.objects.filter(user=self.user, is_primary=True).update(is_primary=False)
        
        return is_primary
    
    def save(self, commit=True):
        instance = super().save(commit=False)
        
        if self.user and not instance.pk:
            instance.user = self.user
            
            # Generate a new secret key if not set
            if not instance.secret_key:
                instance.generate_secret_key()
        
        if commit:
            instance.save()
        
        return instance


class MFAVerificationForm(forms.Form):
    """
    Form for MFA verification
    """
    token = forms.CharField(
        label=_('Verification Code'),
        max_length=6,
        min_length=6,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-lg text-center',
            'placeholder': '000000',
            'autocomplete': 'off',
            'inputmode': 'numeric',
            'pattern': '[0-9]*',
        })
    )
    
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        self.device = kwargs.pop('device', None)
        super().__init__(*args, **kwargs)
    
    def clean_token(self):
        token = self.cleaned_data.get('token')
        
        if not token.isdigit():
            raise ValidationError(_('Verification code must contain only digits'))
        
        return token
    
    def clean(self):
        cleaned_data = super().clean()
        token = cleaned_data.get('token')
        
        if token and self.device:
            if not self.device.verify_token(token):
                raise ValidationError(_('Invalid verification code'))
        
        return cleaned_data


class EnhancedPasswordChangeForm(PasswordChangeForm):
    """
    Enhanced password change form with policy validation
    """
    def __init__(self, *args, **kwargs):
        self.security_settings = kwargs.pop('security_settings', None)
        super().__init__(*args, **kwargs)
        
        # Add help text based on security settings
        if self.security_settings:
            help_text = []
            
            if self.security_settings.password_min_length > 0:
                help_text.append(_('At least {} characters').format(self.security_settings.password_min_length))
            
            if self.security_settings.password_require_uppercase:
                help_text.append(_('At least one uppercase letter'))
            
            if self.security_settings.password_require_lowercase:
                help_text.append(_('At least one lowercase letter'))
            
            if self.security_settings.password_require_numbers:
                help_text.append(_('At least one number'))
            
            if self.security_settings.password_require_special_chars:
                help_text.append(_('At least one special character'))
            
            if help_text:
                self.fields['new_password1'].help_text = '<br>'.join(help_text)
    
    def clean_new_password1(self):
        password = super().clean_new_password1()
        
        if self.security_settings:
            # Check password length
            if len(password) < self.security_settings.password_min_length:
                raise ValidationError(
                    _('Password must be at least {} characters long').format(
                        self.security_settings.password_min_length
                    )
                )
            
            # Check for uppercase letters
            if self.security_settings.password_require_uppercase and not any(c.isupper() for c in password):
                raise ValidationError(_('Password must contain at least one uppercase letter'))
            
            # Check for lowercase letters
            if self.security_settings.password_require_lowercase and not any(c.islower() for c in password):
                raise ValidationError(_('Password must contain at least one lowercase letter'))
            
            # Check for numbers
            if self.security_settings.password_require_numbers and not any(c.isdigit() for c in password):
                raise ValidationError(_('Password must contain at least one number'))
            
            # Check for special characters
            if self.security_settings.password_require_special_chars and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
                raise ValidationError(_('Password must contain at least one special character'))
        
        return password
