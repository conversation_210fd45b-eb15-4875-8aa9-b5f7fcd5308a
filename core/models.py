from django.db import models
from django.utils.translation import gettext_lazy as _
from companies.models import Company, Branch

class SystemSetting(models.Model):
    """
    Model for system-wide settings
    """
    key = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Key')
    )
    value = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Value')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('System Setting')
        verbose_name_plural = _('System Settings')
        ordering = ['key']

    def __str__(self):
        return self.key


class CompanySetting(models.Model):
    """
    Model for company-specific settings
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='settings',
        verbose_name=_('Company')
    )
    key = models.CharField(
        max_length=100,
        verbose_name=_('Key')
    )
    value = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Value')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Company Setting')
        verbose_name_plural = _('Company Settings')
        unique_together = ['company', 'key']
        ordering = ['company', 'key']

    def __str__(self):
        return f"{self.company.name} - {self.key}"
