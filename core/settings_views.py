import os
import datetime
import logging
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponse, JsonResponse
from django.conf import settings
from django.db import connection
from django.utils import timezone
from .models import SystemSetting, CompanySetting
from .forms import SystemSettingForm, CompanySettingForm, BackupForm, RestoreForm

# Get logger
logger = logging.getLogger(__name__)

@login_required
def settings_dashboard(request):
    """
    Settings dashboard view
    """
    # Get system information
    system_info = {
        'version': '1.0.0',  # This should come from a version file or setting
        'database': connection.vendor,
        'last_update': SystemSetting.objects.order_by('-updated_at').first().updated_at if SystemSetting.objects.exists() else None,
        'server_time': timezone.now(),
    }

    # Get counts for stats cards
    stats = {
        'users_count': request.user.company.users.filter(is_active=True).count() if request.user.company else 0,
        'modules_count': request.user.company.modules.filter(is_active=True).count() if request.user.company else 0,
        'branches_count': request.user.company.branches.filter(active=True).count() if request.user.company else 0,
        'last_backup': None,  # This would come from backup records
    }

    # Get recent activities (placeholder for now)
    recent_activities = []

    # Get system health (placeholder for now)
    system_health = []

    context = {
        'system_info': system_info,
        'stats': stats,
        'recent_activities': recent_activities,
        'system_health': system_health,
    }

    return render(request, 'modules/settings/dashboard.html', context)

@login_required
@permission_required('core.view_systemsetting')
def system_settings_list(request):
    """
    List all system settings
    """
    settings = SystemSetting.objects.all().order_by('key')

    return render(request, 'modules/settings/system_settings_list.html', {
        'settings': settings,
    })

@login_required
@permission_required('core.add_systemsetting')
def system_setting_create(request):
    """
    Create a new system setting
    """
    if request.method == 'POST':
        form = SystemSettingForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, _('System setting created successfully.'))
            return redirect('core:settings:system_settings_list')
    else:
        form = SystemSettingForm()

    return render(request, 'modules/settings/system_setting_form.html', {
        'form': form,
    })

@login_required
@permission_required('core.change_systemsetting')
def system_setting_edit(request, pk):
    """
    Edit an existing system setting
    """
    setting = get_object_or_404(SystemSetting, pk=pk)

    if request.method == 'POST':
        form = SystemSettingForm(request.POST, instance=setting)
        if form.is_valid():
            form.save()
            messages.success(request, _('System setting updated successfully.'))
            return redirect('core:settings:system_settings_list')
    else:
        form = SystemSettingForm(instance=setting)

    return render(request, 'modules/settings/system_setting_form.html', {
        'form': form,
        'setting': setting,
    })

@login_required
@permission_required('core.delete_systemsetting')
def system_setting_delete(request, pk):
    """
    Delete a system setting
    """
    setting = get_object_or_404(SystemSetting, pk=pk)

    if request.method == 'POST':
        setting.delete()
        messages.success(request, _('System setting deleted successfully.'))
        return redirect('core:settings:system_settings_list')

    return render(request, 'modules/settings/system_setting_confirm_delete.html', {
        'setting': setting,
    })

@login_required
@permission_required('core.view_companysetting')
def company_settings_list(request):
    """
    List all company settings
    """
    # For superusers, show all company settings
    # For regular users, show only their company's settings
    if request.user.is_superuser:
        settings = CompanySetting.objects.all().order_by('company', 'key')
    else:
        settings = CompanySetting.objects.filter(company=request.user.company).order_by('key') if request.user.company else CompanySetting.objects.none()

    return render(request, 'modules/settings/company_settings_list.html', {
        'settings': settings,
    })

@login_required
@permission_required('core.add_companysetting')
def company_setting_create(request):
    """
    Create a new company setting
    """
    if request.method == 'POST':
        form = CompanySettingForm(request.POST, user=request.user)
        form.user = request.user  # Set user for clean method
        if form.is_valid():
            form.save()
            messages.success(request, _('Company setting created successfully.'))
            return redirect('core:settings:company_settings_list')
    else:
        form = CompanySettingForm(user=request.user)

    return render(request, 'modules/settings/company_setting_form.html', {
        'form': form,
    })

@login_required
@permission_required('core.change_companysetting')
def company_setting_edit(request, pk):
    """
    Edit an existing company setting
    """
    setting = get_object_or_404(CompanySetting, pk=pk)

    # Check if user has permission to edit this setting
    if not request.user.is_superuser and request.user.company != setting.company:
        messages.error(request, _('You do not have permission to edit this setting.'))
        return redirect('core:settings:company_settings_list')

    if request.method == 'POST':
        form = CompanySettingForm(request.POST, instance=setting, user=request.user)
        form.user = request.user  # Set user for clean method
        if form.is_valid():
            form.save()
            messages.success(request, _('Company setting updated successfully.'))
            return redirect('core:settings:company_settings_list')
    else:
        form = CompanySettingForm(instance=setting, user=request.user)

    return render(request, 'modules/settings/company_setting_form.html', {
        'form': form,
        'setting': setting,
    })

@login_required
@permission_required('core.delete_companysetting')
def company_setting_delete(request, pk):
    """
    Delete a company setting
    """
    setting = get_object_or_404(CompanySetting, pk=pk)

    # Check if user has permission to delete this setting
    if not request.user.is_superuser and request.user.company != setting.company:
        messages.error(request, _('You do not have permission to delete this setting.'))
        return redirect('core:settings:company_settings_list')

    if request.method == 'POST':
        setting.delete()
        messages.success(request, _('Company setting deleted successfully.'))
        return redirect('core:settings:company_settings_list')

    return render(request, 'modules/settings/company_setting_confirm_delete.html', {
        'setting': setting,
    })

from .backup_utils import BackupManager

@login_required
@permission_required('core.view_systemsetting')
def backup_list(request):
    """
    List all backups
    """
    # Get all available backups
    backups = BackupManager.get_backups()

    return render(request, 'modules/settings/backup_list.html', {
        'backups': backups,
    })

@login_required
@permission_required('core.add_systemsetting')
def backup_create(request):
    """
    Create a new backup
    """
    if request.method == 'POST':
        form = BackupForm(request.POST)
        if form.is_valid():
            try:
                backup_name = form.cleaned_data.get('backup_name')
                include_media = form.cleaned_data.get('include_media', True)

                # Create the backup
                backup_info = BackupManager.create_backup(
                    backup_name=backup_name,
                    include_media=include_media
                )

                messages.success(request, _('Backup created successfully.'))
                return redirect('core:settings:backup_list')
            except Exception as e:
                messages.error(request, _('Error creating backup: {}').format(str(e)))
    else:
        form = BackupForm()

    return render(request, 'modules/settings/backup_form.html', {
        'form': form,
    })

@login_required
@permission_required('core.change_systemsetting')
def backup_restore(request, filename):
    """
    Restore from a backup
    """
    if filename == 'upload':
        # Handle uploaded backup file
        if request.method == 'POST':
            form = RestoreForm(request.POST, request.FILES)
            if form.is_valid():
                try:
                    backup_file = request.FILES['backup_file']
                    confirm = form.cleaned_data.get('confirm', False)

                    # Restore from the backup
                    BackupManager.restore_backup(backup_file, confirm=confirm)

                    messages.success(request, _('System restored successfully.'))
                    return redirect('core:settings:backup_list')
                except Exception as e:
                    messages.error(request, _('Error restoring backup: {}').format(str(e)))
        else:
            form = RestoreForm()

        return render(request, 'modules/settings/restore_form.html', {
            'form': form,
            'filename': filename,
        })
    else:
        # Handle existing backup file
        if request.method == 'POST':
            form = RestoreForm(request.POST)
            if form.is_valid():
                try:
                    confirm = form.cleaned_data.get('confirm', False)

                    # Restore from the backup
                    BackupManager.restore_backup(filename, confirm=confirm)

                    messages.success(request, _('System restored successfully.'))
                    return redirect('core:settings:backup_list')
                except Exception as e:
                    messages.error(request, _('Error restoring backup: {}').format(str(e)))
        else:
            form = RestoreForm()

        return render(request, 'modules/settings/restore_form.html', {
            'form': form,
            'filename': filename,
        })

@login_required
@permission_required('core.view_systemsetting')
def backup_download(request, filename):
    """
    Download a backup file
    """
    from django.http import FileResponse
    import os
    from .backup_utils import BACKUP_DIR

    file_path = os.path.join(BACKUP_DIR, filename)
    if os.path.exists(file_path):
        response = FileResponse(open(file_path, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        return response
    else:
        messages.error(request, _('Backup file not found.'))
        return redirect('core:settings:backup_list')

@login_required
@permission_required('core.delete_systemsetting')
def backup_delete(request, filename):
    """
    Delete a backup file
    """
    if request.method == 'POST':
        try:
            # Delete the backup
            BackupManager.delete_backup(filename)

            messages.success(request, _('Backup deleted successfully.'))
            return redirect('core:settings:backup_list')
        except Exception as e:
            messages.error(request, _('Error deleting backup: {}').format(str(e)))
            return redirect('core:settings:backup_list')

    return render(request, 'modules/settings/backup_confirm_delete.html', {
        'filename': filename,
    })

from .log_utils import LogManager

@login_required
@permission_required('core.view_systemsetting')
def system_logs(request):
    """
    View system logs
    """
    # Get all available log types
    log_types = LogManager.get_log_types()

    return render(request, 'modules/settings/logs_list.html', {
        'log_types': log_types,
    })

@login_required
@permission_required('core.view_systemsetting')
def view_log(request, log_type):
    """
    View a specific log file
    """
    try:
        # Get the log content
        log_content = LogManager.get_log_content(log_type)

        # Get log type info
        log_types = LogManager.get_log_types()
        log_info = next((lt for lt in log_types if lt['name'] == log_type), None)

        return render(request, 'modules/settings/log_view.html', {
            'log_type': log_type,
            'log_content': log_content,
            'log_info': log_info,
        })
    except Exception as e:
        messages.error(request, _('Error viewing log: {}').format(str(e)))
        return redirect('core:settings:system_logs')

@login_required
@permission_required('core.view_systemsetting')
def download_log(request, log_type):
    """
    Download a log file
    """
    try:
        # Get log type info
        log_types = LogManager.get_log_types()
        log_info = next((lt for lt in log_types if lt['name'] == log_type), None)

        if not log_info:
            messages.error(request, _('Invalid log type.'))
            return redirect('core:settings:system_logs')

        log_file = log_info['file']

        if not os.path.exists(log_file):
            messages.error(request, _('Log file does not exist.'))
            return redirect('core:settings:system_logs')

        # Serve the file
        from django.http import FileResponse
        response = FileResponse(open(log_file, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{os.path.basename(log_file)}"'
        return response
    except Exception as e:
        messages.error(request, _('Error downloading log: {}').format(str(e)))
        return redirect('core:settings:system_logs')

@login_required
@permission_required('core.delete_systemsetting')
def clear_log(request, log_type):
    """
    Clear a log file
    """
    if request.method == 'POST':
        try:
            # Clear the log
            LogManager.clear_log(log_type)

            messages.success(request, _('Log cleared successfully.'))
            return redirect('core:settings:system_logs')
        except Exception as e:
            messages.error(request, _('Error clearing log: {}').format(str(e)))
            return redirect('core:settings:system_logs')

    # Get log type info
    log_types = LogManager.get_log_types()
    log_info = next((lt for lt in log_types if lt['name'] == log_type), None)

    return render(request, 'modules/settings/log_confirm_clear.html', {
        'log_type': log_type,
        'log_info': log_info,
    })
