import os
import datetime
import zipfile
import tempfile
import shutil
import json
import logging
from django.conf import settings
from django.core.management import call_command
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django.db import connection

# Get logger
logger = logging.getLogger(__name__)

# Define backup directory
BACKUP_DIR = os.path.join(settings.BASE_DIR, 'backups')

# Ensure backup directory exists
if not os.path.exists(BACKUP_DIR):
    os.makedirs(BACKUP_DIR)

class BackupManager:
    """
    Utility class for managing backups
    """
    
    @staticmethod
    def create_backup(backup_name=None, include_media=True):
        """
        Create a backup of the database and settings
        
        Args:
            backup_name (str): Optional name for the backup
            include_media (bool): Whether to include media files
            
        Returns:
            dict: Information about the created backup
        """
        try:
            # Generate backup name if not provided
            if not backup_name:
                timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
                backup_name = f"backup_{timestamp}"
            
            # Ensure backup name is safe
            backup_name = ''.join(c for c in backup_name if c.isalnum() or c in '_-')
            
            # Create a temporary directory for the backup files
            with tempfile.TemporaryDirectory() as temp_dir:
                # Create database dump
                db_file = os.path.join(temp_dir, 'db.json')
                with open(db_file, 'w') as f:
                    call_command('dumpdata', exclude=['contenttypes', 'auth.permission'], indent=4, output=db_file)
                
                # Create metadata file
                metadata = {
                    'created_at': timezone.now().isoformat(),
                    'django_version': settings.DJANGO_VERSION,
                    'database_engine': connection.vendor,
                    'include_media': include_media,
                    'backup_name': backup_name,
                }
                
                metadata_file = os.path.join(temp_dir, 'metadata.json')
                with open(metadata_file, 'w') as f:
                    json.dump(metadata, f, indent=4)
                
                # Create zip file
                zip_filename = f"{backup_name}.zip"
                zip_path = os.path.join(BACKUP_DIR, zip_filename)
                
                with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # Add database dump
                    zipf.write(db_file, 'db.json')
                    
                    # Add metadata
                    zipf.write(metadata_file, 'metadata.json')
                    
                    # Add media files if requested
                    if include_media and os.path.exists(settings.MEDIA_ROOT):
                        for root, dirs, files in os.walk(settings.MEDIA_ROOT):
                            for file in files:
                                file_path = os.path.join(root, file)
                                arcname = os.path.relpath(file_path, settings.MEDIA_ROOT)
                                zipf.write(file_path, os.path.join('media', arcname))
            
            # Get file size
            size_bytes = os.path.getsize(zip_path)
            size_mb = size_bytes / (1024 * 1024)
            
            # Log success
            logger.info(f"Backup created successfully: {zip_filename} ({size_mb:.2f} MB)")
            
            return {
                'filename': zip_filename,
                'path': zip_path,
                'size': f"{size_mb:.2f} MB",
                'created_at': timezone.now(),
                'include_media': include_media,
            }
            
        except Exception as e:
            logger.error(f"Error creating backup: {str(e)}")
            raise
    
    @staticmethod
    def restore_backup(backup_file, confirm=False):
        """
        Restore from a backup file
        
        Args:
            backup_file: The backup file (either a path or a file object)
            confirm (bool): Confirmation to proceed with restore
            
        Returns:
            bool: True if successful
        """
        if not confirm:
            raise ValueError(_("Restoration must be confirmed"))
        
        try:
            # Create a temporary directory for extraction
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract the zip file
                if isinstance(backup_file, str):
                    # It's a path
                    backup_path = backup_file
                    if not os.path.exists(backup_path):
                        backup_path = os.path.join(BACKUP_DIR, backup_file)
                else:
                    # It's a file object
                    backup_path = os.path.join(temp_dir, 'uploaded_backup.zip')
                    with open(backup_path, 'wb') as f:
                        for chunk in backup_file.chunks():
                            f.write(chunk)
                
                # Extract the zip file
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Read metadata
                metadata_file = os.path.join(temp_dir, 'metadata.json')
                if not os.path.exists(metadata_file):
                    raise ValueError(_("Invalid backup file: metadata.json not found"))
                
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)
                
                # Check database dump
                db_file = os.path.join(temp_dir, 'db.json')
                if not os.path.exists(db_file):
                    raise ValueError(_("Invalid backup file: db.json not found"))
                
                # Restore media files if included in the backup
                if metadata.get('include_media', False) and os.path.exists(os.path.join(temp_dir, 'media')):
                    # Backup current media directory
                    if os.path.exists(settings.MEDIA_ROOT):
                        media_backup = os.path.join(temp_dir, 'media_backup')
                        shutil.copytree(settings.MEDIA_ROOT, media_backup)
                    
                    # Clear current media directory
                    if os.path.exists(settings.MEDIA_ROOT):
                        shutil.rmtree(settings.MEDIA_ROOT)
                        os.makedirs(settings.MEDIA_ROOT)
                    
                    # Copy media files from backup
                    media_src = os.path.join(temp_dir, 'media')
                    for item in os.listdir(media_src):
                        s = os.path.join(media_src, item)
                        d = os.path.join(settings.MEDIA_ROOT, item)
                        if os.path.isdir(s):
                            shutil.copytree(s, d)
                        else:
                            shutil.copy2(s, d)
                
                # Restore database
                call_command('flush', interactive=False)
                call_command('loaddata', db_file)
                
                # Log success
                logger.info(f"Backup restored successfully from {os.path.basename(backup_path)}")
                
                return True
                
        except Exception as e:
            logger.error(f"Error restoring backup: {str(e)}")
            raise
    
    @staticmethod
    def get_backups():
        """
        Get a list of available backups
        
        Returns:
            list: List of backup information dictionaries
        """
        backups = []
        
        try:
            for filename in os.listdir(BACKUP_DIR):
                if filename.endswith('.zip'):
                    file_path = os.path.join(BACKUP_DIR, filename)
                    
                    # Get file stats
                    stats = os.stat(file_path)
                    size_bytes = stats.st_size
                    size_mb = size_bytes / (1024 * 1024)
                    created_at = datetime.datetime.fromtimestamp(stats.st_ctime, tz=timezone.get_current_timezone())
                    
                    # Try to extract metadata
                    metadata = {}
                    try:
                        with zipfile.ZipFile(file_path, 'r') as zipf:
                            if 'metadata.json' in zipf.namelist():
                                with zipf.open('metadata.json') as f:
                                    metadata = json.load(f)
                    except:
                        pass
                    
                    backups.append({
                        'filename': filename,
                        'path': file_path,
                        'size': f"{size_mb:.2f} MB",
                        'created_at': metadata.get('created_at', created_at),
                        'include_media': metadata.get('include_media', False),
                        'type': 'Full Backup' if metadata.get('include_media', False) else 'Database Only',
                    })
            
            # Sort by creation date (newest first)
            backups.sort(key=lambda x: x['created_at'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error getting backups: {str(e)}")
        
        return backups
    
    @staticmethod
    def delete_backup(filename):
        """
        Delete a backup file
        
        Args:
            filename (str): The filename of the backup to delete
            
        Returns:
            bool: True if successful
        """
        try:
            file_path = os.path.join(BACKUP_DIR, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Backup deleted: {filename}")
                return True
            else:
                logger.warning(f"Backup file not found: {filename}")
                return False
        except Exception as e:
            logger.error(f"Error deleting backup: {str(e)}")
            raise
