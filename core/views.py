from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from django.utils.translation import get_language
from django.contrib.auth import get_user_model
from companies.models import Company, Branch
from modules.models import Module, CompanyModule
from .models import SystemSetting, CompanySetting

User = get_user_model()

@login_required
def dashboard(request):
    """
    Main dashboard view
    """
    # Get user's company and branch
    company = request.user.company
    branch = request.user.branch

    # Get available modules
    if request.user.is_superuser:
        # For superusers, show all modules
        modules = Module.objects.all()
    elif company:
        # For users with company, show company modules
        company_modules = CompanyModule.objects.filter(
            company=company,
            is_active=True
        ).select_related('module')

        if company_modules.exists():
            modules = [cm.module for cm in company_modules]
        else:
            # If no company modules, show all modules for now (for development)
            modules = Module.objects.all()
    else:
        # For users without company, show core modules
        modules = Module.objects.filter(is_core=True)

    context = {
        'company': company,
        'branch': branch,
        'modules': modules,
        'LANGUAGE_CODE': get_language(),
    }

    return render(request, 'core/dashboard.html', context)

@login_required
def ajax_search(request):
    """
    AJAX search view that searches across multiple models
    """
    query = request.GET.get('q', '')
    search_type = request.GET.get('type', 'all')
    company_id = request.GET.get('company_id', None)

    if not query:
        return JsonResponse({'results': []})

    results = []

    # Filter by company if provided
    company_filter = Q(company_id=company_id) if company_id else Q()

    # Search in Users
    if search_type in ['all', 'users']:
        users = User.objects.filter(
            company_filter &
            (Q(username__icontains=query) |
             Q(first_name__icontains=query) |
             Q(last_name__icontains=query) |
             Q(email__icontains=query))
        )[:10]

        for user in users:
            results.append({
                'id': user.id,
                'text': f"{user.get_full_name()} ({user.username})",
                'type': 'user',
                'url': f"/users/edit/{user.id}/",
            })

    # Search in Companies
    if search_type in ['all', 'companies']:
        companies = Company.objects.filter(
            Q(name__icontains=query) |
            Q(code__icontains=query) |
            Q(tax_number__icontains=query)
        )[:10]

        for company in companies:
            results.append({
                'id': company.id,
                'text': company.name,
                'type': 'company',
                'url': f"/companies/edit/{company.id}/",
            })

    # Search in Branches
    if search_type in ['all', 'branches']:
        branches = Branch.objects.filter(
            company_filter &
            (Q(name__icontains=query) |
             Q(code__icontains=query))
        )[:10]

        for branch in branches:
            results.append({
                'id': branch.id,
                'text': f"{branch.name} ({branch.company.name})",
                'type': 'branch',
                'url': f"/companies/branch/edit/{branch.id}/",
            })

    # Search in Modules
    if search_type in ['all', 'modules']:
        modules = Module.objects.filter(
            Q(name__icontains=query) |
            Q(code__icontains=query) |
            Q(description__icontains=query)
        )[:10]

        for module in modules:
            results.append({
                'id': module.id,
                'text': module.name,
                'type': 'module',
                'url': f"/modules/",  # Link to modules list with this module highlighted
            })

    return JsonResponse({'results': results})
