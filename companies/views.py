from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.http import HttpResponseRedirect
from django.urls import reverse
from .models import Company, Branch
from .forms import CompanyForm, BranchForm
from django.contrib.auth import get_user_model

User = get_user_model()

@login_required
@permission_required('companies.view_company')
def company_list(request):
    """
    List all companies
    """
    # Show all companies for superusers, only user's company for others
    if request.user.is_superuser:
        companies = Company.objects.all()
    else:
        companies = Company.objects.filter(pk=request.user.company.pk) if request.user.company else Company.objects.none()

    return render(request, 'companies/company_list.html', {'companies': companies})

@login_required
@permission_required('companies.add_company')
def company_create(request):
    """
    Create a new company
    """
    if request.method == 'POST':
        form = CompanyForm(request.POST, request.FILES)
        if form.is_valid():
            company = form.save()
            messages.success(request, _('Company created successfully.'))
            return redirect('companies:company_list')
    else:
        form = CompanyForm()

    return render(request, 'companies/company_form.html', {'form': form})

@login_required
@permission_required('companies.change_company')
def company_edit(request, pk):
    """
    Edit an existing company
    """
    company = get_object_or_404(Company, pk=pk)

    # Check if user has permission to edit this company
    if not request.user.is_superuser and request.user.company != company:
        messages.error(request, _('You do not have permission to edit this company.'))
        return redirect('companies:company_list')

    if request.method == 'POST':
        form = CompanyForm(request.POST, request.FILES, instance=company)
        if form.is_valid():
            form.save()
            messages.success(request, _('Company updated successfully.'))
            return redirect('companies:company_list')
    else:
        form = CompanyForm(instance=company)

    return render(request, 'companies/company_form.html', {'form': form, 'company': company})

@login_required
@permission_required('companies.delete_company')
def company_delete(request, pk):
    """
    Delete a company
    """
    company = get_object_or_404(Company, pk=pk)

    # Check if user has permission to delete this company
    if not request.user.is_superuser:
        messages.error(request, _('You do not have permission to delete companies.'))
        return redirect('companies:company_list')

    if request.method == 'POST':
        company.delete()
        messages.success(request, _('Company deleted successfully.'))
        return redirect('companies:company_list')

    return render(request, 'companies/company_confirm_delete.html', {'company': company})

@login_required
@permission_required('companies.view_branch')
def branch_list(request):
    """
    List all branches
    """
    # Show all branches for superusers, only branches of user's company for others
    if request.user.is_superuser:
        branches = Branch.objects.all()
    else:
        branches = Branch.objects.filter(company=request.user.company) if request.user.company else Branch.objects.none()

    return render(request, 'companies/branch_list.html', {'branches': branches})

@login_required
@permission_required('companies.add_branch')
def branch_create(request):
    """
    Create a new branch
    """
    if request.method == 'POST':
        form = BranchForm(request.POST)
        if form.is_valid():
            # Set company to user's company if not superuser
            branch = form.save(commit=False)
            if not request.user.is_superuser and not branch.company:
                branch.company = request.user.company
            branch.save()

            messages.success(request, _('Branch created successfully.'))
            return redirect('companies:branch_list')
    else:
        # Pre-select user's company if not superuser
        initial = {}
        if not request.user.is_superuser and request.user.company:
            initial = {'company': request.user.company}

        form = BranchForm(initial=initial)

        # Limit company choices for non-superusers
        if not request.user.is_superuser and request.user.company:
            form.fields['company'].queryset = Company.objects.filter(pk=request.user.company.pk)

    return render(request, 'companies/branch_form.html', {'form': form})

@login_required
@permission_required('companies.change_branch')
def branch_edit(request, pk):
    """
    Edit an existing branch
    """
    branch = get_object_or_404(Branch, pk=pk)

    # Check if user has permission to edit this branch
    if not request.user.is_superuser and request.user.company != branch.company:
        messages.error(request, _('You do not have permission to edit this branch.'))
        return redirect('companies:branch_list')

    if request.method == 'POST':
        form = BranchForm(request.POST, instance=branch)
        if form.is_valid():
            form.save()
            messages.success(request, _('Branch updated successfully.'))
            return redirect('companies:branch_list')
    else:
        form = BranchForm(instance=branch)

        # Limit company choices for non-superusers
        if not request.user.is_superuser:
            form.fields['company'].queryset = Company.objects.filter(pk=request.user.company.pk)

    return render(request, 'companies/branch_form.html', {'form': form, 'branch': branch})

@login_required
@permission_required('companies.delete_branch')
def branch_delete(request, pk):
    """
    Delete a branch
    """
    branch = get_object_or_404(Branch, pk=pk)

    # Check if user has permission to delete this branch
    if not request.user.is_superuser and request.user.company != branch.company:
        messages.error(request, _('You do not have permission to delete this branch.'))
        return redirect('companies:branch_list')

    if request.method == 'POST':
        branch.delete()
        messages.success(request, _('Branch deleted successfully.'))
        return redirect('companies:branch_list')

    return render(request, 'companies/branch_confirm_delete.html', {'branch': branch})


@login_required
@permission_required('companies.view_company')
def company_print(request, pk):
    """
    Print company information
    """
    company = get_object_or_404(Company, pk=pk)

    # Check if user has permission to view this company
    if not request.user.is_superuser and request.user.company != company:
        messages.error(request, _('You do not have permission to view this company.'))
        return redirect('companies:company_list')

    context = {
        'company': company,
        'now': timezone.now(),
    }

    return render(request, 'companies/company_print.html', context)


@login_required
@permission_required('companies.view_branch')
def branch_print(request, pk):
    """
    Print branch information
    """
    branch = get_object_or_404(Branch, pk=pk)

    # Check if user has permission to view this branch
    if not request.user.is_superuser and request.user.company != branch.company:
        messages.error(request, _('You do not have permission to view this branch.'))
        return redirect('companies:branch_list')

    context = {
        'branch': branch,
        'now': timezone.now(),
    }

    return render(request, 'companies/branch_print.html', context)


@login_required
def set_active_company_branch(request, company_id, branch_id):
    """
    Set active company and branch for the current user
    """
    # Get company and branch
    company = get_object_or_404(Company, pk=company_id)
    branch = get_object_or_404(Branch, pk=branch_id, company=company)

    # Check if branch belongs to company
    if branch.company != company:
        messages.error(request, _('Branch does not belong to the selected company.'))
        return redirect('core:dashboard')

    # Update user's company and branch
    user = request.user
    user.company = company
    user.branch = branch
    user.save()

    # Store in session for quick access
    request.session['active_company_id'] = company.id
    request.session['active_branch_id'] = branch.id

    # Show success message
    messages.success(request, _('Active company set to {} and branch set to {}.').format(company.name, branch.name))

    # Redirect back to referring page or dashboard
    next_url = request.GET.get('next', reverse('core:dashboard'))
    return HttpResponseRedirect(next_url)
