from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Company, Branch

class BranchInline(admin.TabularInline):
    """
    Inline admin for Branch model
    """
    model = Branch
    extra = 1

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    """
    Admin for Company model
    """
    list_display = ('name', 'code', 'tax_number', 'phone', 'email', 'active')
    list_filter = ('active',)
    search_fields = ('name', 'code', 'tax_number', 'phone', 'email')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'tax_number', 'logo')
        }),
        (_('Contact Information'), {
            'fields': ('address', 'phone', 'email', 'website')
        }),
        (_('Status'), {
            'fields': ('active',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    inlines = [BranchInline]

@admin.register(Branch)
class BranchAdmin(admin.ModelAdmin):
    """
    Admin for Branch model
    """
    list_display = ('name', 'company', 'code', 'phone', 'email', 'active')
    list_filter = ('active', 'company')
    search_fields = ('name', 'code', 'phone', 'email', 'company__name')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'code')
        }),
        (_('Contact Information'), {
            'fields': ('address', 'phone', 'email', 'manager')
        }),
        (_('Status'), {
            'fields': ('active',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
