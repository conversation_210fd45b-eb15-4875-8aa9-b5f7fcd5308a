# Generated by Django 5.2.1 on 2025-05-21 14:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Company',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('name_en', models.CharField(max_length=100, null=True, verbose_name='Name')),
                ('name_ar', models.CharField(max_length=100, null=True, verbose_name='Name')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Code')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='Tax Number')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('address_en', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('address_ar', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email')),
                ('website', models.URLField(blank=True, null=True, verbose_name='Website')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/', verbose_name='Logo')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Company',
                'verbose_name_plural': 'Companies',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('name_en', models.CharField(max_length=100, null=True, verbose_name='Name')),
                ('name_ar', models.CharField(max_length=100, null=True, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('address_en', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('address_ar', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email')),
                ('manager', models.CharField(blank=True, max_length=100, null=True, verbose_name='Manager')),
                ('active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='branches', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Branch',
                'verbose_name_plural': 'Branches',
                'ordering': ['company', 'name'],
                'unique_together': {('company', 'code')},
            },
        ),
    ]
