from django.db import models
from django.utils.translation import gettext_lazy as _

class Company(models.Model):
    """
    Model for companies in the ERP system
    """
    name = models.CharField(
        max_length=100,
        verbose_name=_('Name')
    )
    code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name=_('Code')
    )
    tax_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Tax Number')
    )
    commercial_register = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Commercial Register')
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Phone')
    )
    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('Email')
    )
    website = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Website')
    )
    facebook = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Facebook Page')
    )
    twitter = models.URLField(
        blank=True,
        null=True,
        verbose_name=_('Twitter Page')
    )
    logo = models.ImageField(
        upload_to='company_logos/',
        blank=True,
        null=True,
        verbose_name=_('Logo')
    )
    active = models.BooleanField(
        default=True,
        verbose_name=_('Active')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Company')
        verbose_name_plural = _('Companies')
        ordering = ['name']

    def __str__(self):
        return self.name


class Branch(models.Model):
    """
    Model for company branches
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='branches',
        verbose_name=_('Company')
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_('Name')
    )
    code = models.CharField(
        max_length=20,
        verbose_name=_('Code')
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )
    phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name=_('Phone')
    )
    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name=_('Email')
    )
    manager = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name=_('Manager')
    )
    header_image = models.ImageField(
        upload_to='branch_headers/',
        blank=True,
        null=True,
        verbose_name=_('Header Image'),
        help_text=_('Image to be used as header in documents and reports')
    )
    footer_image = models.ImageField(
        upload_to='branch_footers/',
        blank=True,
        null=True,
        verbose_name=_('Footer Image'),
        help_text=_('Image to be used as footer in documents and reports')
    )
    active = models.BooleanField(
        default=True,
        verbose_name=_('Active')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Branch')
        verbose_name_plural = _('Branches')
        ordering = ['company', 'name']
        unique_together = ['company', 'code']

    def __str__(self):
        return f"{self.company.name} - {self.name}"
