from django.urls import path
from . import views

app_name = 'companies'

urlpatterns = [
    path('', views.company_list, name='company_list'),
    path('create/', views.company_create, name='company_create'),
    path('edit/<int:pk>/', views.company_edit, name='company_edit'),
    path('delete/<int:pk>/', views.company_delete, name='company_delete'),
    path('print/<int:pk>/', views.company_print, name='company_print'),
    path('branches/', views.branch_list, name='branch_list'),
    path('branches/create/', views.branch_create, name='branch_create'),
    path('branches/edit/<int:pk>/', views.branch_edit, name='branch_edit'),
    path('branches/delete/<int:pk>/', views.branch_delete, name='branch_delete'),
    path('branches/print/<int:pk>/', views.branch_print, name='branch_print'),
    path('set-active/<int:company_id>/<int:branch_id>/', views.set_active_company_branch, name='set_active_company_branch'),
]
