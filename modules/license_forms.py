from django import forms
from django.utils.translation import gettext_lazy as _
from .models import ModuleLicense, Module
from companies.models import Company

class ModuleLicenseForm(forms.ModelForm):
    """
    Form for creating/editing module licenses
    """
    class Meta:
        model = ModuleLicense
        fields = [
            'company', 'module', 'license_type', 
            'start_date', 'end_date', 'max_users', 'is_active'
        ]
        widgets = {
            'company': forms.Select(attrs={'class': 'form-select'}),
            'module': forms.Select(attrs={'class': 'form-select'}),
            'license_type': forms.Select(attrs={'class': 'form-select'}),
            'start_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
            'max_users': forms.NumberInput(attrs={'class': 'form-control', 'min': '1'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set required fields
        self.fields['company'].required = True
        self.fields['module'].required = True
        self.fields['license_type'].required = True
        self.fields['start_date'].required = True
        self.fields['end_date'].required = True
        
        # Set help texts
        self.fields['max_users'].help_text = _('Maximum number of users allowed for this license')
        self.fields['is_active'].help_text = _('Whether this license is active')
        
        # Filter modules that are not core
        self.fields['module'].queryset = Module.objects.filter(is_core=False)
        
        # Filter active companies
        self.fields['company'].queryset = Company.objects.filter(active=True)
    
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        
        if start_date and end_date and start_date > end_date:
            raise forms.ValidationError(_('End date must be after start date'))
        
        return cleaned_data
