# Generated by Django 5.2.1 on 2025-05-25 11:38

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounting', '0006_checkbook_remaining_count_checkbook_used_count'),
        ('companies', '0003_branch_footer_image_branch_header_image'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sku', models.CharField(help_text='Stock Keeping Unit', max_length=50, verbose_name='SKU')),
                ('name', models.CharField(max_length=200, verbose_name='Product Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('product_type', models.CharField(choices=[('product', 'Product'), ('service', 'Service'), ('consumable', 'Consumable')], default='product', max_length=20, verbose_name='Product Type')),
                ('cost_price', models.DecimalField(decimal_places=2, default=0, max_digits=18, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Cost Price')),
                ('selling_price', models.DecimalField(decimal_places=2, default=0, max_digits=18, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Selling Price')),
                ('track_stock', models.BooleanField(default=True, verbose_name='Track Stock')),
                ('min_stock_level', models.DecimalField(decimal_places=2, default=0, max_digits=18, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Minimum Stock Level')),
                ('max_stock_level', models.DecimalField(decimal_places=2, default=0, max_digits=18, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Maximum Stock Level')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('can_be_sold', models.BooleanField(default=True, verbose_name='Can be Sold')),
                ('can_be_purchased', models.BooleanField(default=True, verbose_name='Can be Purchased')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_products', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Product',
                'verbose_name_plural': 'Products',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductAccountLink',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cogs_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cogs_products', to='accounting.account', verbose_name='Cost of Goods Sold Account')),
                ('expense_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expense_products', to='accounting.account', verbose_name='Expense Account')),
                ('income_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='income_products', to='accounting.account', verbose_name='Income Account')),
                ('inventory_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_products', to='accounting.account', verbose_name='Inventory Account')),
                ('product', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='account_link', to='inventory.product', verbose_name='Product')),
            ],
            options={
                'verbose_name': 'Product Account Link',
                'verbose_name_plural': 'Product Account Links',
            },
        ),
        migrations.CreateModel(
            name='ProductBarcode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('barcode', models.CharField(max_length=50, unique=True, verbose_name='Barcode')),
                ('barcode_type', models.CharField(choices=[('EAN13', 'EAN-13'), ('EAN8', 'EAN-8'), ('UPC', 'UPC'), ('CODE128', 'Code 128'), ('CODE39', 'Code 39'), ('QR', 'QR Code')], default='EAN13', max_length=20, verbose_name='Barcode Type')),
                ('is_primary', models.BooleanField(default=False, verbose_name='Is Primary Barcode')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='barcodes', to='inventory.product', verbose_name='Product')),
            ],
            options={
                'verbose_name': 'Product Barcode',
                'verbose_name_plural': 'Product Barcodes',
                'ordering': ['-is_primary', 'barcode'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Category Code')),
                ('name', models.CharField(max_length=100, verbose_name='Category Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_categories', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Product Category',
                'verbose_name_plural': 'Product Categories',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.AddField(
            model_name='product',
            name='category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='inventory.productcategory', verbose_name='Category'),
        ),
        migrations.CreateModel(
            name='ProductSubcategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Subcategory Code')),
                ('name', models.CharField(max_length=100, verbose_name='Subcategory Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='inventory.productcategory', verbose_name='Main Category')),
            ],
            options={
                'verbose_name': 'Product Subcategory',
                'verbose_name_plural': 'Product Subcategories',
                'ordering': ['name'],
                'unique_together': {('category', 'code')},
            },
        ),
        migrations.AddField(
            model_name='product',
            name='subcategory',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='products', to='inventory.productsubcategory', verbose_name='Subcategory'),
        ),
        migrations.CreateModel(
            name='UnitOfMeasure',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=10, verbose_name='Unit Code')),
                ('name', models.CharField(max_length=50, verbose_name='Unit Name')),
                ('symbol', models.CharField(max_length=10, verbose_name='Symbol')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='units_of_measure', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Unit of Measure',
                'verbose_name_plural': 'Units of Measure',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.AddField(
            model_name='product',
            name='unit_of_measure',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='inventory.unitofmeasure', verbose_name='Unit of Measure'),
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Warehouse Code')),
                ('name', models.CharField(max_length=100, verbose_name='Warehouse Name')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='warehouses', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouses', to='companies.company', verbose_name='Company')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='Warehouse Manager')),
            ],
            options={
                'verbose_name': 'Warehouse',
                'verbose_name_plural': 'Warehouses',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'Stock In'), ('out', 'Stock Out'), ('transfer', 'Transfer'), ('adjustment', 'Adjustment')], max_length=20, verbose_name='Movement Type')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=18, validators=[django.core.validators.MinValueValidator(Decimal('0.01'))], verbose_name='Quantity')),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0, max_digits=18, validators=[django.core.validators.MinValueValidator(Decimal('0'))], verbose_name='Unit Cost')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Total Cost')),
                ('reference_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='Reference Number')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_movements', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.product', verbose_name='Product')),
                ('destination_warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='incoming_transfers', to='inventory.warehouse', verbose_name='Destination Warehouse')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.warehouse', verbose_name='Warehouse')),
            ],
            options={
                'verbose_name': 'Stock Movement',
                'verbose_name_plural': 'Stock Movements',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductPermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permission_type', models.CharField(choices=[('view', 'View'), ('create', 'Create'), ('edit', 'Edit'), ('delete', 'Delete'), ('manage_stock', 'Manage Stock'), ('view_cost', 'View Cost'), ('edit_price', 'Edit Price')], max_length=20, verbose_name='Permission Type')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('granted_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_product_permissions', to=settings.AUTH_USER_MODEL, verbose_name='Granted By')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permissions', to='inventory.product', verbose_name='Product')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_permissions', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'Product Permission',
                'verbose_name_plural': 'Product Permissions',
                'ordering': ['product', 'user', 'permission_type'],
                'unique_together': {('product', 'user', 'permission_type')},
            },
        ),
        migrations.AlterUniqueTogether(
            name='product',
            unique_together={('company', 'sku')},
        ),
    ]
