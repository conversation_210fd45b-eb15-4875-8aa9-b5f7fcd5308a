"""
إدارة وحدة المخازن
Inventory Module Admin
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import (
    Warehouse, ProductCategory, ProductSubcategory, UnitOfMeasure,
    Product, ProductBarcode, StockMovement, ProductPermission, ProductAccountLink
)


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    """
    إدارة المستودعات
    Warehouse Admin
    """
    list_display = ('code', 'name', 'company', 'branch', 'manager', 'is_active', 'created_at')
    list_filter = ('company', 'branch', 'is_active', 'created_at')
    search_fields = ('code', 'name', 'address')
    ordering = ('company', 'name')
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('company', 'branch', 'code', 'name')
        }),
        (_('Details'), {
            'fields': ('address', 'manager', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    """
    إدارة المجموعات الرئيسية
    Product Category Admin
    """
    list_display = ('code', 'name', 'company', 'products_count', 'subcategories_count', 'is_active', 'created_at')
    list_filter = ('company', 'is_active', 'created_at')
    search_fields = ('code', 'name', 'description')
    ordering = ('company', 'name')
    readonly_fields = ('created_at', 'updated_at')
    
    def products_count(self, obj):
        return obj.products.count()
    products_count.short_description = _('Products Count')
    
    def subcategories_count(self, obj):
        return obj.subcategories.count()
    subcategories_count.short_description = _('Subcategories Count')


class ProductSubcategoryInline(admin.TabularInline):
    """
    المجموعات الفرعية كـ inline
    Subcategories Inline
    """
    model = ProductSubcategory
    extra = 1
    fields = ('code', 'name', 'is_active')


@admin.register(ProductSubcategory)
class ProductSubcategoryAdmin(admin.ModelAdmin):
    """
    إدارة المجموعات الفرعية
    Product Subcategory Admin
    """
    list_display = ('code', 'name', 'category', 'products_count', 'is_active', 'created_at')
    list_filter = ('category__company', 'category', 'is_active', 'created_at')
    search_fields = ('code', 'name', 'description', 'category__name')
    ordering = ('category', 'name')
    readonly_fields = ('created_at', 'updated_at')
    
    def products_count(self, obj):
        return obj.products.count()
    products_count.short_description = _('Products Count')


@admin.register(UnitOfMeasure)
class UnitOfMeasureAdmin(admin.ModelAdmin):
    """
    إدارة وحدات القياس
    Unit of Measure Admin
    """
    list_display = ('code', 'name', 'symbol', 'company', 'is_active', 'created_at')
    list_filter = ('company', 'is_active', 'created_at')
    search_fields = ('code', 'name', 'symbol')
    ordering = ('company', 'name')
    readonly_fields = ('created_at', 'updated_at')


class ProductBarcodeInline(admin.TabularInline):
    """
    باركودات المنتج كـ inline
    Product Barcodes Inline
    """
    model = ProductBarcode
    extra = 1
    fields = ('barcode', 'barcode_type', 'is_primary')


class ProductAccountLinkInline(admin.StackedInline):
    """
    ربط حسابات المنتج كـ inline
    Product Account Link Inline
    """
    model = ProductAccountLink
    extra = 0
    fields = ('income_account', 'expense_account', 'inventory_account', 'cogs_account')


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """
    إدارة المنتجات
    Product Admin
    """
    list_display = (
        'sku', 'name', 'category', 'subcategory', 'product_type',
        'cost_price', 'selling_price', 'current_stock_display', 'is_active', 'created_at'
    )
    list_filter = (
        'company', 'category', 'subcategory', 'product_type',
        'is_active', 'can_be_sold', 'can_be_purchased', 'track_stock', 'created_at'
    )
    search_fields = ('sku', 'name', 'description')
    ordering = ('company', 'name')
    readonly_fields = ('created_by', 'created_at', 'updated_at')
    inlines = [ProductBarcodeInline, ProductAccountLinkInline]
    
    fieldsets = (
        (None, {
            'fields': ('company', 'category', 'subcategory', 'sku', 'name', 'description')
        }),
        (_('Product Details'), {
            'fields': ('product_type', 'unit_of_measure')
        }),
        (_('Pricing'), {
            'fields': ('cost_price', 'selling_price')
        }),
        (_('Stock Management'), {
            'fields': ('track_stock', 'min_stock_level', 'max_stock_level')
        }),
        (_('Status'), {
            'fields': ('is_active', 'can_be_sold', 'can_be_purchased')
        }),
        (_('Audit'), {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def current_stock_display(self, obj):
        if not obj.track_stock:
            return _('Not Tracked')
        
        current_stock = obj.get_current_stock()
        if obj.is_low_stock():
            return format_html(
                '<span style="color: red; font-weight: bold;">{}</span>',
                current_stock
            )
        return current_stock
    current_stock_display.short_description = _('Current Stock')


@admin.register(ProductBarcode)
class ProductBarcodeAdmin(admin.ModelAdmin):
    """
    إدارة باركودات المنتجات
    Product Barcode Admin
    """
    list_display = ('barcode', 'product', 'barcode_type', 'is_primary', 'created_at')
    list_filter = ('barcode_type', 'is_primary', 'created_at')
    search_fields = ('barcode', 'product__sku', 'product__name')
    ordering = ('product', '-is_primary', 'barcode')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    """
    إدارة حركات المخزون
    Stock Movement Admin
    """
    list_display = (
        'reference_number', 'product', 'warehouse', 'movement_type',
        'quantity', 'unit_cost', 'total_cost', 'created_by', 'created_at'
    )
    list_filter = (
        'company', 'warehouse', 'movement_type', 'created_at'
    )
    search_fields = (
        'reference_number', 'product__sku', 'product__name', 'notes'
    )
    ordering = ('-created_at',)
    readonly_fields = ('total_cost', 'created_by', 'created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            'fields': ('company', 'warehouse', 'product', 'movement_type')
        }),
        (_('Quantity & Cost'), {
            'fields': ('quantity', 'unit_cost', 'total_cost')
        }),
        (_('Details'), {
            'fields': ('reference_number', 'destination_warehouse', 'notes')
        }),
        (_('Audit'), {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(ProductPermission)
class ProductPermissionAdmin(admin.ModelAdmin):
    """
    إدارة صلاحيات المنتجات
    Product Permission Admin
    """
    list_display = ('product', 'user', 'permission_type', 'granted_by', 'created_at')
    list_filter = ('permission_type', 'created_at')
    search_fields = ('product__sku', 'product__name', 'user__username')
    ordering = ('product', 'user', 'permission_type')
    readonly_fields = ('created_at',)


@admin.register(ProductAccountLink)
class ProductAccountLinkAdmin(admin.ModelAdmin):
    """
    إدارة ربط حسابات المنتجات
    Product Account Link Admin
    """
    list_display = ('product', 'income_account', 'expense_account', 'inventory_account', 'cogs_account')
    search_fields = ('product__sku', 'product__name')
    ordering = ('product',)
    readonly_fields = ('created_at', 'updated_at')


# تخصيص عنوان الإدارة
admin.site.site_header = _('ERP System Administration')
admin.site.site_title = _('ERP Admin')
admin.site.index_title = _('Welcome to ERP Administration')
