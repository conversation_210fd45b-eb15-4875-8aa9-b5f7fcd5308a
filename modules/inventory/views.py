"""
عروض وحدة المخازن
Inventory Module Views
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.http import JsonResponse, HttpResponse
from django.db.models import Q, Sum, Count
from django.core.paginator import Paginator
from django.template.loader import render_to_string
from decimal import Decimal

from core.models import Company
from .models import (
    Warehouse, ProductCategory, ProductSubcategory, UnitOfMeasure,
    Product, ProductBarcode, StockMovement, ProductPermission, ProductAccountLink
)
from .forms import (
    WarehouseForm, ProductCategoryForm, ProductSubcategoryForm, UnitOfMeasureForm,
    ProductForm, ProductBarcodeForm, StockMovementForm
)


def get_user_company(request):
    """
    دالة مساعدة للحصول على شركة المستخدم
    Helper function to get user's company
    """
    if hasattr(request.user, 'company') and request.user.company:
        return request.user.company
    return Company.objects.first()


@login_required
def inventory_dashboard(request):
    """
    لوحة تحكم المخازن
    Inventory Dashboard
    """
    company = get_user_company(request)

    # إحصائيات عامة
    total_products = Product.objects.filter(company=company).count()
    total_categories = ProductCategory.objects.filter(company=company).count()
    total_warehouses = Warehouse.objects.filter(company=company).count()

    # المنتجات منخفضة المخزون
    low_stock_products = []
    for product in Product.objects.filter(company=company, track_stock=True):
        if product.is_low_stock():
            low_stock_products.append({
                'product': product,
                'current_stock': product.get_current_stock(),
                'min_stock': product.min_stock_level
            })

    # آخر حركات المخزون
    recent_movements = StockMovement.objects.filter(
        company=company
    ).select_related('product', 'warehouse')[:10]

    context = {
        'total_products': total_products,
        'total_categories': total_categories,
        'total_warehouses': total_warehouses,
        'low_stock_count': len(low_stock_products),
        'low_stock_products': low_stock_products[:5],  # أول 5 فقط
        'recent_movements': recent_movements,
    }

    return render(request, 'modules/inventory/dashboard.html', context)


# ==================== المستودعات ====================

@login_required
def warehouse_list(request):
    """
    قائمة المستودعات
    Warehouse List
    """
    company = get_user_company(request)
    warehouses = Warehouse.objects.filter(company=company)

    # البحث
    search = request.GET.get('search')
    if search:
        warehouses = warehouses.filter(
            Q(name__icontains=search) | Q(code__icontains=search)
        )

    # التصفية
    is_active = request.GET.get('is_active')
    if is_active:
        warehouses = warehouses.filter(is_active=is_active == 'true')

    # الترقيم
    paginator = Paginator(warehouses, 20)
    page_number = request.GET.get('page')
    warehouses = paginator.get_page(page_number)

    context = {
        'warehouses': warehouses,
        'search': search,
        'is_active': is_active,
    }

    return render(request, 'modules/inventory/warehouse_list.html', context)


@login_required
def warehouse_create(request):
    """
    إنشاء مستودع جديد
    Create New Warehouse
    """
    company = get_user_company(request)

    if request.method == 'POST':
        form = WarehouseForm(request.POST, company=company)
        if form.is_valid():
            warehouse = form.save(commit=False)
            warehouse.company = company
            warehouse.save()
            messages.success(request, _('Warehouse created successfully.'))
            return redirect('inventory:warehouse_list')
    else:
        form = WarehouseForm(company=company)

    context = {
        'form': form,
        'title': _('Create New Warehouse'),
    }

    return render(request, 'modules/inventory/warehouse_form.html', context)


@login_required
def warehouse_detail(request, warehouse_id):
    """
    تفاصيل المستودع
    Warehouse Detail
    """
    company = get_user_company(request)
    warehouse = get_object_or_404(Warehouse, id=warehouse_id, company=company)

    # إحصائيات المستودع
    total_products = Product.objects.filter(
        company=company,
        stock_movements__warehouse=warehouse
    ).distinct().count()

    total_stock_value = StockMovement.objects.filter(
        warehouse=warehouse,
        movement_type='in'
    ).aggregate(total=Sum('total_cost'))['total'] or 0

    # آخر حركات المخزون
    recent_movements = StockMovement.objects.filter(
        warehouse=warehouse
    ).select_related('product')[:10]

    context = {
        'warehouse': warehouse,
        'total_products': total_products,
        'total_stock_value': total_stock_value,
        'recent_movements': recent_movements,
    }

    return render(request, 'modules/inventory/warehouse_detail.html', context)


@login_required
def warehouse_edit(request, warehouse_id):
    """
    تعديل المستودع
    Edit Warehouse
    """
    company = get_user_company(request)
    warehouse = get_object_or_404(Warehouse, id=warehouse_id, company=company)

    if request.method == 'POST':
        form = WarehouseForm(request.POST, instance=warehouse, company=company)
        if form.is_valid():
            form.save()
            messages.success(request, _('Warehouse updated successfully.'))
            return redirect('inventory:warehouse_detail', warehouse_id=warehouse.id)
    else:
        form = WarehouseForm(instance=warehouse, company=company)

    context = {
        'form': form,
        'warehouse': warehouse,
        'title': _('Edit Warehouse'),
    }

    return render(request, 'modules/inventory/warehouse_form.html', context)


# ==================== المجموعات الرئيسية ====================

@login_required
def category_list(request):
    """
    قائمة المجموعات الرئيسية
    Category List
    """
    company = get_user_company(request)
    categories = ProductCategory.objects.filter(company=company)

    # البحث
    search = request.GET.get('search')
    if search:
        categories = categories.filter(
            Q(name__icontains=search) | Q(code__icontains=search)
        )

    # التصفية
    is_active = request.GET.get('is_active')
    if is_active:
        categories = categories.filter(is_active=is_active == 'true')

    # إضافة عدد المنتجات لكل مجموعة
    categories = categories.annotate(
        products_count=Count('products'),
        subcategories_count=Count('subcategories')
    )

    # الترقيم
    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    categories = paginator.get_page(page_number)

    context = {
        'categories': categories,
        'search': search,
        'is_active': is_active,
    }

    return render(request, 'modules/inventory/category_list.html', context)


@login_required
def category_create(request):
    """
    إنشاء مجموعة رئيسية جديدة
    Create New Category
    """
    company = get_user_company(request)

    if request.method == 'POST':
        form = ProductCategoryForm(request.POST)
        if form.is_valid():
            category = form.save(commit=False)
            category.company = company
            category.save()
            messages.success(request, _('Category created successfully.'))
            return redirect('inventory:category_list')
    else:
        form = ProductCategoryForm()

    context = {
        'form': form,
        'title': _('Create New Category'),
    }

    return render(request, 'modules/inventory/category_form.html', context)


@login_required
def category_detail(request, category_id):
    """
    تفاصيل المجموعة الرئيسية
    Category Detail
    """
    company = get_user_company(request)
    category = get_object_or_404(ProductCategory, id=category_id, company=company)

    # المجموعات الفرعية
    subcategories = category.subcategories.filter(is_active=True)

    # المنتجات
    products = category.products.filter(is_active=True)[:10]

    context = {
        'category': category,
        'subcategories': subcategories,
        'products': products,
        'subcategories_count': subcategories.count(),
        'products_count': category.products.count(),
    }

    return render(request, 'modules/inventory/category_detail.html', context)


@login_required
def category_edit(request, category_id):
    """
    تعديل المجموعة الرئيسية
    Edit Category
    """
    company = get_user_company(request)
    category = get_object_or_404(ProductCategory, id=category_id, company=company)

    if request.method == 'POST':
        form = ProductCategoryForm(request.POST, instance=category)
        if form.is_valid():
            form.save()
            messages.success(request, _('Category updated successfully.'))
            return redirect('inventory:category_detail', category_id=category.id)
    else:
        form = ProductCategoryForm(instance=category)

    context = {
        'form': form,
        'category': category,
        'title': _('Edit Category'),
    }

    return render(request, 'modules/inventory/category_form.html', context)


# ==================== المجموعات الفرعية ====================

@login_required
def subcategory_list(request):
    """
    قائمة المجموعات الفرعية
    Subcategory List
    """
    company = get_user_company(request)
    subcategories = ProductSubcategory.objects.filter(
        category__company=company
    ).select_related('category')

    # البحث
    search = request.GET.get('search')
    if search:
        subcategories = subcategories.filter(
            Q(name__icontains=search) | Q(code__icontains=search) |
            Q(category__name__icontains=search)
        )

    # التصفية حسب المجموعة الرئيسية
    category_id = request.GET.get('category')
    if category_id:
        subcategories = subcategories.filter(category_id=category_id)

    # التصفية حسب الحالة
    is_active = request.GET.get('is_active')
    if is_active:
        subcategories = subcategories.filter(is_active=is_active == 'true')

    # إضافة عدد المنتجات
    subcategories = subcategories.annotate(products_count=Count('products'))

    # الترقيم
    paginator = Paginator(subcategories, 20)
    page_number = request.GET.get('page')
    subcategories = paginator.get_page(page_number)

    # المجموعات الرئيسية للفلترة
    categories = ProductCategory.objects.filter(company=company, is_active=True)

    context = {
        'subcategories': subcategories,
        'categories': categories,
        'search': search,
        'category_id': category_id,
        'is_active': is_active,
    }

    return render(request, 'modules/inventory/subcategory_list.html', context)


@login_required
def subcategory_create(request):
    """
    إنشاء مجموعة فرعية جديدة
    Create New Subcategory
    """
    company = get_user_company(request)

    if request.method == 'POST':
        form = ProductSubcategoryForm(request.POST, company=company)
        if form.is_valid():
            subcategory = form.save()
            messages.success(request, _('Subcategory created successfully.'))
            return redirect('inventory:subcategory_list')
    else:
        form = ProductSubcategoryForm(company=company)

    context = {
        'form': form,
        'title': _('Create New Subcategory'),
    }

    return render(request, 'modules/inventory/subcategory_form.html', context)


@login_required
def subcategory_detail(request, subcategory_id):
    """
    تفاصيل المجموعة الفرعية
    Subcategory Detail
    """
    subcategory = get_object_or_404(
        ProductSubcategory.objects.select_related('category'),
        id=subcategory_id,
        category__company=get_user_company(request)
    )

    # المنتجات
    products = subcategory.products.filter(is_active=True)[:10]

    context = {
        'subcategory': subcategory,
        'products': products,
        'products_count': subcategory.products.count(),
    }

    return render(request, 'modules/inventory/subcategory_detail.html', context)


@login_required
def subcategory_edit(request, subcategory_id):
    """
    تعديل المجموعة الفرعية
    Edit Subcategory
    """
    company = get_user_company(request)
    subcategory = get_object_or_404(
        ProductSubcategory.objects.select_related('category'),
        id=subcategory_id,
        category__company=company
    )

    if request.method == 'POST':
        form = ProductSubcategoryForm(request.POST, instance=subcategory, company=company)
        if form.is_valid():
            form.save()
            messages.success(request, _('Subcategory updated successfully.'))
            return redirect('inventory:subcategory_detail', subcategory_id=subcategory.id)
    else:
        form = ProductSubcategoryForm(instance=subcategory, company=company)

    context = {
        'form': form,
        'subcategory': subcategory,
        'title': _('Edit Subcategory'),
    }

    return render(request, 'modules/inventory/subcategory_form.html', context)


# ==================== وحدات القياس ====================

@login_required
def unit_list(request):
    """
    قائمة وحدات القياس
    Unit of Measure List
    """
    company = get_user_company(request)
    units = UnitOfMeasure.objects.filter(company=company)

    # البحث
    search = request.GET.get('search')
    if search:
        units = units.filter(
            Q(name__icontains=search) | Q(code__icontains=search) | Q(symbol__icontains=search)
        )

    # التصفية
    is_active = request.GET.get('is_active')
    if is_active:
        units = units.filter(is_active=is_active == 'true')

    # الترقيم
    paginator = Paginator(units, 20)
    page_number = request.GET.get('page')
    units = paginator.get_page(page_number)

    context = {
        'units': units,
        'search': search,
        'is_active': is_active,
    }

    return render(request, 'modules/inventory/unit_list.html', context)


@login_required
def unit_create(request):
    """
    إنشاء وحدة قياس جديدة
    Create New Unit of Measure
    """
    company = get_user_company(request)

    if request.method == 'POST':
        form = UnitOfMeasureForm(request.POST)
        if form.is_valid():
            unit = form.save(commit=False)
            unit.company = company
            unit.save()
            messages.success(request, _('Unit of measure created successfully.'))
            return redirect('inventory:unit_list')
    else:
        form = UnitOfMeasureForm()

    context = {
        'form': form,
        'title': _('Create New Unit of Measure'),
    }

    return render(request, 'modules/inventory/unit_form.html', context)


@login_required
def unit_edit(request, unit_id):
    """
    تعديل وحدة القياس
    Edit Unit of Measure
    """
    company = get_user_company(request)
    unit = get_object_or_404(UnitOfMeasure, id=unit_id, company=company)

    if request.method == 'POST':
        form = UnitOfMeasureForm(request.POST, instance=unit)
        if form.is_valid():
            form.save()
            messages.success(request, _('Unit of measure updated successfully.'))
            return redirect('inventory:unit_list')
    else:
        form = UnitOfMeasureForm(instance=unit)

    context = {
        'form': form,
        'unit': unit,
        'title': _('Edit Unit of Measure'),
    }

    return render(request, 'modules/inventory/unit_form.html', context)


# ==================== المنتجات ====================

@login_required
def product_list(request):
    """
    قائمة المنتجات
    Product List
    """
    company = get_user_company(request)
    products = Product.objects.filter(company=company).select_related(
        'category', 'subcategory', 'unit_of_measure'
    )

    # البحث
    search = request.GET.get('search')
    if search:
        products = products.filter(
            Q(name__icontains=search) | Q(sku__icontains=search) |
            Q(description__icontains=search)
        )

    # التصفية
    category_id = request.GET.get('category')
    if category_id:
        products = products.filter(category_id=category_id)

    subcategory_id = request.GET.get('subcategory')
    if subcategory_id:
        products = products.filter(subcategory_id=subcategory_id)

    product_type = request.GET.get('product_type')
    if product_type:
        products = products.filter(product_type=product_type)

    is_active = request.GET.get('is_active')
    if is_active:
        products = products.filter(is_active=is_active == 'true')

    # الترقيم
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    products = paginator.get_page(page_number)

    # البيانات للفلترة
    categories = ProductCategory.objects.filter(company=company, is_active=True)
    subcategories = ProductSubcategory.objects.filter(
        category__company=company, is_active=True
    )

    context = {
        'products': products,
        'categories': categories,
        'subcategories': subcategories,
        'search': search,
        'category_id': category_id,
        'subcategory_id': subcategory_id,
        'product_type': product_type,
        'is_active': is_active,
        'product_types': Product.PRODUCT_TYPES,
    }

    return render(request, 'modules/inventory/product_list.html', context)


@login_required
def product_create(request):
    """
    إنشاء منتج جديد
    Create New Product
    """
    company = get_user_company(request)

    if request.method == 'POST':
        form = ProductForm(request.POST, company=company)
        if form.is_valid():
            product = form.save(commit=False)
            product.company = company
            product.created_by = request.user
            product.save()
            messages.success(request, _('Product created successfully.'))
            return redirect('inventory:product_detail', product_id=product.id)
    else:
        form = ProductForm(company=company)

    context = {
        'form': form,
        'title': _('Create New Product'),
    }

    return render(request, 'modules/inventory/product_form.html', context)


@login_required
def product_detail(request, product_id):
    """
    تفاصيل المنتج
    Product Detail
    """
    company = get_user_company(request)
    product = get_object_or_404(
        Product.objects.select_related('category', 'subcategory', 'unit_of_measure'),
        id=product_id,
        company=company
    )

    # الباركودات
    barcodes = product.barcodes.all()

    # حركات المخزون الأخيرة
    recent_movements = product.stock_movements.select_related('warehouse')[:10]

    # المخزون الحالي
    current_stock = product.get_current_stock()

    # ربط الحسابات
    account_link = getattr(product, 'account_link', None)

    context = {
        'product': product,
        'barcodes': barcodes,
        'recent_movements': recent_movements,
        'current_stock': current_stock,
        'account_link': account_link,
        'is_low_stock': product.is_low_stock(),
    }

    return render(request, 'modules/inventory/product_detail.html', context)


@login_required
def product_edit(request, product_id):
    """
    تعديل المنتج
    Edit Product
    """
    company = get_user_company(request)
    product = get_object_or_404(Product, id=product_id, company=company)

    if request.method == 'POST':
        form = ProductForm(request.POST, instance=product, company=company)
        if form.is_valid():
            form.save()
            messages.success(request, _('Product updated successfully.'))
            return redirect('inventory:product_detail', product_id=product.id)
    else:
        form = ProductForm(instance=product, company=company)

    context = {
        'form': form,
        'product': product,
        'title': _('Edit Product'),
    }

    return render(request, 'modules/inventory/product_form.html', context)


# ==================== Views مؤقتة للـ URLs المفقودة ====================

@login_required
def product_barcode_list(request, product_id):
    """مؤقت - قائمة باركودات المنتج"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Product Barcodes'})

@login_required
def product_barcode_create(request, product_id):
    """مؤقت - إنشاء باركود"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Create Barcode'})

@login_required
def product_barcode_edit(request, barcode_id):
    """مؤقت - تعديل باركود"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Edit Barcode'})

@login_required
def product_barcode_delete(request, barcode_id):
    """مؤقت - حذف باركود"""
    return redirect('inventory:dashboard')

@login_required
def barcode_print(request, barcode_id):
    """مؤقت - طباعة باركود"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Print Barcode'})

@login_required
def stock_movement_list(request):
    """مؤقت - قائمة حركات المخزون"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Stock Movements'})

@login_required
def stock_movement_create(request):
    """مؤقت - إنشاء حركة مخزون"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Create Stock Movement'})

@login_required
def stock_movement_detail(request, movement_id):
    """مؤقت - تفاصيل حركة المخزون"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Stock Movement Detail'})

@login_required
def product_permission_list(request, product_id):
    """مؤقت - صلاحيات المنتج"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Product Permissions'})

@login_required
def product_permission_create(request, product_id):
    """مؤقت - إنشاء صلاحية"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Create Permission'})

@login_required
def product_permission_delete(request, permission_id):
    """مؤقت - حذف صلاحية"""
    return redirect('inventory:dashboard')

@login_required
def product_account_link(request, product_id):
    """مؤقت - ربط الحسابات"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Product Account Link'})

@login_required
def ajax_subcategories(request):
    """مؤقت - AJAX للمجموعات الفرعية"""
    return JsonResponse({'subcategories': []})

@login_required
def ajax_product_search(request):
    """مؤقت - AJAX للبحث عن المنتجات"""
    return JsonResponse({'products': []})

@login_required
def ajax_barcode_generate(request):
    """مؤقت - AJAX لتوليد باركود"""
    return JsonResponse({'barcode': '*************'})

@login_required
def inventory_reports(request):
    """مؤقت - التقارير"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Inventory Reports'})

@login_required
def stock_levels_report(request):
    """مؤقت - تقرير مستويات المخزون"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Stock Levels Report'})

@login_required
def low_stock_report(request):
    """مؤقت - تقرير المخزون المنخفض"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Low Stock Report'})

@login_required
def stock_movements_report(request):
    """مؤقت - تقرير حركات المخزون"""
    return render(request, 'modules/inventory/coming_soon.html', {'title': 'Stock Movements Report'})