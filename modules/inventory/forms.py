"""
نماذج وحدة المخازن
Inventory Module Forms
"""

from django import forms
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Fieldset, Row, Column, Submit, HTML

from .models import (
    Warehouse, ProductCategory, ProductSubcategory, UnitOfMeasure,
    Product, ProductBarcode, StockMovement, ProductPermission, ProductAccountLink
)

User = get_user_model()


class WarehouseForm(forms.ModelForm):
    """
    نموذج المستودع
    Warehouse Form
    """
    class Meta:
        model = Warehouse
        fields = [
            'code', 'name', 'address', 'branch', 'manager', 'is_active'
        ]
        widgets = {
            'address': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            # فلترة الفروع والمدراء حسب الشركة
            self.fields['branch'].queryset = self.company.branches.filter(active=True)
            self.fields['manager'].queryset = User.objects.filter(company=self.company)

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('code', css_class='form-group col-md-6 mb-0'),
                    Column('name', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                Row(
                    Column('branch', css_class='form-group col-md-6 mb-0'),
                    Column('manager', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                'address',
                'is_active',
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )


class ProductCategoryForm(forms.ModelForm):
    """
    نموذج المجموعة الرئيسية
    Product Category Form
    """
    class Meta:
        model = ProductCategory
        fields = ['code', 'name', 'description', 'is_active']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Category Information'),
                Row(
                    Column('code', css_class='form-group col-md-6 mb-0'),
                    Column('name', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                'description',
                'is_active',
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )


class ProductSubcategoryForm(forms.ModelForm):
    """
    نموذج المجموعة الفرعية
    Product Subcategory Form
    """
    class Meta:
        model = ProductSubcategory
        fields = ['category', 'code', 'name', 'description', 'is_active']
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            self.fields['category'].queryset = ProductCategory.objects.filter(
                company=self.company, is_active=True
            )

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Subcategory Information'),
                'category',
                Row(
                    Column('code', css_class='form-group col-md-6 mb-0'),
                    Column('name', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                'description',
                'is_active',
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )


class UnitOfMeasureForm(forms.ModelForm):
    """
    نموذج وحدة القياس
    Unit of Measure Form
    """
    class Meta:
        model = UnitOfMeasure
        fields = ['code', 'name', 'symbol', 'is_active']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Unit Information'),
                Row(
                    Column('code', css_class='form-group col-md-4 mb-0'),
                    Column('name', css_class='form-group col-md-4 mb-0'),
                    Column('symbol', css_class='form-group col-md-4 mb-0'),
                    css_class='form-row'
                ),
                'is_active',
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )


class ProductForm(forms.ModelForm):
    """
    نموذج المنتج
    Product Form
    """
    class Meta:
        model = Product
        fields = [
            'category', 'subcategory', 'sku', 'name', 'description',
            'product_type', 'unit_of_measure', 'cost_price', 'selling_price',
            'track_stock', 'min_stock_level', 'max_stock_level',
            'is_active', 'can_be_sold', 'can_be_purchased'
        ]
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            # فلترة البيانات حسب الشركة
            self.fields['category'].queryset = ProductCategory.objects.filter(
                company=self.company, is_active=True
            )
            self.fields['unit_of_measure'].queryset = UnitOfMeasure.objects.filter(
                company=self.company, is_active=True
            )

        # تحديث المجموعات الفرعية عند تغيير المجموعة الرئيسية
        if 'category' in self.data:
            try:
                category_id = int(self.data.get('category'))
                self.fields['subcategory'].queryset = ProductSubcategory.objects.filter(
                    category_id=category_id, is_active=True
                )
            except (ValueError, TypeError):
                pass
        elif self.instance.pk:
            self.fields['subcategory'].queryset = self.instance.category.subcategories.filter(
                is_active=True
            )

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Basic Information'),
                Row(
                    Column('category', css_class='form-group col-md-6 mb-0'),
                    Column('subcategory', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                Row(
                    Column('sku', css_class='form-group col-md-6 mb-0'),
                    Column('product_type', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                'name',
                'description',
            ),
            Fieldset(
                _('Pricing & Units'),
                Row(
                    Column('unit_of_measure', css_class='form-group col-md-4 mb-0'),
                    Column('cost_price', css_class='form-group col-md-4 mb-0'),
                    Column('selling_price', css_class='form-group col-md-4 mb-0'),
                    css_class='form-row'
                ),
            ),
            Fieldset(
                _('Stock Management'),
                'track_stock',
                Row(
                    Column('min_stock_level', css_class='form-group col-md-6 mb-0'),
                    Column('max_stock_level', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
            ),
            Fieldset(
                _('Status'),
                Row(
                    Column('is_active', css_class='form-group col-md-4 mb-0'),
                    Column('can_be_sold', css_class='form-group col-md-4 mb-0'),
                    Column('can_be_purchased', css_class='form-group col-md-4 mb-0'),
                    css_class='form-row'
                ),
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )


class ProductBarcodeForm(forms.ModelForm):
    """
    نموذج باركود المنتج
    Product Barcode Form
    """
    class Meta:
        model = ProductBarcode
        fields = ['barcode', 'barcode_type', 'is_primary']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Barcode Information'),
                'barcode',
                Row(
                    Column('barcode_type', css_class='form-group col-md-6 mb-0'),
                    Column('is_primary', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )


class StockMovementForm(forms.ModelForm):
    """
    نموذج حركة المخزون
    Stock Movement Form
    """
    class Meta:
        model = StockMovement
        fields = [
            'warehouse', 'product', 'movement_type', 'quantity',
            'unit_cost', 'reference_number', 'destination_warehouse', 'notes'
        ]
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        
        if self.company:
            # فلترة البيانات حسب الشركة
            self.fields['warehouse'].queryset = Warehouse.objects.filter(
                company=self.company, is_active=True
            )
            self.fields['destination_warehouse'].queryset = Warehouse.objects.filter(
                company=self.company, is_active=True
            )
            self.fields['product'].queryset = Product.objects.filter(
                company=self.company, is_active=True
            )

        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.layout = Layout(
            Fieldset(
                _('Movement Information'),
                Row(
                    Column('warehouse', css_class='form-group col-md-6 mb-0'),
                    Column('product', css_class='form-group col-md-6 mb-0'),
                    css_class='form-row'
                ),
                Row(
                    Column('movement_type', css_class='form-group col-md-4 mb-0'),
                    Column('quantity', css_class='form-group col-md-4 mb-0'),
                    Column('unit_cost', css_class='form-group col-md-4 mb-0'),
                    css_class='form-row'
                ),
                'reference_number',
                'destination_warehouse',
                'notes',
            ),
            Submit('submit', _('Save'), css_class='btn btn-success')
        )
