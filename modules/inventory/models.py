"""
نماذج وحدة المخازن
Inventory Module Models
"""

import uuid
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from django.urls import reverse
from decimal import Decimal

from core.models import Company
from companies.models import Branch

User = get_user_model()


class Warehouse(models.Model):
    """
    نموذج المستودع
    Warehouse Model
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='warehouses',
        verbose_name=_('Company')
    )
    branch = models.ForeignKey(
        Branch,
        on_delete=models.CASCADE,
        related_name='warehouses',
        verbose_name=_('Branch'),
        null=True,
        blank=True
    )
    code = models.CharField(
        max_length=20,
        verbose_name=_('Warehouse Code')
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_('Warehouse Name')
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Address')
    )
    manager = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='managed_warehouses',
        verbose_name=_('Warehouse Manager')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Warehouse')
        verbose_name_plural = _('Warehouses')
        unique_together = ['company', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class ProductCategory(models.Model):
    """
    نموذج المجموعة الرئيسية للمنتجات
    Main Product Category Model
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='product_categories',
        verbose_name=_('Company')
    )
    code = models.CharField(
        max_length=20,
        verbose_name=_('Category Code')
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_('Category Name')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Product Category')
        verbose_name_plural = _('Product Categories')
        unique_together = ['company', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"

    def get_absolute_url(self):
        return reverse('inventory:category_detail', kwargs={'category_id': self.pk})


class ProductSubcategory(models.Model):
    """
    نموذج المجموعة الفرعية للمنتجات
    Product Subcategory Model
    """
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.CASCADE,
        related_name='subcategories',
        verbose_name=_('Main Category')
    )
    code = models.CharField(
        max_length=20,
        verbose_name=_('Subcategory Code')
    )
    name = models.CharField(
        max_length=100,
        verbose_name=_('Subcategory Name')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Product Subcategory')
        verbose_name_plural = _('Product Subcategories')
        unique_together = ['category', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.category.code}-{self.code} - {self.name}"

    def get_absolute_url(self):
        return reverse('inventory:subcategory_detail', kwargs={'subcategory_id': self.pk})


class UnitOfMeasure(models.Model):
    """
    نموذج وحدة القياس
    Unit of Measure Model
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='units_of_measure',
        verbose_name=_('Company')
    )
    code = models.CharField(
        max_length=10,
        verbose_name=_('Unit Code')
    )
    name = models.CharField(
        max_length=50,
        verbose_name=_('Unit Name')
    )
    symbol = models.CharField(
        max_length=10,
        verbose_name=_('Symbol')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Unit of Measure')
        verbose_name_plural = _('Units of Measure')
        unique_together = ['company', 'code']
        ordering = ['name']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Product(models.Model):
    """
    نموذج المنتج
    Product Model
    """
    PRODUCT_TYPES = [
        ('product', _('Product')),
        ('service', _('Service')),
        ('consumable', _('Consumable')),
    ]

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name=_('Company')
    )
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name=_('Category')
    )
    subcategory = models.ForeignKey(
        ProductSubcategory,
        on_delete=models.CASCADE,
        related_name='products',
        verbose_name=_('Subcategory'),
        null=True,
        blank=True
    )

    # Basic Information
    sku = models.CharField(
        max_length=50,
        verbose_name=_('SKU'),
        help_text=_('Stock Keeping Unit')
    )
    name = models.CharField(
        max_length=200,
        verbose_name=_('Product Name')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    product_type = models.CharField(
        max_length=20,
        choices=PRODUCT_TYPES,
        default='product',
        verbose_name=_('Product Type')
    )

    # Units and Pricing
    unit_of_measure = models.ForeignKey(
        UnitOfMeasure,
        on_delete=models.PROTECT,
        related_name='products',
        verbose_name=_('Unit of Measure')
    )
    cost_price = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name=_('Cost Price')
    )
    selling_price = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name=_('Selling Price')
    )

    # Stock Management
    track_stock = models.BooleanField(
        default=True,
        verbose_name=_('Track Stock')
    )
    min_stock_level = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name=_('Minimum Stock Level')
    )
    max_stock_level = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name=_('Maximum Stock Level')
    )

    # Status
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    can_be_sold = models.BooleanField(
        default=True,
        verbose_name=_('Can be Sold')
    )
    can_be_purchased = models.BooleanField(
        default=True,
        verbose_name=_('Can be Purchased')
    )

    # Timestamps
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_products',
        verbose_name=_('Created By')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Product')
        verbose_name_plural = _('Products')
        unique_together = ['company', 'sku']
        ordering = ['name']

    def __str__(self):
        return f"{self.sku} - {self.name}"

    def get_absolute_url(self):
        return reverse('inventory:product_detail', kwargs={'product_id': self.pk})

    def get_current_stock(self, warehouse=None):
        """
        حساب المخزون الحالي
        Calculate current stock
        """
        from .models import StockMovement

        movements = StockMovement.objects.filter(product=self)
        if warehouse:
            movements = movements.filter(warehouse=warehouse)

        total_in = movements.filter(movement_type='in').aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

        total_out = movements.filter(movement_type='out').aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

        return total_in - total_out

    def is_low_stock(self, warehouse=None):
        """
        فحص إذا كان المخزون منخفض
        Check if stock is low
        """
        if not self.track_stock:
            return False

        current_stock = self.get_current_stock(warehouse)
        return current_stock <= self.min_stock_level


class ProductBarcode(models.Model):
    """
    نموذج باركود المنتج
    Product Barcode Model
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='barcodes',
        verbose_name=_('Product')
    )
    barcode = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Barcode')
    )
    barcode_type = models.CharField(
        max_length=20,
        choices=[
            ('EAN13', 'EAN-13'),
            ('EAN8', 'EAN-8'),
            ('UPC', 'UPC'),
            ('CODE128', 'Code 128'),
            ('CODE39', 'Code 39'),
            ('QR', 'QR Code'),
        ],
        default='EAN13',
        verbose_name=_('Barcode Type')
    )
    is_primary = models.BooleanField(
        default=False,
        verbose_name=_('Is Primary Barcode')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Product Barcode')
        verbose_name_plural = _('Product Barcodes')
        ordering = ['-is_primary', 'barcode']

    def __str__(self):
        return f"{self.product.sku} - {self.barcode}"

    def save(self, *args, **kwargs):
        # إذا كان هذا الباركود الأساسي، قم بإلغاء الأساسية من الباركودات الأخرى
        if self.is_primary:
            ProductBarcode.objects.filter(
                product=self.product,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)


class StockMovement(models.Model):
    """
    نموذج حركة المخزون
    Stock Movement Model
    """
    MOVEMENT_TYPES = [
        ('in', _('Stock In')),
        ('out', _('Stock Out')),
        ('transfer', _('Transfer')),
        ('adjustment', _('Adjustment')),
    ]

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='stock_movements',
        verbose_name=_('Company')
    )
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name='stock_movements',
        verbose_name=_('Warehouse')
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='stock_movements',
        verbose_name=_('Product')
    )

    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        verbose_name=_('Movement Type')
    )
    quantity = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.01'))],
        verbose_name=_('Quantity')
    )
    unit_cost = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name=_('Unit Cost')
    )
    total_cost = models.DecimalField(
        max_digits=18,
        decimal_places=2,
        default=0,
        verbose_name=_('Total Cost')
    )

    reference_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name=_('Reference Number')
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Notes')
    )

    # For transfers
    destination_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name='incoming_transfers',
        null=True,
        blank=True,
        verbose_name=_('Destination Warehouse')
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='stock_movements',
        verbose_name=_('Created By')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Stock Movement')
        verbose_name_plural = _('Stock Movements')
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.movement_type} - {self.product.sku} - {self.quantity}"

    def save(self, *args, **kwargs):
        # حساب التكلفة الإجمالية
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)


class ProductPermission(models.Model):
    """
    نموذج صلاحيات المنتج
    Product Permission Model
    """
    PERMISSION_TYPES = [
        ('view', _('View')),
        ('create', _('Create')),
        ('edit', _('Edit')),
        ('delete', _('Delete')),
        ('manage_stock', _('Manage Stock')),
        ('view_cost', _('View Cost')),
        ('edit_price', _('Edit Price')),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='permissions',
        verbose_name=_('Product')
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='product_permissions',
        verbose_name=_('User')
    )
    permission_type = models.CharField(
        max_length=20,
        choices=PERMISSION_TYPES,
        verbose_name=_('Permission Type')
    )
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='granted_product_permissions',
        verbose_name=_('Granted By')
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = _('Product Permission')
        verbose_name_plural = _('Product Permissions')
        unique_together = ['product', 'user', 'permission_type']
        ordering = ['product', 'user', 'permission_type']

    def __str__(self):
        return f"{self.product.sku} - {self.user.username} - {self.permission_type}"


class ProductAccountLink(models.Model):
    """
    نموذج ربط المنتج بحسابات المصروفات والإيرادات
    Product Account Link Model
    """
    product = models.OneToOneField(
        Product,
        on_delete=models.CASCADE,
        related_name='account_link',
        verbose_name=_('Product')
    )

    # ربط بحسابات المحاسبة
    income_account = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='income_products',
        verbose_name=_('Income Account')
    )
    expense_account = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='expense_products',
        verbose_name=_('Expense Account')
    )
    inventory_account = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='inventory_products',
        verbose_name=_('Inventory Account')
    )
    cogs_account = models.ForeignKey(
        'accounting.Account',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cogs_products',
        verbose_name=_('Cost of Goods Sold Account')
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Product Account Link')
        verbose_name_plural = _('Product Account Links')

    def __str__(self):
        return f"{self.product.sku} - Account Links"
