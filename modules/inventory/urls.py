"""
روابط وحدة المخازن
Inventory Module URLs
"""

from django.urls import path
from . import views

app_name = 'inventory'

urlpatterns = [
    # لوحة التحكم
    path('', views.inventory_dashboard, name='dashboard'),
    
    # المستودعات
    path('warehouses/', views.warehouse_list, name='warehouse_list'),
    path('warehouses/create/', views.warehouse_create, name='warehouse_create'),
    path('warehouses/<int:warehouse_id>/', views.warehouse_detail, name='warehouse_detail'),
    path('warehouses/<int:warehouse_id>/edit/', views.warehouse_edit, name='warehouse_edit'),
    
    # المجموعات الرئيسية
    path('categories/', views.category_list, name='category_list'),
    path('categories/create/', views.category_create, name='category_create'),
    path('categories/<int:category_id>/', views.category_detail, name='category_detail'),
    path('categories/<int:category_id>/edit/', views.category_edit, name='category_edit'),
    
    # المجموعات الفرعية
    path('subcategories/', views.subcategory_list, name='subcategory_list'),
    path('subcategories/create/', views.subcategory_create, name='subcategory_create'),
    path('subcategories/<int:subcategory_id>/', views.subcategory_detail, name='subcategory_detail'),
    path('subcategories/<int:subcategory_id>/edit/', views.subcategory_edit, name='subcategory_edit'),
    
    # وحدات القياس
    path('units/', views.unit_list, name='unit_list'),
    path('units/create/', views.unit_create, name='unit_create'),
    path('units/<int:unit_id>/edit/', views.unit_edit, name='unit_edit'),
    
    # المنتجات
    path('products/', views.product_list, name='product_list'),
    path('products/create/', views.product_create, name='product_create'),
    path('products/<int:product_id>/', views.product_detail, name='product_detail'),
    path('products/<int:product_id>/edit/', views.product_edit, name='product_edit'),
    
    # الباركود
    path('products/<int:product_id>/barcodes/', views.product_barcode_list, name='product_barcode_list'),
    path('products/<int:product_id>/barcodes/create/', views.product_barcode_create, name='product_barcode_create'),
    path('barcodes/<int:barcode_id>/edit/', views.product_barcode_edit, name='product_barcode_edit'),
    path('barcodes/<int:barcode_id>/delete/', views.product_barcode_delete, name='product_barcode_delete'),
    path('barcodes/<int:barcode_id>/print/', views.barcode_print, name='barcode_print'),
    
    # حركات المخزون
    path('stock-movements/', views.stock_movement_list, name='stock_movement_list'),
    path('stock-movements/create/', views.stock_movement_create, name='stock_movement_create'),
    path('stock-movements/<int:movement_id>/', views.stock_movement_detail, name='stock_movement_detail'),
    
    # صلاحيات المنتجات
    path('products/<int:product_id>/permissions/', views.product_permission_list, name='product_permission_list'),
    path('products/<int:product_id>/permissions/create/', views.product_permission_create, name='product_permission_create'),
    path('permissions/<int:permission_id>/delete/', views.product_permission_delete, name='product_permission_delete'),
    
    # ربط الحسابات
    path('products/<int:product_id>/accounts/', views.product_account_link, name='product_account_link'),
    
    # AJAX endpoints
    path('ajax/subcategories/', views.ajax_subcategories, name='ajax_subcategories'),
    path('ajax/product-search/', views.ajax_product_search, name='ajax_product_search'),
    path('ajax/barcode-generate/', views.ajax_barcode_generate, name='ajax_barcode_generate'),
    
    # التقارير
    path('reports/', views.inventory_reports, name='reports'),
    path('reports/stock-levels/', views.stock_levels_report, name='stock_levels_report'),
    path('reports/low-stock/', views.low_stock_report, name='low_stock_report'),
    path('reports/stock-movements/', views.stock_movements_report, name='stock_movements_report'),
]
