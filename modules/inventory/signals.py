"""
إشارات وحدة المخازن
Inventory Module Signals
"""

from django.db.models.signals import post_save, pre_delete
from django.dispatch import receiver
from django.utils.translation import gettext as _

from .models import Product, ProductBarcode, StockMovement, ProductAccountLink


@receiver(post_save, sender=Product)
def create_product_account_link(sender, instance, created, **kwargs):
    """
    إنشاء ربط حسابات تلقائي عند إنشاء منتج جديد
    Create automatic account link when a new product is created
    """
    if created:
        ProductAccountLink.objects.get_or_create(product=instance)


@receiver(post_save, sender=ProductBarcode)
def ensure_single_primary_barcode(sender, instance, **kwargs):
    """
    التأكد من وجود باركود أساسي واحد فقط لكل منتج
    Ensure only one primary barcode per product
    """
    if instance.is_primary:
        # إلغاء الأساسية من الباركودات الأخرى
        ProductBarcode.objects.filter(
            product=instance.product,
            is_primary=True
        ).exclude(pk=instance.pk).update(is_primary=False)


@receiver(post_save, sender=StockMovement)
def update_product_cost(sender, instance, created, **kwargs):
    """
    تحديث تكلفة المنتج بناءً على حركات المخزون
    Update product cost based on stock movements
    """
    if created and instance.movement_type == 'in' and instance.unit_cost > 0:
        # حساب متوسط التكلفة المرجح
        product = instance.product
        
        # الحصول على جميع حركات الدخول
        in_movements = StockMovement.objects.filter(
            product=product,
            movement_type='in',
            unit_cost__gt=0
        )
        
        if in_movements.exists():
            total_quantity = sum(movement.quantity for movement in in_movements)
            total_cost = sum(movement.total_cost for movement in in_movements)
            
            if total_quantity > 0:
                weighted_average_cost = total_cost / total_quantity
                product.cost_price = weighted_average_cost
                product.save(update_fields=['cost_price'])


@receiver(pre_delete, sender=Product)
def check_product_dependencies(sender, instance, **kwargs):
    """
    فحص التبعيات قبل حذف المنتج
    Check dependencies before deleting product
    """
    # فحص وجود حركات مخزون
    if instance.stock_movements.exists():
        raise ValueError(
            _('Cannot delete product with existing stock movements. '
              'Please remove all stock movements first.')
        )
    
    # فحص وجود صلاحيات
    if instance.permissions.exists():
        # حذف الصلاحيات تلقائياً
        instance.permissions.all().delete()


@receiver(pre_delete, sender=ProductBarcode)
def prevent_primary_barcode_deletion(sender, instance, **kwargs):
    """
    منع حذف الباركود الأساسي إذا كان الوحيد
    Prevent deletion of primary barcode if it's the only one
    """
    if instance.is_primary:
        other_barcodes = ProductBarcode.objects.filter(
            product=instance.product
        ).exclude(pk=instance.pk)
        
        if not other_barcodes.exists():
            raise ValueError(
                _('Cannot delete the only barcode for this product. '
                  'Please add another barcode first.')
            )
        else:
            # جعل أول باركود آخر أساسي
            other_barcodes.first().update(is_primary=True)
