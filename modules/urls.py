from django.urls import path
from . import views

app_name = 'modules'

urlpatterns = [
    # Module management
    path('', views.module_list, name='module_list'),
    path('detail/<str:module_code>/', views.module_detail, name='module_detail'),
    path('install/<str:module_code>/', views.module_install, name='module_install'),
    path('uninstall/<str:module_code>/', views.module_uninstall, name='module_uninstall'),
    path('update/<str:module_code>/', views.module_update, name='module_update'),

    # License management
    path('licenses/', views.license_management, name='license_management'),
    path('licenses/add/', views.license_add, name='license_add'),
    path('licenses/edit/<int:license_id>/', views.license_edit, name='license_edit'),
    path('licenses/delete/<int:license_id>/', views.license_delete, name='license_delete'),

    # Module dashboards
    path('<str:module_code>/', views.module_dashboard, name='module_dashboard'),
]
