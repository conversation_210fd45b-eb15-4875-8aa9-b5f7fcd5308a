from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils.timezone import now
from companies.models import Company

class Module(models.Model):
    """
    Model for system modules
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('Name')
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name=_('Code')
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name=_('Description')
    )
    version = models.CharField(
        max_length=20,
        verbose_name=_('Version')
    )
    icon = models.CharField(
        max_length=50,
        default='fa fa-puzzle-piece',
        verbose_name=_('Icon')
    )
    is_core = models.BooleanField(
        default=False,
        verbose_name=_('Is Core Module')
    )
    dependencies = models.ManyToManyField(
        'self',
        symmetrical=False,
        blank=True,
        related_name='dependent_modules',
        verbose_name=_('Dependencies')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Module')
        verbose_name_plural = _('Modules')
        ordering = ['name']

    def __str__(self):
        return self.name


class CompanyModule(models.Model):
    """
    Model for company module subscriptions
    """
    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='modules',
        verbose_name=_('Company')
    )
    module = models.ForeignKey(
        Module,
        on_delete=models.CASCADE,
        related_name='companies',
        verbose_name=_('Module')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    installed_version = models.CharField(
        max_length=20,
        verbose_name=_('Installed Version')
    )
    installed_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Installed At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Company Module')
        verbose_name_plural = _('Company Modules')
        unique_together = ['company', 'module']

    def __str__(self):
        return f"{self.company.name} - {self.module.name}"


class ModuleLicense(models.Model):
    """
    Model for module licenses
    """
    LICENSE_TYPES = (
        ('trial', _('Trial')),
        ('standard', _('Standard')),
        ('premium', _('Premium')),
        ('enterprise', _('Enterprise')),
    )

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='licenses',
        verbose_name=_('Company')
    )
    module = models.ForeignKey(
        Module,
        on_delete=models.CASCADE,
        related_name='licenses',
        verbose_name=_('Module')
    )
    license_type = models.CharField(
        max_length=20,
        choices=LICENSE_TYPES,
        verbose_name=_('License Type')
    )
    license_key = models.CharField(
        max_length=100,
        unique=True,
        verbose_name=_('License Key')
    )
    start_date = models.DateField(
        verbose_name=_('Start Date')
    )
    end_date = models.DateField(
        verbose_name=_('End Date')
    )
    max_users = models.PositiveIntegerField(
        default=1,
        verbose_name=_('Max Users')
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name=_('Is Active')
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_('Created At')
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name=_('Updated At')
    )

    class Meta:
        verbose_name = _('Module License')
        verbose_name_plural = _('Module Licenses')
        unique_together = ['company', 'module']
        ordering = ['-end_date', 'company', 'module']

    def __str__(self):
        return f"{self.company.name} - {self.module.name} ({self.get_license_type_display()})"

    @property
    def is_expired(self):
        """
        Check if the license is expired
        """
        return self.end_date < now().date()

    @property
    def days_remaining(self):
        """
        Get the number of days remaining until expiration
        """
        if self.is_expired:
            return 0
        return (self.end_date - now().date()).days

    def save(self, *args, **kwargs):
        """
        Generate a license key if not provided
        """
        if not self.license_key:
            import uuid
            import hashlib

            # Generate a unique license key
            key_base = f"{self.company.id}-{self.module.id}-{self.license_type}-{uuid.uuid4()}"
            self.license_key = hashlib.sha256(key_base.encode()).hexdigest()[:32].upper()

        super().save(*args, **kwargs)
