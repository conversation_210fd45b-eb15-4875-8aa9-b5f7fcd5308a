# Generated by Django 5.2.1 on 2025-05-21 19:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0001_initial'),
        ('modules', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ModuleLicense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('license_type', models.CharField(choices=[('trial', 'Trial'), ('standard', 'Standard'), ('premium', 'Premium'), ('enterprise', 'Enterprise')], max_length=20, verbose_name='License Type')),
                ('license_key', models.CharField(max_length=100, unique=True, verbose_name='License Key')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('max_users', models.PositiveIntegerField(default=1, verbose_name='Max Users')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='licenses', to='companies.company', verbose_name='Company')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='licenses', to='modules.module', verbose_name='Module')),
            ],
            options={
                'verbose_name': 'Module License',
                'verbose_name_plural': 'Module Licenses',
                'ordering': ['-end_date', 'company', 'module'],
                'unique_together': {('company', 'module')},
            },
        ),
    ]
