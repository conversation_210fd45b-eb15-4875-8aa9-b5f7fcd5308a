# Generated by Django 5.2.1 on 2025-05-21 14:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Module',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('name_en', models.CharField(max_length=100, null=True, unique=True, verbose_name='Name')),
                ('name_ar', models.CharField(max_length=100, null=True, unique=True, verbose_name='Name')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Code')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_en', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('description_ar', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('version', models.CharField(max_length=20, verbose_name='Version')),
                ('icon', models.CharField(default='fa fa-puzzle-piece', max_length=50, verbose_name='Icon')),
                ('is_core', models.BooleanField(default=False, verbose_name='Is Core Module')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('dependencies', models.ManyToManyField(blank=True, related_name='dependent_modules', to='modules.module', verbose_name='Dependencies')),
            ],
            options={
                'verbose_name': 'Module',
                'verbose_name_plural': 'Modules',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CompanyModule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('installed_version', models.CharField(max_length=20, verbose_name='Installed Version')),
                ('installed_at', models.DateTimeField(auto_now_add=True, verbose_name='Installed At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='modules', to='companies.company', verbose_name='Company')),
                ('module', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='companies', to='modules.module', verbose_name='Module')),
            ],
            options={
                'verbose_name': 'Company Module',
                'verbose_name_plural': 'Company Modules',
                'unique_together': {('company', 'module')},
            },
        ),
    ]
