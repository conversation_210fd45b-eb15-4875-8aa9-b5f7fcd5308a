from django import forms
from django.utils.translation import gettext_lazy as _
from .models import Module, CompanyModule

class ModuleForm(forms.ModelForm):
    """
    Form for creating/editing modules
    """
    class Meta:
        model = Module
        fields = [
            'name', 'code', 'description', 'version', 
            'icon', 'is_core', 'dependencies'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'version': forms.TextInput(attrs={'class': 'form-control'}),
            'icon': forms.TextInput(attrs={'class': 'form-control'}),
            'is_core': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'dependencies': forms.SelectMultiple(attrs={'class': 'form-control select2'}),
        }

class CompanyModuleForm(forms.ModelForm):
    """
    Form for creating/editing company modules
    """
    class Meta:
        model = CompanyModule
        fields = [
            'company', 'module', 'is_active', 'installed_version'
        ]
        widgets = {
            'company': forms.Select(attrs={'class': 'form-control select2'}),
            'module': forms.Select(attrs={'class': 'form-control select2'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'installed_version': forms.TextInput(attrs={'class': 'form-control'}),
        }
