from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils.timezone import now
from .models import Module, CompanyModule, ModuleLicense
from companies.models import Company
from .license_forms import ModuleLicenseForm

@login_required
@permission_required('modules.view_modulelicense')
def license_management(request):
    """
    View for managing module licenses
    """
    # Get all licenses
    licenses = ModuleLicense.objects.all().select_related('company', 'module')
    
    # Get all companies and modules for the add license form
    companies = Company.objects.filter(active=True)
    modules = Module.objects.filter(is_core=False)
    
    # Calculate statistics
    active_licenses_count = licenses.filter(is_active=True).count()
    expired_licenses_count = sum(1 for license in licenses if license.is_expired)
    licensed_companies_count = Company.objects.filter(licenses__isnull=False).distinct().count()
    
    context = {
        'licenses': licenses,
        'companies': companies,
        'modules': modules,
        'active_licenses_count': active_licenses_count,
        'expired_licenses_count': expired_licenses_count,
        'licensed_companies_count': licensed_companies_count,
    }
    
    return render(request, 'modules/license_management.html', context)

@login_required
@permission_required('modules.add_modulelicense')
def license_add(request):
    """
    View for adding a new module license
    """
    if request.method == 'POST':
        form = ModuleLicenseForm(request.POST)
        if form.is_valid():
            license = form.save()
            messages.success(request, _('License added successfully.'))
            return redirect('modules:license_management')
    else:
        form = ModuleLicenseForm()
    
    context = {
        'form': form,
        'companies': Company.objects.filter(active=True),
        'modules': Module.objects.filter(is_core=False),
    }
    
    return render(request, 'modules/license_form.html', context)

@login_required
@permission_required('modules.change_modulelicense')
def license_edit(request, license_id):
    """
    View for editing an existing module license
    """
    license = get_object_or_404(ModuleLicense, pk=license_id)
    
    if request.method == 'POST':
        form = ModuleLicenseForm(request.POST, instance=license)
        if form.is_valid():
            form.save()
            messages.success(request, _('License updated successfully.'))
            return redirect('modules:license_management')
    else:
        form = ModuleLicenseForm(instance=license)
    
    context = {
        'form': form,
        'license': license,
        'companies': Company.objects.filter(active=True),
        'modules': Module.objects.filter(is_core=False),
    }
    
    return render(request, 'modules/license_form.html', context)

@login_required
@permission_required('modules.delete_modulelicense')
def license_delete(request, license_id):
    """
    View for deleting a module license
    """
    license = get_object_or_404(ModuleLicense, pk=license_id)
    
    if request.method == 'POST':
        license.delete()
        messages.success(request, _('License deleted successfully.'))
        return redirect('modules:license_management')
    
    context = {
        'license': license,
    }
    
    return render(request, 'modules/license_confirm_delete.html', context)
