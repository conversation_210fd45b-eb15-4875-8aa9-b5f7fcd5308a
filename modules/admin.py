from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import Module, CompanyModule, ModuleLicense

class CompanyModuleInline(admin.TabularInline):
    """
    Inline admin for CompanyModule model
    """
    model = CompanyModule
    extra = 1

class ModuleLicenseInline(admin.TabularInline):
    """
    Inline admin for ModuleLicense model
    """
    model = ModuleLicense
    extra = 1
    fields = ('license_type', 'start_date', 'end_date', 'max_users', 'is_active')

@admin.register(Module)
class ModuleAdmin(admin.ModelAdmin):
    """
    Admin for Module model
    """
    list_display = ('name', 'code', 'version', 'is_core', 'created_at')
    list_filter = ('is_core',)
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('created_at', 'updated_at')
    filter_horizontal = ('dependencies',)
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'description', 'version', 'icon')
        }),
        (_('Configuration'), {
            'fields': ('is_core', 'dependencies')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    inlines = [CompanyModuleInline, ModuleLicenseInline]

@admin.register(CompanyModule)
class CompanyModuleAdmin(admin.ModelAdmin):
    """
    Admin for CompanyModule model
    """
    list_display = ('company', 'module', 'is_active', 'installed_version', 'installed_at')
    list_filter = ('is_active', 'company', 'module')
    search_fields = ('company__name', 'module__name')
    readonly_fields = ('installed_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('company', 'module', 'is_active', 'installed_version')
        }),
        (_('Timestamps'), {
            'fields': ('installed_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(ModuleLicense)
class ModuleLicenseAdmin(admin.ModelAdmin):
    """
    Admin for ModuleLicense model
    """
    list_display = ('company', 'module', 'license_type', 'start_date', 'end_date', 'is_active', 'is_expired', 'days_remaining')
    list_filter = ('license_type', 'is_active', 'company', 'module')
    search_fields = ('company__name', 'module__name', 'license_key')
    readonly_fields = ('license_key', 'created_at', 'updated_at', 'is_expired', 'days_remaining')
    fieldsets = (
        (None, {
            'fields': ('company', 'module', 'license_type', 'license_key')
        }),
        (_('License Details'), {
            'fields': ('start_date', 'end_date', 'max_users', 'is_active')
        }),
        (_('Status'), {
            'fields': ('is_expired', 'days_remaining')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def is_expired(self, obj):
        return obj.is_expired
    is_expired.boolean = True
    is_expired.short_description = _('Expired')

    def days_remaining(self, obj):
        return obj.days_remaining
    days_remaining.short_description = _('Days Remaining')
