from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.template.loader import get_template
from django.utils.translation import get_language
from .models import Module, CompanyModule, ModuleLicense

@login_required
@permission_required('modules.view_module')
def module_list(request):
    """
    List all modules
    """
    # Get all modules
    modules = Module.objects.all()

    # Get company modules for the user's company
    company_modules = []
    if request.user.company:
        company_modules = CompanyModule.objects.filter(
            company=request.user.company
        ).select_related('module')

    # Create a dictionary of installed modules for easy lookup
    installed_modules = {}
    for cm in company_modules:
        installed_modules[cm.module.id] = {
            'is_active': cm.is_active,
            'installed_version': cm.installed_version
        }

    context = {
        'modules': modules,
        'installed_modules': installed_modules,
        'company_modules': company_modules
    }

    return render(request, 'modules/module_list.html', context)

@login_required
@permission_required('modules.view_module')
def module_detail(request, module_code):
    """
    View details of a specific module
    """
    # Get the module
    module = get_object_or_404(Module, code=module_code)

    # Check if module is installed for the user's company
    module_installed = False
    module_active = False
    company_module = None

    if request.user.company:
        try:
            company_module = CompanyModule.objects.get(
                company=request.user.company,
                module=module
            )
            module_installed = True
            module_active = company_module.is_active
        except CompanyModule.DoesNotExist:
            pass

    # Get installed modules for dependencies
    installed_modules = {}
    if request.user.company:
        for cm in CompanyModule.objects.filter(company=request.user.company):
            installed_modules[cm.module.id] = {
                'is_active': cm.is_active,
                'installed_version': cm.installed_version
            }

    context = {
        'module': module,
        'module_installed': module_installed,
        'module_active': module_active,
        'company_module': company_module,
        'installed_modules': installed_modules
    }

    return render(request, 'modules/module_detail.html', context)

@login_required
@permission_required('modules.add_companymodule')
def module_install(request, module_code):
    """
    Install a module for the user's company
    """
    # Check if user has a company
    if not request.user.company:
        messages.error(request, _('You need to be associated with a company to install modules.'))
        return redirect('modules:module_list')

    # Get the module
    module = get_object_or_404(Module, code=module_code)

    # Check if the company has a license for this module
    has_license = False
    if not module.is_core:
        try:
            license = ModuleLicense.objects.get(
                company=request.user.company,
                module=module,
                is_active=True
            )
            if license.is_expired:
                messages.error(request, _('Your license for this module has expired. Please renew your license.'))
                return redirect('modules:module_detail', module_code=module_code)
            has_license = True
        except ModuleLicense.DoesNotExist:
            messages.error(request, _('Your company does not have a license for this module. Please contact the administrator.'))
            return redirect('modules:module_detail', module_code=module_code)

    # If it's a core module or the company has a license
    if module.is_core or has_license:
        # Check if module is already installed
        company_module, created = CompanyModule.objects.get_or_create(
            company=request.user.company,
            module=module,
            defaults={
                'is_active': True,
                'installed_version': module.version
            }
        )

        if not created:
            # Update the module if it already exists
            company_module.is_active = True
            company_module.installed_version = module.version
            company_module.save()
            messages.success(request, _('Module updated successfully.'))
        else:
            messages.success(request, _('Module installed successfully.'))

        # Check and install dependencies
        dependencies = module.dependencies.all()
        for dependency in dependencies:
            dep_cm, dep_created = CompanyModule.objects.get_or_create(
                company=request.user.company,
                module=dependency,
                defaults={
                    'is_active': True,
                    'installed_version': dependency.version
                }
            )

            if dep_created:
                messages.info(request, _('Dependency {0} installed.').format(dependency.name))

    return redirect('modules:module_detail', module_code=module_code)

@login_required
@permission_required('modules.change_companymodule')
def module_uninstall(request, module_code):
    """
    Uninstall a module for the user's company
    """
    # Check if user has a company
    if not request.user.company:
        messages.error(request, _('You need to be associated with a company to uninstall modules.'))
        return redirect('modules:module_list')

    # Get the module
    module = get_object_or_404(Module, code=module_code)

    # Check if module is installed
    try:
        company_module = CompanyModule.objects.get(
            company=request.user.company,
            module=module
        )

        # Check if any other modules depend on this one
        dependent_modules = []
        for cm in CompanyModule.objects.filter(company=request.user.company, is_active=True):
            if module in cm.module.dependencies.all():
                dependent_modules.append(cm.module)

        if dependent_modules:
            context = {
                'module': module,
                'dependent_modules': dependent_modules,
                'action': 'uninstall'
            }
            return render(request, 'modules/module_confirm.html', context)

        # Deactivate the module
        company_module.is_active = False
        company_module.save()
        messages.success(request, _('Module uninstalled successfully.'))
    except CompanyModule.DoesNotExist:
        messages.error(request, _('Module is not installed.'))

    return redirect('modules:module_detail', module_code=module_code)

@login_required
@permission_required('modules.change_companymodule')
def module_update(request, module_code):
    """
    Update a module for the user's company
    """
    # Check if user has a company
    if not request.user.company:
        messages.error(request, _('You need to be associated with a company to update modules.'))
        return redirect('modules:module_list')

    # Get the module
    module = get_object_or_404(Module, code=module_code)

    # Check if module is installed
    try:
        company_module = CompanyModule.objects.get(
            company=request.user.company,
            module=module
        )

        # Check if the installed version is different from the current version
        if company_module.installed_version != module.version:
            # Update the module
            company_module.installed_version = module.version
            company_module.save()
            messages.success(request, _('Module updated successfully.'))
        else:
            messages.info(request, _('Module is already up to date.'))
    except CompanyModule.DoesNotExist:
        messages.error(request, _('Module is not installed.'))

    return redirect('modules:module_detail', module_code=module_code)

@login_required
def module_dashboard(request, module_code):
    """
    Display the dashboard for a specific module
    """
    # Get the module
    try:
        module = Module.objects.get(code=module_code)
    except Module.DoesNotExist:
        messages.error(request, _('Module not found.'))
        return redirect('core:dashboard')

    # Check if the module is installed and active for the user's company
    if request.user.company:
        try:
            company_module = CompanyModule.objects.get(
                company=request.user.company,
                module=module,
                is_active=True
            )
        except CompanyModule.DoesNotExist:
            messages.error(request, _('Module is not installed or not active.'))
            return redirect('modules:module_detail', module_code=module_code)
    else:
        # Allow superusers to access module dashboards even without a company
        if not request.user.is_superuser:
            messages.error(request, _('You need to be associated with a company to access module dashboards.'))
            return redirect('core:dashboard')

    # Check if the template exists
    template_path = f'modules/{module_code}/dashboard.html'
    try:
        get_template(template_path)
        print(f"Template found: {template_path}")
    except Exception as e:
        # If template doesn't exist, use a generic template
        print(f"Template not found: {template_path}, Error: {e}")
        messages.warning(request, _('This module is still under development.'))
        return redirect('core:dashboard')

    context = {
        'module': module,
        'LANGUAGE_CODE': get_language(),
    }

    return render(request, template_path, context)
