import os
import polib
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Manually compiles .po files to .mo files'

    def handle(self, *args, **options):
        locale_path = os.path.join(settings.BASE_DIR, 'locale')

        if not os.path.exists(locale_path):
            self.stdout.write(self.style.ERROR(f'Locale directory not found at {locale_path}'))
            return

        for root, dirs, files in os.walk(locale_path):
            for file in files:
                if file.endswith('.po'):
                    po_file_path = os.path.join(root, file)
                    mo_file_path = po_file_path[:-3] + '.mo'

                    try:
                        self.stdout.write(f'Processing {po_file_path}')
                        po = polib.pofile(po_file_path)
                        po.save_as_mofile(mo_file_path)
                        self.stdout.write(self.style.SUCCESS(f'Successfully compiled {mo_file_path}'))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f'Error compiling {po_file_path}: {str(e)}'))

        self.stdout.write(self.style.SUCCESS('Translation compilation complete'))
