from django.core.management.base import BaseCommand
from modules.models import Module
from django.utils.translation import gettext_lazy as _

class Command(BaseCommand):
    help = 'Creates the default modules for the ERP system'

    def handle(self, *args, **options):
        # Define modules with their details
        modules_data = [
            {
                'name': _('Human Resources Management'),
                'name_ar': 'إدارة الموارد البشرية',
                'code': 'hr',
                'description': _('Manage employees, departments, contracts, payroll, attendance, and more.'),
                'description_ar': 'إدارة الموظفين، الأقسام، العقود، الرواتب، الحضور والانصراف، وأكثر.',
                'version': '1.0.0',
                'icon': 'fas fa-users',
                'is_core': False,
            },
            {
                'name': _('Inventory Management'),
                'name_ar': 'إدارة المخازن',
                'code': 'inventory',
                'description': _('Manage warehouses, products, stock movements, and inventory valuation.'),
                'description_ar': 'إدارة المستودعات، المنتجات، حركة المخزون، وتقييم المخزون.',
                'version': '1.0.0',
                'icon': 'fas fa-boxes',
                'is_core': False,
            },
            {
                'name': _('Procurement Management'),
                'name_ar': 'إدارة المشتريات',
                'code': 'procurement',
                'description': _('Manage purchase requests, quotations, purchase orders, and vendor management.'),
                'description_ar': 'إدارة طلبات الشراء، عروض الأسعار، أوامر الشراء، وإدارة الموردين.',
                'version': '1.0.0',
                'icon': 'fas fa-shopping-cart',
                'is_core': False,
            },
            {
                'name': _('Sales Management'),
                'name_ar': 'إدارة المبيعات',
                'code': 'sales',
                'description': _('Manage sales quotations, orders, invoices, and customer management.'),
                'description_ar': 'إدارة عروض المبيعات، الطلبات، الفواتير، وإدارة العملاء.',
                'version': '1.0.0',
                'icon': 'fas fa-chart-line',
                'is_core': False,
            },
            {
                'name': _('Point of Sale'),
                'name_ar': 'نقاط البيع',
                'code': 'pos',
                'description': _('Manage retail operations, cash registers, and sales transactions.'),
                'description_ar': 'إدارة عمليات البيع بالتجزئة، أجهزة الكاشير، ومعاملات المبيعات.',
                'version': '1.0.0',
                'icon': 'fas fa-cash-register',
                'is_core': False,
            },
            {
                'name': _('Hotel Management'),
                'name_ar': 'إدارة الفنادق',
                'code': 'hotel',
                'description': _('Manage hotel operations, room bookings, guest services, and facilities.'),
                'description_ar': 'إدارة عمليات الفندق، حجز الغرف، خدمات الضيوف، والمرافق.',
                'version': '1.0.0',
                'icon': 'fas fa-hotel',
                'is_core': False,
            },
            {
                'name': _('Restaurant & Cafe Management'),
                'name_ar': 'إدارة المطاعم والكافيهات',
                'code': 'restaurant',
                'description': _('Manage restaurant operations, orders, kitchen, and table management.'),
                'description_ar': 'إدارة عمليات المطعم، الطلبات، المطبخ، وإدارة الطاولات.',
                'version': '1.0.0',
                'icon': 'fas fa-utensils',
                'is_core': False,
            },
            {
                'name': _('Accounting Management'),
                'name_ar': 'إدارة الحسابات',
                'code': 'accounting',
                'description': _('Manage general ledger, accounts receivable, accounts payable, and financial reporting.'),
                'description_ar': 'إدارة دفتر الأستاذ العام، الذمم المدينة، الذمم الدائنة، والتقارير المالية.',
                'version': '1.0.0',
                'icon': 'fas fa-calculator',
                'is_core': False,
            },
            {
                'name': _('Financial Management'),
                'name_ar': 'الإدارة المالية',
                'code': 'finance',
                'description': _('Manage budgets, financial planning, cash flow, and financial analysis.'),
                'description_ar': 'إدارة الميزانيات، التخطيط المالي، التدفق النقدي، والتحليل المالي.',
                'version': '1.0.0',
                'icon': 'fas fa-money-bill-wave',
                'is_core': False,
            },
            {
                'name': _('Manufacturing Management'),
                'name_ar': 'إدارة التصنيع',
                'code': 'manufacturing',
                'description': _('Manage production orders, bill of materials, work centers, and manufacturing processes.'),
                'description_ar': 'إدارة أوامر الإنتاج، قائمة المواد، مراكز العمل، وعمليات التصنيع.',
                'version': '1.0.0',
                'icon': 'fas fa-industry',
                'is_core': False,
            },
            {
                'name': _('Project Management'),
                'name_ar': 'إدارة المقاولات والمشاريع',
                'code': 'project',
                'description': _('Manage projects, tasks, timelines, resources, and project budgets.'),
                'description_ar': 'إدارة المشاريع، المهام، الجداول الزمنية، الموارد، وميزانيات المشاريع.',
                'version': '1.0.0',
                'icon': 'fas fa-tasks',
                'is_core': False,
            },
            {
                'name': _('Fleet Management'),
                'name_ar': 'السيارات والمعدات',
                'code': 'fleet',
                'description': _('Manage vehicles, maintenance, fuel logs, and vehicle assignments.'),
                'description_ar': 'إدارة المركبات، الصيانة، سجلات الوقود، وتخصيص المركبات.',
                'version': '1.0.0',
                'icon': 'fas fa-truck',
                'is_core': False,
            },
            {
                'name': _('Document Management'),
                'name_ar': 'إدارة الأرشيف الإلكتروني',
                'code': 'document',
                'description': _('Manage documents, files, categories, and document workflows.'),
                'description_ar': 'إدارة المستندات، الملفات، التصنيفات، وسير عمل المستندات.',
                'version': '1.0.0',
                'icon': 'fas fa-file-alt',
                'is_core': False,
            },
            {
                'name': _('Customer Relationship Management'),
                'name_ar': 'إدارة علاقات العملاء',
                'code': 'crm',
                'description': _('Manage leads, opportunities, customer communications, and sales pipeline.'),
                'description_ar': 'إدارة العملاء المحتملين، الفرص، اتصالات العملاء، وخط أنابيب المبيعات.',
                'version': '1.0.0',
                'icon': 'fas fa-handshake',
                'is_core': False,
            },
            {
                'name': _('Front Desk Management'),
                'name_ar': 'إدارة الفرونت ديسك',
                'code': 'frontdesk',
                'description': _('Manage reception, visitor management, appointments, and front office operations.'),
                'description_ar': 'إدارة الاستقبال، إدارة الزوار، المواعيد، وعمليات المكتب الأمامي.',
                'version': '1.0.0',
                'icon': 'fas fa-desktop',
                'is_core': False,
            },
            {
                'name': _('E-Commerce Management'),
                'name_ar': 'إدارة المتجر الإلكتروني',
                'code': 'ecommerce',
                'description': _('Manage online store, products, orders, and customer accounts.'),
                'description_ar': 'إدارة المتجر الإلكتروني، المنتجات، الطلبات، وحسابات العملاء.',
                'version': '1.0.0',
                'icon': 'fas fa-shopping-bag',
                'is_core': False,
            },
            {
                'name': _('System Settings'),
                'name_ar': 'الإعدادات العامة للنظام',
                'code': 'settings',
                'description': _('Manage system configurations, preferences, and global settings.'),
                'description_ar': 'إدارة إعدادات النظام، التفضيلات، والإعدادات العامة.',
                'version': '1.0.0',
                'icon': 'fas fa-cogs',
                'is_core': True,
            },
        ]

        # Create modules
        created_count = 0
        updated_count = 0
        
        for module_data in modules_data:
            module, created = Module.objects.update_or_create(
                code=module_data['code'],
                defaults={
                    'name': module_data['name'],
                    'description': module_data['description'],
                    'version': module_data['version'],
                    'icon': module_data['icon'],
                    'is_core': module_data['is_core'],
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f'Created module: {module.name}'))
            else:
                updated_count += 1
                self.stdout.write(self.style.WARNING(f'Updated module: {module.name}'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} modules and updated {updated_count} modules.'))
