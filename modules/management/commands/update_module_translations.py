from django.core.management.base import BaseCommand
from modules.models import Module
from django.utils.translation import gettext_lazy as _

class Command(BaseCommand):
    help = 'Updates module names and descriptions with translatable strings'

    def handle(self, *args, **options):
        # Define module data with translatable strings
        module_data = [
            {
                'code': 'hr',
                'name': _('Human Resources Management'),
                'description': _('Manage your organization\'s most valuable asset - your people.'),
            },
            {
                'code': 'inventory',
                'name': _('Inventory Management'),
                'description': _('Keep track of your stock levels, manage warehouses, and optimize inventory operations.'),
            },
            {
                'code': 'procurement',
                'name': _('Procurement Management'),
                'description': _('Streamline your purchasing process from requisition to payment.'),
            },
            {
                'code': 'sales',
                'name': _('Sales Management'),
                'description': _('Boost your sales performance with comprehensive tools for quotations, orders, and invoicing.'),
            },
            {
                'code': 'pos',
                'name': _('Point of Sale'),
                'description': _('Manage your retail operations with an easy-to-use point of sale system.'),
            },
            {
                'code': 'hotel',
                'name': _('Hotel Management'),
                'description': _('Manage your hotel operations efficiently.'),
            },
            {
                'code': 'restaurant',
                'name': _('Restaurant & Cafe Management'),
                'description': _('Run your food service business smoothly.'),
            },
            {
                'code': 'accounting',
                'name': _('Accounting Management'),
                'description': _('Keep your finances in order with comprehensive accounting tools.'),
            },
            {
                'code': 'finance',
                'name': _('Financial Management'),
                'description': _('Take control of your financial operations.'),
            },
            {
                'code': 'manufacturing',
                'name': _('Manufacturing Management'),
                'description': _('Optimize your production processes.'),
            },
            {
                'code': 'project',
                'name': _('Project Management'),
                'description': _('Deliver projects on time and within budget.'),
            },
            {
                'code': 'fleet',
                'name': _('Fleet Management'),
                'description': _('Manage your vehicle fleet efficiently.'),
            },
            {
                'code': 'document',
                'name': _('Document Management'),
                'description': _('Organize and secure your documents.'),
            },
            {
                'code': 'crm',
                'name': _('Customer Relationship Management'),
                'description': _('Build stronger customer relationships.'),
            },
            {
                'code': 'frontdesk',
                'name': _('Front Desk Management'),
                'description': _('Streamline your reception operations.'),
            },
            {
                'code': 'ecommerce',
                'name': _('E-Commerce Management'),
                'description': _('Expand your business online.'),
            },
            {
                'code': 'settings',
                'name': _('System Settings'),
                'description': _('Configure your system to match your business needs.'),
            },
        ]
        
        # Update module names and descriptions
        updated_count = 0
        
        for data in module_data:
            try:
                module = Module.objects.get(code=data['code'])
                module.name = data['name']
                module.description = data['description']
                module.save()
                updated_count += 1
                self.stdout.write(self.style.SUCCESS(f'Updated module {module.code}'))
            except Module.DoesNotExist:
                self.stdout.write(self.style.WARNING(f'Module with code {data["code"]} not found'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} modules'))
