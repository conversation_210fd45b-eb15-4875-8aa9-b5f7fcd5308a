from django.core.management.base import BaseCommand
from modules.models import Module
from django.utils.translation import gettext_lazy as _

class Command(BaseCommand):
    help = 'Updates module icons with more distinctive ones'

    def handle(self, *args, **options):
        # Define custom icons for each module
        module_icons = {
            'hr': 'fas fa-user-tie',
            'inventory': 'fas fa-warehouse',
            'procurement': 'fas fa-shopping-cart',
            'sales': 'fas fa-chart-line',
            'pos': 'fas fa-cash-register',
            'hotel': 'fas fa-hotel',
            'restaurant': 'fas fa-utensils',
            'accounting': 'fas fa-calculator',
            'finance': 'fas fa-money-bill-wave',
            'manufacturing': 'fas fa-industry',
            'project': 'fas fa-project-diagram',
            'fleet': 'fas fa-truck',
            'document': 'fas fa-file-archive',
            'crm': 'fas fa-handshake',
            'frontdesk': 'fas fa-desktop',
            'ecommerce': 'fas fa-shopping-bag',
            'settings': 'fas fa-cogs',
        }
        
        # Update module icons
        updated_count = 0
        
        for code, icon in module_icons.items():
            try:
                module = Module.objects.get(code=code)
                module.icon = icon
                module.save()
                updated_count += 1
                self.stdout.write(self.style.SUCCESS(f'Updated icon for {module.name} to {icon}'))
            except Module.DoesNotExist:
                self.stdout.write(self.style.WARNING(f'Module with code {code} not found'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} module icons'))
