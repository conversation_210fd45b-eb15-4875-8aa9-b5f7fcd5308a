"""
Django management command to create sample HR data (attendance, commissions, etc.)
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils.translation import gettext as _
from django.utils import timezone
from companies.models import Company
from modules.hr.models import (
    Employee, Attendance, AttendanceSummary, Commission, Payroll,
    AdvanceRequest, Contract, Disciplinary, Investigation
)
from datetime import date, datetime, timedelta
import random
from decimal import Decimal


class Command(BaseCommand):
    help = 'Create sample HR data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create sample data for',
            required=True
        )

    def handle(self, *args, **options):
        company_id = options['company_id']

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise CommandError(f'Company with ID {company_id} does not exist')

        # Get employees
        employees = Employee.objects.filter(company=company, status='active')
        if not employees.exists():
            raise CommandError(f'No active employees found for company {company.name}')

        self.stdout.write(f'Creating sample HR data for company: {company.name}')

        # Create attendance records for the last 30 days
        self.create_attendance_records(employees)

        # Create commission records
        self.create_commission_records(employees)

        # Create advance requests
        self.create_advance_requests(employees)

        # Create contracts
        self.create_contracts(employees)

        # Create some disciplinary actions
        self.create_disciplinary_actions(employees)

        # Create investigations
        self.create_investigations(employees)

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample HR data for company {company.name}'
            )
        )

    def create_attendance_records(self, employees):
        """إنشاء سجلات حضور للـ 30 يوم الماضية"""
        self.stdout.write('Creating attendance records...')

        end_date = date.today()
        start_date = end_date - timedelta(days=30)

        attendance_count = 0

        for employee in employees:
            current_date = start_date
            while current_date <= end_date:
                # تخطي عطلات نهاية الأسبوع (الجمعة والسبت)
                if current_date.weekday() in [4, 5]:  # Friday=4, Saturday=5
                    current_date += timedelta(days=1)
                    continue

                # احتمال 90% للحضور
                if random.random() < 0.9:
                    # وقت الدخول (8:00 - 9:30)
                    check_in_hour = random.randint(8, 9)
                    check_in_minute = random.randint(0, 30) if check_in_hour == 9 else random.randint(0, 59)
                    check_in_time = datetime.combine(current_date, datetime.min.time().replace(
                        hour=check_in_hour, minute=check_in_minute
                    ))

                    # إنشاء سجل دخول
                    check_in_attendance, created = Attendance.objects.get_or_create(
                        employee=employee,
                        date=current_date,
                        attendance_type='check_in',
                        defaults={
                            'time': check_in_time.time(),
                            'entry_method': 'biometric'
                        }
                    )
                    if created:
                        attendance_count += 1

                    # وقت الخروج (16:30 - 18:00)
                    check_out_hour = random.randint(16, 17)
                    check_out_minute = random.randint(30, 59) if check_out_hour == 16 else random.randint(0, 59)
                    check_out_time = datetime.combine(current_date, datetime.min.time().replace(
                        hour=check_out_hour, minute=check_out_minute
                    ))

                    # إنشاء سجل خروج
                    check_out_attendance, created = Attendance.objects.get_or_create(
                        employee=employee,
                        date=current_date,
                        attendance_type='check_out',
                        defaults={
                            'time': check_out_time.time(),
                            'entry_method': 'biometric'
                        }
                    )
                    if created:
                        attendance_count += 1

                    # حساب ساعات العمل
                    working_hours = (check_out_time - check_in_time).total_seconds() / 3600
                    is_late = check_in_time.time() > datetime.min.time().replace(hour=8, minute=15)

                    # إنشاء ملخص الحضور (أو تحديثه إذا كان موجوداً)
                    summary, created = AttendanceSummary.objects.get_or_create(
                        employee=employee,
                        date=current_date,
                        defaults={
                            'check_in_time': check_in_time.time(),
                            'check_out_time': check_out_time.time(),
                            'working_hours': working_hours,
                            'overtime_hours': max(0, working_hours - 8),
                            'is_absent': False,
                            'is_late': is_late
                        }
                    )
                else:
                    # غياب
                    summary, created = AttendanceSummary.objects.get_or_create(
                        employee=employee,
                        date=current_date,
                        defaults={
                            'is_absent': True,
                            'is_late': False
                        }
                    )

                current_date += timedelta(days=1)

        self.stdout.write(f'  Created {attendance_count} attendance records')

    def create_commission_records(self, employees):
        """إنشاء سجلات عمولات"""
        self.stdout.write('Creating commission records...')

        commission_count = 0
        commission_types = ['sales', 'target_achievement', 'bonus', 'incentive']
        statuses = ['pending', 'approved', 'rejected', 'paid']

        for employee in employees:
            # إنشاء 3-7 عمولات لكل موظف
            num_commissions = random.randint(3, 7)

            for _ in range(num_commissions):
                commission_type = random.choice(commission_types)
                amount = Decimal(str(random.randint(500, 5000)))
                status = random.choice(statuses)

                # تاريخ العمولة (آخر 3 أشهر)
                days_ago = random.randint(1, 90)
                commission_date = date.today() - timedelta(days=days_ago)

                Commission.objects.create(
                    employee=employee,
                    commission_type=commission_type,
                    amount=amount,
                    percentage=random.randint(1, 10) if commission_type == 'sales' else None,
                    base_amount=amount * Decimal('1.2') if commission_type == 'sales' else None,
                    period_start=commission_date - timedelta(days=30),
                    period_end=commission_date,
                    status=status,
                    notes=f'عمولة {commission_type} للموظف {employee.full_name}'
                )
                commission_count += 1

        self.stdout.write(f'  Created {commission_count} commission records')

    def create_advance_requests(self, employees):
        """إنشاء طلبات سلف"""
        self.stdout.write('Creating advance requests...')

        advance_count = 0
        advance_types = ['salary', 'emergency', 'travel', 'other']
        statuses = ['pending', 'approved', 'rejected', 'paid']

        # إنشاء سلف لـ 50% من الموظفين
        selected_employees = random.sample(list(employees), len(employees) // 2)

        for employee in selected_employees:
            advance_type = random.choice(advance_types)
            amount = Decimal(str(random.randint(1000, int(employee.basic_salary))))
            status = random.choice(statuses)

            AdvanceRequest.objects.create(
                employee=employee,
                advance_type=advance_type,
                amount=amount,
                reason=f'طلب سلفة {advance_type} للموظف {employee.full_name}',
                required_date=date.today() + timedelta(days=random.randint(1, 30)),
                installments=random.randint(1, 12),
                status=status
            )
            advance_count += 1

        self.stdout.write(f'  Created {advance_count} advance requests')

    def create_contracts(self, employees):
        """إنشاء عقود"""
        self.stdout.write('Creating contracts...')

        contract_count = 0
        contract_types = ['permanent', 'temporary', 'part_time', 'consultant', 'trainee']

        for i, employee in enumerate(employees, 1):
            contract_type = random.choice(contract_types)
            start_date = employee.hire_date

            # تحديد تاريخ انتهاء العقد
            if contract_type == 'permanent':
                end_date = None
            else:
                end_date = start_date + timedelta(days=random.randint(365, 1095))  # 1-3 سنوات

            # إنشاء رقم عقد فريد
            contract_number = f'CON-{employee.employee_id}-{i:04d}'

            Contract.objects.create(
                employee=employee,
                contract_number=contract_number,
                contract_type=contract_type,
                start_date=start_date,
                end_date=end_date,
                position_title=employee.position.title if employee.position else 'موظف',
                basic_salary=employee.basic_salary,
                working_hours=8,
                probation_period=random.randint(1, 6) if contract_type != 'permanent' else 0,
                terms_and_conditions='شروط وأحكام العقد القياسية',
                benefits='المزايا الأساسية للموظف',
                status='active'
            )
            contract_count += 1

        self.stdout.write(f'  Created {contract_count} contracts')

    def create_disciplinary_actions(self, employees):
        """إنشاء إجراءات تأديبية"""
        self.stdout.write('Creating disciplinary actions...')

        disciplinary_count = 0
        disciplinary_types = ['warning', 'written_warning', 'suspension', 'salary_deduction', 'termination']

        # إنشاء إجراءات تأديبية لـ 20% من الموظفين
        selected_employees = random.sample(list(employees), max(1, len(employees) // 5))

        for employee in selected_employees:
            disciplinary_type = random.choice(disciplinary_types[:-1])  # استبعاد الفصل

            # اختيار مستخدم آخر كمبلغ (إذا كان متاحاً)
            from users.models import User
            users = User.objects.filter(is_active=True)
            reporter = random.choice(users) if users.exists() else None

            Disciplinary.objects.create(
                employee=employee,
                reported_by=reporter,
                disciplinary_number=f'DIS-{employee.employee_id}-{random.randint(100, 999)}',
                disciplinary_type=disciplinary_type,
                incident_date=date.today() - timedelta(days=random.randint(1, 60)),
                violation_description=f'مخالفة من نوع {disciplinary_type}',
                evidence='الأدلة والشواهد',
                penalty_description=f'الجزاء المطبق: {disciplinary_type}',
                suspension_days=random.randint(1, 7) if disciplinary_type == 'suspension' else None,
                deduction_amount=Decimal(str(random.randint(100, 1000))) if disciplinary_type == 'salary_deduction' else None,
                status='implemented'
            )
            disciplinary_count += 1

        self.stdout.write(f'  Created {disciplinary_count} disciplinary actions')

    def create_investigations(self, employees):
        """إنشاء تحقيقات"""
        self.stdout.write('Creating investigations...')

        investigation_count = 0
        investigation_types = ['misconduct', 'policy_violation', 'harassment', 'theft', 'other']
        statuses = ['opened', 'in_progress', 'closed']

        # إنشاء تحقيقات لـ 10% من الموظفين
        selected_employees = random.sample(list(employees), max(1, len(employees) // 10))

        for employee in selected_employees:
            investigation_type = random.choice(investigation_types)
            status = random.choice(statuses)

            # اختيار مستخدم كمحقق
            from users.models import User
            users = User.objects.filter(is_active=True)
            investigator = random.choice(users) if users.exists() else None

            Investigation.objects.create(
                employee=employee,
                investigation_number=f'INV-{employee.employee_id}-{random.randint(100, 999)}',
                investigation_type=investigation_type,
                case_title=f'تحقيق في قضية {investigation_type}',
                case_description=f'وصف تفصيلي للقضية المتعلقة بـ {investigation_type}',
                incident_date=date.today() - timedelta(days=random.randint(1, 30)),
                investigator=investigator,
                witnesses='الشهود المعنيون',
                evidence_collected='الأدلة المجمعة',
                status=status
            )
            investigation_count += 1

        self.stdout.write(f'  Created {investigation_count} investigations')
