"""
Django management command to create test data for HR module
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils.translation import gettext as _
from django.utils import timezone
from companies.models import Company, Branch
from modules.hr.models import (
    Department, Position, Employee, WorkSchedule, EmployeeSchedule,
    AttendanceDevice, SalesTeam, SalesRepresentative
)
from users.models import User
from datetime import date, timedelta
import random


class Command(BaseCommand):
    help = 'Create test data for HR module'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create test data for',
            required=True
        )

    def handle(self, *args, **options):
        company_id = options['company_id']

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise CommandError(f'Company with ID {company_id} does not exist')

        # Get the first branch for this company
        branch = Branch.objects.filter(company=company).first()
        if not branch:
            raise CommandError(f'No branches found for company {company.name}')

        self.stdout.write(f'Creating HR test data for company: {company.name}')

        # Create departments
        departments_data = [
            {'name': 'الإدارة العامة', 'code': 'MGMT', 'description': 'الإدارة العليا والتخطيط الاستراتيجي'},
            {'name': 'الموارد البشرية', 'code': 'HR', 'description': 'إدارة شؤون الموظفين'},
            {'name': 'المحاسبة والمالية', 'code': 'FIN', 'description': 'الشؤون المالية والمحاسبية'},
            {'name': 'المبيعات', 'code': 'SALES', 'description': 'فريق المبيعات والتسويق'},
            {'name': 'تقنية المعلومات', 'code': 'IT', 'description': 'الدعم التقني وتطوير الأنظمة'},
            {'name': 'العمليات', 'code': 'OPS', 'description': 'العمليات التشغيلية'},
        ]

        departments = {}
        for dept_data in departments_data:
            dept, created = Department.objects.get_or_create(
                code=dept_data['code'],
                company=company,
                defaults={
                    'name': dept_data['name'],
                    'description': dept_data['description'],
                    'is_active': True
                }
            )
            departments[dept_data['code']] = dept
            if created:
                self.stdout.write(f'  Created department: {dept.name}')

        # Create positions
        positions_data = [
            {'title': 'المدير العام', 'code': 'CEO', 'department': 'MGMT', 'level': 1},
            {'title': 'مدير الموارد البشرية', 'code': 'HR_MGR', 'department': 'HR', 'level': 2},
            {'title': 'مدير المحاسبة', 'code': 'FIN_MGR', 'department': 'FIN', 'level': 2},
            {'title': 'مدير المبيعات', 'code': 'SALES_MGR', 'department': 'SALES', 'level': 2},
            {'title': 'مدير تقنية المعلومات', 'code': 'IT_MGR', 'department': 'IT', 'level': 2},
            {'title': 'أخصائي موارد بشرية', 'code': 'HR_SPEC', 'department': 'HR', 'level': 3},
            {'title': 'محاسب', 'code': 'ACCOUNTANT', 'department': 'FIN', 'level': 3},
            {'title': 'مندوب مبيعات', 'code': 'SALES_REP', 'department': 'SALES', 'level': 3},
            {'title': 'مطور برمجيات', 'code': 'DEV', 'department': 'IT', 'level': 3},
            {'title': 'فني دعم', 'code': 'SUPPORT', 'department': 'IT', 'level': 4},
        ]

        positions = {}
        for pos_data in positions_data:
            pos, created = Position.objects.get_or_create(
                code=pos_data['code'],
                company=company,
                defaults={
                    'title': pos_data['title'],
                    'department': departments[pos_data['department']],
                    'level': pos_data['level'],
                    'is_active': True
                }
            )
            positions[pos_data['code']] = pos
            if created:
                self.stdout.write(f'  Created position: {pos.title}')

        # Create work schedule
        schedule, created = WorkSchedule.objects.get_or_create(
            name='الدوام الرسمي',
            company=company,
            defaults={
                'start_time': '08:00',
                'end_time': '17:00',
                'break_start': '12:00',
                'break_end': '13:00',
                'working_days': '1,2,3,4,5',  # Monday to Friday
                'grace_period_minutes': 15,
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'  Created work schedule: {schedule.name}')

        # Create attendance device
        device, created = AttendanceDevice.objects.get_or_create(
            name='جهاز البصمة الرئيسي',
            company=company,
            branch=branch,
            defaults={
                'device_type': 'biometric',
                'location': 'المدخل الرئيسي',
                'is_active': True
            }
        )
        if created:
            self.stdout.write(f'  Created attendance device: {device.name}')

        # Create employees
        employees_data = [
            {'first_name': 'أحمد', 'last_name': 'محمد', 'position': 'CEO', 'salary': 15000},
            {'first_name': 'فاطمة', 'last_name': 'علي', 'position': 'HR_MGR', 'salary': 8000},
            {'first_name': 'محمد', 'last_name': 'حسن', 'position': 'FIN_MGR', 'salary': 9000},
            {'first_name': 'نورا', 'last_name': 'أحمد', 'position': 'SALES_MGR', 'salary': 10000},
            {'first_name': 'خالد', 'last_name': 'سالم', 'position': 'IT_MGR', 'salary': 8500},
            {'first_name': 'مريم', 'last_name': 'عبدالله', 'position': 'HR_SPEC', 'salary': 5000},
            {'first_name': 'عمر', 'last_name': 'يوسف', 'position': 'ACCOUNTANT', 'salary': 6000},
            {'first_name': 'سارة', 'last_name': 'محمود', 'position': 'SALES_REP', 'salary': 4500},
            {'first_name': 'حسام', 'last_name': 'طارق', 'position': 'DEV', 'salary': 7000},
            {'first_name': 'ليلى', 'last_name': 'فاروق', 'position': 'SUPPORT', 'salary': 4000},
        ]

        employees = []
        for i, emp_data in enumerate(employees_data, 1):
            # Generate unique national ID
            national_id = f"2900101{i:06d}"
            
            emp, created = Employee.objects.get_or_create(
                national_id=national_id,
                company=company,
                defaults={
                    'first_name': emp_data['first_name'],
                    'last_name': emp_data['last_name'],
                    'arabic_name': f"{emp_data['first_name']} {emp_data['last_name']}",
                    'gender': random.choice(['male', 'female']),
                    'birth_date': date(1990, random.randint(1, 12), random.randint(1, 28)),
                    'hire_date': date.today() - timedelta(days=random.randint(30, 365)),
                    'mobile': f"01{random.randint(*********, *********)}",
                    'email': f"{emp_data['first_name'].lower()}.{emp_data['last_name'].lower()}@{company.name.lower()}.com",
                    'branch': branch,
                    'department': positions[emp_data['position']].department,
                    'position': positions[emp_data['position']],
                    'basic_salary': emp_data['salary'],
                    'status': 'active'
                }
            )
            
            if created:
                employees.append(emp)
                self.stdout.write(f'  Created employee: {emp.full_name}')
                
                # Create employee schedule
                EmployeeSchedule.objects.create(
                    employee=emp,
                    schedule=schedule,
                    start_date=emp.hire_date,
                    is_active=True
                )

        # Create sales team
        if employees:
            sales_manager = next((emp for emp in employees if emp.position.code == 'SALES_MGR'), None)
            if sales_manager:
                team, created = SalesTeam.objects.get_or_create(
                    code='TEAM001',
                    company=company,
                    defaults={
                        'name': 'فريق المبيعات الأول',
                        'manager': sales_manager,
                        'target_amount': 100000,
                        'commission_rate': 5.0,
                        'is_active': True
                    }
                )
                
                if created:
                    self.stdout.write(f'  Created sales team: {team.name}')
                    
                    # Add sales representatives to team
                    sales_reps = [emp for emp in employees if emp.position.code == 'SALES_REP']
                    for i, rep in enumerate(sales_reps, 1):
                        sales_rep, created = SalesRepresentative.objects.get_or_create(
                            employee=rep,
                            defaults={
                                'rep_code': f'REP{i:03d}',
                                'team': team,
                                'territory': f'المنطقة {i}',
                                'commission_rate': 3.0,
                                'target_amount': 20000,
                                'is_active': True
                            }
                        )
                        
                        if created:
                            self.stdout.write(f'    Added sales rep: {rep.full_name}')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created HR test data for company {company.name}'
            )
        )

        # Display summary
        self.stdout.write('\nSummary:')
        self.stdout.write(f'  Departments: {Department.objects.filter(company=company).count()}')
        self.stdout.write(f'  Positions: {Position.objects.filter(company=company).count()}')
        self.stdout.write(f'  Employees: {Employee.objects.filter(company=company).count()}')
        self.stdout.write(f'  Sales Teams: {SalesTeam.objects.filter(company=company).count()}')
        self.stdout.write(f'  Sales Representatives: {SalesRepresentative.objects.filter(employee__company=company).count()}')
