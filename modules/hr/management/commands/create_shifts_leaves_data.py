"""
إنشاء بيانات تجريبية للشفتات والإجازات
Create sample data for shifts and leaves
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta, time
from decimal import Decimal

from companies.models import Company
from modules.hr.models import Shift, LeaveType, LeaveRequest, Employee


class Command(BaseCommand):
    help = 'Create sample shifts and leave data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create data for',
        )

    def handle(self, *args, **options):
        company_id = options.get('company_id')
        
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Company with ID {company_id} does not exist')
                )
                return
        else:
            company = Company.objects.first()
            if not company:
                self.stdout.write(
                    self.style.ERROR('No companies found. Please create a company first.')
                )
                return

        self.stdout.write(f'Creating sample data for company: {company.name}')

        # إنشاء الشفتات
        self.create_shifts(company)
        
        # إنشاء أنواع الإجازات
        self.create_leave_types(company)
        
        # إنشاء طلبات إجازات تجريبية
        self.create_leave_requests(company)

        self.stdout.write(
            self.style.SUCCESS('Successfully created sample shifts and leave data')
        )

    def create_shifts(self, company):
        """إنشاء شفتات تجريبية"""
        shifts_data = [
            {
                'name': 'الشفت الصباحي',
                'shift_type': 'morning',
                'start_time': time(8, 0),
                'end_time': time(16, 0),
                'break_start': time(12, 0),
                'break_end': time(13, 0),
                'break_duration': timedelta(hours=1),
                'monday': True,
                'tuesday': True,
                'wednesday': True,
                'thursday': True,
                'friday': True,
                'saturday': False,
                'sunday': False,
            },
            {
                'name': 'الشفت المسائي',
                'shift_type': 'evening',
                'start_time': time(16, 0),
                'end_time': time(0, 0),  # منتصف الليل
                'break_start': time(20, 0),
                'break_end': time(21, 0),
                'break_duration': timedelta(hours=1),
                'monday': True,
                'tuesday': True,
                'wednesday': True,
                'thursday': True,
                'friday': True,
                'saturday': False,
                'sunday': False,
            },
            {
                'name': 'الشفت الليلي',
                'shift_type': 'night',
                'start_time': time(0, 0),  # منتصف الليل
                'end_time': time(8, 0),
                'break_start': time(4, 0),
                'break_end': time(5, 0),
                'break_duration': timedelta(hours=1),
                'monday': True,
                'tuesday': True,
                'wednesday': True,
                'thursday': True,
                'friday': True,
                'saturday': False,
                'sunday': False,
            },
            {
                'name': 'الشفت المرن',
                'shift_type': 'flexible',
                'start_time': time(9, 0),
                'end_time': time(17, 0),
                'break_start': time(12, 30),
                'break_end': time(13, 30),
                'break_duration': timedelta(hours=1),
                'monday': True,
                'tuesday': True,
                'wednesday': True,
                'thursday': True,
                'friday': True,
                'saturday': True,
                'sunday': False,
            },
        ]

        for shift_data in shifts_data:
            shift, created = Shift.objects.get_or_create(
                name=shift_data['name'],
                company=company,
                defaults=shift_data
            )
            if created:
                self.stdout.write(f'Created shift: {shift.name}')
            else:
                self.stdout.write(f'Shift already exists: {shift.name}')

    def create_leave_types(self, company):
        """إنشاء أنواع الإجازات"""
        leave_types_data = [
            {
                'name': 'إجازة سنوية',
                'code': 'ANNUAL',
                'max_days_per_year': 30,
                'max_consecutive_days': 15,
                'min_notice_days': 7,
                'is_paid': True,
                'salary_percentage': Decimal('100.00'),
                'requires_approval': True,
                'requires_medical_certificate': False,
                'carry_forward': True,
            },
            {
                'name': 'إجازة مرضية',
                'code': 'SICK',
                'max_days_per_year': 15,
                'max_consecutive_days': 7,
                'min_notice_days': 1,
                'is_paid': True,
                'salary_percentage': Decimal('100.00'),
                'requires_approval': True,
                'requires_medical_certificate': True,
                'carry_forward': False,
            },
            {
                'name': 'إجازة طارئة',
                'code': 'EMERGENCY',
                'max_days_per_year': 5,
                'max_consecutive_days': 3,
                'min_notice_days': 0,
                'is_paid': True,
                'salary_percentage': Decimal('100.00'),
                'requires_approval': True,
                'requires_medical_certificate': False,
                'carry_forward': False,
            },
            {
                'name': 'إجازة أمومة',
                'code': 'MATERNITY',
                'max_days_per_year': 90,
                'max_consecutive_days': 90,
                'min_notice_days': 30,
                'is_paid': True,
                'salary_percentage': Decimal('100.00'),
                'requires_approval': True,
                'requires_medical_certificate': True,
                'carry_forward': False,
            },
            {
                'name': 'إجازة بدون راتب',
                'code': 'UNPAID',
                'max_days_per_year': 30,
                'max_consecutive_days': 30,
                'min_notice_days': 14,
                'is_paid': False,
                'salary_percentage': Decimal('0.00'),
                'requires_approval': True,
                'requires_medical_certificate': False,
                'carry_forward': False,
            },
        ]

        for leave_type_data in leave_types_data:
            leave_type, created = LeaveType.objects.get_or_create(
                code=leave_type_data['code'],
                company=company,
                defaults=leave_type_data
            )
            if created:
                self.stdout.write(f'Created leave type: {leave_type.name}')
            else:
                self.stdout.write(f'Leave type already exists: {leave_type.name}')

    def create_leave_requests(self, company):
        """إنشاء طلبات إجازات تجريبية"""
        # الحصول على الموظفين والإجازات
        employees = Employee.objects.filter(company=company, status='active')[:3]
        leave_types = LeaveType.objects.filter(company=company)

        if not employees.exists():
            self.stdout.write('No active employees found. Skipping leave requests creation.')
            return

        if not leave_types.exists():
            self.stdout.write('No leave types found. Skipping leave requests creation.')
            return

        # إنشاء طلبات تجريبية
        today = timezone.now().date()
        
        leave_requests_data = [
            {
                'employee': employees[0],
                'leave_type': leave_types.filter(code='ANNUAL').first(),
                'start_date': today + timedelta(days=30),
                'end_date': today + timedelta(days=37),
                'reason': 'إجازة سنوية للراحة والاستجمام',
                'status': 'pending',
            },
            {
                'employee': employees[1] if len(employees) > 1 else employees[0],
                'leave_type': leave_types.filter(code='SICK').first(),
                'start_date': today + timedelta(days=5),
                'end_date': today + timedelta(days=7),
                'reason': 'إجازة مرضية - زيارة طبيب',
                'status': 'pending',
            },
        ]

        if len(employees) > 2:
            leave_requests_data.append({
                'employee': employees[2],
                'leave_type': leave_types.filter(code='EMERGENCY').first(),
                'start_date': today + timedelta(days=2),
                'end_date': today + timedelta(days=2),
                'reason': 'ظرف طارئ عائلي',
                'status': 'approved',
            })

        for request_data in leave_requests_data:
            if request_data['leave_type']:
                leave_request, created = LeaveRequest.objects.get_or_create(
                    employee=request_data['employee'],
                    start_date=request_data['start_date'],
                    end_date=request_data['end_date'],
                    defaults=request_data
                )
                if created:
                    self.stdout.write(f'Created leave request for: {leave_request.employee.full_name}')
                else:
                    self.stdout.write(f'Leave request already exists for: {leave_request.employee.full_name}')
