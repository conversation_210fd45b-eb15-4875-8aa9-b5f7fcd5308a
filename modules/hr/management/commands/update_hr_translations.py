import os
from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class Command(BaseCommand):
    help = 'Updates HR module translations with common terms'

    def handle(self, *args, **options):
        # Common HR translations
        hr_translations = {
            # Basic terms
            'Human Resources': 'الموارد البشرية',
            'HR Management': 'إدارة الموارد البشرية',
            'Dashboard': 'لوحة التحكم',
            'Employee': 'الموظف',
            'Employees': 'الموظفون',
            'Department': 'القسم',
            'Departments': 'الأقسام',
            'Position': 'المنصب',
            'Salary': 'الراتب',
            'Basic Salary': 'الراتب الأساسي',
            
            # Advances
            'Advances': 'السلف',
            'Advance': 'سلفة',
            'Advance Request': 'طلب سلفة',
            'Employee Advances': 'سلف الموظفين',
            'New Advance Request': 'طلب سلفة جديد',
            'Pending Advances': 'السلف المعلقة',
            'Salary Advance': 'سلفة راتب',
            'Emergency': 'طوارئ',
            'Travel': 'سفر',
            'Installments': 'الأقساط',
            'Required Date': 'التاريخ المطلوب',
            'Reason': 'السبب',
            
            # Custodies
            'Custodies': 'العهد',
            'Custody': 'عهدة',
            'Employee Custodies': 'عهد الموظفين',
            'Custody Details': 'تفاصيل العهدة',
            'New Custody Record': 'سجل عهدة جديد',
            'Active Custodies': 'العهد النشطة',
            'Custody Number': 'رقم العهدة',
            'Equipment': 'معدات',
            'Vehicle': 'مركبة',
            'Tools': 'أدوات',
            'Documents': 'مستندات',
            'Cash': 'نقدية',
            'Item Name': 'اسم الصنف',
            'Serial Number': 'الرقم التسلسلي',
            'Quantity': 'الكمية',
            'Total Value': 'القيمة الإجمالية',
            'Delivery Date': 'تاريخ التسليم',
            'Expected Return Date': 'تاريخ الإرجاع المتوقع',
            'Delivered By': 'سُلم بواسطة',
            'Condition on Delivery': 'الحالة عند التسليم',
            
            # Status
            'Pending': 'معلق',
            'Approved': 'موافق عليه',
            'Rejected': 'مرفوض',
            'Paid': 'مدفوع',
            'Active': 'نشط',
            'Inactive': 'غير نشط',
            'Returned': 'مُرجع',
            'Lost': 'مفقود',
            'Damaged': 'تالف',
            'Overdue': 'متأخر',
            
            # Actions
            'Save': 'حفظ',
            'Cancel': 'إلغاء',
            'Edit': 'تعديل',
            'Delete': 'حذف',
            'View': 'عرض',
            'Create': 'إنشاء',
            'Add': 'إضافة',
            'Search': 'بحث',
            'Filter': 'تصفية',
            'Back': 'رجوع',
            'Approve': 'موافقة',
            'Reject': 'رفض',
            'Return': 'إرجاع',
            
            # Navigation
            'Quick Actions': 'الإجراءات السريعة',
            'Main Features': 'الميزات الرئيسية',
            'View All': 'عرض الكل',
            'Back to List': 'العودة للقائمة',
            
            # Alerts
            'Pending Actions Required': 'إجراءات معلقة مطلوبة',
            'All Systems Running Smoothly': 'جميع الأنظمة تعمل بسلاسة',
            'High Volume of Advance Requests': 'حجم كبير من طلبات السلف',
            'Overdue Custodies Alert': 'تنبيه العهد المتأخرة',
            'No Advance Requests': 'لا توجد طلبات سلف',
            'No Custodies Found': 'لم يتم العثور على عهد',
            
            # Common phrases
            'You have': 'لديك',
            'Total': 'الإجمالي',
            'Amount': 'المبلغ',
            'Type': 'النوع',
            'Status': 'الحالة',
            'Actions': 'الإجراءات',
            'Date': 'التاريخ',
            'Name': 'الاسم',
            'Description': 'الوصف',
            'Notes': 'ملاحظات',
            'All': 'الكل',
            'Other': 'أخرى',
            'Close': 'إغلاق',
        }
        
        self.stdout.write(self.style.SUCCESS(f'HR module has {len(hr_translations)} translation entries'))
        self.stdout.write(self.style.SUCCESS('Translation entries are already defined in the .po file'))
        self.stdout.write(self.style.SUCCESS('Run "python manage.py compile_hr_translations" to compile them'))
