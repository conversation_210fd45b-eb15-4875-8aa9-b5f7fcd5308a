"""
Django management command to clear HR test data
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils.translation import gettext as _
from companies.models import Company
from modules.hr.models import (
    Attendance, AttendanceSummary, Commission, Payroll,
    AdvanceRequest, AdvanceInstallment, Contract, Disciplinary, Investigation
)


class Command(BaseCommand):
    help = 'Clear HR test data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to clear data for',
            required=True
        )
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm deletion',
        )

    def handle(self, *args, **options):
        company_id = options['company_id']
        confirm = options['confirm']

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise CommandError(f'Company with ID {company_id} does not exist')

        if not confirm:
            self.stdout.write(
                self.style.WARNING(
                    f'This will delete ALL HR data for company: {company.name}\n'
                    'Add --confirm to proceed'
                )
            )
            return

        self.stdout.write(f'Clearing HR data for company: {company.name}')

        # Clear attendance data
        attendance_count = Attendance.objects.filter(employee__company=company).count()
        Attendance.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {attendance_count} attendance records')

        summary_count = AttendanceSummary.objects.filter(employee__company=company).count()
        AttendanceSummary.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {summary_count} attendance summaries')

        # Clear commission data
        commission_count = Commission.objects.filter(employee__company=company).count()
        Commission.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {commission_count} commissions')

        # Clear advance data
        advance_count = AdvanceRequest.objects.filter(employee__company=company).count()
        AdvanceRequest.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {advance_count} advance requests')

        # Clear contract data
        contract_count = Contract.objects.filter(employee__company=company).count()
        Contract.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {contract_count} contracts')

        # Clear disciplinary data
        disciplinary_count = Disciplinary.objects.filter(employee__company=company).count()
        Disciplinary.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {disciplinary_count} disciplinary actions')

        # Clear investigation data
        investigation_count = Investigation.objects.filter(employee__company=company).count()
        Investigation.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {investigation_count} investigations')

        # Clear payroll data
        payroll_count = Payroll.objects.filter(employee__company=company).count()
        Payroll.objects.filter(employee__company=company).delete()
        self.stdout.write(f'  Deleted {payroll_count} payroll records')

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully cleared HR data for company {company.name}'
            )
        )
