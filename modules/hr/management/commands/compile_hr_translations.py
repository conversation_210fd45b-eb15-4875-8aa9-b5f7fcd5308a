import os
import polib
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Compiles HR module translation files (.po to .mo)'

    def handle(self, *args, **options):
        hr_locale_path = os.path.join(settings.BASE_DIR, 'modules', 'hr', 'locale')

        if not os.path.exists(hr_locale_path):
            self.stdout.write(self.style.ERROR(f'HR locale directory not found at {hr_locale_path}'))
            return

        compiled_count = 0
        
        for root, dirs, files in os.walk(hr_locale_path):
            for file in files:
                if file.endswith('.po'):
                    po_file_path = os.path.join(root, file)
                    mo_file_path = po_file_path[:-3] + '.mo'

                    try:
                        self.stdout.write(f'Processing {po_file_path}')
                        po = polib.pofile(po_file_path)
                        po.save_as_mofile(mo_file_path)
                        compiled_count += 1
                        self.stdout.write(self.style.SUCCESS(f'Successfully compiled {mo_file_path}'))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f'Error compiling {po_file_path}: {str(e)}'))

        self.stdout.write(self.style.SUCCESS(f'HR translation compilation complete. Compiled {compiled_count} files.'))
