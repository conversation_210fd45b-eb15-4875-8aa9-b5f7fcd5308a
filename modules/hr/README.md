# وحدة الموارد البشرية - HR Module

## نظرة عامة

وحدة الموارد البشرية هي نظام شامل لإدارة جميع جوانب الموارد البشرية في المؤسسة، بما في ذلك إدارة الموظفين، الحضور والانصراف، الرواتب، العمولات، السلف، العقود، والجزاءات.

## الميزات الرئيسية

### 1. إدارة الموظفين
- **ملفات الموظفين الشاملة**: معلومات شخصية، وظيفية، ومالية
- **الهيكل التنظيمي**: الأقسام والمناصب الوظيفية
- **إدارة المدراء المباشرين**: هيكل إداري متدرج
- **حالات الموظفين**: نشط، غير نشط، منتهي الخدمة، موقوف

### 2. نظام الحضور والانصراف
- **طرق متعددة للتسجيل**:
  - تسجيل يدوي
  - أجهزة البصمة
  - تطبيق الهاتف مع GPS
  - البوابة الإلكترونية
- **جداول العمل المرنة**
- **حساب الساعات الإضافية تلقائياً**
- **تتبع التأخير والانصراف المبكر**
- **تقارير الحضور التفصيلية**

### 3. إدارة المناديب وفرق المبيعات
- **فرق المبيعات**: تنظيم المناديب في فرق
- **إدارة المناطق الجغرافية**
- **تحديد الأهداف والنسب**
- **تتبع الأداء والإنجازات**

### 4. نظام العمولات
- **أنواع العمولات**:
  - عمولة المبيعات
  - عمولة تحقيق الهدف
  - مكافآت
  - حوافز
- **حساب العمولات تلقائياً**
- **نظام الموافقات**
- **إرسال للحسابات للصرف**

### 5. إدارة الرواتب
- **كشوف الرواتب الشهرية**
- **مكونات الراتب**:
  - الراتب الأساسي
  - البدلات
  - العمولات
  - الإضافي
- **الخصومات**:
  - السلف
  - التأمينات
  - الضرائب
  - الجزاءات
- **التكامل مع الحسابات**

### 6. السلف والعهد
- **طلبات السلف الإلكترونية**
- **أنواع السلف**:
  - سلفة راتب
  - سلفة طوارئ
  - سلفة سفر
  - أخرى
- **نظام التقسيط**
- **التصفية التلقائية من الراتب**
- **إرسال للحسابات**

### 7. إدارة العقود
- **أنواع العقود**:
  - دائم
  - مؤقت
  - جزئي
  - استشاري
  - تدريبي
- **إدارة دورة حياة العقد**
- **التوقيعات الإلكترونية**
- **تنبيهات انتهاء العقود**
- **تجديد العقود**

### 8. الجزاءات والتحقيقات
- **أنواع الجزاءات**:
  - إنذار
  - إنذار كتابي
  - إيقاف
  - خصم من الراتب
  - فصل
- **محاضر التحقيق**
- **جلسات التحقيق**
- **نظام الموافقات**
- **الأرشفة الإلكترونية**

### 9. التقارير الشاملة
- **تقارير الموظفين**
- **تقارير الحضور والانصراف**
- **تقارير الرواتب**
- **تقارير العمولات**
- **تقارير السلف**
- **تقارير العقود**
- **تقارير الجزاءات**

## النماذج الأساسية

### Employee (الموظف)
- المعلومات الشخصية والوظيفية
- الراتب الأساسي
- حالة الموظف
- تواريخ التوظيف والإنهاء

### Department (القسم)
- اسم ورمز القسم
- الوصف
- المدير المسؤول
- الهيكل الهرمي

### Position (المنصب)
- عنوان المنصب
- المستوى الوظيفي
- المتطلبات
- القسم التابع له

### Attendance (الحضور)
- تسجيلات الدخول والخروج
- طريقة التسجيل
- الموقع الجغرافي
- التحقق من الصحة

### Commission (العمولة)
- نوع العمولة
- المبلغ والنسبة
- فترة الاستحقاق
- حالة الموافقة

### Payroll (كشف الراتب)
- مكونات الراتب
- الخصومات
- صافي الراتب
- معلومات الحضور

### AdvanceRequest (طلب السلفة)
- نوع ومبلغ السلفة
- السبب والتبرير
- عدد الأقساط
- حالة الطلب

### Contract (العقد)
- نوع العقد
- تواريخ البداية والنهاية
- الشروط والأحكام
- حالة التوقيع

## التثبيت والإعداد

### 1. إضافة الوحدة
```python
# في settings.py
INSTALLED_APPS = [
    # ...
    'modules.hr',
]
```

### 2. تطبيق الهجرات
```bash
python manage.py makemigrations hr
python manage.py migrate hr
```

### 3. إنشاء البيانات التجريبية
```bash
python manage.py create_hr_test_data --company-id=1
```

### 4. إضافة الروابط
```python
# في urls.py
urlpatterns = [
    # ...
    path('hr/', include('modules.hr.urls')),
]
```

## الاستخدام

### الوصول للوحدة
```
http://localhost:8000/ar/hr/
```

### الصفحات الرئيسية
- **لوحة التحكم**: `/hr/`
- **قائمة الموظفين**: `/hr/employees/`
- **تسجيل الحضور**: `/hr/attendance/`
- **العمولات**: `/hr/commissions/`
- **الرواتب**: `/hr/payroll/`
- **السلف**: `/hr/advances/`
- **العقود**: `/hr/contracts/`

## API Endpoints

### الموظفين
- `GET /hr/api/employees/` - قائمة الموظفين
- `POST /hr/employees/create/` - إضافة موظف جديد

### الحضور
- `POST /hr/api/attendance/check-in/` - تسجيل دخول
- `POST /hr/api/attendance/check-out/` - تسجيل خروج
- `POST /hr/api/attendance/gps/` - تسجيل بالموقع

### العمولات
- `POST /hr/api/commission/calculate/` - حساب العمولة

## التكامل مع الوحدات الأخرى

### وحدة المحاسبة
- إرسال العمولات المعتمدة للحسابات
- إرسال كشوف الرواتب للحسابات
- إرسال السلف المدفوعة للحسابات

### وحدة الشركات
- ربط الموظفين بالشركات والفروع
- فلترة البيانات حسب الشركة النشطة

## الأمان والصلاحيات

- **التحقق من الهوية**: جميع الصفحات محمية بتسجيل الدخول
- **فلترة البيانات**: عرض بيانات الشركة النشطة فقط
- **نظام الموافقات**: موافقات متدرجة للعمليات المالية
- **تسجيل العمليات**: تتبع جميع التغييرات

## الاختبارات

### تشغيل الاختبارات
```bash
python manage.py test modules.hr
```

### البيانات التجريبية
- 6 أقسام
- 10 مناصب وظيفية
- 10 موظفين
- فريق مبيعات واحد
- مندوب مبيعات واحد

## التطوير المستقبلي

### المرحلة التالية
- [ ] تطوير تطبيق الهاتف للحضور
- [ ] تكامل مع أجهزة البصمة
- [ ] نظام الإجازات
- [ ] تقييم الأداء
- [ ] التدريب والتطوير
- [ ] إدارة المواهب

### التحسينات المطلوبة
- [ ] واجهة مستخدم محسنة
- [ ] تقارير أكثر تفصيلاً
- [ ] إشعارات تلقائية
- [ ] تصدير البيانات
- [ ] النسخ الاحتياطي

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع الوثائق التقنية
- تحقق من ملفات السجلات
- تواصل مع فريق التطوير

## الترخيص

هذه الوحدة جزء من نظام ERP وتخضع لنفس شروط الترخيص.
