from django import template
from django.db.models import Sum, Count
from django.utils.translation import gettext as _
from django.utils.translation import get_language

register = template.Library()

@register.filter
def add_class(field, css_class):
    """Add a CSS class to a form field"""
    return field.as_widget(attrs={"class": css_class})

@register.filter
def sum_advances(advances):
    """Sum advance amounts"""
    if not advances:
        return 0
    return advances.aggregate(total=Sum('amount'))['total'] or 0

@register.filter
def sum_custodies(custodies):
    """Sum custody values"""
    if not custodies:
        return 0
    return custodies.aggregate(total=Sum('total_value'))['total'] or 0

@register.filter
def count_pending(queryset):
    """Count pending items"""
    if not queryset:
        return 0
    return queryset.filter(status='pending').count()

@register.filter
def count_active(queryset):
    """Count active items"""
    if not queryset:
        return 0
    return queryset.filter(status='active').count()

@register.filter
def mul(value, arg):
    """Multiply filter"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    """Divide filter"""
    try:
        return float(value) / float(arg) if float(arg) != 0 else 0
    except (ValueError, TypeError):
        return 0

@register.filter
def percentage(value, total):
    """Calculate percentage"""
    try:
        if float(total) == 0:
            return 0
        return (float(value) / float(total)) * 100
    except (ValueError, TypeError):
        return 0

@register.simple_tag
def get_employee_count(company):
    """Get total employee count for a company"""
    from modules.hr.models import Employee
    return Employee.objects.filter(company=company, status='active').count()

@register.simple_tag
def get_pending_advances_count(company):
    """Get pending advances count for a company"""
    from modules.hr.models import AdvanceRequest
    return AdvanceRequest.objects.filter(
        employee__company=company, 
        status='pending'
    ).count()

@register.simple_tag
def get_active_custodies_count(company):
    """Get active custodies count for a company"""
    from modules.hr.models import Custody
    return Custody.objects.filter(
        employee__company=company, 
        status='active'
    ).count()

@register.simple_tag
def get_overdue_custodies_count(company):
    """Get overdue custodies count for a company"""
    from modules.hr.models import Custody
    from datetime import date
    return Custody.objects.filter(
        employee__company=company,
        status='active',
        expected_return_date__lt=date.today()
    ).count()

@register.filter
def translate_status(status):
    """Translate status values"""
    status_translations = {
        'pending': _('Pending'),
        'approved': _('Approved'),
        'rejected': _('Rejected'),
        'paid': _('Paid'),
        'active': _('Active'),
        'inactive': _('Inactive'),
        'returned': _('Returned'),
        'lost': _('Lost'),
        'damaged': _('Damaged'),
    }
    return status_translations.get(status, status)

@register.filter
def translate_type(type_value):
    """Translate type values"""
    type_translations = {
        'salary': _('Salary Advance'),
        'emergency': _('Emergency'),
        'travel': _('Travel'),
        'other': _('Other'),
        'equipment': _('Equipment'),
        'vehicle': _('Vehicle'),
        'tools': _('Tools'),
        'documents': _('Documents'),
        'cash': _('Cash'),
    }
    return type_translations.get(type_value, type_value)

@register.filter
def is_arabic():
    """Check if current language is Arabic"""
    return get_language() == 'ar'

@register.filter
def format_currency(amount):
    """Format currency amount"""
    try:
        amount = float(amount)
        if get_language() == 'ar':
            return f"{amount:,.2f} ر.س"
        else:
            return f"${amount:,.2f}"
    except (ValueError, TypeError):
        return amount

@register.filter
def days_between(date1, date2):
    """Calculate days between two dates"""
    try:
        if date1 and date2:
            return (date2 - date1).days
        return 0
    except:
        return 0

@register.filter
def is_overdue(custody):
    """Check if custody is overdue"""
    try:
        from datetime import date
        if custody.status == 'active' and custody.expected_return_date:
            return custody.expected_return_date < date.today()
        return False
    except:
        return False

@register.filter
def get_overdue_days(custody):
    """Get number of overdue days"""
    try:
        from datetime import date
        if custody.status == 'active' and custody.expected_return_date:
            if custody.expected_return_date < date.today():
                return (date.today() - custody.expected_return_date).days
        return 0
    except:
        return 0

@register.inclusion_tag('modules/hr/tags/employee_card.html')
def employee_card(employee):
    """Render employee card"""
    return {'employee': employee}

@register.inclusion_tag('modules/hr/tags/advance_status_badge.html')
def advance_status_badge(advance):
    """Render advance status badge"""
    return {'advance': advance}

@register.inclusion_tag('modules/hr/tags/custody_status_badge.html')
def custody_status_badge(custody):
    """Render custody status badge"""
    return {'custody': custody}

@register.simple_tag
def hr_breadcrumb(*args):
    """Generate HR breadcrumb"""
    breadcrumbs = []
    for i in range(0, len(args), 2):
        if i + 1 < len(args):
            breadcrumbs.append({
                'title': args[i],
                'url': args[i + 1]
            })
        else:
            breadcrumbs.append({
                'title': args[i],
                'url': None
            })
    return breadcrumbs
