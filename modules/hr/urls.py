"""
روابط وحدة الموارد البشرية
HR Module URLs
"""

from django.urls import path
from . import views

app_name = 'hr'

urlpatterns = [
    # لوحة التحكم
    path('', views.hr_dashboard, name='dashboard'),

    # الموظفين
    path('employees/', views.employee_list, name='employee_list'),
    path('employees/<int:employee_id>/', views.employee_detail, name='employee_detail'),
    path('employees/create/', views.employee_create, name='employee_create'),
    path('employees/<int:employee_id>/edit/', views.employee_edit, name='employee_edit'),
    path('employees/<int:employee_id>/delete/', views.employee_delete, name='employee_delete'),

    # الحضور والانصراف
    path('attendance/', views.attendance_entry, name='attendance_entry'),
    path('attendance/report/', views.attendance_report, name='attendance_report'),
    path('attendance/summary/', views.attendance_summary, name='attendance_summary'),
    path('attendance/manual/', views.manual_attendance, name='manual_attendance'),
    path('attendance/gps/', views.gps_attendance, name='gps_attendance'),

    # العمولات
    path('commissions/', views.commission_list, name='commission_list'),
    path('commissions/create/', views.commission_create, name='commission_create'),
    path('commissions/<int:commission_id>/', views.commission_detail, name='commission_detail'),
    path('commissions/<int:commission_id>/edit/', views.commission_edit, name='commission_edit'),
    path('commissions/<int:commission_id>/approve/', views.commission_approve, name='commission_approve'),
    path('commissions/<int:commission_id>/reject/', views.commission_reject, name='commission_reject'),
    path('commissions/calculate/', views.commission_calculate, name='commission_calculate'),
    path('commissions/send-to-accounting/', views.commission_send_to_accounting, name='commission_send_to_accounting'),

    # الرواتب
    path('payroll/', views.payroll_list, name='payroll_list'),
    path('payroll/create/', views.payroll_create, name='payroll_create'),
    path('payroll/<int:payroll_id>/', views.payroll_detail, name='payroll_detail'),
    path('payroll/<int:payroll_id>/edit/', views.payroll_edit, name='payroll_edit'),
    path('payroll/<int:payroll_id>/approve/', views.payroll_approve, name='payroll_approve'),
    path('payroll/<int:payroll_id>/print/', views.payroll_print, name='payroll_print'),
    path('payroll/generate/', views.payroll_generate, name='payroll_generate'),
    path('payroll/send-to-accounting/', views.payroll_send_to_accounting, name='payroll_send_to_accounting'),

    # السلف
    path('advances/', views.advance_list, name='advance_list'),
    path('advances/create/', views.advance_create, name='advance_create'),
    path('advances/<int:advance_id>/', views.advance_detail, name='advance_detail'),
    path('advances/<int:advance_id>/edit/', views.advance_edit, name='advance_edit'),
    path('advances/<int:advance_id>/approve/', views.advance_approve, name='advance_approve'),
    path('advances/<int:advance_id>/reject/', views.advance_reject, name='advance_reject'),
    # path('advances/<int:advance_id>/pay/', views.advance_pay, name='advance_pay'),
    # path('advances/<int:advance_id>/installments/', views.advance_installments, name='advance_installments'),
    # path('advances/settlement/', views.advance_settlement, name='advance_settlement'),

    # العهد
    path('custodies/', views.custody_list, name='custody_list'),
    path('custodies/create/', views.custody_create, name='custody_create'),
    path('custodies/<int:custody_id>/', views.custody_detail, name='custody_detail'),
    path('custodies/<int:custody_id>/edit/', views.custody_edit, name='custody_edit'),
    path('custodies/<int:custody_id>/return/', views.custody_return, name='custody_return'),

    # الشفتات
    path('shifts/', views.shift_list, name='shift_list'),
    path('shifts/create/', views.shift_create, name='shift_create'),
    path('shifts/<int:shift_id>/', views.shift_detail, name='shift_detail'),
    path('shifts/<int:shift_id>/edit/', views.shift_edit, name='shift_edit'),
    path('shifts/<int:shift_id>/delete/', views.shift_delete, name='shift_delete'),

    # أنواع الإجازات
    path('leave-types/', views.leave_type_list, name='leave_type_list'),
    path('leave-types/create/', views.leave_type_create, name='leave_type_create'),
    path('leave-types/<int:leave_type_id>/', views.leave_type_detail, name='leave_type_detail'),
    path('leave-types/<int:leave_type_id>/edit/', views.leave_type_edit, name='leave_type_edit'),
    path('leave-types/<int:leave_type_id>/delete/', views.leave_type_delete, name='leave_type_delete'),

    # طلبات الإجازات
    path('leaves/', views.leave_request_list, name='leave_request_list'),
    path('leaves/create/', views.leave_request_create, name='leave_request_create'),
    path('leaves/<int:leave_id>/', views.leave_request_detail, name='leave_request_detail'),
    path('leaves/<int:leave_id>/edit/', views.leave_request_edit, name='leave_request_edit'),
    path('leaves/<int:leave_id>/delete/', views.leave_request_delete, name='leave_request_delete'),
    path('leaves/<int:leave_id>/approve/', views.leave_request_approve, name='leave_request_approve'),
    path('leaves/<int:leave_id>/reject/', views.leave_request_reject, name='leave_request_reject'),

    # العقود
    path('contracts/', views.contract_list, name='contract_list'),
    path('contracts/create/', views.contract_create, name='contract_create'),
    path('contracts/<int:contract_id>/', views.contract_detail, name='contract_detail'),
    path('contracts/<int:contract_id>/edit/', views.contract_edit, name='contract_edit'),
    # path('contracts/<int:contract_id>/sign/', views.contract_sign, name='contract_sign'),
    # path('contracts/<int:contract_id>/renew/', views.contract_renew, name='contract_renew'),
    # path('contracts/<int:contract_id>/terminate/', views.contract_terminate, name='contract_terminate'),
    # path('contracts/expiring/', views.contracts_expiring, name='contracts_expiring'),

    # الجزاءات
    path('disciplinary/', views.disciplinary_list, name='disciplinary_list'),
    path('disciplinary/create/', views.disciplinary_create, name='disciplinary_create'),
    path('disciplinary/<int:disciplinary_id>/', views.disciplinary_detail, name='disciplinary_detail'),
    path('disciplinary/<int:disciplinary_id>/edit/', views.disciplinary_edit, name='disciplinary_edit'),
    # path('disciplinary/<int:disciplinary_id>/approve/', views.disciplinary_approve, name='disciplinary_approve'),
    # path('disciplinary/<int:disciplinary_id>/implement/', views.disciplinary_implement, name='disciplinary_implement'),

    # التحقيقات
    path('investigations/', views.investigation_list, name='investigation_list'),
    path('investigations/create/', views.investigation_create, name='investigation_create'),
    path('investigations/<int:investigation_id>/', views.investigation_detail, name='investigation_detail'),
    path('investigations/<int:investigation_id>/edit/', views.investigation_edit, name='investigation_edit'),
    # path('investigations/<int:investigation_id>/sessions/', views.investigation_sessions, name='investigation_sessions'),
    # path('investigations/<int:investigation_id>/sessions/create/', views.investigation_session_create, name='investigation_session_create'),
    # path('investigations/<int:investigation_id>/close/', views.investigation_close, name='investigation_close'),

    # فرق المبيعات والمناديب
    path('sales-teams/', views.sales_team_list, name='sales_team_list'),
    path('sales-teams/create/', views.sales_team_create, name='sales_team_create'),
    path('sales-teams/<int:team_id>/', views.sales_team_detail, name='sales_team_detail'),
    path('sales-teams/<int:team_id>/edit/', views.sales_team_edit, name='sales_team_edit'),

    path('sales-reps/', views.sales_rep_list, name='sales_rep_list'),
    path('sales-representatives/', views.sales_rep_list, name='sales_representative_list'),  # Alias
    path('sales-reps/create/', views.sales_rep_create, name='sales_rep_create'),
    path('sales-reps/<int:rep_id>/', views.sales_rep_detail, name='sales_rep_detail'),
    path('sales-reps/<int:rep_id>/edit/', views.sales_rep_edit, name='sales_rep_edit'),

    # TODO: Add disciplinary URLs after creating views

    # التقارير
    path('reports/', views.reports_dashboard, name='reports_dashboard'),
    path('reports/attendance/', views.attendance_report_detailed, name='attendance_report_detailed'),
    path('reports/payroll/', views.payroll_report, name='payroll_report'),
    path('reports/commissions/', views.commission_report, name='commission_report'),
    path('reports/advances/', views.advance_report, name='advance_report'),
    path('reports/employees/', views.employee_report, name='employee_report'),
    path('reports/contracts/', views.contract_report, name='contract_report'),
    path('reports/disciplinary/', views.disciplinary_report, name='disciplinary_report'),

    # API endpoints
    path('api/employees/', views.api_employees, name='api_employees'),
    path('api/attendance/check-in/', views.api_attendance_check_in, name='api_attendance_check_in'),
    path('api/attendance/check-out/', views.api_attendance_check_out, name='api_attendance_check_out'),
    path('api/attendance/gps/', views.api_attendance_gps, name='api_attendance_gps'),
    path('api/commission/calculate/', views.api_commission_calculate, name='api_commission_calculate'),

    # إعدادات
    path('settings/', views.hr_settings, name='settings'),
    path('settings/departments/', views.department_list, name='department_list'),
    path('settings/departments/create/', views.department_create, name='department_create'),
    path('settings/departments/<int:department_id>/edit/', views.department_edit, name='department_edit'),

    path('settings/positions/', views.position_list, name='position_list'),
    path('settings/positions/create/', views.position_create, name='position_create'),
    path('settings/positions/<int:position_id>/edit/', views.position_edit, name='position_edit'),

    path('settings/work-schedules/', views.work_schedule_list, name='work_schedule_list'),
    path('settings/work-schedules/create/', views.work_schedule_create, name='work_schedule_create'),
    path('settings/work-schedules/<int:schedule_id>/edit/', views.work_schedule_edit, name='work_schedule_edit'),

    path('settings/attendance-devices/', views.attendance_device_list, name='attendance_device_list'),
    path('settings/attendance-devices/create/', views.attendance_device_create, name='attendance_device_create'),
    path('settings/attendance-devices/<int:device_id>/edit/', views.attendance_device_edit, name='attendance_device_edit'),

    # تشخيص
    path('debug/', views.debug_info, name='debug_info'),
]
