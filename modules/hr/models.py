"""
نماذج وحدة الموارد البشرية
Human Resources Models
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
import uuid

from companies.models import Company, Branch

User = get_user_model()


class Department(models.Model):
    """نموذج الأقسام"""
    name = models.CharField(_('Department Name'), max_length=100)
    code = models.CharField(_('Department Code'), max_length=20, unique=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='departments', verbose_name=_('Company'))
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='sub_departments', verbose_name=_('Parent Department'))
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', verbose_name=_('Manager'))
    description = models.TextField(_('Description'), blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        unique_together = ('company', 'code')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Position(models.Model):
    """نموذج المناصب الوظيفية"""
    title = models.CharField(_('Position Title'), max_length=100)
    code = models.CharField(_('Position Code'), max_length=20)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='positions', verbose_name=_('Company'))
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='positions', verbose_name=_('Department'))
    level = models.IntegerField(_('Level'), default=1, validators=[MinValueValidator(1), MaxValueValidator(10)])
    description = models.TextField(_('Description'), blank=True)
    requirements = models.TextField(_('Requirements'), blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Position')
        verbose_name_plural = _('Positions')
        unique_together = ('company', 'code')
        ordering = ['department', 'level', 'title']

    def __str__(self):
        return f"{self.title} - {self.department.name}"


class Employee(models.Model):
    """نموذج الموظفين"""
    GENDER_CHOICES = [
        ('male', _('Male')),
        ('female', _('Female')),
    ]

    MARITAL_STATUS_CHOICES = [
        ('single', _('Single')),
        ('married', _('Married')),
        ('divorced', _('Divorced')),
        ('widowed', _('Widowed')),
    ]

    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('terminated', _('Terminated')),
        ('suspended', _('Suspended')),
    ]

    # معلومات أساسية
    employee_id = models.CharField(_('Employee ID'), max_length=20, unique=True)
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, related_name='employee_profile', verbose_name=_('User Account'))
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='employees', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='employees', verbose_name=_('Branch'))

    # معلومات شخصية
    first_name = models.CharField(_('First Name'), max_length=50)
    last_name = models.CharField(_('Last Name'), max_length=50)
    arabic_name = models.CharField(_('Arabic Name'), max_length=100, blank=True)
    national_id = models.CharField(_('National ID'), max_length=20, unique=True)
    passport_number = models.CharField(_('Passport Number'), max_length=20, blank=True)
    birth_date = models.DateField(_('Birth Date'), null=True, blank=True)
    gender = models.CharField(_('Gender'), max_length=10, choices=GENDER_CHOICES)
    marital_status = models.CharField(_('Marital Status'), max_length=10, choices=MARITAL_STATUS_CHOICES, default='single')

    # معلومات الاتصال
    phone = models.CharField(_('Phone'), max_length=20, blank=True)
    mobile = models.CharField(_('Mobile'), max_length=20)
    email = models.EmailField(_('Email'), blank=True)
    address = models.TextField(_('Address'), blank=True)
    emergency_contact_name = models.CharField(_('Emergency Contact Name'), max_length=100, blank=True)
    emergency_contact_phone = models.CharField(_('Emergency Contact Phone'), max_length=20, blank=True)

    # معلومات وظيفية
    department = models.ForeignKey(Department, on_delete=models.PROTECT, related_name='employees', verbose_name=_('Department'))
    position = models.ForeignKey(Position, on_delete=models.PROTECT, related_name='employees', verbose_name=_('Position'))
    direct_manager = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='subordinates', verbose_name=_('Direct Manager'))
    hire_date = models.DateField(_('Hire Date'))
    termination_date = models.DateField(_('Termination Date'), null=True, blank=True)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='active')

    # معلومات الراتب
    basic_salary = models.DecimalField(_('Basic Salary'), max_digits=10, decimal_places=2, default=0)

    # صورة الموظف
    photo = models.ImageField(_('Photo'), upload_to='employees/photos/', null=True, blank=True)

    # معلومات إضافية
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.employee_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        if self.birth_date:
            today = timezone.now().date()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def years_of_service(self):
        if self.hire_date:
            end_date = self.termination_date or timezone.now().date()
            return end_date.year - self.hire_date.year
        return 0


class AttendanceDevice(models.Model):
    """أجهزة الحضور والانصراف"""
    DEVICE_TYPES = [
        ('manual', _('Manual Entry')),
        ('biometric', _('Biometric Device')),
        ('card', _('Card Reader')),
        ('mobile', _('Mobile App')),
        ('web', _('Web Portal')),
    ]

    name = models.CharField(_('Device Name'), max_length=100)
    device_type = models.CharField(_('Device Type'), max_length=20, choices=DEVICE_TYPES)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='attendance_devices', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='attendance_devices', verbose_name=_('Branch'))
    location = models.CharField(_('Location'), max_length=200, blank=True)
    ip_address = models.GenericIPAddressField(_('IP Address'), null=True, blank=True)
    serial_number = models.CharField(_('Serial Number'), max_length=50, blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Attendance Device')
        verbose_name_plural = _('Attendance Devices')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_device_type_display()})"


class WorkSchedule(models.Model):
    """جداول العمل"""
    name = models.CharField(_('Schedule Name'), max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='work_schedules', verbose_name=_('Company'))
    start_time = models.TimeField(_('Start Time'))
    end_time = models.TimeField(_('End Time'))
    break_start = models.TimeField(_('Break Start'), null=True, blank=True)
    break_end = models.TimeField(_('Break End'), null=True, blank=True)
    working_days = models.CharField(_('Working Days'), max_length=20, default='1,2,3,4,5')  # 1=Monday, 7=Sunday
    is_flexible = models.BooleanField(_('Flexible Hours'), default=False)
    grace_period_minutes = models.IntegerField(_('Grace Period (Minutes)'), default=15)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Work Schedule')
        verbose_name_plural = _('Work Schedules')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"


class EmployeeSchedule(models.Model):
    """جداول عمل الموظفين"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='schedules', verbose_name=_('Employee'))
    schedule = models.ForeignKey(WorkSchedule, on_delete=models.CASCADE, related_name='employee_schedules', verbose_name=_('Schedule'))
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'), null=True, blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Employee Schedule')
        verbose_name_plural = _('Employee Schedules')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.schedule.name}"


class Attendance(models.Model):
    """سجل الحضور والانصراف"""
    ATTENDANCE_TYPES = [
        ('check_in', _('Check In')),
        ('check_out', _('Check Out')),
        ('break_start', _('Break Start')),
        ('break_end', _('Break End')),
    ]

    ENTRY_METHODS = [
        ('manual', _('Manual Entry')),
        ('biometric', _('Biometric')),
        ('card', _('Card')),
        ('mobile_gps', _('Mobile GPS')),
        ('web', _('Web Portal')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendances', verbose_name=_('Employee'))
    date = models.DateField(_('Date'))
    time = models.TimeField(_('Time'))
    attendance_type = models.CharField(_('Type'), max_length=20, choices=ATTENDANCE_TYPES)
    entry_method = models.CharField(_('Entry Method'), max_length=20, choices=ENTRY_METHODS, default='manual')
    device = models.ForeignKey(AttendanceDevice, on_delete=models.SET_NULL, null=True, blank=True, related_name='attendances', verbose_name=_('Device'))

    # معلومات الموقع الجغرافي
    latitude = models.DecimalField(_('Latitude'), max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(_('Longitude'), max_digits=11, decimal_places=8, null=True, blank=True)
    location_accuracy = models.FloatField(_('Location Accuracy'), null=True, blank=True)

    # معلومات إضافية
    notes = models.TextField(_('Notes'), blank=True)
    is_verified = models.BooleanField(_('Verified'), default=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_attendances', verbose_name=_('Verified By'))

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Attendance')
        verbose_name_plural = _('Attendances')
        ordering = ['-date', '-time']
        unique_together = ('employee', 'date', 'time', 'attendance_type')

    def __str__(self):
        return f"{self.employee.full_name} - {self.date} {self.time} ({self.get_attendance_type_display()})"


class AttendanceSummary(models.Model):
    """ملخص الحضور اليومي"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendance_summaries', verbose_name=_('Employee'))
    date = models.DateField(_('Date'))

    # أوقات الحضور والانصراف
    check_in_time = models.TimeField(_('Check In Time'), null=True, blank=True)
    check_out_time = models.TimeField(_('Check Out Time'), null=True, blank=True)

    # أوقات الاستراحة
    break_start_time = models.TimeField(_('Break Start'), null=True, blank=True)
    break_end_time = models.TimeField(_('Break End'), null=True, blank=True)

    # حساب الساعات
    total_hours = models.DecimalField(_('Total Hours'), max_digits=5, decimal_places=2, default=0)
    break_hours = models.DecimalField(_('Break Hours'), max_digits=5, decimal_places=2, default=0)
    working_hours = models.DecimalField(_('Working Hours'), max_digits=5, decimal_places=2, default=0)
    overtime_hours = models.DecimalField(_('Overtime Hours'), max_digits=5, decimal_places=2, default=0)

    # حالة الحضور
    is_late = models.BooleanField(_('Late'), default=False)
    late_minutes = models.IntegerField(_('Late Minutes'), default=0)
    is_early_leave = models.BooleanField(_('Early Leave'), default=False)
    early_leave_minutes = models.IntegerField(_('Early Leave Minutes'), default=0)
    is_absent = models.BooleanField(_('Absent'), default=False)

    # ملاحظات
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Attendance Summary')
        verbose_name_plural = _('Attendance Summaries')
        unique_together = ('employee', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date}"

    def calculate_hours(self):
        """حساب الساعات تلقائياً"""
        if self.check_in_time and self.check_out_time:
            # حساب إجمالي الساعات
            check_in_datetime = timezone.datetime.combine(self.date, self.check_in_time)
            check_out_datetime = timezone.datetime.combine(self.date, self.check_out_time)

            # إذا كان وقت الانصراف في اليوم التالي
            if self.check_out_time < self.check_in_time:
                check_out_datetime += timezone.timedelta(days=1)

            total_time = check_out_datetime - check_in_datetime
            self.total_hours = Decimal(str(total_time.total_seconds() / 3600))

            # حساب ساعات الاستراحة
            if self.break_start_time and self.break_end_time:
                break_start_datetime = timezone.datetime.combine(self.date, self.break_start_time)
                break_end_datetime = timezone.datetime.combine(self.date, self.break_end_time)

                if self.break_end_time < self.break_start_time:
                    break_end_datetime += timezone.timedelta(days=1)

                break_time = break_end_datetime - break_start_datetime
                self.break_hours = Decimal(str(break_time.total_seconds() / 3600))

            # حساب ساعات العمل الفعلية
            self.working_hours = self.total_hours - self.break_hours

            # حساب الإضافي (أكثر من 8 ساعات)
            if self.working_hours > 8:
                self.overtime_hours = self.working_hours - 8
            else:
                self.overtime_hours = 0

        self.save()


class SalesTeam(models.Model):
    """فرق المبيعات"""
    name = models.CharField(_('Team Name'), max_length=100)
    code = models.CharField(_('Team Code'), max_length=20)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='sales_teams', verbose_name=_('Company'))
    manager = models.ForeignKey(Employee, on_delete=models.PROTECT, related_name='managed_sales_teams', verbose_name=_('Team Manager'))
    target_amount = models.DecimalField(_('Target Amount'), max_digits=15, decimal_places=2, default=0)
    commission_rate = models.DecimalField(_('Commission Rate %'), max_digits=5, decimal_places=2, default=0)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Sales Team')
        verbose_name_plural = _('Sales Teams')
        unique_together = ('company', 'code')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class SalesRepresentative(models.Model):
    """المناديب"""
    employee = models.OneToOneField(Employee, on_delete=models.CASCADE, related_name='sales_rep', verbose_name=_('Employee'))
    rep_code = models.CharField(_('Representative Code'), max_length=20)
    team = models.ForeignKey(SalesTeam, on_delete=models.PROTECT, related_name='representatives', verbose_name=_('Sales Team'))
    territory = models.CharField(_('Territory'), max_length=200, blank=True)
    commission_rate = models.DecimalField(_('Commission Rate %'), max_digits=5, decimal_places=2, default=0)
    target_amount = models.DecimalField(_('Target Amount'), max_digits=15, decimal_places=2, default=0)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Sales Representative')
        verbose_name_plural = _('Sales Representatives')
        unique_together = ('employee', 'rep_code')
        ordering = ['employee__first_name', 'employee__last_name']

    def __str__(self):
        return f"{self.employee.full_name} ({self.rep_code})"


class Commission(models.Model):
    """العمولات"""
    COMMISSION_TYPES = [
        ('sales', _('Sales Commission')),
        ('target', _('Target Achievement')),
        ('bonus', _('Bonus')),
        ('incentive', _('Incentive')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('paid', _('Paid')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='commissions', verbose_name=_('Employee'))
    commission_type = models.CharField(_('Commission Type'), max_length=20, choices=COMMISSION_TYPES)
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    percentage = models.DecimalField(_('Percentage'), max_digits=5, decimal_places=2, null=True, blank=True)
    base_amount = models.DecimalField(_('Base Amount'), max_digits=15, decimal_places=2, null=True, blank=True)
    period_start = models.DateField(_('Period Start'))
    period_end = models.DateField(_('Period End'))
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة والدفع
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_commissions', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    paid_at = models.DateTimeField(_('Paid At'), null=True, blank=True)

    # ربط مع الحسابات
    accounting_entry = models.CharField(_('Accounting Entry'), max_length=100, blank=True)

    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Commission')
        verbose_name_plural = _('Commissions')
        ordering = ['-period_end', '-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_commission_type_display()} - {self.amount}"


class Payroll(models.Model):
    """كشف الراتب"""
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('approved', _('Approved')),
        ('paid', _('Paid')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='payrolls', verbose_name=_('Employee'))
    month = models.IntegerField(_('Month'), validators=[MinValueValidator(1), MaxValueValidator(12)])
    year = models.IntegerField(_('Year'))

    # مكونات الراتب
    basic_salary = models.DecimalField(_('Basic Salary'), max_digits=10, decimal_places=2, default=0)
    allowances = models.DecimalField(_('Allowances'), max_digits=10, decimal_places=2, default=0)
    commissions = models.DecimalField(_('Commissions'), max_digits=10, decimal_places=2, default=0)
    overtime_amount = models.DecimalField(_('Overtime Amount'), max_digits=10, decimal_places=2, default=0)

    # الخصومات
    deductions = models.DecimalField(_('Deductions'), max_digits=10, decimal_places=2, default=0)
    advances = models.DecimalField(_('Advances'), max_digits=10, decimal_places=2, default=0)
    insurance = models.DecimalField(_('Insurance'), max_digits=10, decimal_places=2, default=0)
    taxes = models.DecimalField(_('Taxes'), max_digits=10, decimal_places=2, default=0)

    # الإجمالي
    gross_salary = models.DecimalField(_('Gross Salary'), max_digits=10, decimal_places=2, default=0)
    total_deductions = models.DecimalField(_('Total Deductions'), max_digits=10, decimal_places=2, default=0)
    net_salary = models.DecimalField(_('Net Salary'), max_digits=10, decimal_places=2, default=0)

    # معلومات الحضور
    working_days = models.IntegerField(_('Working Days'), default=0)
    actual_days = models.IntegerField(_('Actual Days'), default=0)
    overtime_hours = models.DecimalField(_('Overtime Hours'), max_digits=5, decimal_places=2, default=0)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='draft')

    # معلومات الموافقة والدفع
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_payrolls', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    paid_at = models.DateTimeField(_('Paid At'), null=True, blank=True)

    # ربط مع الحسابات
    accounting_entry = models.CharField(_('Accounting Entry'), max_length=100, blank=True)

    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Payroll')
        verbose_name_plural = _('Payrolls')
        unique_together = ('employee', 'month', 'year')
        ordering = ['-year', '-month']

    def __str__(self):
        return f"{self.employee.full_name} - {self.month}/{self.year}"

    def calculate_totals(self):
        """حساب الإجماليات"""
        self.gross_salary = self.basic_salary + self.allowances + self.commissions + self.overtime_amount
        self.total_deductions = self.deductions + self.advances + self.insurance + self.taxes
        self.net_salary = self.gross_salary - self.total_deductions
        self.save()


class AdvanceRequest(models.Model):
    """طلبات السلف"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('paid', _('Paid')),
        ('settled', _('Settled')),
    ]

    ADVANCE_TYPES = [
        ('salary', _('Salary Advance')),
        ('emergency', _('Emergency Advance')),
        ('travel', _('Travel Advance')),
        ('other', _('Other')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='advance_requests', verbose_name=_('Employee'))
    advance_type = models.CharField(_('Advance Type'), max_length=20, choices=ADVANCE_TYPES)
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    reason = models.TextField(_('Reason'))
    request_date = models.DateField(_('Request Date'), auto_now_add=True)
    required_date = models.DateField(_('Required Date'))

    # معلومات التقسيط
    installments = models.IntegerField(_('Number of Installments'), default=1)
    installment_amount = models.DecimalField(_('Installment Amount'), max_digits=10, decimal_places=2, default=0)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_advances', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    rejection_reason = models.TextField(_('Rejection Reason'), blank=True)

    # معلومات الدفع
    paid_at = models.DateTimeField(_('Paid At'), null=True, blank=True)
    paid_amount = models.DecimalField(_('Paid Amount'), max_digits=10, decimal_places=2, default=0)

    # ربط مع الحسابات
    accounting_entry = models.CharField(_('Accounting Entry'), max_length=100, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Advance Request')
        verbose_name_plural = _('Advance Requests')
        ordering = ['-request_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_advance_type_display()} - {self.amount}"

    def calculate_installment_amount(self):
        """حساب قيمة القسط"""
        if self.installments > 0:
            self.installment_amount = self.amount / self.installments
            # لا نحفظ هنا لتجنب الحلقة اللا نهائية


class AdvanceInstallment(models.Model):
    """أقساط السلف"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('deducted', _('Deducted')),
        ('waived', _('Waived')),
    ]

    advance_request = models.ForeignKey(AdvanceRequest, on_delete=models.CASCADE, related_name='installment_records', verbose_name=_('Advance Request'))
    installment_number = models.IntegerField(_('Installment Number'))
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    due_date = models.DateField(_('Due Date'))
    deduction_date = models.DateField(_('Deduction Date'), null=True, blank=True)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # ربط مع كشف الراتب
    payroll = models.ForeignKey(Payroll, on_delete=models.SET_NULL, null=True, blank=True, related_name='advance_deductions', verbose_name=_('Payroll'))

    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Advance Installment')
        verbose_name_plural = _('Advance Installments')
        unique_together = ('advance_request', 'installment_number')
        ordering = ['due_date']

    def __str__(self):
        return f"{self.advance_request.employee.full_name} - Installment {self.installment_number}"


class Contract(models.Model):
    """العقود"""
    CONTRACT_TYPES = [
        ('permanent', _('Permanent')),
        ('temporary', _('Temporary')),
        ('part_time', _('Part Time')),
        ('consultant', _('Consultant')),
        ('internship', _('Internship')),
    ]

    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('active', _('Active')),
        ('expired', _('Expired')),
        ('terminated', _('Terminated')),
        ('renewed', _('Renewed')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='contracts', verbose_name=_('Employee'))
    contract_number = models.CharField(_('Contract Number'), max_length=50, unique=True)
    contract_type = models.CharField(_('Contract Type'), max_length=20, choices=CONTRACT_TYPES)
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'), null=True, blank=True)

    # تفاصيل العقد
    position_title = models.CharField(_('Position Title'), max_length=100)
    basic_salary = models.DecimalField(_('Basic Salary'), max_digits=10, decimal_places=2)
    working_hours = models.IntegerField(_('Working Hours per Week'), default=40)
    probation_period = models.IntegerField(_('Probation Period (Days)'), default=90)

    # شروط العقد
    terms_and_conditions = models.TextField(_('Terms and Conditions'), blank=True)
    benefits = models.TextField(_('Benefits'), blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='draft')

    # معلومات التوقيع
    signed_by_employee = models.BooleanField(_('Signed by Employee'), default=False)
    employee_signature_date = models.DateField(_('Employee Signature Date'), null=True, blank=True)
    signed_by_company = models.BooleanField(_('Signed by Company'), default=False)
    company_signature_date = models.DateField(_('Company Signature Date'), null=True, blank=True)
    company_representative = models.CharField(_('Company Representative'), max_length=100, blank=True)

    # ملفات العقد
    contract_file = models.FileField(_('Contract File'), upload_to='contracts/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Contract')
        verbose_name_plural = _('Contracts')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.contract_number}"

    @property
    def is_active(self):
        """التحقق من صحة العقد"""
        today = timezone.now().date()
        if self.status == 'active' and self.start_date <= today:
            if self.end_date is None or self.end_date >= today:
                return True
        return False


class Disciplinary(models.Model):
    """الجزاءات"""
    DISCIPLINARY_TYPES = [
        ('warning', _('Warning')),
        ('written_warning', _('Written Warning')),
        ('suspension', _('Suspension')),
        ('salary_deduction', _('Salary Deduction')),
        ('termination', _('Termination')),
        ('other', _('Other')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('implemented', _('Implemented')),
        ('appealed', _('Appealed')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='disciplinaries', verbose_name=_('Employee'))
    disciplinary_number = models.CharField(_('Disciplinary Number'), max_length=50, unique=True)
    disciplinary_type = models.CharField(_('Disciplinary Type'), max_length=20, choices=DISCIPLINARY_TYPES)
    incident_date = models.DateField(_('Incident Date'))
    report_date = models.DateField(_('Report Date'), auto_now_add=True)

    # تفاصيل المخالفة
    violation_description = models.TextField(_('Violation Description'))
    evidence = models.TextField(_('Evidence'), blank=True)

    # تفاصيل الجزاء
    penalty_description = models.TextField(_('Penalty Description'))
    suspension_days = models.IntegerField(_('Suspension Days'), null=True, blank=True)
    deduction_amount = models.DecimalField(_('Deduction Amount'), max_digits=10, decimal_places=2, null=True, blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة
    reported_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='reported_disciplinaries', verbose_name=_('Reported By'))
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_disciplinaries', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)

    # معلومات التنفيذ
    implemented_at = models.DateTimeField(_('Implemented At'), null=True, blank=True)
    implementation_notes = models.TextField(_('Implementation Notes'), blank=True)

    # ملفات مرفقة
    attachment = models.FileField(_('Attachment'), upload_to='disciplinaries/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Disciplinary Action')
        verbose_name_plural = _('Disciplinary Actions')
        ordering = ['-incident_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.disciplinary_number}"


class Investigation(models.Model):
    """محاضر التحقيق"""
    STATUS_CHOICES = [
        ('opened', _('Opened')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('closed', _('Closed')),
        ('suspended', _('Suspended')),
    ]

    INVESTIGATION_TYPES = [
        ('misconduct', _('Misconduct')),
        ('theft', _('Theft')),
        ('harassment', _('Harassment')),
        ('attendance', _('Attendance Issues')),
        ('performance', _('Performance Issues')),
        ('other', _('Other')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='investigations', verbose_name=_('Employee'))
    investigation_number = models.CharField(_('Investigation Number'), max_length=50, unique=True)
    investigation_type = models.CharField(_('Investigation Type'), max_length=20, choices=INVESTIGATION_TYPES)

    # تفاصيل القضية
    case_title = models.CharField(_('Case Title'), max_length=200)
    case_description = models.TextField(_('Case Description'))
    incident_date = models.DateField(_('Incident Date'))
    report_date = models.DateField(_('Report Date'), auto_now_add=True)

    # معلومات التحقيق
    investigator = models.ForeignKey(User, on_delete=models.PROTECT, related_name='investigations', verbose_name=_('Investigator'))
    investigation_start_date = models.DateField(_('Investigation Start Date'), null=True, blank=True)
    investigation_end_date = models.DateField(_('Investigation End Date'), null=True, blank=True)

    # النتائج
    findings = models.TextField(_('Findings'), blank=True)
    recommendations = models.TextField(_('Recommendations'), blank=True)
    final_decision = models.TextField(_('Final Decision'), blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='opened')

    # الشهود والأدلة
    witnesses = models.TextField(_('Witnesses'), blank=True)
    evidence_collected = models.TextField(_('Evidence Collected'), blank=True)

    # ملفات مرفقة
    attachment = models.FileField(_('Attachment'), upload_to='investigations/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Investigation')
        verbose_name_plural = _('Investigations')
        ordering = ['-report_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.investigation_number}"


class InvestigationSession(models.Model):
    """جلسات التحقيق"""
    investigation = models.ForeignKey(Investigation, on_delete=models.CASCADE, related_name='sessions', verbose_name=_('Investigation'))
    session_number = models.IntegerField(_('Session Number'))
    session_date = models.DateTimeField(_('Session Date'))
    location = models.CharField(_('Location'), max_length=200, blank=True)

    # المشاركون
    attendees = models.TextField(_('Attendees'))

    # محضر الجلسة
    minutes = models.TextField(_('Session Minutes'))
    employee_statement = models.TextField(_('Employee Statement'), blank=True)

    # ملفات الجلسة
    recording = models.FileField(_('Recording'), upload_to='investigation_sessions/', null=True, blank=True)
    documents = models.FileField(_('Documents'), upload_to='investigation_sessions/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Investigation Session')
        verbose_name_plural = _('Investigation Sessions')
        unique_together = ('investigation', 'session_number')
        ordering = ['session_date']

    def __str__(self):
        return f"{self.investigation.investigation_number} - Session {self.session_number}"


class Custody(models.Model):
    """العهد"""
    CUSTODY_TYPES = [
        ('equipment', 'معدات'),
        ('vehicle', 'مركبة'),
        ('tools', 'أدوات'),
        ('documents', 'مستندات'),
        ('cash', 'نقدية'),
        ('other', 'أخرى'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشطة'),
        ('returned', 'مُرجعة'),
        ('lost', 'مفقودة'),
        ('damaged', 'تالفة'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='custodies', verbose_name='الموظف')
    custody_number = models.CharField(max_length=50, unique=True, verbose_name='رقم العهدة')
    custody_type = models.CharField(max_length=20, choices=CUSTODY_TYPES, verbose_name='نوع العهدة')
    item_name = models.CharField(max_length=200, verbose_name='اسم الصنف')
    item_description = models.TextField(verbose_name='وصف الصنف')
    serial_number = models.CharField(max_length=100, blank=True, verbose_name='الرقم التسلسلي')
    quantity = models.PositiveIntegerField(default=1, verbose_name='الكمية')
    unit_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='قيمة الوحدة')
    total_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='القيمة الإجمالية')
    delivery_date = models.DateField(verbose_name='تاريخ التسليم')
    expected_return_date = models.DateField(null=True, blank=True, verbose_name='تاريخ الإرجاع المتوقع')
    actual_return_date = models.DateField(null=True, blank=True, verbose_name='تاريخ الإرجاع الفعلي')
    delivered_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='delivered_custodies', verbose_name='المُسلم')
    received_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='received_custodies', verbose_name='المُستلم')
    condition_on_delivery = models.TextField(verbose_name='حالة الصنف عند التسليم')
    condition_on_return = models.TextField(blank=True, verbose_name='حالة الصنف عند الإرجاع')
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'عهدة'
        verbose_name_plural = 'العهد'
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.custody_number:
            # إنشاء رقم عهدة تلقائي
            last_custody = Custody.objects.filter(
                employee__company=self.employee.company
            ).order_by('-id').first()

            if last_custody and last_custody.custody_number:
                try:
                    last_number = int(last_custody.custody_number.split('-')[-1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.custody_number = f"CUS-{self.employee.company.code}-{new_number:04d}"

        # حساب القيمة الإجمالية
        self.total_value = self.quantity * self.unit_value

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.custody_number} - {self.item_name}"

    def is_overdue(self):
        """التحقق من تأخر إرجاع العهدة"""
        if self.status == 'active' and self.expected_return_date:
            return timezone.now().date() > self.expected_return_date
        return False

    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.is_overdue():
            return (timezone.now().date() - self.expected_return_date).days
        return 0
