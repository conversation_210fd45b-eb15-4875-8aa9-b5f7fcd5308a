"""
نماذج وحدة الموارد البشرية
Human Resources Models
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal
from datetime import timedelta
import uuid

from companies.models import Company, Branch

User = get_user_model()


class Department(models.Model):
    """نموذج الأقسام"""
    name = models.CharField(_('Department Name'), max_length=100)
    code = models.CharField(_('Department Code'), max_length=20, unique=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='departments', verbose_name=_('Company'))
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='sub_departments', verbose_name=_('Parent Department'))
    manager = models.ForeignKey('Employee', on_delete=models.SET_NULL, null=True, blank=True, related_name='managed_departments', verbose_name=_('Manager'))
    description = models.TextField(_('Description'), blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Department')
        verbose_name_plural = _('Departments')
        unique_together = ('company', 'code')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Position(models.Model):
    """نموذج المناصب الوظيفية"""
    title = models.CharField(_('Position Title'), max_length=100)
    code = models.CharField(_('Position Code'), max_length=20)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='positions', verbose_name=_('Company'))
    department = models.ForeignKey(Department, on_delete=models.CASCADE, related_name='positions', verbose_name=_('Department'))
    level = models.IntegerField(_('Level'), default=1, validators=[MinValueValidator(1), MaxValueValidator(10)])
    description = models.TextField(_('Description'), blank=True)
    requirements = models.TextField(_('Requirements'), blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Position')
        verbose_name_plural = _('Positions')
        unique_together = ('company', 'code')
        ordering = ['department', 'level', 'title']

    def __str__(self):
        return f"{self.title} - {self.department.name}"


class Employee(models.Model):
    """نموذج الموظفين"""
    GENDER_CHOICES = [
        ('male', _('Male')),
        ('female', _('Female')),
    ]

    MARITAL_STATUS_CHOICES = [
        ('single', _('Single')),
        ('married', _('Married')),
        ('divorced', _('Divorced')),
        ('widowed', _('Widowed')),
    ]

    STATUS_CHOICES = [
        ('active', _('Active')),
        ('inactive', _('Inactive')),
        ('terminated', _('Terminated')),
        ('suspended', _('Suspended')),
    ]

    # معلومات أساسية
    employee_id = models.CharField(_('Employee ID'), max_length=20, unique=True)
    user = models.OneToOneField(User, on_delete=models.CASCADE, null=True, blank=True, related_name='employee_profile', verbose_name=_('User Account'))
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='employees', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='employees', verbose_name=_('Branch'))

    # معلومات شخصية
    first_name = models.CharField(_('First Name'), max_length=50)
    last_name = models.CharField(_('Last Name'), max_length=50)
    arabic_name = models.CharField(_('Arabic Name'), max_length=100, blank=True)
    national_id = models.CharField(_('National ID'), max_length=20, unique=True)
    passport_number = models.CharField(_('Passport Number'), max_length=20, blank=True)
    birth_date = models.DateField(_('Birth Date'), null=True, blank=True)
    gender = models.CharField(_('Gender'), max_length=10, choices=GENDER_CHOICES)
    marital_status = models.CharField(_('Marital Status'), max_length=10, choices=MARITAL_STATUS_CHOICES, default='single')

    # معلومات الاتصال
    phone = models.CharField(_('Phone'), max_length=20, blank=True)
    mobile = models.CharField(_('Mobile'), max_length=20)
    email = models.EmailField(_('Email'), blank=True)
    address = models.TextField(_('Address'), blank=True)
    emergency_contact_name = models.CharField(_('Emergency Contact Name'), max_length=100, blank=True)
    emergency_contact_phone = models.CharField(_('Emergency Contact Phone'), max_length=20, blank=True)

    # معلومات وظيفية
    department = models.ForeignKey(Department, on_delete=models.PROTECT, related_name='employees', verbose_name=_('Department'))
    position = models.ForeignKey(Position, on_delete=models.PROTECT, related_name='employees', verbose_name=_('Position'))
    direct_manager = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='subordinates', verbose_name=_('Direct Manager'))
    shift = models.ForeignKey('Shift', on_delete=models.SET_NULL, null=True, blank=True, related_name='employees', verbose_name=_('Work Shift'))
    hire_date = models.DateField(_('Hire Date'))
    termination_date = models.DateField(_('Termination Date'), null=True, blank=True)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='active')

    # معلومات الراتب
    basic_salary = models.DecimalField(_('Basic Salary'), max_digits=10, decimal_places=2, default=0)

    # صورة الموظف
    photo = models.ImageField(_('Photo'), upload_to='employees/photos/', null=True, blank=True)

    # معلومات إضافية
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Employee')
        verbose_name_plural = _('Employees')
        ordering = ['first_name', 'last_name']

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.employee_id})"

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        if self.birth_date:
            today = timezone.now().date()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    @property
    def years_of_service(self):
        if self.hire_date:
            end_date = self.termination_date or timezone.now().date()
            return end_date.year - self.hire_date.year
        return 0

    # ==================== طرق الشفتات ====================

    def get_current_shift(self):
        """الحصول على الشفت الحالي للموظف"""
        return self.shift

    def is_working_today(self):
        """فحص إذا كان الموظف يعمل اليوم حسب شفته"""
        if not self.shift:
            return False

        today_weekday = timezone.now().weekday()
        return self.shift.is_working_day(today_weekday)

    def get_shift_times_for_date(self, date):
        """الحصول على أوقات الشفت لتاريخ معين"""
        if not self.shift:
            return None

        weekday = date.weekday()
        if not self.shift.is_working_day(weekday):
            return None

        return {
            'start_time': self.shift.start_time,
            'end_time': self.shift.end_time,
            'break_start': self.shift.break_start,
            'break_end': self.shift.break_end,
            'break_duration': self.shift.break_duration,
        }

    # ==================== طرق الإجازات ====================

    def get_pending_leave_requests(self):
        """الحصول على طلبات الإجازات المعلقة"""
        return self.leave_requests.filter(status='pending')

    def get_approved_leave_requests(self):
        """الحصول على طلبات الإجازات المعتمدة"""
        return self.leave_requests.filter(status='approved')

    def get_leave_requests_for_year(self, year=None):
        """الحصول على طلبات الإجازات لسنة معينة"""
        if year is None:
            year = timezone.now().year
        return self.leave_requests.filter(start_date__year=year)

    def get_total_leave_days_for_year(self, year=None):
        """حساب إجمالي أيام الإجازات المأخوذة في السنة"""
        if year is None:
            year = timezone.now().year

        approved_leaves = self.leave_requests.filter(
            status='approved',
            start_date__year=year
        )

        total_days = sum(leave.total_days for leave in approved_leaves)
        return total_days

    def get_remaining_leave_days(self, leave_type, year=None):
        """حساب أيام الإجازة المتبقية لنوع معين"""
        if year is None:
            year = timezone.now().year

        used_days = self.leave_requests.filter(
            status='approved',
            leave_type=leave_type,
            start_date__year=year
        ).aggregate(
            total=models.Sum('total_days')
        )['total'] or 0

        return max(0, leave_type.max_days_per_year - used_days)

    def can_take_leave(self, leave_type, start_date, end_date):
        """فحص إمكانية أخذ إجازة"""
        # حساب عدد الأيام المطلوبة
        requested_days = (end_date - start_date).days + 1

        # فحص الحد الأقصى للأيام المتتالية
        if requested_days > leave_type.max_consecutive_days:
            return False, f"تتجاوز الحد الأقصى للأيام المتتالية ({leave_type.max_consecutive_days} أيام)"

        # فحص الأيام المتبقية
        remaining_days = self.get_remaining_leave_days(leave_type, start_date.year)
        if requested_days > remaining_days:
            return False, f"الأيام المتبقية غير كافية ({remaining_days} أيام متبقية)"

        # فحص فترة الإشعار المسبق
        notice_days = (start_date - timezone.now().date()).days
        if notice_days < leave_type.min_notice_days:
            return False, f"يجب الإشعار قبل {leave_type.min_notice_days} أيام على الأقل"

        # فحص تداخل الإجازات
        overlapping_leaves = self.leave_requests.filter(
            status__in=['pending', 'approved'],
            start_date__lte=end_date,
            end_date__gte=start_date
        )

        if overlapping_leaves.exists():
            return False, "يوجد تداخل مع إجازة أخرى"

        return True, "يمكن أخذ الإجازة"

    def is_on_leave(self, date=None):
        """فحص إذا كان الموظف في إجازة في تاريخ معين"""
        if date is None:
            date = timezone.now().date()

        return self.leave_requests.filter(
            status='approved',
            start_date__lte=date,
            end_date__gte=date
        ).exists()

    @property
    def is_currently_on_leave(self):
        """فحص إذا كان الموظف في إجازة حالياً"""
        return self.is_on_leave()

    def get_current_leave(self):
        """الحصول على الإجازة الحالية إن وجدت"""
        today = timezone.now().date()
        return self.leave_requests.filter(
            status='approved',
            start_date__lte=today,
            end_date__gte=today
        ).first()

    @property
    def current_leave(self):
        """الحصول على الإجازة الحالية كـ property"""
        return self.get_current_leave()

    def get_disciplinary_actions(self, status=None, year=None):
        """الحصول على الإجراءات التأديبية للموظف"""
        actions = self.disciplinary_actions.all()

        if status:
            actions = actions.filter(status=status)

        if year:
            actions = actions.filter(violation_date__year=year)

        return actions.order_by('-violation_date')

    def get_total_deductions_for_month(self, year, month):
        """حساب إجمالي الخصومات للشهر"""
        actions = self.disciplinary_actions.filter(
            status='applied',
            violation_date__year=year,
            violation_date__month=month
        )

        total_deduction = 0
        for action in actions:
            total_deduction += action.deduction_amount

        return total_deduction

    def get_violation_count_by_type(self, violation_type, year=None):
        """عدد المخالفات حسب النوع"""
        actions = self.disciplinary_actions.filter(
            rule__violation_type=violation_type,
            status='applied'
        )

        if year:
            actions = actions.filter(violation_date__year=year)

        return actions.count()

    def has_pending_disciplinary_actions(self):
        """فحص وجود إجراءات تأديبية معلقة"""
        return self.disciplinary_actions.filter(status='pending').exists()

    def apply_disciplinary_rule(self, violation_type, violation_details, violation_date=None, applied_by=None):
        """تطبيق قاعدة تأديبية على الموظف"""
        from django.utils import timezone

        if violation_date is None:
            violation_date = timezone.now().date()

        # البحث عن القاعدة المناسبة
        violation_count = self.get_violation_count_by_type(violation_type, violation_date.year)

        # تحديد التكرار
        if violation_count == 0:
            frequency = 'first'
        elif violation_count == 1:
            frequency = 'second'
        elif violation_count == 2:
            frequency = 'third'
        else:
            frequency = 'repeated'

        # البحث عن القاعدة
        rule = DisciplinaryRule.objects.filter(
            company=self.company,
            violation_type=violation_type,
            frequency=frequency,
            is_active=True
        ).first()

        if not rule:
            # البحث عن قاعدة متكررة إذا لم توجد قاعدة محددة
            rule = DisciplinaryRule.objects.filter(
                company=self.company,
                violation_type=violation_type,
                frequency='repeated',
                is_active=True
            ).first()

        if rule:
            # إنشاء إجراء تأديبي
            action = DisciplinaryAction.objects.create(
                employee=self,
                rule=rule,
                violation_date=violation_date,
                violation_details=violation_details
            )

            # تطبيق الإجراء تلقائياً إذا كان مفعلاً
            if rule.auto_apply and applied_by:
                action.apply_action(applied_by)

            return action

        return None


class AttendanceDevice(models.Model):
    """أجهزة الحضور والانصراف"""
    DEVICE_TYPES = [
        ('manual', _('Manual Entry')),
        ('biometric', _('Biometric Device')),
        ('card', _('Card Reader')),
        ('mobile', _('Mobile App')),
        ('web', _('Web Portal')),
    ]

    name = models.CharField(_('Device Name'), max_length=100)
    device_type = models.CharField(_('Device Type'), max_length=20, choices=DEVICE_TYPES)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='attendance_devices', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='attendance_devices', verbose_name=_('Branch'))
    location = models.CharField(_('Location'), max_length=200, blank=True)
    ip_address = models.GenericIPAddressField(_('IP Address'), null=True, blank=True)
    serial_number = models.CharField(_('Serial Number'), max_length=50, blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Attendance Device')
        verbose_name_plural = _('Attendance Devices')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.get_device_type_display()})"


class DisciplinaryRule(models.Model):
    """قواعد الجزاءات والخصومات"""

    VIOLATION_TYPE_CHOICES = [
        ('late_arrival', _('تأخير في الحضور')),
        ('early_departure', _('انصراف مبكر')),
        ('absence', _('غياب')),
        ('unauthorized_absence', _('غياب بدون إذن')),
        ('overtime_violation', _('مخالفة الوقت الإضافي')),
        ('break_violation', _('مخالفة وقت الاستراحة')),
        ('dress_code', _('مخالفة زي العمل')),
        ('misconduct', _('سوء سلوك')),
        ('other', _('أخرى')),
    ]

    PENALTY_TYPE_CHOICES = [
        ('warning', _('إنذار')),
        ('salary_deduction_fixed', _('خصم مبلغ ثابت')),
        ('salary_deduction_percentage', _('خصم نسبة من الراتب')),
        ('day_deduction', _('خصم أيام')),
        ('leave_deduction', _('خصم من الإجازات')),
        ('suspension', _('إيقاف عن العمل')),
        ('termination', _('فصل')),
    ]

    FREQUENCY_CHOICES = [
        ('first', _('المرة الأولى')),
        ('second', _('المرة الثانية')),
        ('third', _('المرة الثالثة')),
        ('repeated', _('متكرر')),
    ]

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='disciplinary_rules')
    name = models.CharField(_('Rule Name'), max_length=200)
    violation_type = models.CharField(_('Violation Type'), max_length=30, choices=VIOLATION_TYPE_CHOICES)
    frequency = models.CharField(_('Frequency'), max_length=20, choices=FREQUENCY_CHOICES)
    penalty_type = models.CharField(_('Penalty Type'), max_length=30, choices=PENALTY_TYPE_CHOICES)

    # للخصم المالي
    deduction_amount = models.DecimalField(_('Deduction Amount'), max_digits=10, decimal_places=2, null=True, blank=True)
    deduction_percentage = models.DecimalField(_('Deduction Percentage'), max_digits=5, decimal_places=2, null=True, blank=True)

    # للخصم من الأيام/الإجازات
    days_deducted = models.IntegerField(_('Days Deducted'), null=True, blank=True)

    # للتأخير - حد التأخير بالدقائق
    late_threshold_minutes = models.IntegerField(_('Late Threshold (Minutes)'), null=True, blank=True)

    # للغياب - عدد الأيام
    absence_days_threshold = models.IntegerField(_('Absence Days Threshold'), null=True, blank=True)

    description = models.TextField(_('Description'), blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    auto_apply = models.BooleanField(_('Auto Apply'), default=False, help_text=_('Apply automatically when violation is detected'))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Disciplinary Rule')
        verbose_name_plural = _('Disciplinary Rules')
        ordering = ['violation_type', 'frequency']
        unique_together = ['company', 'violation_type', 'frequency']

    def __str__(self):
        return f"{self.name} - {self.get_violation_type_display()} ({self.get_frequency_display()})"

    def calculate_deduction(self, employee_salary):
        """حساب مبلغ الخصم بناءً على راتب الموظف"""
        if self.penalty_type == 'salary_deduction_fixed':
            return self.deduction_amount or 0
        elif self.penalty_type == 'salary_deduction_percentage':
            return (employee_salary * (self.deduction_percentage or 0)) / 100
        return 0


class DisciplinaryAction(models.Model):
    """إجراءات الجزاءات المطبقة على الموظفين"""

    STATUS_CHOICES = [
        ('pending', _('معلق')),
        ('applied', _('مطبق')),
        ('cancelled', _('ملغي')),
        ('appealed', _('مستأنف')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='disciplinary_actions')
    rule = models.ForeignKey(DisciplinaryRule, on_delete=models.CASCADE, related_name='actions')

    violation_date = models.DateField(_('Violation Date'))
    violation_time = models.TimeField(_('Violation Time'), null=True, blank=True)
    violation_details = models.TextField(_('Violation Details'))

    # تفاصيل الخصم المطبق
    deduction_amount = models.DecimalField(_('Applied Deduction Amount'), max_digits=10, decimal_places=2, default=0)
    days_deducted = models.IntegerField(_('Days Deducted'), default=0)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')
    applied_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='applied_disciplinary_actions')
    applied_at = models.DateTimeField(_('Applied At'), null=True, blank=True)

    # ربط مع كشف الراتب
    payroll = models.ForeignKey('Payroll', on_delete=models.SET_NULL, null=True, blank=True, related_name='disciplinary_deductions')

    notes = models.TextField(_('Notes'), blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Disciplinary Action')
        verbose_name_plural = _('Disciplinary Actions')
        ordering = ['-violation_date', '-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.rule.name} ({self.violation_date})"

    def apply_action(self, applied_by_user):
        """تطبيق الإجراء التأديبي"""
        if self.status == 'pending':
            self.status = 'applied'
            self.applied_by = applied_by_user
            self.applied_at = timezone.now()

            # حساب مبلغ الخصم
            if self.rule.penalty_type in ['salary_deduction_fixed', 'salary_deduction_percentage']:
                self.deduction_amount = self.rule.calculate_deduction(self.employee.basic_salary)

            if self.rule.penalty_type == 'day_deduction':
                self.days_deducted = self.rule.days_deducted or 0

            self.save()
            return True
        return False

    def cancel_action(self):
        """إلغاء الإجراء التأديبي"""
        if self.status in ['pending', 'applied']:
            self.status = 'cancelled'
            self.save()
            return True
        return False

    @property
    def total_deduction_amount(self):
        """إجمالي مبلغ الخصم"""
        return self.deduction_amount

    def get_violation_count(self):
        """عدد المخالفات السابقة من نفس النوع"""
        return DisciplinaryAction.objects.filter(
            employee=self.employee,
            rule__violation_type=self.rule.violation_type,
            status='applied',
            violation_date__lt=self.violation_date
        ).count()


class WorkSchedule(models.Model):
    """جداول العمل"""
    name = models.CharField(_('Schedule Name'), max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='work_schedules', verbose_name=_('Company'))
    start_time = models.TimeField(_('Start Time'))
    end_time = models.TimeField(_('End Time'))
    break_start = models.TimeField(_('Break Start'), null=True, blank=True)
    break_end = models.TimeField(_('Break End'), null=True, blank=True)
    working_days = models.CharField(_('Working Days'), max_length=20, default='1,2,3,4,5')  # 1=Monday, 7=Sunday
    is_flexible = models.BooleanField(_('Flexible Hours'), default=False)
    grace_period_minutes = models.IntegerField(_('Grace Period (Minutes)'), default=15)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Work Schedule')
        verbose_name_plural = _('Work Schedules')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"

    @property
    def total_working_hours(self):
        """حساب إجمالي ساعات العمل"""
        from datetime import datetime, timedelta
        start = datetime.combine(datetime.today(), self.start_time)
        end = datetime.combine(datetime.today(), self.end_time)

        # إذا كان وقت الانتهاء في اليوم التالي
        if end < start:
            end += timedelta(days=1)

        total_time = end - start

        # طرح وقت الاستراحة
        if self.break_start and self.break_end:
            break_start = datetime.combine(datetime.today(), self.break_start)
            break_end = datetime.combine(datetime.today(), self.break_end)
            if break_end < break_start:
                break_end += timedelta(days=1)
            break_time = break_end - break_start
            total_time -= break_time

        return total_time


class Shift(models.Model):
    """الشفتات"""
    SHIFT_TYPES = [
        ('morning', _('Morning Shift')),
        ('evening', _('Evening Shift')),
        ('night', _('Night Shift')),
        ('rotating', _('Rotating Shift')),
        ('flexible', _('Flexible Shift')),
    ]

    name = models.CharField(_('Shift Name'), max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='shifts', verbose_name=_('Company'))
    shift_type = models.CharField(_('Shift Type'), max_length=20, choices=SHIFT_TYPES)
    start_time = models.TimeField(_('Start Time'))
    end_time = models.TimeField(_('End Time'))
    break_start = models.TimeField(_('Break Start'), null=True, blank=True)
    break_end = models.TimeField(_('Break End'), null=True, blank=True)
    break_duration = models.DurationField(_('Break Duration'), default=timedelta(hours=1))

    # أيام العمل
    monday = models.BooleanField(_('Monday'), default=True)
    tuesday = models.BooleanField(_('Tuesday'), default=True)
    wednesday = models.BooleanField(_('Wednesday'), default=True)
    thursday = models.BooleanField(_('Thursday'), default=True)
    friday = models.BooleanField(_('Friday'), default=True)
    saturday = models.BooleanField(_('Saturday'), default=False)
    sunday = models.BooleanField(_('Sunday'), default=False)

    # إعدادات الحضور
    grace_period_in = models.DurationField(
        _('Grace Period In'),
        default=timedelta(minutes=15)
    )
    grace_period_out = models.DurationField(
        _('Grace Period Out'),
        default=timedelta(minutes=15)
    )
    overtime_threshold = models.DurationField(
        _('Overtime Threshold'),
        default=timedelta(hours=8)
    )

    # إعدادات أخرى
    allow_early_checkin = models.BooleanField(_('Allow Early Check-in'), default=True)
    allow_late_checkout = models.BooleanField(_('Allow Late Check-out'), default=True)
    auto_checkout = models.BooleanField(_('Auto Check-out'), default=False)
    auto_checkout_time = models.TimeField(_('Auto Check-out Time'), null=True, blank=True)

    is_active = models.BooleanField(_('Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Shift')
        verbose_name_plural = _('Shifts')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"

    @property
    def working_days_list(self):
        """قائمة أيام العمل"""
        days = []
        if self.monday: days.append(_('Monday'))
        if self.tuesday: days.append(_('Tuesday'))
        if self.wednesday: days.append(_('Wednesday'))
        if self.thursday: days.append(_('Thursday'))
        if self.friday: days.append(_('Friday'))
        if self.saturday: days.append(_('Saturday'))
        if self.sunday: days.append(_('Sunday'))
        return days

    @property
    def total_working_hours(self):
        """حساب إجمالي ساعات العمل"""
        from datetime import datetime, timedelta
        start = datetime.combine(datetime.today(), self.start_time)
        end = datetime.combine(datetime.today(), self.end_time)

        # إذا كان وقت الانتهاء في اليوم التالي
        if end < start:
            end += timedelta(days=1)

        total_time = end - start
        return total_time - self.break_duration

    def is_working_day(self, weekday):
        """فحص إذا كان اليوم يوم عمل"""
        working_days = {
            0: self.monday,    # الاثنين
            1: self.tuesday,   # الثلاثاء
            2: self.wednesday, # الأربعاء
            3: self.thursday,  # الخميس
            4: self.friday,    # الجمعة
            5: self.saturday,  # السبت
            6: self.sunday,    # الأحد
        }
        return working_days.get(weekday, False)


class LeaveType(models.Model):
    """أنواع الإجازات"""
    name = models.CharField(_('Leave Type Name'), max_length=100)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='leave_types', verbose_name=_('Company'))
    code = models.CharField(_('Code'), max_length=20)

    # إعدادات الإجازة
    max_days_per_year = models.IntegerField(_('Max Days Per Year'), default=30)
    max_consecutive_days = models.IntegerField(_('Max Consecutive Days'), default=30)
    min_notice_days = models.IntegerField(_('Minimum Notice Days'), default=3)

    # إعدادات الراتب
    is_paid = models.BooleanField(_('Paid Leave'), default=True)
    salary_percentage = models.DecimalField(_('Salary Percentage'), max_digits=5, decimal_places=2, default=100)

    # إعدادات أخرى
    requires_approval = models.BooleanField(_('Requires Approval'), default=True)
    requires_medical_certificate = models.BooleanField(_('Requires Medical Certificate'), default=False)
    carry_forward = models.BooleanField(_('Carry Forward to Next Year'), default=False)

    is_active = models.BooleanField(_('Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Leave Type')
        verbose_name_plural = _('Leave Types')
        unique_together = ('company', 'code')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class LeaveRequest(models.Model):
    """طلبات الإجازات"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='leave_requests', verbose_name=_('Employee'))
    leave_type = models.ForeignKey(LeaveType, on_delete=models.CASCADE, related_name='requests', verbose_name=_('Leave Type'))

    # تواريخ الإجازة
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'))
    total_days = models.IntegerField(_('Total Days'), default=0)

    # تفاصيل الطلب
    reason = models.TextField(_('Reason'))
    emergency_contact = models.CharField(_('Emergency Contact'), max_length=200, blank=True)
    replacement_employee = models.ForeignKey(
        Employee,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='replacement_leaves',
        verbose_name=_('Replacement Employee')
    )

    # المرفقات
    medical_certificate = models.FileField(_('Medical Certificate'), upload_to='leave_certificates/', null=True, blank=True)
    attachment = models.FileField(_('Attachment'), upload_to='leave_attachments/', null=True, blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة
    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leaves',
        verbose_name=_('Approved By')
    )
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    rejection_reason = models.TextField(_('Rejection Reason'), blank=True)

    # معلومات الإرجاع
    actual_return_date = models.DateField(_('Actual Return Date'), null=True, blank=True)
    return_notes = models.TextField(_('Return Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Leave Request')
        verbose_name_plural = _('Leave Requests')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.leave_type.name} ({self.start_date} to {self.end_date})"

    def calculate_total_days(self):
        """حساب إجمالي أيام الإجازة"""
        if self.start_date and self.end_date:
            delta = self.end_date - self.start_date
            self.total_days = delta.days + 1
        return self.total_days

    def save(self, *args, **kwargs):
        """حفظ مع حساب الأيام"""
        self.calculate_total_days()
        super().save(*args, **kwargs)


class EmployeeSchedule(models.Model):
    """جداول عمل الموظفين"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='schedules', verbose_name=_('Employee'))
    schedule = models.ForeignKey(WorkSchedule, on_delete=models.CASCADE, related_name='employee_schedules', verbose_name=_('Schedule'))
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'), null=True, blank=True)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Employee Schedule')
        verbose_name_plural = _('Employee Schedules')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.schedule.name}"


class Attendance(models.Model):
    """سجل الحضور والانصراف"""
    ATTENDANCE_TYPES = [
        ('check_in', _('Check In')),
        ('check_out', _('Check Out')),
        ('break_start', _('Break Start')),
        ('break_end', _('Break End')),
    ]

    ENTRY_METHODS = [
        ('manual', _('Manual Entry')),
        ('biometric', _('Biometric')),
        ('card', _('Card')),
        ('mobile_gps', _('Mobile GPS')),
        ('web', _('Web Portal')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendances', verbose_name=_('Employee'))
    date = models.DateField(_('Date'))
    time = models.TimeField(_('Time'))
    attendance_type = models.CharField(_('Type'), max_length=20, choices=ATTENDANCE_TYPES)
    entry_method = models.CharField(_('Entry Method'), max_length=20, choices=ENTRY_METHODS, default='manual')
    device = models.ForeignKey(AttendanceDevice, on_delete=models.SET_NULL, null=True, blank=True, related_name='attendances', verbose_name=_('Device'))

    # معلومات الموقع الجغرافي
    latitude = models.DecimalField(_('Latitude'), max_digits=10, decimal_places=8, null=True, blank=True)
    longitude = models.DecimalField(_('Longitude'), max_digits=11, decimal_places=8, null=True, blank=True)
    location_accuracy = models.FloatField(_('Location Accuracy'), null=True, blank=True)

    # معلومات إضافية
    notes = models.TextField(_('Notes'), blank=True)
    is_verified = models.BooleanField(_('Verified'), default=True)
    verified_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='verified_attendances', verbose_name=_('Verified By'))

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Attendance')
        verbose_name_plural = _('Attendances')
        ordering = ['-date', '-time']
        unique_together = ('employee', 'date', 'time', 'attendance_type')

    def __str__(self):
        return f"{self.employee.full_name} - {self.date} {self.time} ({self.get_attendance_type_display()})"


class AttendanceSummary(models.Model):
    """ملخص الحضور اليومي"""
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='attendance_summaries', verbose_name=_('Employee'))
    date = models.DateField(_('Date'))

    # أوقات الحضور والانصراف
    check_in_time = models.TimeField(_('Check In Time'), null=True, blank=True)
    check_out_time = models.TimeField(_('Check Out Time'), null=True, blank=True)

    # أوقات الاستراحة
    break_start_time = models.TimeField(_('Break Start'), null=True, blank=True)
    break_end_time = models.TimeField(_('Break End'), null=True, blank=True)

    # حساب الساعات
    total_hours = models.DecimalField(_('Total Hours'), max_digits=5, decimal_places=2, default=0)
    break_hours = models.DecimalField(_('Break Hours'), max_digits=5, decimal_places=2, default=0)
    working_hours = models.DecimalField(_('Working Hours'), max_digits=5, decimal_places=2, default=0)
    overtime_hours = models.DecimalField(_('Overtime Hours'), max_digits=5, decimal_places=2, default=0)

    # حالة الحضور
    is_late = models.BooleanField(_('Late'), default=False)
    late_minutes = models.IntegerField(_('Late Minutes'), default=0)
    is_early_leave = models.BooleanField(_('Early Leave'), default=False)
    early_leave_minutes = models.IntegerField(_('Early Leave Minutes'), default=0)
    is_absent = models.BooleanField(_('Absent'), default=False)

    # ملاحظات
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Attendance Summary')
        verbose_name_plural = _('Attendance Summaries')
        unique_together = ('employee', 'date')
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.date}"

    def calculate_hours(self):
        """حساب الساعات تلقائياً"""
        if self.check_in_time and self.check_out_time:
            # حساب إجمالي الساعات
            check_in_datetime = timezone.datetime.combine(self.date, self.check_in_time)
            check_out_datetime = timezone.datetime.combine(self.date, self.check_out_time)

            # إذا كان وقت الانصراف في اليوم التالي
            if self.check_out_time < self.check_in_time:
                check_out_datetime += timezone.timedelta(days=1)

            total_time = check_out_datetime - check_in_datetime
            self.total_hours = Decimal(str(total_time.total_seconds() / 3600))

            # حساب ساعات الاستراحة
            if self.break_start_time and self.break_end_time:
                break_start_datetime = timezone.datetime.combine(self.date, self.break_start_time)
                break_end_datetime = timezone.datetime.combine(self.date, self.break_end_time)

                if self.break_end_time < self.break_start_time:
                    break_end_datetime += timezone.timedelta(days=1)

                break_time = break_end_datetime - break_start_datetime
                self.break_hours = Decimal(str(break_time.total_seconds() / 3600))

            # حساب ساعات العمل الفعلية
            self.working_hours = self.total_hours - self.break_hours

            # حساب الإضافي (أكثر من 8 ساعات)
            if self.working_hours > 8:
                self.overtime_hours = self.working_hours - 8
            else:
                self.overtime_hours = 0

        self.save()


class SalesTeam(models.Model):
    """فرق المبيعات"""
    name = models.CharField(_('Team Name'), max_length=100)
    code = models.CharField(_('Team Code'), max_length=20)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='sales_teams', verbose_name=_('Company'))
    manager = models.ForeignKey(Employee, on_delete=models.PROTECT, related_name='managed_sales_teams', verbose_name=_('Team Manager'))
    target_amount = models.DecimalField(_('Target Amount'), max_digits=15, decimal_places=2, default=0)
    commission_rate = models.DecimalField(_('Commission Rate %'), max_digits=5, decimal_places=2, default=0)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Sales Team')
        verbose_name_plural = _('Sales Teams')
        unique_together = ('company', 'code')
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class SalesRepresentative(models.Model):
    """المناديب"""
    employee = models.OneToOneField(Employee, on_delete=models.CASCADE, related_name='sales_rep', verbose_name=_('Employee'))
    rep_code = models.CharField(_('Representative Code'), max_length=20)
    team = models.ForeignKey(SalesTeam, on_delete=models.PROTECT, related_name='representatives', verbose_name=_('Sales Team'))
    territory = models.CharField(_('Territory'), max_length=200, blank=True)
    commission_rate = models.DecimalField(_('Commission Rate %'), max_digits=5, decimal_places=2, default=0)
    target_amount = models.DecimalField(_('Target Amount'), max_digits=15, decimal_places=2, default=0)
    is_active = models.BooleanField(_('Active'), default=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Sales Representative')
        verbose_name_plural = _('Sales Representatives')
        unique_together = ('employee', 'rep_code')
        ordering = ['employee__first_name', 'employee__last_name']

    def __str__(self):
        return f"{self.employee.full_name} ({self.rep_code})"


class Commission(models.Model):
    """العمولات"""
    COMMISSION_TYPES = [
        ('sales', _('Sales Commission')),
        ('target', _('Target Achievement')),
        ('bonus', _('Bonus')),
        ('incentive', _('Incentive')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('paid', _('Paid')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='commissions', verbose_name=_('Employee'))
    commission_type = models.CharField(_('Commission Type'), max_length=20, choices=COMMISSION_TYPES)
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    percentage = models.DecimalField(_('Percentage'), max_digits=5, decimal_places=2, null=True, blank=True)
    base_amount = models.DecimalField(_('Base Amount'), max_digits=15, decimal_places=2, null=True, blank=True)
    period_start = models.DateField(_('Period Start'))
    period_end = models.DateField(_('Period End'))
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة والدفع
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_commissions', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    paid_at = models.DateTimeField(_('Paid At'), null=True, blank=True)

    # ربط مع الحسابات
    accounting_entry = models.CharField(_('Accounting Entry'), max_length=100, blank=True)

    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Commission')
        verbose_name_plural = _('Commissions')
        ordering = ['-period_end', '-created_at']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_commission_type_display()} - {self.amount}"


class Payroll(models.Model):
    """كشف الراتب"""
    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('approved', _('Approved')),
        ('paid', _('Paid')),
        ('cancelled', _('Cancelled')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='payrolls', verbose_name=_('Employee'))
    month = models.IntegerField(_('Month'), validators=[MinValueValidator(1), MaxValueValidator(12)])
    year = models.IntegerField(_('Year'))

    # مكونات الراتب
    basic_salary = models.DecimalField(_('Basic Salary'), max_digits=10, decimal_places=2, default=0)
    allowances = models.DecimalField(_('Allowances'), max_digits=10, decimal_places=2, default=0)
    commissions = models.DecimalField(_('Commissions'), max_digits=10, decimal_places=2, default=0)
    overtime_amount = models.DecimalField(_('Overtime Amount'), max_digits=10, decimal_places=2, default=0)

    # الخصومات
    deductions = models.DecimalField(_('Deductions'), max_digits=10, decimal_places=2, default=0)
    advances = models.DecimalField(_('Advances'), max_digits=10, decimal_places=2, default=0)
    insurance = models.DecimalField(_('Insurance'), max_digits=10, decimal_places=2, default=0)
    taxes = models.DecimalField(_('Taxes'), max_digits=10, decimal_places=2, default=0)

    # الإجمالي
    gross_salary = models.DecimalField(_('Gross Salary'), max_digits=10, decimal_places=2, default=0)
    total_deductions = models.DecimalField(_('Total Deductions'), max_digits=10, decimal_places=2, default=0)
    net_salary = models.DecimalField(_('Net Salary'), max_digits=10, decimal_places=2, default=0)

    # معلومات الحضور
    working_days = models.IntegerField(_('Working Days'), default=0)
    actual_days = models.IntegerField(_('Actual Days'), default=0)
    overtime_hours = models.DecimalField(_('Overtime Hours'), max_digits=5, decimal_places=2, default=0)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='draft')

    # معلومات الموافقة والدفع
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_payrolls', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    paid_at = models.DateTimeField(_('Paid At'), null=True, blank=True)

    # ربط مع الحسابات
    accounting_entry = models.CharField(_('Accounting Entry'), max_length=100, blank=True)

    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Payroll')
        verbose_name_plural = _('Payrolls')
        unique_together = ('employee', 'month', 'year')
        ordering = ['-year', '-month']

    def __str__(self):
        return f"{self.employee.full_name} - {self.month}/{self.year}"

    def calculate_totals(self):
        """حساب الإجماليات"""
        self.gross_salary = self.basic_salary + self.allowances + self.commissions + self.overtime_amount
        self.total_deductions = self.deductions + self.advances + self.insurance + self.taxes
        self.net_salary = self.gross_salary - self.total_deductions
        self.save()


class AdvanceRequest(models.Model):
    """طلبات السلف"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('paid', _('Paid')),
        ('settled', _('Settled')),
    ]

    ADVANCE_TYPES = [
        ('salary', _('Salary Advance')),
        ('emergency', _('Emergency Advance')),
        ('travel', _('Travel Advance')),
        ('other', _('Other')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='advance_requests', verbose_name=_('Employee'))
    advance_type = models.CharField(_('Advance Type'), max_length=20, choices=ADVANCE_TYPES)
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    reason = models.TextField(_('Reason'))
    request_date = models.DateField(_('Request Date'), auto_now_add=True)
    required_date = models.DateField(_('Required Date'))

    # معلومات التقسيط
    installments = models.IntegerField(_('Number of Installments'), default=1)
    installment_amount = models.DecimalField(_('Installment Amount'), max_digits=10, decimal_places=2, default=0)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_advances', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)
    rejection_reason = models.TextField(_('Rejection Reason'), blank=True)

    # معلومات الدفع
    paid_at = models.DateTimeField(_('Paid At'), null=True, blank=True)
    paid_amount = models.DecimalField(_('Paid Amount'), max_digits=10, decimal_places=2, default=0)

    # ربط مع الحسابات
    accounting_entry = models.CharField(_('Accounting Entry'), max_length=100, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Advance Request')
        verbose_name_plural = _('Advance Requests')
        ordering = ['-request_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.get_advance_type_display()} - {self.amount}"

    def calculate_installment_amount(self):
        """حساب قيمة القسط"""
        if self.installments > 0:
            self.installment_amount = self.amount / self.installments
            # لا نحفظ هنا لتجنب الحلقة اللا نهائية


class AdvanceInstallment(models.Model):
    """أقساط السلف"""
    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('deducted', _('Deducted')),
        ('waived', _('Waived')),
    ]

    advance_request = models.ForeignKey(AdvanceRequest, on_delete=models.CASCADE, related_name='installment_records', verbose_name=_('Advance Request'))
    installment_number = models.IntegerField(_('Installment Number'))
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    due_date = models.DateField(_('Due Date'))
    deduction_date = models.DateField(_('Deduction Date'), null=True, blank=True)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # ربط مع كشف الراتب
    payroll = models.ForeignKey(Payroll, on_delete=models.SET_NULL, null=True, blank=True, related_name='advance_deductions', verbose_name=_('Payroll'))

    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Advance Installment')
        verbose_name_plural = _('Advance Installments')
        unique_together = ('advance_request', 'installment_number')
        ordering = ['due_date']

    def __str__(self):
        return f"{self.advance_request.employee.full_name} - Installment {self.installment_number}"


class Contract(models.Model):
    """العقود"""
    CONTRACT_TYPES = [
        ('permanent', _('Permanent')),
        ('temporary', _('Temporary')),
        ('part_time', _('Part Time')),
        ('consultant', _('Consultant')),
        ('internship', _('Internship')),
    ]

    STATUS_CHOICES = [
        ('draft', _('Draft')),
        ('active', _('Active')),
        ('expired', _('Expired')),
        ('terminated', _('Terminated')),
        ('renewed', _('Renewed')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='contracts', verbose_name=_('Employee'))
    contract_number = models.CharField(_('Contract Number'), max_length=50, unique=True)
    contract_type = models.CharField(_('Contract Type'), max_length=20, choices=CONTRACT_TYPES)
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'), null=True, blank=True)

    # تفاصيل العقد
    position_title = models.CharField(_('Position Title'), max_length=100)
    basic_salary = models.DecimalField(_('Basic Salary'), max_digits=10, decimal_places=2)
    working_hours = models.IntegerField(_('Working Hours per Week'), default=40)
    probation_period = models.IntegerField(_('Probation Period (Days)'), default=90)

    # شروط العقد
    terms_and_conditions = models.TextField(_('Terms and Conditions'), blank=True)
    benefits = models.TextField(_('Benefits'), blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='draft')

    # معلومات التوقيع
    signed_by_employee = models.BooleanField(_('Signed by Employee'), default=False)
    employee_signature_date = models.DateField(_('Employee Signature Date'), null=True, blank=True)
    signed_by_company = models.BooleanField(_('Signed by Company'), default=False)
    company_signature_date = models.DateField(_('Company Signature Date'), null=True, blank=True)
    company_representative = models.CharField(_('Company Representative'), max_length=100, blank=True)

    # ملفات العقد
    contract_file = models.FileField(_('Contract File'), upload_to='contracts/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Contract')
        verbose_name_plural = _('Contracts')
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.contract_number}"

    @property
    def is_active(self):
        """التحقق من صحة العقد"""
        today = timezone.now().date()
        if self.status == 'active' and self.start_date <= today:
            if self.end_date is None or self.end_date >= today:
                return True
        return False


class Disciplinary(models.Model):
    """الجزاءات"""
    DISCIPLINARY_TYPES = [
        ('warning', _('Warning')),
        ('written_warning', _('Written Warning')),
        ('suspension', _('Suspension')),
        ('salary_deduction', _('Salary Deduction')),
        ('termination', _('Termination')),
        ('other', _('Other')),
    ]

    STATUS_CHOICES = [
        ('pending', _('Pending')),
        ('approved', _('Approved')),
        ('rejected', _('Rejected')),
        ('implemented', _('Implemented')),
        ('appealed', _('Appealed')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='disciplinaries', verbose_name=_('Employee'))
    disciplinary_number = models.CharField(_('Disciplinary Number'), max_length=50, unique=True)
    disciplinary_type = models.CharField(_('Disciplinary Type'), max_length=20, choices=DISCIPLINARY_TYPES)
    incident_date = models.DateField(_('Incident Date'))
    report_date = models.DateField(_('Report Date'), auto_now_add=True)

    # تفاصيل المخالفة
    violation_description = models.TextField(_('Violation Description'))
    evidence = models.TextField(_('Evidence'), blank=True)

    # تفاصيل الجزاء
    penalty_description = models.TextField(_('Penalty Description'))
    suspension_days = models.IntegerField(_('Suspension Days'), null=True, blank=True)
    deduction_amount = models.DecimalField(_('Deduction Amount'), max_digits=10, decimal_places=2, null=True, blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='pending')

    # معلومات الموافقة
    reported_by = models.ForeignKey(User, on_delete=models.PROTECT, related_name='reported_disciplinaries', verbose_name=_('Reported By'))
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_disciplinaries', verbose_name=_('Approved By'))
    approved_at = models.DateTimeField(_('Approved At'), null=True, blank=True)

    # معلومات التنفيذ
    implemented_at = models.DateTimeField(_('Implemented At'), null=True, blank=True)
    implementation_notes = models.TextField(_('Implementation Notes'), blank=True)

    # ملفات مرفقة
    attachment = models.FileField(_('Attachment'), upload_to='disciplinaries/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Disciplinary Action')
        verbose_name_plural = _('Disciplinary Actions')
        ordering = ['-incident_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.disciplinary_number}"


class Investigation(models.Model):
    """محاضر التحقيق"""
    STATUS_CHOICES = [
        ('opened', _('Opened')),
        ('in_progress', _('In Progress')),
        ('completed', _('Completed')),
        ('closed', _('Closed')),
        ('suspended', _('Suspended')),
    ]

    INVESTIGATION_TYPES = [
        ('misconduct', _('Misconduct')),
        ('theft', _('Theft')),
        ('harassment', _('Harassment')),
        ('attendance', _('Attendance Issues')),
        ('performance', _('Performance Issues')),
        ('other', _('Other')),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='investigations', verbose_name=_('Employee'))
    investigation_number = models.CharField(_('Investigation Number'), max_length=50, unique=True)
    investigation_type = models.CharField(_('Investigation Type'), max_length=20, choices=INVESTIGATION_TYPES)

    # تفاصيل القضية
    case_title = models.CharField(_('Case Title'), max_length=200)
    case_description = models.TextField(_('Case Description'))
    incident_date = models.DateField(_('Incident Date'))
    report_date = models.DateField(_('Report Date'), auto_now_add=True)

    # معلومات التحقيق
    investigator = models.ForeignKey(User, on_delete=models.PROTECT, related_name='investigations', verbose_name=_('Investigator'))
    investigation_start_date = models.DateField(_('Investigation Start Date'), null=True, blank=True)
    investigation_end_date = models.DateField(_('Investigation End Date'), null=True, blank=True)

    # النتائج
    findings = models.TextField(_('Findings'), blank=True)
    recommendations = models.TextField(_('Recommendations'), blank=True)
    final_decision = models.TextField(_('Final Decision'), blank=True)

    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='opened')

    # الشهود والأدلة
    witnesses = models.TextField(_('Witnesses'), blank=True)
    evidence_collected = models.TextField(_('Evidence Collected'), blank=True)

    # ملفات مرفقة
    attachment = models.FileField(_('Attachment'), upload_to='investigations/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Investigation')
        verbose_name_plural = _('Investigations')
        ordering = ['-report_date']

    def __str__(self):
        return f"{self.employee.full_name} - {self.investigation_number}"


class InvestigationSession(models.Model):
    """جلسات التحقيق"""
    investigation = models.ForeignKey(Investigation, on_delete=models.CASCADE, related_name='sessions', verbose_name=_('Investigation'))
    session_number = models.IntegerField(_('Session Number'))
    session_date = models.DateTimeField(_('Session Date'))
    location = models.CharField(_('Location'), max_length=200, blank=True)

    # المشاركون
    attendees = models.TextField(_('Attendees'))

    # محضر الجلسة
    minutes = models.TextField(_('Session Minutes'))
    employee_statement = models.TextField(_('Employee Statement'), blank=True)

    # ملفات الجلسة
    recording = models.FileField(_('Recording'), upload_to='investigation_sessions/', null=True, blank=True)
    documents = models.FileField(_('Documents'), upload_to='investigation_sessions/', null=True, blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Investigation Session')
        verbose_name_plural = _('Investigation Sessions')
        unique_together = ('investigation', 'session_number')
        ordering = ['session_date']

    def __str__(self):
        return f"{self.investigation.investigation_number} - Session {self.session_number}"


class Custody(models.Model):
    """العهد"""
    CUSTODY_TYPES = [
        ('equipment', 'معدات'),
        ('vehicle', 'مركبة'),
        ('tools', 'أدوات'),
        ('documents', 'مستندات'),
        ('cash', 'نقدية'),
        ('other', 'أخرى'),
    ]

    STATUS_CHOICES = [
        ('active', 'نشطة'),
        ('returned', 'مُرجعة'),
        ('lost', 'مفقودة'),
        ('damaged', 'تالفة'),
    ]

    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='custodies', verbose_name='الموظف')
    custody_number = models.CharField(max_length=50, unique=True, verbose_name='رقم العهدة')
    custody_type = models.CharField(max_length=20, choices=CUSTODY_TYPES, verbose_name='نوع العهدة')
    item_name = models.CharField(max_length=200, verbose_name='اسم الصنف')
    item_description = models.TextField(verbose_name='وصف الصنف')
    serial_number = models.CharField(max_length=100, blank=True, verbose_name='الرقم التسلسلي')
    quantity = models.PositiveIntegerField(default=1, verbose_name='الكمية')
    unit_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='قيمة الوحدة')
    total_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='القيمة الإجمالية')
    delivery_date = models.DateField(verbose_name='تاريخ التسليم')
    expected_return_date = models.DateField(null=True, blank=True, verbose_name='تاريخ الإرجاع المتوقع')
    actual_return_date = models.DateField(null=True, blank=True, verbose_name='تاريخ الإرجاع الفعلي')
    delivered_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='delivered_custodies', verbose_name='المُسلم')
    received_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='received_custodies', verbose_name='المُستلم')
    condition_on_delivery = models.TextField(verbose_name='حالة الصنف عند التسليم')
    condition_on_return = models.TextField(blank=True, verbose_name='حالة الصنف عند الإرجاع')
    notes = models.TextField(blank=True, verbose_name='ملاحظات')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active', verbose_name='الحالة')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'عهدة'
        verbose_name_plural = 'العهد'
        ordering = ['-created_at']

    def save(self, *args, **kwargs):
        if not self.custody_number:
            # إنشاء رقم عهدة تلقائي
            last_custody = Custody.objects.filter(
                employee__company=self.employee.company
            ).order_by('-id').first()

            if last_custody and last_custody.custody_number:
                try:
                    last_number = int(last_custody.custody_number.split('-')[-1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.custody_number = f"CUS-{self.employee.company.code}-{new_number:04d}"

        # حساب القيمة الإجمالية
        self.total_value = self.quantity * self.unit_value

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.custody_number} - {self.item_name}"

    def is_overdue(self):
        """التحقق من تأخر إرجاع العهدة"""
        if self.status == 'active' and self.expected_return_date:
            return timezone.now().date() > self.expected_return_date
        return False

    def days_overdue(self):
        """عدد أيام التأخير"""
        if self.is_overdue():
            return (timezone.now().date() - self.expected_return_date).days
        return 0
