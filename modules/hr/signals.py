"""
إشارات وحدة الموارد البشرية
HR Module Signals
"""

from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from .models import (
    Attendance, AttendanceSummary, Employee, AdvanceRequest,
    AdvanceInstallment, Commission, Payroll
)


@receiver(post_save, sender=Attendance)
def update_attendance_summary(sender, instance, created, **kwargs):
    """تحديث ملخص الحضور عند إضافة سجل حضور جديد"""
    if created:
        # الحصول على أو إنشاء ملخص الحضور لهذا اليوم
        summary, created = AttendanceSummary.objects.get_or_create(
            employee=instance.employee,
            date=instance.date,
            defaults={
                'is_absent': False
            }
        )

        # تحديث الأوقات حسب نوع الحضور
        if instance.attendance_type == 'check_in':
            summary.check_in_time = instance.time
        elif instance.attendance_type == 'check_out':
            summary.check_out_time = instance.time
        elif instance.attendance_type == 'break_start':
            summary.break_start_time = instance.time
        elif instance.attendance_type == 'break_end':
            summary.break_end_time = instance.time

        summary.save()

        # حساب الساعات إذا كان لدينا وقت دخول وخروج
        if summary.check_in_time and summary.check_out_time:
            summary.calculate_hours()


@receiver(post_save, sender=AdvanceRequest)
def create_advance_installments(sender, instance, created, **kwargs):
    """إنشاء أقساط السلفة عند الموافقة عليها"""
    if instance.status == 'approved' and not instance.installment_records.exists():
        # حساب قيمة القسط
        instance.calculate_installment_amount()

        # إنشاء الأقساط
        for i in range(1, instance.installments + 1):
            # حساب تاريخ الاستحقاق (شهرياً)
            due_date = instance.approved_at.date() + timedelta(days=30 * i)

            AdvanceInstallment.objects.create(
                advance_request=instance,
                installment_number=i,
                amount=instance.installment_amount,
                due_date=due_date
            )


@receiver(pre_save, sender=Employee)
def generate_employee_id(sender, instance, **kwargs):
    """إنشاء رقم الموظف تلقائياً إذا لم يكن موجوداً"""
    if not instance.employee_id:
        # الحصول على آخر رقم موظف
        last_employee = Employee.objects.filter(
            company=instance.company
        ).order_by('-id').first()

        if last_employee and last_employee.employee_id:
            try:
                # استخراج الرقم من آخر موظف
                last_number = int(last_employee.employee_id.split('-')[-1])
                new_number = last_number + 1
            except (ValueError, IndexError):
                new_number = 1
        else:
            new_number = 1

        # تكوين رقم الموظف الجديد
        instance.employee_id = f"EMP-{instance.company.code}-{new_number:04d}"


@receiver(post_save, sender=Commission)
def update_payroll_commission(sender, instance, created, **kwargs):
    """تحديث العمولة في كشف الراتب عند الموافقة"""
    if instance.status == 'approved':
        # البحث عن كشف الراتب للشهر المناسب
        month = instance.period_end.month
        year = instance.period_end.year

        try:
            payroll = Payroll.objects.get(
                employee=instance.employee,
                month=month,
                year=year
            )

            # إضافة العمولة إلى كشف الراتب
            payroll.commissions += instance.amount
            payroll.calculate_totals()

        except Payroll.DoesNotExist:
            # إنشاء كشف راتب جديد إذا لم يكن موجوداً
            payroll = Payroll.objects.create(
                employee=instance.employee,
                month=month,
                year=year,
                basic_salary=instance.employee.basic_salary,
                commissions=instance.amount
            )
            payroll.calculate_totals()


def calculate_monthly_attendance_summary(employee, month, year):
    """حساب ملخص الحضور الشهري للموظف"""
    from calendar import monthrange

    # الحصول على عدد أيام الشهر
    days_in_month = monthrange(year, month)[1]

    # إحصائيات الحضور
    total_working_days = 0
    total_present_days = 0
    total_absent_days = 0
    total_late_days = 0
    total_working_hours = Decimal('0.00')
    total_overtime_hours = Decimal('0.00')

    # مراجعة كل يوم في الشهر
    for day in range(1, days_in_month + 1):
        date = datetime(year, month, day).date()

        # تخطي عطلات نهاية الأسبوع (الجمعة والسبت)
        if date.weekday() in [4, 5]:  # 4=Friday, 5=Saturday
            continue

        total_working_days += 1

        try:
            summary = AttendanceSummary.objects.get(
                employee=employee,
                date=date
            )

            if summary.is_absent:
                total_absent_days += 1
            else:
                total_present_days += 1
                total_working_hours += summary.working_hours
                total_overtime_hours += summary.overtime_hours

                if summary.is_late:
                    total_late_days += 1

        except AttendanceSummary.DoesNotExist:
            # إذا لم يكن هناك سجل حضور، يعتبر غائب
            total_absent_days += 1

    return {
        'total_working_days': total_working_days,
        'total_present_days': total_present_days,
        'total_absent_days': total_absent_days,
        'total_late_days': total_late_days,
        'total_working_hours': total_working_hours,
        'total_overtime_hours': total_overtime_hours,
        'attendance_percentage': (total_present_days / total_working_days * 100) if total_working_days > 0 else 0
    }


def generate_monthly_payroll(employee, month, year):
    """إنشاء كشف راتب شهري للموظف"""
    # التحقق من وجود كشف راتب للشهر
    payroll, created = Payroll.objects.get_or_create(
        employee=employee,
        month=month,
        year=year,
        defaults={
            'basic_salary': employee.basic_salary,
            'working_days': 0,
            'actual_days': 0,
            'overtime_hours': Decimal('0.00')
        }
    )

    if created or payroll.status == 'draft':
        # حساب إحصائيات الحضور
        attendance_stats = calculate_monthly_attendance_summary(employee, month, year)

        # تحديث بيانات الحضور
        payroll.working_days = attendance_stats['total_working_days']
        payroll.actual_days = attendance_stats['total_present_days']
        payroll.overtime_hours = attendance_stats['total_overtime_hours']

        # حساب راتب الحضور (نسبة من الراتب الأساسي)
        if payroll.working_days > 0:
            attendance_ratio = payroll.actual_days / payroll.working_days
            payroll.basic_salary = employee.basic_salary * Decimal(str(attendance_ratio))

        # حساب الإضافي
        overtime_rate = Decimal('1.5')  # معدل الإضافي 1.5 ضعف الساعة العادية
        if payroll.overtime_hours > 0:
            hourly_rate = employee.basic_salary / Decimal('176')  # 22 يوم × 8 ساعات
            payroll.overtime_amount = payroll.overtime_hours * hourly_rate * overtime_rate

        # جمع العمولات المعتمدة للشهر
        from django.db import models
        approved_commissions = Commission.objects.filter(
            employee=employee,
            status='approved',
            period_end__month=month,
            period_end__year=year
        ).aggregate(total=models.Sum('amount'))['total'] or Decimal('0.00')

        payroll.commissions = approved_commissions

        # خصم الأقساط المستحقة
        due_installments = AdvanceInstallment.objects.filter(
            advance_request__employee=employee,
            due_date__month=month,
            due_date__year=year,
            status='pending'
        )

        total_advance_deductions = sum(inst.amount for inst in due_installments)
        payroll.advances = Decimal(str(total_advance_deductions))

        # تحديث حالة الأقساط
        for installment in due_installments:
            installment.status = 'deducted'
            installment.payroll = payroll
            installment.deduction_date = timezone.now().date()
            installment.save()

        # حساب الإجماليات
        payroll.calculate_totals()

    return payroll
