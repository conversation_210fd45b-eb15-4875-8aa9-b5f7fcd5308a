# نظام الترجمة لوحدة الموارد البشرية

## نظرة عامة

تم تطوير نظام ترجمة شامل لوحدة الموارد البشرية باستخدام نفس تقنية وحدة الحسابات، مع إضافات متخصصة للموارد البشرية.

## الملفات المتضمنة

### ملفات الترجمة
- `modules/hr/locale/ar/LC_MESSAGES/django.po` - ملف الترجمة العربية
- `modules/hr/locale/ar/LC_MESSAGES/django.mo` - ملف الترجمة المجمع

### Template Tags
- `modules/hr/templatetags/hr_tags.py` - مكتبة الوسوم والفلاتر المخصصة
- `modules/hr/templatetags/__init__.py` - ملف التهيئة

### قوالب Template Tags
- `templates/modules/hr/tags/advance_status_badge.html` - شارة حالة السلف
- `templates/modules/hr/tags/custody_status_badge.html` - شارة حالة العهد
- `templates/modules/hr/tags/employee_card.html` - بطاقة الموظف

### أوامر الإدارة
- `modules/hr/management/commands/compile_hr_translations.py` - تجميع الترجمات
- `modules/hr/management/commands/update_hr_translations.py` - تحديث الترجمات

## المميزات

### 1. الترجمات الشاملة
- **720+ مصطلح مترجم** للموارد البشرية
- **ترجمة الحالات والأنواع** (معلق، موافق عليه، مرفوض، إلخ)
- **ترجمة الرسائل التحذيرية** الذكية
- **ترجمة النماذج والقوائم** والتقارير

### 2. Template Tags المتقدمة
```python
# فلاتر الترجمة
{{ advance.status|translate_status }}  # معلق
{{ custody.custody_type|translate_type }}  # معدات

# فلاتر العملة
{{ amount|format_currency }}  # 1,500.00 ر.س

# فلاتر الحسابات
{{ advances|sum_advances }}
{{ custodies|sum_custodies }}

# وسوم الإحصائيات
{% get_employee_count company %}
{% get_pending_advances_count company %}
{% get_overdue_custodies_count company %}
```

### 3. قوالب جاهزة
```django
{% load hr_tags %}

<!-- شارات الحالة -->
{% advance_status_badge advance %}
{% custody_status_badge custody %}

<!-- بطاقة الموظف -->
{% employee_card employee %}
```

### 4. فلاتر متخصصة
- `translate_status` - ترجمة الحالات
- `translate_type` - ترجمة الأنواع
- `format_currency` - تنسيق العملة (ر.س / $)
- `is_arabic` - فحص اللغة العربية
- `days_between` - حساب الأيام بين التواريخ
- `is_overdue` - فحص التأخير
- `get_overdue_days` - عدد أيام التأخير

## الاستخدام

### 1. تجميع الترجمات
```bash
python manage.py compile_hr_translations
```

### 2. في القوالب
```django
{% load i18n %}
{% load hr_tags %}

<!-- الترجمة الأساسية -->
{% trans "Human Resources" %}

<!-- استخدام الفلاتر -->
{{ advance.amount|format_currency }}
{{ advance.status|translate_status }}

<!-- استخدام القوالب الجاهزة -->
{% advance_status_badge advance %}
```

### 3. في Python
```python
from django.utils.translation import gettext as _

# استخدام الترجمة
message = _("Advance Request")
```

## المصطلحات المترجمة

### الأساسية
- Human Resources → الموارد البشرية
- Employee → الموظف
- Department → القسم
- Position → المنصب
- Salary → الراتب

### السلف
- Advances → السلف
- Advance Request → طلب سلفة
- Salary Advance → سلفة راتب
- Emergency → طوارئ
- Installments → الأقساط

### العهد
- Custodies → العهد
- Custody → عهدة
- Equipment → معدات
- Vehicle → مركبة
- Tools → أدوات
- Documents → مستندات

### الحالات
- Pending → معلق
- Approved → موافق عليه
- Rejected → مرفوض
- Active → نشط
- Returned → مُرجع
- Lost → مفقود
- Damaged → تالف

### الإجراءات
- Save → حفظ
- Cancel → إلغاء
- Edit → تعديل
- Delete → حذف
- Approve → موافقة
- Reject → رفض

## التطوير المستقبلي

### إضافات مخططة
1. **ترجمة التقارير** - ترجمة جميع التقارير
2. **ترجمة الإشعارات** - ترجمة رسائل البريد الإلكتروني
3. **ترجمة المساعدة** - ترجمة نصوص المساعدة
4. **لغات إضافية** - دعم الفرنسية والإنجليزية

### تحسينات مقترحة
1. **ترجمة ديناميكية** - تحديث الترجمات دون إعادة تشغيل
2. **ترجمة السياق** - ترجمة حسب السياق
3. **ترجمة التواريخ** - تنسيق التواريخ حسب اللغة
4. **ترجمة الأرقام** - تنسيق الأرقام حسب اللغة

## الصيانة

### تحديث الترجمات
1. إضافة المصطلحات الجديدة في `django.po`
2. تشغيل `python manage.py compile_hr_translations`
3. إعادة تشغيل الخادم

### إضافة فلاتر جديدة
1. إضافة الفلتر في `hr_tags.py`
2. إضافة الترجمات المطلوبة
3. استخدام الفلتر في القوالب

### إضافة قوالب جاهزة
1. إنشاء القالب في `templates/modules/hr/tags/`
2. إضافة الوسم في `hr_tags.py`
3. استخدام الوسم في القوالب

## الدعم

للمساعدة أو الاستفسارات حول نظام الترجمة، يرجى مراجعة:
- ملف `django.po` للمصطلحات المتاحة
- ملف `hr_tags.py` للفلاتر والوسوم
- قوالب `templates/modules/hr/tags/` للقوالب الجاهزة
