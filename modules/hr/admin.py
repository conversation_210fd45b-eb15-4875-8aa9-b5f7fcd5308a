"""
إدارة وحدة الموارد البشرية
HR Module Admin
"""

from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import (
    Department, Position, Employee, AttendanceDevice, WorkSchedule,
    EmployeeSchedule, Attendance, AttendanceSummary, SalesTeam,
    SalesRepresentative, Commission, Payroll, AdvanceRequest,
    AdvanceInstallment, Contract, Disciplinary, Investigation,
    InvestigationSession, Custody
)


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'company', 'parent', 'manager', 'is_active']
    list_filter = ['company', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    ordering = ['company', 'name']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'code', 'company', 'parent')
        }),
        (_('Management'), {
            'fields': ('manager', 'description')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
    )


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ['title', 'code', 'department', 'level', 'is_active']
    list_filter = ['department__company', 'department', 'level', 'is_active']
    search_fields = ['title', 'code', 'description']
    ordering = ['department', 'level', 'title']


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'full_name', 'department', 'position', 'status', 'hire_date']
    list_filter = ['company', 'department', 'position', 'status', 'gender', 'hire_date']
    search_fields = ['employee_id', 'first_name', 'last_name', 'arabic_name', 'national_id', 'email']
    ordering = ['employee_id']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('employee_id', 'user', 'company', 'branch')
        }),
        (_('Personal Information'), {
            'fields': (
                ('first_name', 'last_name'),
                'arabic_name',
                ('national_id', 'passport_number'),
                ('birth_date', 'gender'),
                'marital_status',
                'photo'
            )
        }),
        (_('Contact Information'), {
            'fields': (
                ('phone', 'mobile'),
                'email',
                'address',
                ('emergency_contact_name', 'emergency_contact_phone')
            )
        }),
        (_('Employment Information'), {
            'fields': (
                ('department', 'position'),
                'direct_manager',
                ('hire_date', 'termination_date'),
                'status',
                'basic_salary'
            )
        }),
        (_('Additional Information'), {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )

    def full_name(self, obj):
        return obj.full_name
    full_name.short_description = _('Full Name')


@admin.register(AttendanceDevice)
class AttendanceDeviceAdmin(admin.ModelAdmin):
    list_display = ['name', 'device_type', 'company', 'branch', 'location', 'is_active']
    list_filter = ['device_type', 'company', 'branch', 'is_active']
    search_fields = ['name', 'location', 'ip_address', 'serial_number']


@admin.register(WorkSchedule)
class WorkScheduleAdmin(admin.ModelAdmin):
    list_display = ['name', 'company', 'start_time', 'end_time', 'working_days', 'is_active']
    list_filter = ['company', 'is_flexible', 'is_active']
    search_fields = ['name']


@admin.register(EmployeeSchedule)
class EmployeeScheduleAdmin(admin.ModelAdmin):
    list_display = ['employee', 'schedule', 'start_date', 'end_date', 'is_active']
    list_filter = ['schedule', 'is_active', 'start_date']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id']


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'date', 'time', 'attendance_type', 'entry_method', 'is_verified']
    list_filter = ['attendance_type', 'entry_method', 'is_verified', 'date', 'device']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id']
    date_hierarchy = 'date'
    ordering = ['-date', '-time']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('employee', 'date', 'time', 'attendance_type')
        }),
        (_('Entry Details'), {
            'fields': ('entry_method', 'device')
        }),
        (_('Location Information'), {
            'fields': ('latitude', 'longitude', 'location_accuracy'),
            'classes': ('collapse',)
        }),
        (_('Verification'), {
            'fields': ('is_verified', 'verified_by', 'notes')
        }),
    )


@admin.register(AttendanceSummary)
class AttendanceSummaryAdmin(admin.ModelAdmin):
    list_display = [
        'employee', 'date', 'check_in_time', 'check_out_time',
        'working_hours', 'is_late', 'is_absent'
    ]
    list_filter = ['is_late', 'is_early_leave', 'is_absent', 'date']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id']
    date_hierarchy = 'date'
    ordering = ['-date']

    readonly_fields = ['total_hours', 'break_hours', 'working_hours', 'overtime_hours']


@admin.register(SalesTeam)
class SalesTeamAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'company', 'manager', 'target_amount', 'commission_rate', 'is_active']
    list_filter = ['company', 'is_active']
    search_fields = ['name', 'code']


@admin.register(SalesRepresentative)
class SalesRepresentativeAdmin(admin.ModelAdmin):
    list_display = ['employee', 'rep_code', 'team', 'territory', 'commission_rate', 'target_amount', 'is_active']
    list_filter = ['team', 'is_active']
    search_fields = ['employee__first_name', 'employee__last_name', 'rep_code', 'territory']


@admin.register(Commission)
class CommissionAdmin(admin.ModelAdmin):
    list_display = ['employee', 'commission_type', 'amount', 'period_start', 'period_end', 'status']
    list_filter = ['commission_type', 'status', 'period_end']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id']
    date_hierarchy = 'period_end'
    ordering = ['-period_end']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('employee', 'commission_type', 'amount')
        }),
        (_('Calculation Details'), {
            'fields': ('percentage', 'base_amount')
        }),
        (_('Period'), {
            'fields': ('period_start', 'period_end')
        }),
        (_('Status'), {
            'fields': ('status', 'approved_by', 'approved_at', 'paid_at')
        }),
        (_('Accounting'), {
            'fields': ('accounting_entry',)
        }),
        (_('Notes'), {
            'fields': ('notes',)
        }),
    )


@admin.register(Payroll)
class PayrollAdmin(admin.ModelAdmin):
    list_display = ['employee', 'month', 'year', 'basic_salary', 'gross_salary', 'net_salary', 'status']
    list_filter = ['status', 'year', 'month']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id']
    ordering = ['-year', '-month']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('employee', 'month', 'year')
        }),
        (_('Salary Components'), {
            'fields': (
                'basic_salary',
                'allowances',
                'commissions',
                'overtime_amount'
            )
        }),
        (_('Deductions'), {
            'fields': (
                'deductions',
                'advances',
                'insurance',
                'taxes'
            )
        }),
        (_('Totals'), {
            'fields': (
                'gross_salary',
                'total_deductions',
                'net_salary'
            )
        }),
        (_('Attendance'), {
            'fields': (
                'working_days',
                'actual_days',
                'overtime_hours'
            )
        }),
        (_('Status'), {
            'fields': ('status', 'approved_by', 'approved_at', 'paid_at')
        }),
        (_('Accounting'), {
            'fields': ('accounting_entry',)
        }),
        (_('Notes'), {
            'fields': ('notes',)
        }),
    )


@admin.register(AdvanceRequest)
class AdvanceRequestAdmin(admin.ModelAdmin):
    list_display = ['employee', 'advance_type', 'amount', 'request_date', 'installments', 'status']
    list_filter = ['advance_type', 'status', 'request_date']
    search_fields = ['employee__first_name', 'employee__last_name', 'employee__employee_id']
    date_hierarchy = 'request_date'
    ordering = ['-request_date']


class AdvanceInstallmentInline(admin.TabularInline):
    model = AdvanceInstallment
    extra = 0
    readonly_fields = ['installment_number', 'amount', 'due_date']


@admin.register(AdvanceInstallment)
class AdvanceInstallmentAdmin(admin.ModelAdmin):
    list_display = ['advance_request', 'installment_number', 'amount', 'due_date', 'status']
    list_filter = ['status', 'due_date']
    search_fields = ['advance_request__employee__first_name', 'advance_request__employee__last_name']
    date_hierarchy = 'due_date'
    ordering = ['due_date']


@admin.register(Contract)
class ContractAdmin(admin.ModelAdmin):
    list_display = ['employee', 'contract_number', 'contract_type', 'start_date', 'end_date', 'status']
    list_filter = ['contract_type', 'status', 'start_date']
    search_fields = ['employee__first_name', 'employee__last_name', 'contract_number']
    date_hierarchy = 'start_date'
    ordering = ['-start_date']


@admin.register(Disciplinary)
class DisciplinaryAdmin(admin.ModelAdmin):
    list_display = ['employee', 'disciplinary_number', 'disciplinary_type', 'incident_date', 'status']
    list_filter = ['disciplinary_type', 'status', 'incident_date']
    search_fields = ['employee__first_name', 'employee__last_name', 'disciplinary_number']
    date_hierarchy = 'incident_date'
    ordering = ['-incident_date']


class InvestigationSessionInline(admin.TabularInline):
    model = InvestigationSession
    extra = 0


@admin.register(Investigation)
class InvestigationAdmin(admin.ModelAdmin):
    list_display = ['employee', 'investigation_number', 'investigation_type', 'case_title', 'status']
    list_filter = ['investigation_type', 'status', 'report_date']
    search_fields = ['employee__first_name', 'employee__last_name', 'investigation_number', 'case_title']
    date_hierarchy = 'report_date'
    ordering = ['-report_date']

    inlines = [InvestigationSessionInline]


@admin.register(InvestigationSession)
class InvestigationSessionAdmin(admin.ModelAdmin):
    list_display = ['investigation', 'session_number', 'session_date', 'location']
    list_filter = ['session_date']
    search_fields = ['investigation__investigation_number', 'investigation__case_title']
    date_hierarchy = 'session_date'
    ordering = ['session_date']


@admin.register(Custody)
class CustodyAdmin(admin.ModelAdmin):
    list_display = ['custody_number', 'employee', 'item_name', 'custody_type', 'total_value', 'delivery_date', 'status']
    list_filter = ['custody_type', 'status', 'delivery_date']
    search_fields = ['custody_number', 'item_name', 'employee__first_name', 'employee__last_name', 'serial_number']
    date_hierarchy = 'delivery_date'
    ordering = ['-delivery_date']

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('employee', 'custody_number', 'custody_type')
        }),
        (_('Item Details'), {
            'fields': (
                'item_name',
                'item_description',
                'serial_number',
                ('quantity', 'unit_value', 'total_value')
            )
        }),
        (_('Delivery Information'), {
            'fields': (
                'delivery_date',
                'expected_return_date',
                'actual_return_date',
                ('delivered_by', 'received_by')
            )
        }),
        (_('Condition'), {
            'fields': (
                'condition_on_delivery',
                'condition_on_return'
            )
        }),
        (_('Status'), {
            'fields': ('status', 'notes')
        }),
    )

    readonly_fields = ['total_value']
