from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class HrConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'modules.hr'
    verbose_name = _('Human Resources')

    def ready(self):
        import modules.hr.signals
        # Import template tags to register them
        try:
            from . import templatetags
        except ImportError:
            pass
