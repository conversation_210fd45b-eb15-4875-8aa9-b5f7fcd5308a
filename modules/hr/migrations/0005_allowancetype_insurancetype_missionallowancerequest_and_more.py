# Generated by Django 5.2.1 on 2025-05-25 07:03

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0003_branch_footer_image_branch_header_image'),
        ('hr', '0004_disciplinaryrule_disciplinaryaction'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AllowanceType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Allowance Name')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('calculation_type', models.CharField(choices=[('fixed', 'مبلغ ثابت'), ('percentage', 'نسبة من الراتب الأساسي'), ('variable', 'متغير حسب الطلب')], max_length=20, verbose_name='Calculation Type')),
                ('default_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Default Amount')),
                ('default_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Default Percentage')),
                ('is_taxable', models.BooleanField(default=True, verbose_name='Taxable')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allowance_types', to='companies.company')),
            ],
            options={
                'verbose_name': 'Allowance Type',
                'verbose_name_plural': 'Allowance Types',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.CreateModel(
            name='InsuranceType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Insurance Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('employee_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Employee Percentage')),
                ('company_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Company Percentage')),
                ('min_salary_for_calculation', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Minimum Salary for Calculation')),
                ('max_salary_for_calculation', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Maximum Salary for Calculation')),
                ('is_mandatory', models.BooleanField(default=True, verbose_name='Mandatory')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='insurance_types', to='companies.company')),
            ],
            options={
                'verbose_name': 'Insurance Type',
                'verbose_name_plural': 'Insurance Types',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.CreateModel(
            name='MissionAllowanceRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_number', models.CharField(max_length=50, unique=True, verbose_name='Request Number')),
                ('mission_type', models.CharField(choices=[('local', 'محلية'), ('domestic', 'داخلية'), ('international', 'خارجية')], max_length=20, verbose_name='Mission Type')),
                ('destination', models.CharField(max_length=200, verbose_name='Destination')),
                ('purpose', models.TextField(verbose_name='Purpose')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('total_days', models.IntegerField(default=0, verbose_name='Total Days')),
                ('daily_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Daily Allowance')),
                ('transportation_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Transportation Allowance')),
                ('accommodation_allowance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Accommodation Allowance')),
                ('other_expenses', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Other Expenses')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Amount')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('submitted', 'مقدم'), ('approved', 'موافق عليه'), ('rejected', 'مرفوض'), ('paid', 'مدفوع')], default='draft', max_length=20, verbose_name='Status')),
                ('submitted_at', models.DateTimeField(blank=True, null=True, verbose_name='Submitted At')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='Rejection Reason')),
                ('attachment', models.FileField(blank=True, upload_to='mission_requests/', verbose_name='Attachment')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_mission_requests', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mission_allowance_requests', to='hr.employee')),
            ],
            options={
                'verbose_name': 'Mission Allowance Request',
                'verbose_name_plural': 'Mission Allowance Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeAllowance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Amount')),
                ('percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Percentage')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('allowance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.allowancetype')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allowances', to='hr.employee')),
            ],
            options={
                'verbose_name': 'Employee Allowance',
                'verbose_name_plural': 'Employee Allowances',
                'ordering': ['-start_date'],
                'unique_together': {('employee', 'allowance_type')},
            },
        ),
        migrations.CreateModel(
            name='EmployeeInsurance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custom_employee_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Custom Employee Percentage')),
                ('custom_company_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Custom Company Percentage')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='insurances', to='hr.employee')),
                ('insurance_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='hr.insurancetype')),
            ],
            options={
                'verbose_name': 'Employee Insurance',
                'verbose_name_plural': 'Employee Insurances',
                'ordering': ['-start_date'],
                'unique_together': {('employee', 'insurance_type')},
            },
        ),
    ]
