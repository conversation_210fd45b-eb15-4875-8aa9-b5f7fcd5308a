# Generated by Django 5.2.1 on 2025-05-24 20:26

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0003_branch_footer_image_branch_header_image'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendanceDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Device Name')),
                ('device_type', models.CharField(choices=[('manual', 'Manual Entry'), ('biometric', 'Biometric Device'), ('card', 'Card Reader'), ('mobile', 'Mobile App'), ('web', 'Web Portal')], max_length=20, verbose_name='Device Type')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='Location')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('serial_number', models.CharField(blank=True, max_length=50, verbose_name='Serial Number')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_devices', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_devices', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Attendance Device',
                'verbose_name_plural': 'Attendance Devices',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Department Name')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Department Code')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='departments', to='companies.company', verbose_name='Company')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_departments', to='hr.department', verbose_name='Parent Department')),
            ],
            options={
                'verbose_name': 'Department',
                'verbose_name_plural': 'Departments',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='Employee ID')),
                ('first_name', models.CharField(max_length=50, verbose_name='First Name')),
                ('last_name', models.CharField(max_length=50, verbose_name='Last Name')),
                ('arabic_name', models.CharField(blank=True, max_length=100, verbose_name='Arabic Name')),
                ('national_id', models.CharField(max_length=20, unique=True, verbose_name='National ID')),
                ('passport_number', models.CharField(blank=True, max_length=20, verbose_name='Passport Number')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='Birth Date')),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female')], max_length=10, verbose_name='Gender')),
                ('marital_status', models.CharField(choices=[('single', 'Single'), ('married', 'Married'), ('divorced', 'Divorced'), ('widowed', 'Widowed')], default='single', max_length=10, verbose_name='Marital Status')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='Phone')),
                ('mobile', models.CharField(max_length=20, verbose_name='Mobile')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('emergency_contact_name', models.CharField(blank=True, max_length=100, verbose_name='Emergency Contact Name')),
                ('emergency_contact_phone', models.CharField(blank=True, max_length=20, verbose_name='Emergency Contact Phone')),
                ('hire_date', models.DateField(verbose_name='Hire Date')),
                ('termination_date', models.DateField(blank=True, null=True, verbose_name='Termination Date')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('terminated', 'Terminated'), ('suspended', 'Suspended')], default='active', max_length=20, verbose_name='Status')),
                ('basic_salary', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Basic Salary')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='employees/photos/', verbose_name='Photo')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='companies.company', verbose_name='Company')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.department', verbose_name='Department')),
                ('direct_manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to='hr.employee', verbose_name='Direct Manager')),
                ('user', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL, verbose_name='User Account')),
            ],
            options={
                'verbose_name': 'Employee',
                'verbose_name_plural': 'Employees',
                'ordering': ['first_name', 'last_name'],
            },
        ),
        migrations.CreateModel(
            name='Disciplinary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('disciplinary_number', models.CharField(max_length=50, unique=True, verbose_name='Disciplinary Number')),
                ('disciplinary_type', models.CharField(choices=[('warning', 'Warning'), ('written_warning', 'Written Warning'), ('suspension', 'Suspension'), ('salary_deduction', 'Salary Deduction'), ('termination', 'Termination'), ('other', 'Other')], max_length=20, verbose_name='Disciplinary Type')),
                ('incident_date', models.DateField(verbose_name='Incident Date')),
                ('report_date', models.DateField(auto_now_add=True, verbose_name='Report Date')),
                ('violation_description', models.TextField(verbose_name='Violation Description')),
                ('evidence', models.TextField(blank=True, verbose_name='Evidence')),
                ('penalty_description', models.TextField(verbose_name='Penalty Description')),
                ('suspension_days', models.IntegerField(blank=True, null=True, verbose_name='Suspension Days')),
                ('deduction_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Deduction Amount')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('implemented', 'Implemented'), ('appealed', 'Appealed')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('implemented_at', models.DateTimeField(blank=True, null=True, verbose_name='Implemented At')),
                ('implementation_notes', models.TextField(blank=True, verbose_name='Implementation Notes')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='disciplinaries/', verbose_name='Attachment')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_disciplinaries', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('reported_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='reported_disciplinaries', to=settings.AUTH_USER_MODEL, verbose_name='Reported By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disciplinaries', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Disciplinary Action',
                'verbose_name_plural': 'Disciplinary Actions',
                'ordering': ['-incident_date'],
            },
        ),
        migrations.AddField(
            model_name='department',
            name='manager',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to='hr.employee', verbose_name='Manager'),
        ),
        migrations.CreateModel(
            name='Contract',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_number', models.CharField(max_length=50, unique=True, verbose_name='Contract Number')),
                ('contract_type', models.CharField(choices=[('permanent', 'Permanent'), ('temporary', 'Temporary'), ('part_time', 'Part Time'), ('consultant', 'Consultant'), ('internship', 'Internship')], max_length=20, verbose_name='Contract Type')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('position_title', models.CharField(max_length=100, verbose_name='Position Title')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Basic Salary')),
                ('working_hours', models.IntegerField(default=40, verbose_name='Working Hours per Week')),
                ('probation_period', models.IntegerField(default=90, verbose_name='Probation Period (Days)')),
                ('terms_and_conditions', models.TextField(blank=True, verbose_name='Terms and Conditions')),
                ('benefits', models.TextField(blank=True, verbose_name='Benefits')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('expired', 'Expired'), ('terminated', 'Terminated'), ('renewed', 'Renewed')], default='draft', max_length=20, verbose_name='Status')),
                ('signed_by_employee', models.BooleanField(default=False, verbose_name='Signed by Employee')),
                ('employee_signature_date', models.DateField(blank=True, null=True, verbose_name='Employee Signature Date')),
                ('signed_by_company', models.BooleanField(default=False, verbose_name='Signed by Company')),
                ('company_signature_date', models.DateField(blank=True, null=True, verbose_name='Company Signature Date')),
                ('company_representative', models.CharField(blank=True, max_length=100, verbose_name='Company Representative')),
                ('contract_file', models.FileField(blank=True, null=True, upload_to='contracts/', verbose_name='Contract File')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contracts', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Contract',
                'verbose_name_plural': 'Contracts',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Commission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_type', models.CharField(choices=[('sales', 'Sales Commission'), ('target', 'Target Achievement'), ('bonus', 'Bonus'), ('incentive', 'Incentive')], max_length=20, verbose_name='Commission Type')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Percentage')),
                ('base_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='Base Amount')),
                ('period_start', models.DateField(verbose_name='Period Start')),
                ('period_end', models.DateField(verbose_name='Period End')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('paid', 'Paid'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='Paid At')),
                ('accounting_entry', models.CharField(blank=True, max_length=100, verbose_name='Accounting Entry')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_commissions', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commissions', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Commission',
                'verbose_name_plural': 'Commissions',
                'ordering': ['-period_end', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AdvanceRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('advance_type', models.CharField(choices=[('salary', 'Salary Advance'), ('emergency', 'Emergency Advance'), ('travel', 'Travel Advance'), ('other', 'Other')], max_length=20, verbose_name='Advance Type')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('reason', models.TextField(verbose_name='Reason')),
                ('request_date', models.DateField(auto_now_add=True, verbose_name='Request Date')),
                ('required_date', models.DateField(verbose_name='Required Date')),
                ('installments', models.IntegerField(default=1, verbose_name='Number of Installments')),
                ('installment_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Installment Amount')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('paid', 'Paid'), ('settled', 'Settled')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='Rejection Reason')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='Paid At')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Paid Amount')),
                ('accounting_entry', models.CharField(blank=True, max_length=100, verbose_name='Accounting Entry')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_advances', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='advance_requests', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Advance Request',
                'verbose_name_plural': 'Advance Requests',
                'ordering': ['-request_date'],
            },
        ),
        migrations.CreateModel(
            name='Investigation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('investigation_number', models.CharField(max_length=50, unique=True, verbose_name='Investigation Number')),
                ('investigation_type', models.CharField(choices=[('misconduct', 'Misconduct'), ('theft', 'Theft'), ('harassment', 'Harassment'), ('attendance', 'Attendance Issues'), ('performance', 'Performance Issues'), ('other', 'Other')], max_length=20, verbose_name='Investigation Type')),
                ('case_title', models.CharField(max_length=200, verbose_name='Case Title')),
                ('case_description', models.TextField(verbose_name='Case Description')),
                ('incident_date', models.DateField(verbose_name='Incident Date')),
                ('report_date', models.DateField(auto_now_add=True, verbose_name='Report Date')),
                ('investigation_start_date', models.DateField(blank=True, null=True, verbose_name='Investigation Start Date')),
                ('investigation_end_date', models.DateField(blank=True, null=True, verbose_name='Investigation End Date')),
                ('findings', models.TextField(blank=True, verbose_name='Findings')),
                ('recommendations', models.TextField(blank=True, verbose_name='Recommendations')),
                ('final_decision', models.TextField(blank=True, verbose_name='Final Decision')),
                ('status', models.CharField(choices=[('opened', 'Opened'), ('in_progress', 'In Progress'), ('completed', 'Completed'), ('closed', 'Closed'), ('suspended', 'Suspended')], default='opened', max_length=20, verbose_name='Status')),
                ('witnesses', models.TextField(blank=True, verbose_name='Witnesses')),
                ('evidence_collected', models.TextField(blank=True, verbose_name='Evidence Collected')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='investigations/', verbose_name='Attachment')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='investigations', to='hr.employee', verbose_name='Employee')),
                ('investigator', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='investigations', to=settings.AUTH_USER_MODEL, verbose_name='Investigator')),
            ],
            options={
                'verbose_name': 'Investigation',
                'verbose_name_plural': 'Investigations',
                'ordering': ['-report_date'],
            },
        ),
        migrations.CreateModel(
            name='Payroll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.IntegerField(validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(12)], verbose_name='Month')),
                ('year', models.IntegerField(verbose_name='Year')),
                ('basic_salary', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Basic Salary')),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Allowances')),
                ('commissions', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Commissions')),
                ('overtime_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Overtime Amount')),
                ('deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Deductions')),
                ('advances', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Advances')),
                ('insurance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Insurance')),
                ('taxes', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Taxes')),
                ('gross_salary', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Gross Salary')),
                ('total_deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Total Deductions')),
                ('net_salary', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Net Salary')),
                ('working_days', models.IntegerField(default=0, verbose_name='Working Days')),
                ('actual_days', models.IntegerField(default=0, verbose_name='Actual Days')),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Overtime Hours')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('approved', 'Approved'), ('paid', 'Paid'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('paid_at', models.DateTimeField(blank=True, null=True, verbose_name='Paid At')),
                ('accounting_entry', models.CharField(blank=True, max_length=100, verbose_name='Accounting Entry')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_payrolls', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payrolls', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Payroll',
                'verbose_name_plural': 'Payrolls',
                'ordering': ['-year', '-month'],
                'unique_together': {('employee', 'month', 'year')},
            },
        ),
        migrations.CreateModel(
            name='Position',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='Position Title')),
                ('code', models.CharField(max_length=20, verbose_name='Position Code')),
                ('level', models.IntegerField(default=1, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(10)], verbose_name='Level')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('requirements', models.TextField(blank=True, verbose_name='Requirements')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='companies.company', verbose_name='Company')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='positions', to='hr.department', verbose_name='Department')),
            ],
            options={
                'verbose_name': 'Position',
                'verbose_name_plural': 'Positions',
                'ordering': ['department', 'level', 'title'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='position',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='hr.position', verbose_name='Position'),
        ),
        migrations.CreateModel(
            name='SalesTeam',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Team Name')),
                ('code', models.CharField(max_length=20, verbose_name='Team Code')),
                ('target_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Target Amount')),
                ('commission_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Commission Rate %')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales_teams', to='companies.company', verbose_name='Company')),
                ('manager', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='managed_sales_teams', to='hr.employee', verbose_name='Team Manager')),
            ],
            options={
                'verbose_name': 'Sales Team',
                'verbose_name_plural': 'Sales Teams',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.CreateModel(
            name='WorkSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Schedule Name')),
                ('start_time', models.TimeField(verbose_name='Start Time')),
                ('end_time', models.TimeField(verbose_name='End Time')),
                ('break_start', models.TimeField(blank=True, null=True, verbose_name='Break Start')),
                ('break_end', models.TimeField(blank=True, null=True, verbose_name='Break End')),
                ('working_days', models.CharField(default='1,2,3,4,5', max_length=20, verbose_name='Working Days')),
                ('is_flexible', models.BooleanField(default=False, verbose_name='Flexible Hours')),
                ('grace_period_minutes', models.IntegerField(default=15, verbose_name='Grace Period (Minutes)')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='work_schedules', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Work Schedule',
                'verbose_name_plural': 'Work Schedules',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='hr.employee', verbose_name='Employee')),
                ('schedule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_schedules', to='hr.workschedule', verbose_name='Schedule')),
            ],
            options={
                'verbose_name': 'Employee Schedule',
                'verbose_name_plural': 'Employee Schedules',
                'ordering': ['-start_date'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='department',
            unique_together={('company', 'code')},
        ),
        migrations.CreateModel(
            name='AttendanceSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Date')),
                ('check_in_time', models.TimeField(blank=True, null=True, verbose_name='Check In Time')),
                ('check_out_time', models.TimeField(blank=True, null=True, verbose_name='Check Out Time')),
                ('break_start_time', models.TimeField(blank=True, null=True, verbose_name='Break Start')),
                ('break_end_time', models.TimeField(blank=True, null=True, verbose_name='Break End')),
                ('total_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Total Hours')),
                ('break_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Break Hours')),
                ('working_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Working Hours')),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Overtime Hours')),
                ('is_late', models.BooleanField(default=False, verbose_name='Late')),
                ('late_minutes', models.IntegerField(default=0, verbose_name='Late Minutes')),
                ('is_early_leave', models.BooleanField(default=False, verbose_name='Early Leave')),
                ('early_leave_minutes', models.IntegerField(default=0, verbose_name='Early Leave Minutes')),
                ('is_absent', models.BooleanField(default=False, verbose_name='Absent')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_summaries', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Attendance Summary',
                'verbose_name_plural': 'Attendance Summaries',
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Date')),
                ('time', models.TimeField(verbose_name='Time')),
                ('attendance_type', models.CharField(choices=[('check_in', 'Check In'), ('check_out', 'Check Out'), ('break_start', 'Break Start'), ('break_end', 'Break End')], max_length=20, verbose_name='Type')),
                ('entry_method', models.CharField(choices=[('manual', 'Manual Entry'), ('biometric', 'Biometric'), ('card', 'Card'), ('mobile_gps', 'Mobile GPS'), ('web', 'Web Portal')], default='manual', max_length=20, verbose_name='Entry Method')),
                ('latitude', models.DecimalField(blank=True, decimal_places=8, max_digits=10, null=True, verbose_name='Latitude')),
                ('longitude', models.DecimalField(blank=True, decimal_places=8, max_digits=11, null=True, verbose_name='Longitude')),
                ('location_accuracy', models.FloatField(blank=True, null=True, verbose_name='Location Accuracy')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('is_verified', models.BooleanField(default=True, verbose_name='Verified')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('verified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='verified_attendances', to=settings.AUTH_USER_MODEL, verbose_name='Verified By')),
                ('device', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='attendances', to='hr.attendancedevice', verbose_name='Device')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='hr.employee', verbose_name='Employee')),
            ],
            options={
                'verbose_name': 'Attendance',
                'verbose_name_plural': 'Attendances',
                'ordering': ['-date', '-time'],
                'unique_together': {('employee', 'date', 'time', 'attendance_type')},
            },
        ),
        migrations.CreateModel(
            name='InvestigationSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_number', models.IntegerField(verbose_name='Session Number')),
                ('session_date', models.DateTimeField(verbose_name='Session Date')),
                ('location', models.CharField(blank=True, max_length=200, verbose_name='Location')),
                ('attendees', models.TextField(verbose_name='Attendees')),
                ('minutes', models.TextField(verbose_name='Session Minutes')),
                ('employee_statement', models.TextField(blank=True, verbose_name='Employee Statement')),
                ('recording', models.FileField(blank=True, null=True, upload_to='investigation_sessions/', verbose_name='Recording')),
                ('documents', models.FileField(blank=True, null=True, upload_to='investigation_sessions/', verbose_name='Documents')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('investigation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to='hr.investigation', verbose_name='Investigation')),
            ],
            options={
                'verbose_name': 'Investigation Session',
                'verbose_name_plural': 'Investigation Sessions',
                'ordering': ['session_date'],
                'unique_together': {('investigation', 'session_number')},
            },
        ),
        migrations.CreateModel(
            name='AdvanceInstallment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('installment_number', models.IntegerField(verbose_name='Installment Number')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('deduction_date', models.DateField(blank=True, null=True, verbose_name='Deduction Date')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('deducted', 'Deducted'), ('waived', 'Waived')], default='pending', max_length=20, verbose_name='Status')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('advance_request', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='installment_records', to='hr.advancerequest', verbose_name='Advance Request')),
                ('payroll', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='advance_deductions', to='hr.payroll', verbose_name='Payroll')),
            ],
            options={
                'verbose_name': 'Advance Installment',
                'verbose_name_plural': 'Advance Installments',
                'ordering': ['due_date'],
                'unique_together': {('advance_request', 'installment_number')},
            },
        ),
        migrations.CreateModel(
            name='SalesRepresentative',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rep_code', models.CharField(max_length=20, verbose_name='Representative Code')),
                ('territory', models.CharField(blank=True, max_length=200, verbose_name='Territory')),
                ('commission_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Commission Rate %')),
                ('target_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='Target Amount')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='sales_rep', to='hr.employee', verbose_name='Employee')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='representatives', to='hr.salesteam', verbose_name='Sales Team')),
            ],
            options={
                'verbose_name': 'Sales Representative',
                'verbose_name_plural': 'Sales Representatives',
                'ordering': ['employee__first_name', 'employee__last_name'],
                'unique_together': {('employee', 'rep_code')},
            },
        ),
    ]
