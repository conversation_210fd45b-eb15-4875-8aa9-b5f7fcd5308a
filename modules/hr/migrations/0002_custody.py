# Generated by Django 5.2.1 on 2025-05-24 21:11

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('hr', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Custody',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('custody_number', models.CharField(max_length=50, unique=True, verbose_name='رقم العهدة')),
                ('custody_type', models.CharField(choices=[('equipment', 'معدات'), ('vehicle', 'مركبة'), ('tools', 'أدوات'), ('documents', 'مستندات'), ('cash', 'نقدية'), ('other', 'أخرى')], max_length=20, verbose_name='نوع العهدة')),
                ('item_name', models.CharField(max_length=200, verbose_name='اسم الصنف')),
                ('item_description', models.TextField(verbose_name='وصف الصنف')),
                ('serial_number', models.CharField(blank=True, max_length=100, verbose_name='الرقم التسلسلي')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='الكمية')),
                ('unit_value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='قيمة الوحدة')),
                ('total_value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='القيمة الإجمالية')),
                ('delivery_date', models.DateField(verbose_name='تاريخ التسليم')),
                ('expected_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع المتوقع')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرجاع الفعلي')),
                ('condition_on_delivery', models.TextField(verbose_name='حالة الصنف عند التسليم')),
                ('condition_on_return', models.TextField(blank=True, verbose_name='حالة الصنف عند الإرجاع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('status', models.CharField(choices=[('active', 'نشطة'), ('returned', 'مُرجعة'), ('lost', 'مفقودة'), ('damaged', 'تالفة')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('delivered_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivered_custodies', to=settings.AUTH_USER_MODEL, verbose_name='المُسلم')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custodies', to='hr.employee', verbose_name='الموظف')),
                ('received_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_custodies', to=settings.AUTH_USER_MODEL, verbose_name='المُستلم')),
            ],
            options={
                'verbose_name': 'عهدة',
                'verbose_name_plural': 'العهد',
                'ordering': ['-created_at'],
            },
        ),
    ]
