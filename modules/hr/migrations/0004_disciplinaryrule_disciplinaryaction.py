# Generated by Django 5.2.1 on 2025-05-24 23:09

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0003_branch_footer_image_branch_header_image'),
        ('hr', '0003_leavetype_leaverequest_shift_employee_shift'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DisciplinaryRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Rule Name')),
                ('violation_type', models.CharField(choices=[('late_arrival', 'تأخير في الحضور'), ('early_departure', 'انصراف مبكر'), ('absence', 'غياب'), ('unauthorized_absence', 'غياب بدون إذن'), ('overtime_violation', 'مخالفة الوقت الإضافي'), ('break_violation', 'مخالفة وقت الاستراحة'), ('dress_code', 'مخالفة زي العمل'), ('misconduct', 'سوء سلوك'), ('other', 'أخرى')], max_length=30, verbose_name='Violation Type')),
                ('frequency', models.CharField(choices=[('first', 'المرة الأولى'), ('second', 'المرة الثانية'), ('third', 'المرة الثالثة'), ('repeated', 'متكرر')], max_length=20, verbose_name='Frequency')),
                ('penalty_type', models.CharField(choices=[('warning', 'إنذار'), ('salary_deduction_fixed', 'خصم مبلغ ثابت'), ('salary_deduction_percentage', 'خصم نسبة من الراتب'), ('day_deduction', 'خصم أيام'), ('leave_deduction', 'خصم من الإجازات'), ('suspension', 'إيقاف عن العمل'), ('termination', 'فصل')], max_length=30, verbose_name='Penalty Type')),
                ('deduction_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Deduction Amount')),
                ('deduction_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='Deduction Percentage')),
                ('days_deducted', models.IntegerField(blank=True, null=True, verbose_name='Days Deducted')),
                ('late_threshold_minutes', models.IntegerField(blank=True, null=True, verbose_name='Late Threshold (Minutes)')),
                ('absence_days_threshold', models.IntegerField(blank=True, null=True, verbose_name='Absence Days Threshold')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('auto_apply', models.BooleanField(default=False, help_text='Apply automatically when violation is detected', verbose_name='Auto Apply')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disciplinary_rules', to='companies.company')),
            ],
            options={
                'verbose_name': 'Disciplinary Rule',
                'verbose_name_plural': 'Disciplinary Rules',
                'ordering': ['violation_type', 'frequency'],
                'unique_together': {('company', 'violation_type', 'frequency')},
            },
        ),
        migrations.CreateModel(
            name='DisciplinaryAction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('violation_date', models.DateField(verbose_name='Violation Date')),
                ('violation_time', models.TimeField(blank=True, null=True, verbose_name='Violation Time')),
                ('violation_details', models.TextField(verbose_name='Violation Details')),
                ('deduction_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Applied Deduction Amount')),
                ('days_deducted', models.IntegerField(default=0, verbose_name='Days Deducted')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('applied', 'مطبق'), ('cancelled', 'ملغي'), ('appealed', 'مستأنف')], default='pending', max_length=20, verbose_name='Status')),
                ('applied_at', models.DateTimeField(blank=True, null=True, verbose_name='Applied At')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applied_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='applied_disciplinary_actions', to=settings.AUTH_USER_MODEL)),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='disciplinary_actions', to='hr.employee')),
                ('payroll', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='disciplinary_deductions', to='hr.payroll')),
                ('rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='actions', to='hr.disciplinaryrule')),
            ],
            options={
                'verbose_name': 'Disciplinary Action',
                'verbose_name_plural': 'Disciplinary Actions',
                'ordering': ['-violation_date', '-created_at'],
            },
        ),
    ]
