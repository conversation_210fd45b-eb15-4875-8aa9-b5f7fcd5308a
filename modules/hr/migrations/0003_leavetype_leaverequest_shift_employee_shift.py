# Generated by Django 5.2.1 on 2025-05-24 22:29

import datetime
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('companies', '0003_branch_footer_image_branch_header_image'),
        ('hr', '0002_custody'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LeaveType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Leave Type Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('max_days_per_year', models.IntegerField(default=30, verbose_name='Max Days Per Year')),
                ('max_consecutive_days', models.IntegerField(default=30, verbose_name='Max Consecutive Days')),
                ('min_notice_days', models.IntegerField(default=3, verbose_name='Minimum Notice Days')),
                ('is_paid', models.BooleanField(default=True, verbose_name='Paid Leave')),
                ('salary_percentage', models.DecimalField(decimal_places=2, default=100, max_digits=5, verbose_name='Salary Percentage')),
                ('requires_approval', models.BooleanField(default=True, verbose_name='Requires Approval')),
                ('requires_medical_certificate', models.BooleanField(default=False, verbose_name='Requires Medical Certificate')),
                ('carry_forward', models.BooleanField(default=False, verbose_name='Carry Forward to Next Year')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_types', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Leave Type',
                'verbose_name_plural': 'Leave Types',
                'ordering': ['name'],
                'unique_together': {('company', 'code')},
            },
        ),
        migrations.CreateModel(
            name='LeaveRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('total_days', models.IntegerField(default=0, verbose_name='Total Days')),
                ('reason', models.TextField(verbose_name='Reason')),
                ('emergency_contact', models.CharField(blank=True, max_length=200, verbose_name='Emergency Contact')),
                ('medical_certificate', models.FileField(blank=True, null=True, upload_to='leave_certificates/', verbose_name='Medical Certificate')),
                ('attachment', models.FileField(blank=True, null=True, upload_to='leave_attachments/', verbose_name='Attachment')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=20, verbose_name='Status')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='Approved At')),
                ('rejection_reason', models.TextField(blank=True, verbose_name='Rejection Reason')),
                ('actual_return_date', models.DateField(blank=True, null=True, verbose_name='Actual Return Date')),
                ('return_notes', models.TextField(blank=True, verbose_name='Return Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_leaves', to=settings.AUTH_USER_MODEL, verbose_name='Approved By')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='leave_requests', to='hr.employee', verbose_name='Employee')),
                ('replacement_employee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='replacement_leaves', to='hr.employee', verbose_name='Replacement Employee')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='hr.leavetype', verbose_name='Leave Type')),
            ],
            options={
                'verbose_name': 'Leave Request',
                'verbose_name_plural': 'Leave Requests',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Shift',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Shift Name')),
                ('shift_type', models.CharField(choices=[('morning', 'Morning Shift'), ('evening', 'Evening Shift'), ('night', 'Night Shift'), ('rotating', 'Rotating Shift'), ('flexible', 'Flexible Shift')], max_length=20, verbose_name='Shift Type')),
                ('start_time', models.TimeField(verbose_name='Start Time')),
                ('end_time', models.TimeField(verbose_name='End Time')),
                ('break_start', models.TimeField(blank=True, null=True, verbose_name='Break Start')),
                ('break_end', models.TimeField(blank=True, null=True, verbose_name='Break End')),
                ('break_duration', models.DurationField(default=datetime.timedelta(seconds=3600), verbose_name='Break Duration')),
                ('monday', models.BooleanField(default=True, verbose_name='Monday')),
                ('tuesday', models.BooleanField(default=True, verbose_name='Tuesday')),
                ('wednesday', models.BooleanField(default=True, verbose_name='Wednesday')),
                ('thursday', models.BooleanField(default=True, verbose_name='Thursday')),
                ('friday', models.BooleanField(default=True, verbose_name='Friday')),
                ('saturday', models.BooleanField(default=False, verbose_name='Saturday')),
                ('sunday', models.BooleanField(default=False, verbose_name='Sunday')),
                ('grace_period_in', models.DurationField(default=datetime.timedelta(seconds=900), verbose_name='Grace Period In')),
                ('grace_period_out', models.DurationField(default=datetime.timedelta(seconds=900), verbose_name='Grace Period Out')),
                ('overtime_threshold', models.DurationField(default=datetime.timedelta(seconds=28800), verbose_name='Overtime Threshold')),
                ('allow_early_checkin', models.BooleanField(default=True, verbose_name='Allow Early Check-in')),
                ('allow_late_checkout', models.BooleanField(default=True, verbose_name='Allow Late Check-out')),
                ('auto_checkout', models.BooleanField(default=False, verbose_name='Auto Check-out')),
                ('auto_checkout_time', models.TimeField(blank=True, null=True, verbose_name='Auto Check-out Time')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shifts', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Shift',
                'verbose_name_plural': 'Shifts',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='employee',
            name='shift',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='employees', to='hr.shift', verbose_name='Work Shift'),
        ),
    ]
