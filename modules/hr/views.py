"""
عروض وحدة الموارد البشرية
HR Module Views
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.utils import timezone
from django.http import JsonResponse
from django.db.models import Q, Sum, Count
from django.core.paginator import Paginator
from datetime import datetime, timedelta
from decimal import Decimal

from companies.models import Company, Branch
from .models import (
    Employee, Department, Position, Attendance, AttendanceSummary,
    SalesTeam, SalesRepresentative, Commission, Payroll, AdvanceRequest,
    Contract, Disciplinary, Investigation
)
from .forms import (
    EmployeeForm, AttendanceForm, CommissionForm, PayrollForm,
    AdvanceRequestForm, ContractForm, DisciplinaryForm, InvestigationForm,
    SalesTeamForm, SalesRepresentativeForm
)


@login_required
def hr_dashboard(request):
    """لوحة تحكم الموارد البشرية"""
    # الحصول على الشركة والفرع النشط
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    if not company_id:
        messages.error(request, _('Please select an active company'))
        return redirect('companies:company_list')

    company = get_object_or_404(Company, id=company_id)
    branch = get_object_or_404(Branch, id=branch_id) if branch_id else None

    # إحصائيات الموظفين
    total_employees = Employee.objects.filter(company=company, status='active').count()
    total_departments = Department.objects.filter(company=company, is_active=True).count()

    # إحصائيات الحضور لليوم
    today = timezone.now().date()
    present_today = AttendanceSummary.objects.filter(
        employee__company=company,
        date=today,
        is_absent=False
    ).count()

    absent_today = AttendanceSummary.objects.filter(
        employee__company=company,
        date=today,
        is_absent=True
    ).count()

    late_today = AttendanceSummary.objects.filter(
        employee__company=company,
        date=today,
        is_late=True
    ).count()

    # إحصائيات العمولات المعلقة
    pending_commissions = Commission.objects.filter(
        employee__company=company,
        status='pending'
    ).aggregate(
        count=Count('id'),
        total=Sum('amount')
    )

    # إحصائيات السلف المعلقة
    pending_advances = AdvanceRequest.objects.filter(
        employee__company=company,
        status='pending'
    ).aggregate(
        count=Count('id'),
        total=Sum('amount')
    )

    # العقود المنتهية قريباً (خلال 30 يوم)
    expiring_contracts = Contract.objects.filter(
        employee__company=company,
        status='active',
        end_date__lte=today + timedelta(days=30),
        end_date__gte=today
    ).count()

    # التحقيقات المفتوحة
    open_investigations = Investigation.objects.filter(
        employee__company=company,
        status__in=['opened', 'in_progress']
    ).count()

    context = {
        'company': company,
        'branch': branch,
        'total_employees': total_employees,
        'total_departments': total_departments,
        'present_today': present_today,
        'absent_today': absent_today,
        'late_today': late_today,
        'pending_commissions': pending_commissions,
        'pending_advances': pending_advances,
        'expiring_contracts': expiring_contracts,
        'open_investigations': open_investigations,
    }

    return render(request, 'modules/hr/dashboard.html', context)


@login_required
def employee_list(request):
    """قائمة الموظفين"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    # فلترة الموظفين
    employees = Employee.objects.filter(company=company)

    # البحث
    search = request.GET.get('search')
    if search:
        employees = employees.filter(
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search) |
            Q(employee_id__icontains=search) |
            Q(national_id__icontains=search)
        )

    # فلترة حسب القسم
    department_id = request.GET.get('department')
    if department_id:
        employees = employees.filter(department_id=department_id)

    # فلترة حسب الحالة
    status = request.GET.get('status')
    if status:
        employees = employees.filter(status=status)

    # ترقيم الصفحات
    paginator = Paginator(employees, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الأقسام للفلترة
    departments = Department.objects.filter(company=company, is_active=True)

    context = {
        'page_obj': page_obj,
        'departments': departments,
        'search': search,
        'selected_department': department_id,
        'selected_status': status,
    }

    return render(request, 'modules/hr/employee_list.html', context)


@login_required
def employee_detail(request, employee_id):
    """تفاصيل الموظف"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    employee = get_object_or_404(Employee, id=employee_id, company=company)

    # إحصائيات الحضور للشهر الحالي
    today = timezone.now().date()
    current_month = today.month
    current_year = today.year

    attendance_stats = AttendanceSummary.objects.filter(
        employee=employee,
        date__month=current_month,
        date__year=current_year
    ).aggregate(
        total_days=Count('id'),
        present_days=Count('id', filter=Q(is_absent=False)),
        late_days=Count('id', filter=Q(is_late=True)),
        total_hours=Sum('working_hours'),
        overtime_hours=Sum('overtime_hours')
    )

    # العمولات الحديثة
    recent_commissions = Commission.objects.filter(
        employee=employee
    ).order_by('-created_at')[:5]

    # السلف النشطة
    active_advances = AdvanceRequest.objects.filter(
        employee=employee,
        status__in=['approved', 'paid']
    ).order_by('-request_date')[:5]

    # العقد النشط
    active_contract = Contract.objects.filter(
        employee=employee,
        status='active'
    ).first()

    context = {
        'employee': employee,
        'attendance_stats': attendance_stats,
        'recent_commissions': recent_commissions,
        'active_advances': active_advances,
        'active_contract': active_contract,
    }

    return render(request, 'modules/hr/employee_detail.html', context)


@login_required
def attendance_entry(request):
    """تسجيل الحضور والانصراف"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    if request.method == 'POST':
        form = AttendanceForm(request.POST, company=company)
        if form.is_valid():
            attendance = form.save(commit=False)
            attendance.save()

            messages.success(request, _('Attendance recorded successfully'))
            return redirect('hr:attendance_entry')
    else:
        form = AttendanceForm(company=company)

    # آخر سجلات الحضور
    recent_attendance = Attendance.objects.filter(
        employee__company=company
    ).order_by('-date', '-time')[:10]

    context = {
        'form': form,
        'recent_attendance': recent_attendance,
    }

    return render(request, 'modules/hr/attendance_entry.html', context)


@login_required
def attendance_report(request):
    """تقرير الحضور والانصراف"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    # فلاتر التقرير
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    employee_id = request.GET.get('employee')
    department_id = request.GET.get('department')

    # تحديد التواريخ الافتراضية (الشهر الحالي)
    if not start_date or not end_date:
        today = timezone.now().date()
        start_date = today.replace(day=1).strftime('%Y-%m-%d')
        end_date = today.strftime('%Y-%m-%d')

    # بناء الاستعلام
    attendance_summaries = AttendanceSummary.objects.filter(
        employee__company=company,
        date__range=[start_date, end_date]
    )

    if employee_id:
        attendance_summaries = attendance_summaries.filter(employee_id=employee_id)

    if department_id:
        attendance_summaries = attendance_summaries.filter(employee__department_id=department_id)

    # ترقيم الصفحات
    paginator = Paginator(attendance_summaries, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # بيانات للفلاتر
    employees = Employee.objects.filter(company=company, status='active')
    departments = Department.objects.filter(company=company, is_active=True)

    context = {
        'page_obj': page_obj,
        'employees': employees,
        'departments': departments,
        'start_date': start_date,
        'end_date': end_date,
        'selected_employee': employee_id,
        'selected_department': department_id,
    }

    return render(request, 'modules/hr/attendance_report.html', context)


@login_required
def commission_list(request):
    """قائمة العمولات"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    commissions = Commission.objects.filter(employee__company=company)

    # فلترة حسب الحالة
    status = request.GET.get('status')
    if status:
        commissions = commissions.filter(status=status)

    # فلترة حسب الموظف
    employee_id = request.GET.get('employee')
    if employee_id:
        commissions = commissions.filter(employee_id=employee_id)

    # ترقيم الصفحات
    paginator = Paginator(commissions, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # بيانات للفلاتر
    employees = Employee.objects.filter(company=company, status='active')

    context = {
        'page_obj': page_obj,
        'employees': employees,
        'selected_status': status,
        'selected_employee': employee_id,
    }

    return render(request, 'modules/hr/commission_list.html', context)


@login_required
def payroll_list(request):
    """قائمة كشوف الرواتب"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    payrolls = Payroll.objects.filter(employee__company=company)

    # فلترة حسب الشهر والسنة
    month = request.GET.get('month')
    year = request.GET.get('year')

    if month:
        payrolls = payrolls.filter(month=month)
    if year:
        payrolls = payrolls.filter(year=year)

    # ترقيم الصفحات
    paginator = Paginator(payrolls, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'selected_month': month,
        'selected_year': year,
    }

    return render(request, 'modules/hr/payroll_list.html', context)


@login_required
def employee_create(request):
    """إنشاء موظف جديد"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, request.FILES, company=company)
        if form.is_valid():
            employee = form.save(commit=False)
            employee.company = company
            employee.save()

            messages.success(request, _('Employee created successfully'))
            return redirect('hr:employee_detail', employee_id=employee.id)
    else:
        form = EmployeeForm(company=company)

    context = {
        'form': form,
        'title': _('Create Employee'),
    }

    return render(request, 'modules/hr/employee_form.html', context)


@login_required
def employee_edit(request, employee_id):
    """تعديل موظف"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)
    employee = get_object_or_404(Employee, id=employee_id, company=company)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, request.FILES, instance=employee, company=company)
        if form.is_valid():
            form.save()

            messages.success(request, _('Employee updated successfully'))
            return redirect('hr:employee_detail', employee_id=employee.id)
    else:
        form = EmployeeForm(instance=employee, company=company)

    context = {
        'form': form,
        'employee': employee,
        'title': _('Edit Employee'),
    }

    return render(request, 'modules/hr/employee_form.html', context)


@login_required
def employee_delete(request, employee_id):
    """حذف موظف"""
    company_id = request.session.get('active_company_id')
    company = get_object_or_404(Company, id=company_id)
    employee = get_object_or_404(Employee, id=employee_id, company=company)

    if request.method == 'POST':
        employee.status = 'terminated'
        employee.termination_date = timezone.now().date()
        employee.save()

        messages.success(request, _('Employee terminated successfully'))
        return redirect('hr:employee_list')

    context = {
        'employee': employee,
    }

    return render(request, 'modules/hr/employee_delete.html', context)


# Placeholder views - سيتم تطويرها لاحقاً
@login_required
def attendance_summary(request):
    return render(request, 'modules/hr/attendance_summary.html')

@login_required
def manual_attendance(request):
    return render(request, 'modules/hr/manual_attendance.html')

@login_required
def gps_attendance(request):
    return render(request, 'modules/hr/gps_attendance.html')

@login_required
def commission_create(request):
    return render(request, 'modules/hr/commission_form.html')

@login_required
def commission_detail(request, commission_id):
    return render(request, 'modules/hr/commission_detail.html')

@login_required
def commission_edit(request, commission_id):
    return render(request, 'modules/hr/commission_form.html')

@login_required
def commission_approve(request, commission_id):
    return redirect('hr:commission_list')

@login_required
def commission_reject(request, commission_id):
    return redirect('hr:commission_list')

@login_required
def commission_calculate(request):
    return render(request, 'modules/hr/commission_calculate.html')

@login_required
def commission_send_to_accounting(request):
    return redirect('hr:commission_list')

@login_required
def payroll_create(request):
    return render(request, 'modules/hr/payroll_form.html')

@login_required
def payroll_detail(request, payroll_id):
    return render(request, 'modules/hr/payroll_detail.html')

@login_required
def payroll_edit(request, payroll_id):
    return render(request, 'modules/hr/payroll_form.html')

@login_required
def payroll_approve(request, payroll_id):
    return redirect('hr:payroll_list')

@login_required
def payroll_print(request, payroll_id):
    return render(request, 'modules/hr/payroll_print.html')

@login_required
def payroll_generate(request):
    return render(request, 'modules/hr/payroll_generate.html')

@login_required
def payroll_send_to_accounting(request):
    return redirect('hr:payroll_list')

@login_required
def advance_list(request):
    return render(request, 'modules/hr/advance_list.html')

@login_required
def advance_create(request):
    return render(request, 'modules/hr/advance_form.html')

@login_required
def advance_detail(request, advance_id):
    return render(request, 'modules/hr/advance_detail.html')

@login_required
def advance_edit(request, advance_id):
    return render(request, 'modules/hr/advance_form.html')

@login_required
def advance_approve(request, advance_id):
    return redirect('hr:advance_list')

@login_required
def advance_reject(request, advance_id):
    return redirect('hr:advance_list')

@login_required
def advance_pay(request, advance_id):
    return redirect('hr:advance_list')

@login_required
def advance_installments(request, advance_id):
    return render(request, 'modules/hr/advance_installments.html')

@login_required
def advance_settlement(request):
    return render(request, 'modules/hr/advance_settlement.html')

@login_required
def contract_list(request):
    return render(request, 'modules/hr/contract_list.html')

@login_required
def contract_create(request):
    return render(request, 'modules/hr/contract_form.html')

@login_required
def contract_detail(request, contract_id):
    return render(request, 'modules/hr/contract_detail.html')

@login_required
def contract_edit(request, contract_id):
    return render(request, 'modules/hr/contract_form.html')

@login_required
def contract_sign(request, contract_id):
    return redirect('hr:contract_list')

@login_required
def contract_renew(request, contract_id):
    return redirect('hr:contract_list')

@login_required
def contract_terminate(request, contract_id):
    return redirect('hr:contract_list')

@login_required
def contracts_expiring(request):
    return render(request, 'modules/hr/contracts_expiring.html')

@login_required
def disciplinary_list(request):
    return render(request, 'modules/hr/disciplinary_list.html')

@login_required
def disciplinary_create(request):
    return render(request, 'modules/hr/disciplinary_form.html')

@login_required
def disciplinary_detail(request, disciplinary_id):
    return render(request, 'modules/hr/disciplinary_detail.html')

@login_required
def disciplinary_edit(request, disciplinary_id):
    return render(request, 'modules/hr/disciplinary_form.html')

@login_required
def disciplinary_approve(request, disciplinary_id):
    return redirect('hr:disciplinary_list')

@login_required
def disciplinary_implement(request, disciplinary_id):
    return redirect('hr:disciplinary_list')

@login_required
def investigation_list(request):
    return render(request, 'modules/hr/investigation_list.html')

@login_required
def investigation_create(request):
    return render(request, 'modules/hr/investigation_form.html')

@login_required
def investigation_detail(request, investigation_id):
    return render(request, 'modules/hr/investigation_detail.html')

@login_required
def investigation_edit(request, investigation_id):
    return render(request, 'modules/hr/investigation_form.html')

@login_required
def investigation_sessions(request, investigation_id):
    return render(request, 'modules/hr/investigation_sessions.html')

@login_required
def investigation_session_create(request, investigation_id):
    return render(request, 'modules/hr/investigation_session_form.html')

@login_required
def investigation_close(request, investigation_id):
    return redirect('hr:investigation_list')

@login_required
def sales_team_list(request):
    return render(request, 'modules/hr/sales_team_list.html')

@login_required
def sales_team_create(request):
    return render(request, 'modules/hr/sales_team_form.html')

@login_required
def sales_team_detail(request, team_id):
    return render(request, 'modules/hr/sales_team_detail.html')

@login_required
def sales_team_edit(request, team_id):
    return render(request, 'modules/hr/sales_team_form.html')

@login_required
def sales_rep_list(request):
    return render(request, 'modules/hr/sales_rep_list.html')

@login_required
def sales_rep_create(request):
    return render(request, 'modules/hr/sales_rep_form.html')

@login_required
def sales_rep_detail(request, rep_id):
    return render(request, 'modules/hr/sales_rep_detail.html')

@login_required
def sales_rep_edit(request, rep_id):
    return render(request, 'modules/hr/sales_rep_form.html')

@login_required
def reports_dashboard(request):
    return render(request, 'modules/hr/reports_dashboard.html')

@login_required
def attendance_report_detailed(request):
    return render(request, 'modules/hr/attendance_report_detailed.html')

@login_required
def payroll_report(request):
    return render(request, 'modules/hr/payroll_report.html')

@login_required
def commission_report(request):
    return render(request, 'modules/hr/commission_report.html')

@login_required
def advance_report(request):
    return render(request, 'modules/hr/advance_report.html')

@login_required
def employee_report(request):
    return render(request, 'modules/hr/employee_report.html')

@login_required
def contract_report(request):
    return render(request, 'modules/hr/contract_report.html')

@login_required
def disciplinary_report(request):
    return render(request, 'modules/hr/disciplinary_report.html')

@login_required
def api_employees(request):
    return JsonResponse({'employees': []})

@login_required
def api_attendance_check_in(request):
    return JsonResponse({'status': 'success'})

@login_required
def api_attendance_check_out(request):
    return JsonResponse({'status': 'success'})

@login_required
def api_attendance_gps(request):
    return JsonResponse({'status': 'success'})

@login_required
def api_commission_calculate(request):
    return JsonResponse({'amount': 0})

@login_required
def hr_settings(request):
    return render(request, 'modules/hr/settings.html')

@login_required
def department_list(request):
    return render(request, 'modules/hr/department_list.html')

@login_required
def department_create(request):
    return render(request, 'modules/hr/department_form.html')

@login_required
def department_edit(request, department_id):
    return render(request, 'modules/hr/department_form.html')

@login_required
def position_list(request):
    return render(request, 'modules/hr/position_list.html')

@login_required
def position_create(request):
    return render(request, 'modules/hr/position_form.html')

@login_required
def position_edit(request, position_id):
    return render(request, 'modules/hr/position_form.html')

@login_required
def work_schedule_list(request):
    return render(request, 'modules/hr/work_schedule_list.html')

@login_required
def work_schedule_create(request):
    return render(request, 'modules/hr/work_schedule_form.html')

@login_required
def work_schedule_edit(request, schedule_id):
    return render(request, 'modules/hr/work_schedule_form.html')

@login_required
def attendance_device_list(request):
    return render(request, 'modules/hr/attendance_device_list.html')

@login_required
def attendance_device_create(request):
    return render(request, 'modules/hr/attendance_device_form.html')

@login_required
def attendance_device_edit(request, device_id):
    return render(request, 'modules/hr/attendance_device_form.html')
