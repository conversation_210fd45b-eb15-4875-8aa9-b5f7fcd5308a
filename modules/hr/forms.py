"""
نماذج وحدة الموارد البشرية
HR Module Forms
"""

from django import forms
from django.utils.translation import gettext_lazy as _
from django.contrib.auth import get_user_model
from django.utils import timezone

from .models import (
    Employee, Department, Position, Attendance, AttendanceDevice,
    Commission, Payroll, AdvanceRequest, Contract, Disciplinary,
    Investigation, SalesTeam, SalesRepresentative, Custody,
    Shift, LeaveType, LeaveRequest, WorkSchedule
)

User = get_user_model()


class EmployeeForm(forms.ModelForm):
    """نموذج الموظف"""

    class Meta:
        model = Employee
        fields = [
            'employee_id', 'user', 'company', 'branch',
            'first_name', 'last_name', 'arabic_name',
            'national_id', 'passport_number', 'birth_date',
            'gender', 'marital_status', 'phone', 'mobile',
            'email', 'address', 'emergency_contact_name',
            'emergency_contact_phone', 'department', 'position',
            'direct_manager', 'shift', 'hire_date', 'basic_salary',
            'photo', 'notes'
        ]
        widgets = {
            'birth_date': forms.DateInput(attrs={'type': 'date'}),
            'hire_date': forms.DateInput(attrs={'type': 'date'}),
            'address': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الأقسام والمناصب حسب الشركة
            self.fields['department'].queryset = Department.objects.filter(
                company=company, is_active=True
            )
            self.fields['position'].queryset = Position.objects.filter(
                company=company, is_active=True
            )
            # فلترة المدراء المباشرين
            self.fields['direct_manager'].queryset = Employee.objects.filter(
                company=company, status='active'
            )
            # فلترة الشفتات
            self.fields['shift'].queryset = Shift.objects.filter(
                company=company, is_active=True
            )


class AttendanceForm(forms.ModelForm):
    """نموذج تسجيل الحضور"""

    class Meta:
        model = Attendance
        fields = [
            'employee', 'date', 'time', 'attendance_type',
            'entry_method', 'device', 'latitude', 'longitude',
            'location_accuracy', 'notes'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'time': forms.TimeInput(attrs={'type': 'time'}),
            'notes': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين والأجهزة حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )
            self.fields['device'].queryset = AttendanceDevice.objects.filter(
                company=company, is_active=True
            )

        # تعيين التاريخ والوقت الحالي كقيم افتراضية
        if not self.instance.pk:
            now = timezone.now()
            self.fields['date'].initial = now.date()
            self.fields['time'].initial = now.time()


class CommissionForm(forms.ModelForm):
    """نموذج العمولة"""

    class Meta:
        model = Commission
        fields = [
            'employee', 'commission_type', 'amount', 'percentage',
            'base_amount', 'period_start', 'period_end', 'notes'
        ]
        widgets = {
            'period_start': forms.DateInput(attrs={'type': 'date'}),
            'period_end': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )

    def clean(self):
        cleaned_data = super().clean()
        amount = cleaned_data.get('amount')
        percentage = cleaned_data.get('percentage')
        base_amount = cleaned_data.get('base_amount')

        # التحقق من صحة حساب العمولة
        if percentage and base_amount:
            calculated_amount = (base_amount * percentage) / 100
            if amount and abs(amount - calculated_amount) > 0.01:
                raise forms.ValidationError(
                    _('Amount does not match calculated commission (%(calculated)s)') % {
                        'calculated': calculated_amount
                    }
                )

        return cleaned_data


class PayrollForm(forms.ModelForm):
    """نموذج كشف الراتب"""

    class Meta:
        model = Payroll
        fields = [
            'employee', 'month', 'year', 'basic_salary',
            'allowances', 'commissions', 'overtime_amount',
            'deductions', 'advances', 'insurance', 'taxes',
            'working_days', 'actual_days', 'overtime_hours',
            'notes'
        ]
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )

        # تعيين الشهر والسنة الحالية كقيم افتراضية
        if not self.instance.pk:
            now = timezone.now()
            self.fields['month'].initial = now.month
            self.fields['year'].initial = now.year


class AdvanceRequestForm(forms.ModelForm):
    """نموذج طلب السلفة"""

    class Meta:
        model = AdvanceRequest
        fields = [
            'employee', 'advance_type', 'amount', 'reason',
            'required_date', 'installments'
        ]
        widgets = {
            'required_date': forms.DateInput(attrs={'type': 'date'}),
            'reason': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount and amount <= 0:
            raise forms.ValidationError(_('Amount must be greater than zero'))
        return amount

    def clean_installments(self):
        installments = self.cleaned_data.get('installments')
        if installments and installments <= 0:
            raise forms.ValidationError(_('Number of installments must be greater than zero'))
        return installments


class ContractForm(forms.ModelForm):
    """نموذج العقد"""

    class Meta:
        model = Contract
        fields = [
            'employee', 'contract_number', 'contract_type',
            'start_date', 'end_date', 'position_title',
            'basic_salary', 'working_hours', 'probation_period',
            'terms_and_conditions', 'benefits', 'contract_file'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'terms_and_conditions': forms.Textarea(attrs={'rows': 5}),
            'benefits': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date <= start_date:
            raise forms.ValidationError(_('End date must be after start date'))

        return cleaned_data


class DisciplinaryForm(forms.ModelForm):
    """نموذج الجزاء"""

    class Meta:
        model = Disciplinary
        fields = [
            'employee', 'disciplinary_number', 'disciplinary_type',
            'incident_date', 'violation_description', 'evidence',
            'penalty_description', 'suspension_days', 'deduction_amount',
            'attachment'
        ]
        widgets = {
            'incident_date': forms.DateInput(attrs={'type': 'date'}),
            'violation_description': forms.Textarea(attrs={'rows': 4}),
            'evidence': forms.Textarea(attrs={'rows': 3}),
            'penalty_description': forms.Textarea(attrs={'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )


class InvestigationForm(forms.ModelForm):
    """نموذج التحقيق"""

    class Meta:
        model = Investigation
        fields = [
            'employee', 'investigation_number', 'investigation_type',
            'case_title', 'case_description', 'incident_date',
            'investigator', 'witnesses', 'evidence_collected',
            'attachment'
        ]
        widgets = {
            'incident_date': forms.DateInput(attrs={'type': 'date'}),
            'case_description': forms.Textarea(attrs={'rows': 4}),
            'witnesses': forms.Textarea(attrs={'rows': 3}),
            'evidence_collected': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )


class SalesTeamForm(forms.ModelForm):
    """نموذج فريق المبيعات"""

    class Meta:
        model = SalesTeam
        fields = [
            'name', 'code', 'company', 'manager',
            'target_amount', 'commission_rate'
        ]

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة المدراء حسب الشركة
            self.fields['manager'].queryset = Employee.objects.filter(
                company=company, status='active'
            )


class SalesRepresentativeForm(forms.ModelForm):
    """نموذج المندوب"""

    class Meta:
        model = SalesRepresentative
        fields = [
            'employee', 'rep_code', 'team', 'territory',
            'commission_rate', 'target_amount'
        ]
        widgets = {
            'territory': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين والفرق حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )
            self.fields['team'].queryset = SalesTeam.objects.filter(
                company=company, is_active=True
            )


class CustodyForm(forms.ModelForm):
    """نموذج العهدة"""

    class Meta:
        model = Custody
        fields = [
            'employee', 'custody_type', 'item_name', 'item_description',
            'serial_number', 'quantity', 'unit_value', 'delivery_date',
            'expected_return_date', 'delivered_by', 'condition_on_delivery',
            'notes'
        ]
        widgets = {
            'delivery_date': forms.DateInput(attrs={'type': 'date'}),
            'expected_return_date': forms.DateInput(attrs={'type': 'date'}),
            'item_description': forms.Textarea(attrs={'rows': 3}),
            'condition_on_delivery': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )

        # تعيين التاريخ الحالي كقيمة افتراضية
        if not self.instance.pk:
            self.fields['delivery_date'].initial = timezone.now().date()

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity and quantity <= 0:
            raise forms.ValidationError(_('Quantity must be greater than zero'))
        return quantity

    def clean_unit_value(self):
        unit_value = self.cleaned_data.get('unit_value')
        if unit_value and unit_value <= 0:
            raise forms.ValidationError(_('Unit value must be greater than zero'))
        return unit_value


class CustodyReturnForm(forms.ModelForm):
    """نموذج إرجاع العهدة"""

    class Meta:
        model = Custody
        fields = [
            'actual_return_date', 'received_by', 'condition_on_return',
            'status', 'notes'
        ]
        widgets = {
            'actual_return_date': forms.DateInput(attrs={'type': 'date'}),
            'condition_on_return': forms.Textarea(attrs={'rows': 3}),
            'notes': forms.Textarea(attrs={'rows': 2}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # تعيين التاريخ الحالي كقيمة افتراضية
        if not self.instance.actual_return_date:
            self.fields['actual_return_date'].initial = timezone.now().date()

        # تحديد خيارات الحالة للإرجاع
        self.fields['status'].choices = [
            ('returned', 'مُرجعة'),
            ('damaged', 'تالفة'),
            ('lost', 'مفقودة'),
        ]


class ShiftForm(forms.ModelForm):
    """نموذج الشفت"""

    class Meta:
        model = Shift
        fields = [
            'name', 'shift_type', 'start_time', 'end_time',
            'break_start', 'break_end', 'break_duration',
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday',
            'grace_period_in', 'grace_period_out', 'overtime_threshold',
            'allow_early_checkin', 'allow_late_checkout', 'auto_checkout', 'auto_checkout_time'
        ]
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'type': 'time'}),
            'break_start': forms.TimeInput(attrs={'type': 'time'}),
            'break_end': forms.TimeInput(attrs={'type': 'time'}),
            'auto_checkout_time': forms.TimeInput(attrs={'type': 'time'}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        # تعيين القيم الافتراضية
        if not self.instance.pk:
            self.fields['break_duration'].initial = timezone.timedelta(hours=1)
            self.fields['grace_period_in'].initial = timezone.timedelta(minutes=15)
            self.fields['grace_period_out'].initial = timezone.timedelta(minutes=15)
            self.fields['overtime_threshold'].initial = timezone.timedelta(hours=8)


class LeaveTypeForm(forms.ModelForm):
    """نموذج نوع الإجازة"""

    class Meta:
        model = LeaveType
        fields = [
            'name', 'code', 'max_days_per_year', 'max_consecutive_days', 'min_notice_days',
            'is_paid', 'salary_percentage', 'requires_approval', 'requires_medical_certificate',
            'carry_forward'
        ]

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        # التحقق من عدم تكرار الكود
        if company:
            self.company = company

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if code:
            # التحقق من عدم تكرار الكود في نفس الشركة
            queryset = LeaveType.objects.filter(company=self.company, code=code)
            if self.instance.pk:
                queryset = queryset.exclude(pk=self.instance.pk)
            if queryset.exists():
                raise forms.ValidationError(_('Leave type with this code already exists'))
        return code


class LeaveRequestForm(forms.ModelForm):
    """نموذج طلب الإجازة"""

    class Meta:
        model = LeaveRequest
        fields = [
            'employee', 'leave_type', 'start_date', 'end_date', 'reason',
            'emergency_contact', 'replacement_employee', 'medical_certificate', 'attachment'
        ]
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
            'reason': forms.Textarea(attrs={'rows': 4}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # فلترة الموظفين وأنواع الإجازات حسب الشركة
            self.fields['employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )
            self.fields['leave_type'].queryset = LeaveType.objects.filter(
                company=company, is_active=True
            )
            self.fields['replacement_employee'].queryset = Employee.objects.filter(
                company=company, status='active'
            )

    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')
        leave_type = cleaned_data.get('leave_type')
        employee = cleaned_data.get('employee')

        if start_date and end_date:
            # التحقق من أن تاريخ الانتهاء بعد تاريخ البداية
            if end_date <= start_date:
                raise forms.ValidationError(_('End date must be after start date'))

            # حساب عدد الأيام
            total_days = (end_date - start_date).days + 1

            if leave_type:
                # التحقق من الحد الأقصى للأيام المتتالية
                if total_days > leave_type.max_consecutive_days:
                    raise forms.ValidationError(
                        _('Maximum consecutive days for this leave type is %(max_days)s days') % {
                            'max_days': leave_type.max_consecutive_days
                        }
                    )

                # التحقق من فترة الإشعار المسبق
                if start_date:
                    notice_days = (start_date - timezone.now().date()).days
                    if notice_days < leave_type.min_notice_days:
                        raise forms.ValidationError(
                            _('Minimum notice period for this leave type is %(min_days)s days') % {
                                'min_days': leave_type.min_notice_days
                            }
                        )

            # التحقق من تداخل الإجازات
            if employee and start_date and end_date:
                overlapping_leaves = LeaveRequest.objects.filter(
                    employee=employee,
                    status__in=['pending', 'approved'],
                    start_date__lte=end_date,
                    end_date__gte=start_date
                )
                if self.instance.pk:
                    overlapping_leaves = overlapping_leaves.exclude(pk=self.instance.pk)

                if overlapping_leaves.exists():
                    raise forms.ValidationError(_('This leave period overlaps with an existing leave request'))

        return cleaned_data


class WorkScheduleForm(forms.ModelForm):
    """نموذج جدول العمل"""

    class Meta:
        model = WorkSchedule
        fields = [
            'name', 'start_time', 'end_time', 'break_start', 'break_end',
            'working_days', 'is_flexible', 'grace_period_minutes'
        ]
        widgets = {
            'start_time': forms.TimeInput(attrs={'type': 'time'}),
            'end_time': forms.TimeInput(attrs={'type': 'time'}),
            'break_start': forms.TimeInput(attrs={'type': 'time'}),
            'break_end': forms.TimeInput(attrs={'type': 'time'}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        # إضافة خيارات أيام العمل
        self.fields['working_days'].help_text = _('Enter working days as comma-separated numbers (1=Monday, 7=Sunday)')

    def clean(self):
        cleaned_data = super().clean()
        start_time = cleaned_data.get('start_time')
        end_time = cleaned_data.get('end_time')
        break_start = cleaned_data.get('break_start')
        break_end = cleaned_data.get('break_end')

        # التحقق من أوقات الاستراحة
        if break_start and break_end:
            if break_end <= break_start:
                raise forms.ValidationError(_('Break end time must be after break start time'))

            # التحقق من أن الاستراحة ضمن ساعات العمل
            if start_time and end_time:
                if break_start < start_time or break_end > end_time:
                    if not (end_time < start_time):  # إلا إذا كان العمل عبر منتصف الليل
                        raise forms.ValidationError(_('Break time must be within working hours'))

        return cleaned_data
