from django import template
from django.db.models import Sum

register = template.Library()

@register.filter
def dictsumattr(queryset, attr):
    """Sum a specific attribute across a queryset"""
    if not queryset:
        return 0
    return queryset.aggregate(total=Sum(attr))['total'] or 0

@register.filter
def add_class(field, css_class):
    """Add a CSS class to a form field"""
    return field.as_widget(attrs={"class": css_class})

@register.filter
def sum_debit(transactions):
    """Sum debit amounts from transactions"""
    if not transactions:
        return 0
    return sum(getattr(t, 'debit', 0) for t in transactions)

@register.filter
def sum_credit(transactions):
    """Sum credit amounts from transactions"""
    if not transactions:
        return 0
    return sum(getattr(t, 'credit', 0) for t in transactions)

@register.filter
def last_balance(transactions):
    """Get the last balance from transactions"""
    if not transactions:
        return 0
    return getattr(transactions[-1], 'balance', 0) if transactions else 0

@register.filter
def mul(value, arg):
    """Multiply filter"""
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0

@register.filter
def div(value, arg):
    """Divide filter"""
    try:
        return float(value) / float(arg) if float(arg) != 0 else 0
    except (ValueError, TypeError):
        return 0
