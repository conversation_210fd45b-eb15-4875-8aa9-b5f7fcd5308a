from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ValidationError
from django.utils import timezone
from mptt.models import MPTTModel, TreeForeignKey
from companies.models import Company, Branch
from users.models import User
import uuid


class FiscalYear(models.Model):
    """Fiscal Year model for accounting periods"""
    STATES = [
        ('draft', _('Draft')),
        ('open', _('Open')),
        ('closed', _('Closed')),
        ('archived', _('Archived')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20, unique=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='fiscal_years', verbose_name=_('Company'))
    start_date = models.DateField(_('Start Date'))
    end_date = models.DateField(_('End Date'))
    state = models.CharField(_('State'), max_length=20, choices=STATES, default='draft')
    is_active = models.BooleanField(_('Active'), default=False)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_fiscal_years', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Fiscal Year')
        verbose_name_plural = _('Fiscal Years')
        ordering = ['-start_date']

    def __str__(self):
        return self.name

    def clean(self):
        # Check if dates are valid
        if self.start_date and self.end_date and self.start_date > self.end_date:
            raise ValidationError(_('Start date must be before end date'))

        # Check for overlapping fiscal years for the same company
        overlapping = FiscalYear.objects.filter(
            company=self.company,
            start_date__lte=self.end_date,
            end_date__gte=self.start_date
        )

        if self.pk:
            overlapping = overlapping.exclude(pk=self.pk)

        if overlapping.exists():
            raise ValidationError(_('This fiscal year overlaps with an existing fiscal year'))

    def save(self, *args, **kwargs):
        self.clean()

        # If this fiscal year is being set as active, deactivate all others
        if self.is_active:
            FiscalYear.objects.filter(company=self.company, is_active=True).update(is_active=False)

        super().save(*args, **kwargs)


class AccountType(models.Model):
    """Account types (Asset, Liability, Equity, Revenue, Expense)"""
    CATEGORIES = [
        ('asset', _('Asset')),
        ('liability', _('Liability')),
        ('equity', _('Equity')),
        ('revenue', _('Revenue')),
        ('expense', _('Expense')),
    ]

    REPORT_TYPES = [
        ('balance_sheet', _('Balance Sheet')),
        ('income_statement', _('Income Statement')),
        ('cash_flow', _('Cash Flow')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    category = models.CharField(_('Category'), max_length=20, choices=CATEGORIES)
    report_type = models.CharField(_('Report Type'), max_length=20, choices=REPORT_TYPES, default='balance_sheet')
    is_debit_balance = models.BooleanField(_('Is Debit Balance'), default=True,
                                          help_text=_('If checked, accounts of this type normally have debit balances'))
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='account_types', verbose_name=_('Company'))
    is_active = models.BooleanField(_('Active'), default=True)
    description = models.TextField(_('Description'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_account_types', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Account Type')
        verbose_name_plural = _('Account Types')
        unique_together = ('code', 'company')
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class AccountChart(models.Model):
    """Chart of Accounts template"""
    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20, unique=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='account_charts', verbose_name=_('Company'))
    is_active = models.BooleanField(_('Active'), default=False)
    description = models.TextField(_('Description'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_account_charts', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Chart of Accounts')
        verbose_name_plural = _('Charts of Accounts')

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        # If this chart is being set as active, deactivate all others
        if self.is_active:
            AccountChart.objects.filter(company=self.company, is_active=True).update(is_active=False)

        super().save(*args, **kwargs)


class Account(MPTTModel):
    """Account model for the chart of accounts"""
    ACCOUNT_LEVELS = [
        (1, _('Level 1 - Main')),
        (2, _('Level 2 - Group')),
        (3, _('Level 3 - Subgroup')),
        (4, _('Level 4 - Detail')),
        (5, _('Level 5 - Transaction')),
    ]

    chart = models.ForeignKey(AccountChart, on_delete=models.CASCADE, related_name='accounts', verbose_name=_('Chart of Accounts'))
    code = models.CharField(_('Code'), max_length=20)
    name = models.CharField(_('Name'), max_length=100)
    type = models.ForeignKey(AccountType, on_delete=models.PROTECT, related_name='accounts', verbose_name=_('Account Type'))
    level = models.IntegerField(_('Level'), choices=ACCOUNT_LEVELS, default=5)
    parent = TreeForeignKey('self', on_delete=models.CASCADE, null=True, blank=True,
                           related_name='children', verbose_name=_('Parent Account'))
    is_active = models.BooleanField(_('Active'), default=True)
    description = models.TextField(_('Description'), blank=True)

    # For transaction accounts only
    allow_manual_transactions = models.BooleanField(_('Allow Manual Transactions'), default=True)
    reconcilable = models.BooleanField(_('Reconcilable'), default=False)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class MPTTMeta:
        order_insertion_by = ['code']

    class Meta:
        verbose_name = _('Account')
        verbose_name_plural = _('Accounts')
        unique_together = ('chart', 'code')

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        # Check if parent account is from the same chart
        if self.parent and self.parent.chart != self.chart:
            raise ValidationError(_('Parent account must be from the same chart of accounts'))

        # Check if level is consistent with parent
        if self.parent:
            if self.level <= self.parent.level:
                raise ValidationError(_('Account level must be greater than parent level'))
        elif self.level != 1:
            # Root accounts must be level 1
            raise ValidationError(_('Root accounts must be level 1'))

        # Only transaction accounts can have manual transactions
        if self.allow_manual_transactions and self.level != 5:
            raise ValidationError(_('Only transaction accounts (level 5) can allow manual transactions'))

        # Only transaction accounts can be reconcilable
        if self.reconcilable and self.level != 5:
            raise ValidationError(_('Only transaction accounts (level 5) can be reconcilable'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class AccountBalance(models.Model):
    """Account balance for a specific fiscal year"""
    account = models.ForeignKey(Account, on_delete=models.CASCADE, related_name='balances', verbose_name=_('Account'))
    fiscal_year = models.ForeignKey(FiscalYear, on_delete=models.CASCADE, related_name='account_balances', verbose_name=_('Fiscal Year'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='account_balances', verbose_name=_('Branch'))

    opening_debit = models.DecimalField(_('Opening Debit'), max_digits=18, decimal_places=2, default=0)
    opening_credit = models.DecimalField(_('Opening Credit'), max_digits=18, decimal_places=2, default=0)

    current_debit = models.DecimalField(_('Current Debit'), max_digits=18, decimal_places=2, default=0)
    current_credit = models.DecimalField(_('Current Credit'), max_digits=18, decimal_places=2, default=0)

    closing_debit = models.DecimalField(_('Closing Debit'), max_digits=18, decimal_places=2, default=0)
    closing_credit = models.DecimalField(_('Closing Credit'), max_digits=18, decimal_places=2, default=0)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Account Balance')
        verbose_name_plural = _('Account Balances')
        unique_together = ('account', 'fiscal_year', 'branch')

    def __str__(self):
        return f"{self.account} - {self.fiscal_year} - {self.branch}"

    @property
    def opening_balance(self):
        """Calculate opening balance based on account type"""
        if self.account.type.is_debit_balance:
            return self.opening_debit - self.opening_credit
        return self.opening_credit - self.opening_debit

    @property
    def current_balance(self):
        """Calculate current balance based on account type"""
        if self.account.type.is_debit_balance:
            return self.current_debit - self.current_credit
        return self.current_credit - self.current_debit

    @property
    def closing_balance(self):
        """Calculate closing balance based on account type"""
        if self.account.type.is_debit_balance:
            return self.closing_debit - self.closing_credit
        return self.closing_credit - self.closing_debit

    def update_closing_balance(self):
        """Update closing balance based on opening and current balances"""
        self.closing_debit = self.opening_debit + self.current_debit
        self.closing_credit = self.opening_credit + self.current_credit
        self.save()


class CashRegister(models.Model):
    """Cash register (treasury) model"""
    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='cash_registers', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='cash_registers', verbose_name=_('Branch'))
    account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='cash_registers', verbose_name=_('Account'))

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_cash_registers', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Cash Register')
        verbose_name_plural = _('Cash Registers')
        unique_together = ('code', 'company')

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        # Check if account is a cash account
        if self.account and self.account.type.category != 'asset':
            raise ValidationError(_('Cash register account must be an asset account'))

        # Check if account allows transactions
        if self.account and not self.account.allow_manual_transactions:
            raise ValidationError(_('Cash register account must allow manual transactions'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class Bank(models.Model):
    """Bank model"""
    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    swift_code = models.CharField(_('SWIFT Code'), max_length=20, blank=True)
    address = models.TextField(_('Address'), blank=True)
    phone = models.CharField(_('Phone'), max_length=20, blank=True)
    email = models.EmailField(_('Email'), blank=True)
    website = models.URLField(_('Website'), blank=True)

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Bank')
        verbose_name_plural = _('Banks')
        ordering = ['name']

    def __str__(self):
        return self.name


class BankAccount(models.Model):
    """Bank account model"""
    ACCOUNT_TYPES = [
        ('current', _('Current Account')),
        ('savings', _('Savings Account')),
        ('deposit', _('Deposit Account')),
        ('credit', _('Credit Account')),
    ]

    CURRENCIES = [
        ('USD', _('US Dollar')),
        ('EUR', _('Euro')),
        ('GBP', _('British Pound')),
        ('SAR', _('Saudi Riyal')),
        ('AED', _('UAE Dirham')),
        ('EGP', _('Egyptian Pound')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    number = models.CharField(_('Account Number'), max_length=50)
    iban = models.CharField(_('IBAN'), max_length=50, blank=True)

    bank = models.ForeignKey(Bank, on_delete=models.CASCADE, related_name='accounts', verbose_name=_('Bank'))
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='bank_accounts', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='bank_accounts', verbose_name=_('Branch'))
    account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='bank_accounts', verbose_name=_('Account'))

    account_type = models.CharField(_('Account Type'), max_length=20, choices=ACCOUNT_TYPES, default='current')
    currency = models.CharField(_('Currency'), max_length=3, choices=CURRENCIES, default='USD')

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_bank_accounts', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Bank Account')
        verbose_name_plural = _('Bank Accounts')
        unique_together = ('bank', 'number', 'company')

    def __str__(self):
        return f"{self.bank.name} - {self.number}"

    def clean(self):
        # Check if account is a bank account
        if self.account and self.account.type.category != 'asset':
            raise ValidationError(_('Bank account must be an asset account'))

        # Check if account allows transactions
        if self.account and not self.account.allow_manual_transactions:
            raise ValidationError(_('Bank account must allow manual transactions'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class CheckBook(models.Model):
    """Check book model"""
    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    bank_account = models.ForeignKey(BankAccount, on_delete=models.CASCADE, related_name='check_books', verbose_name=_('Bank Account'))

    start_number = models.CharField(_('Start Number'), max_length=20)
    end_number = models.CharField(_('End Number'), max_length=20)
    next_number = models.CharField(_('Next Number'), max_length=20)

    # Track check book usage
    used_count = models.IntegerField(_('Used Checks'), default=0)
    remaining_count = models.IntegerField(_('Remaining Checks'), default=0)

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_check_books', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Check Book')
        verbose_name_plural = _('Check Books')
        unique_together = ('bank_account', 'code')

    def __str__(self):
        return f"{self.bank_account} - {self.code}"

    def clean(self):
        """Validate check book numbers"""
        # Ensure start_number, end_number, and next_number are valid
        if self.start_number and self.end_number:
            # Extract numeric parts
            start_numeric = ''.join(filter(str.isdigit, self.start_number))
            end_numeric = ''.join(filter(str.isdigit, self.end_number))

            # Get prefixes
            start_prefix = self.start_number.replace(start_numeric, '', 1)
            end_prefix = self.end_number.replace(end_numeric, '', 1)

            # Validate prefixes match
            if start_prefix != end_prefix:
                raise ValidationError(_('Start number and end number must have the same prefix format'))

            # Validate numeric parts
            try:
                start_num = int(start_numeric)
                end_num = int(end_numeric)

                if start_num >= end_num:
                    raise ValidationError(_('End number must be greater than start number'))

                # Set next_number if not provided or invalid
                if not self.next_number or self.next_number == '':
                    self.next_number = self.start_number
                else:
                    next_numeric = ''.join(filter(str.isdigit, self.next_number))
                    next_prefix = self.next_number.replace(next_numeric, '', 1)

                    if next_prefix != start_prefix:
                        raise ValidationError(_('Next number must have the same prefix format as start and end numbers'))

                    try:
                        next_num = int(next_numeric)
                        if next_num < start_num or next_num > end_num:
                            raise ValidationError(_('Next number must be between start and end numbers'))
                    except ValueError:
                        raise ValidationError(_('Next number must be a valid number'))

                # Calculate remaining checks
                self.remaining_count = end_num - start_num + 1 - self.used_count

            except ValueError:
                raise ValidationError(_('Start and end numbers must contain valid numeric parts'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def get_next_check_number(self):
        """Get the next check number and increment it"""
        current = self.next_number

        # Extract numeric part
        numeric_part = ''.join(filter(str.isdigit, current))
        prefix = current.replace(numeric_part, '', 1)

        try:
            next_num = int(numeric_part) + 1
            # Format with leading zeros if needed
            formatted_next = f"{prefix}{next_num:0{len(numeric_part)}d}"

            # Check if we've reached the end
            end_numeric = ''.join(filter(str.isdigit, self.end_number))
            if int(numeric_part) >= int(end_numeric):
                # We've reached the end of the check book
                self.is_active = False
            else:
                self.next_number = formatted_next

            self.used_count += 1
            self.remaining_count -= 1
            self.save()

            return current
        except ValueError:
            # If conversion fails, just return current
            return current


class Check(models.Model):
    """Check model for both incoming and outgoing checks"""
    CHECK_TYPES = [
        ('incoming', _('Incoming')),
        ('outgoing', _('Outgoing')),
    ]

    CHECK_STATES = [
        ('draft', _('Draft')),
        ('registered', _('Registered')),
        ('pending', _('Pending')),
        ('collected', _('Collected')),
        ('deposited', _('Deposited')),
        ('bounced', _('Bounced')),
        ('cancelled', _('Cancelled')),
    ]

    number = models.CharField(_('Check Number'), max_length=50)
    amount = models.DecimalField(_('Amount'), max_digits=18, decimal_places=2)
    date = models.DateField(_('Date'))
    due_date = models.DateField(_('Due Date'))

    check_type = models.CharField(_('Check Type'), max_length=10, choices=CHECK_TYPES)
    state = models.CharField(_('State'), max_length=20, choices=CHECK_STATES, default='draft')

    # For outgoing checks
    check_book = models.ForeignKey(CheckBook, on_delete=models.SET_NULL, null=True, blank=True,
                                  related_name='checks', verbose_name=_('Check Book'))

    # For incoming checks
    bank_name = models.CharField(_('Bank Name'), max_length=100, blank=True)
    account_number = models.CharField(_('Account Number'), max_length=50, blank=True)

    # Common fields
    bank_account = models.ForeignKey(BankAccount, on_delete=models.CASCADE, related_name='checks',
                                    verbose_name=_('Bank Account'))
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='checks', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='checks', verbose_name=_('Branch'))

    # Partner information (customer or vendor)
    partner_name = models.CharField(_('Partner Name'), max_length=100)
    partner_type = models.CharField(_('Partner Type'), max_length=20, choices=[('customer', _('Customer')), ('vendor', _('Vendor'))])
    partner_id = models.IntegerField(_('Partner ID'), null=True, blank=True)

    memo = models.TextField(_('Memo'), blank=True)
    image = models.ImageField(_('Check Image'), upload_to='checks/', null=True, blank=True)

    # Tracking fields
    reference = models.CharField(_('Reference'), max_length=50, unique=True, default=uuid.uuid4)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_checks', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Check')
        verbose_name_plural = _('Checks')
        unique_together = ('bank_account', 'number', 'check_type')

    def __str__(self):
        return f"{self.number} - {self.amount}"

    def clean(self):
        # Validate check book for outgoing checks
        if self.check_type == 'outgoing' and not self.check_book:
            raise ValidationError(_('Check book is required for outgoing checks'))

        # Validate bank name and account number for incoming checks
        if self.check_type == 'incoming' and not self.bank_name:
            raise ValidationError(_('Bank name is required for incoming checks'))

    def save(self, *args, **kwargs):
        self.clean()

        # Generate reference if not provided
        if not self.reference:
            self.reference = str(uuid.uuid4())

        super().save(*args, **kwargs)


class Journal(models.Model):
    """Journal model for accounting entries"""
    JOURNAL_TYPES = [
        ('general', _('General')),
        ('sale', _('Sale')),
        ('purchase', _('Purchase')),
        ('cash', _('Cash')),
        ('bank', _('Bank')),
        ('asset', _('Asset')),
        ('closing', _('Closing')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    type = models.CharField(_('Type'), max_length=20, choices=JOURNAL_TYPES)

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='journals', verbose_name=_('Company'))

    # Default accounts for specific journal types
    default_debit_account = models.ForeignKey(Account, on_delete=models.PROTECT, null=True, blank=True,
                                            related_name='debit_journals', verbose_name=_('Default Debit Account'))
    default_credit_account = models.ForeignKey(Account, on_delete=models.PROTECT, null=True, blank=True,
                                             related_name='credit_journals', verbose_name=_('Default Credit Account'))

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_journals', verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Journal')
        verbose_name_plural = _('Journals')
        unique_together = ('code', 'company')

    def __str__(self):
        return f"{self.code} - {self.name}"


class JournalEntry(models.Model):
    """Journal entry model for accounting transactions"""
    STATES = [
        ('draft', _('Draft')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    reference = models.CharField(_('Reference'), max_length=50, unique=True, default=uuid.uuid4)
    date = models.DateField(_('Date'))

    journal = models.ForeignKey(Journal, on_delete=models.PROTECT, related_name='entries', verbose_name=_('Journal'))
    fiscal_year = models.ForeignKey(FiscalYear, on_delete=models.PROTECT, related_name='journal_entries', verbose_name=_('Fiscal Year'))

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='journal_entries', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='journal_entries', verbose_name=_('Branch'))

    state = models.CharField(_('State'), max_length=20, choices=STATES, default='draft')

    # Optional fields for specific entry types
    partner_name = models.CharField(_('Partner Name'), max_length=100, blank=True)
    partner_type = models.CharField(_('Partner Type'), max_length=20,
                                   choices=[('customer', _('Customer')), ('vendor', _('Vendor'))], blank=True)
    partner_id = models.IntegerField(_('Partner ID'), null=True, blank=True)

    memo = models.TextField(_('Memo'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_journal_entries', verbose_name=_('Created By'))
    posted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='posted_journal_entries', verbose_name=_('Posted By'))

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    posted_at = models.DateTimeField(_('Posted At'), null=True, blank=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Journal Entry')
        verbose_name_plural = _('Journal Entries')
        ordering = ['-date', '-id']

    def __str__(self):
        return f"{self.reference} - {self.name}"

    @property
    def is_balanced(self):
        """Check if the journal entry is balanced (debits = credits)"""
        debit_sum = self.lines.aggregate(models.Sum('debit'))['debit__sum'] or 0
        credit_sum = self.lines.aggregate(models.Sum('credit'))['credit__sum'] or 0
        return abs(debit_sum - credit_sum) < 0.01  # Allow for small rounding differences

    def post(self, user=None):
        """Post the journal entry"""
        if self.state != 'draft':
            raise ValidationError(_('Only draft entries can be posted'))

        if not self.is_balanced:
            raise ValidationError(_('Journal entry must be balanced before posting'))

        self.state = 'posted'
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save()

        # Update account balances
        for line in self.lines.all():
            balance, created = AccountBalance.objects.get_or_create(
                account=line.account,
                fiscal_year=self.fiscal_year,
                branch=self.branch
            )

            balance.current_debit += line.debit
            balance.current_credit += line.credit
            balance.update_closing_balance()

    def cancel(self):
        """Cancel the journal entry"""
        if self.state != 'posted':
            raise ValidationError(_('Only posted entries can be cancelled'))

        # Reverse the effect on account balances
        for line in self.lines.all():
            balance = AccountBalance.objects.get(
                account=line.account,
                fiscal_year=self.fiscal_year,
                branch=self.branch
            )

            balance.current_debit -= line.debit
            balance.current_credit -= line.credit
            balance.update_closing_balance()

        self.state = 'cancelled'
        self.save()


class JournalEntryLine(models.Model):
    """Journal entry line model for individual accounting transactions"""
    entry = models.ForeignKey(JournalEntry, on_delete=models.CASCADE, related_name='lines', verbose_name=_('Journal Entry'))
    account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='journal_lines', verbose_name=_('Account'))

    name = models.CharField(_('Description'), max_length=200)
    debit = models.DecimalField(_('Debit'), max_digits=18, decimal_places=2, default=0)
    credit = models.DecimalField(_('Credit'), max_digits=18, decimal_places=2, default=0)

    # Optional fields for reconciliation
    partner_name = models.CharField(_('Partner Name'), max_length=100, blank=True)
    partner_type = models.CharField(_('Partner Type'), max_length=20,
                                   choices=[('customer', _('Customer')), ('vendor', _('Vendor'))], blank=True)
    partner_id = models.IntegerField(_('Partner ID'), null=True, blank=True)

    # For reconciliation
    reconciled = models.BooleanField(_('Reconciled'), default=False)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Journal Entry Line')
        verbose_name_plural = _('Journal Entry Lines')

    def __str__(self):
        return f"{self.entry.reference} - {self.name}"

    def clean(self):
        # Check if account allows manual transactions
        if not self.account.allow_manual_transactions and self.entry.journal.type == 'general':
            raise ValidationError(_('This account does not allow manual transactions'))

        # Check if debit and credit are valid
        if self.debit < 0 or self.credit < 0:
            raise ValidationError(_('Debit and credit amounts must be positive'))

        if self.debit > 0 and self.credit > 0:
            raise ValidationError(_('A journal item cannot have both debit and credit values'))

        if self.debit == 0 and self.credit == 0:
            raise ValidationError(_('A journal item must have either a debit or credit value'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class PaymentMethod(models.Model):
    """Payment method model"""
    PAYMENT_TYPES = [
        ('cash', _('Cash')),
        ('bank', _('Bank Transfer')),
        ('check', _('Check')),
        ('card', _('Credit/Debit Card')),
        ('online', _('Online Payment')),
        ('other', _('Other')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    payment_type = models.CharField(_('Payment Type'), max_length=20, choices=PAYMENT_TYPES)

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='payment_methods', verbose_name=_('Company'))

    # Default accounts
    account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='payment_methods',
                               verbose_name=_('Account'), null=True, blank=True)

    # For cash payment methods
    cash_register = models.ForeignKey(CashRegister, on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='payment_methods', verbose_name=_('Cash Register'))

    # For bank payment methods
    bank_account = models.ForeignKey(BankAccount, on_delete=models.SET_NULL, null=True, blank=True,
                                    related_name='payment_methods', verbose_name=_('Bank Account'))

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Payment Method')
        verbose_name_plural = _('Payment Methods')
        unique_together = ('code', 'company')

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        # Validate based on payment type
        if self.payment_type == 'cash' and not self.cash_register:
            raise ValidationError(_('Cash register is required for cash payment methods'))

        if self.payment_type == 'bank' and not self.bank_account:
            raise ValidationError(_('Bank account is required for bank payment methods'))

        if self.payment_type == 'check' and not self.bank_account:
            raise ValidationError(_('Bank account is required for check payment methods'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class Voucher(models.Model):
    """Base model for payment and receipt vouchers"""
    VOUCHER_TYPES = [
        ('receipt', _('Receipt')),
        ('payment', _('Payment')),
    ]

    STATES = [
        ('draft', _('Draft')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
    ]

    number = models.CharField(_('Number'), max_length=50)
    reference = models.CharField(_('Reference'), max_length=50, unique=True, default=uuid.uuid4)
    date = models.DateField(_('Date'))

    voucher_type = models.CharField(_('Voucher Type'), max_length=20, choices=VOUCHER_TYPES)
    state = models.CharField(_('State'), max_length=20, choices=STATES, default='draft')

    amount = models.DecimalField(_('Amount'), max_digits=18, decimal_places=2)

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='vouchers', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='vouchers', verbose_name=_('Branch'))
    fiscal_year = models.ForeignKey(FiscalYear, on_delete=models.PROTECT, related_name='vouchers', verbose_name=_('Fiscal Year'))

    # Partner information
    partner_name = models.CharField(_('Partner Name'), max_length=100)
    partner_type = models.CharField(_('Partner Type'), max_length=20, choices=[('customer', _('Customer')), ('vendor', _('Vendor'))])
    partner_id = models.IntegerField(_('Partner ID'), null=True, blank=True)

    # Payment method
    payment_method = models.ForeignKey(PaymentMethod, on_delete=models.PROTECT, related_name='vouchers', verbose_name=_('Payment Method'))

    # For check payments
    payment_check = models.ForeignKey(Check, on_delete=models.SET_NULL, null=True, blank=True, related_name='vouchers', verbose_name=_('Check'))

    # Journal entry created for this voucher
    journal_entry = models.ForeignKey(JournalEntry, on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='vouchers', verbose_name=_('Journal Entry'))

    memo = models.TextField(_('Memo'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_vouchers', verbose_name=_('Created By'))
    posted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='posted_vouchers', verbose_name=_('Posted By'))

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    posted_at = models.DateTimeField(_('Posted At'), null=True, blank=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Voucher')
        verbose_name_plural = _('Vouchers')
        unique_together = ('number', 'voucher_type', 'company', 'fiscal_year')

    def __str__(self):
        return f"{self.number} - {self.amount}"

    def create_journal_entry(self, user=None):
        """Create a journal entry for this voucher"""
        if self.journal_entry:
            raise ValidationError(_('Journal entry already exists for this voucher'))

        # Determine journal type based on payment method
        if self.payment_method.payment_type == 'cash':
            journal_type = 'cash'
        elif self.payment_method.payment_type in ['bank', 'check']:
            journal_type = 'bank'
        else:
            journal_type = 'general'

        # Get or create journal
        journal, created = Journal.objects.get_or_create(
            company=self.company,
            type=journal_type,
            defaults={
                'name': f"{journal_type.capitalize()} Journal",
                'code': f"{journal_type.upper()}"
            }
        )

        # Create journal entry
        entry = JournalEntry.objects.create(
            name=f"{'Receipt' if self.voucher_type == 'receipt' else 'Payment'} {self.number}",
            reference=f"{self.voucher_type.upper()}-{self.number}",
            date=self.date,
            journal=journal,
            fiscal_year=self.fiscal_year,
            company=self.company,
            branch=self.branch,
            partner_name=self.partner_name,
            partner_type=self.partner_type,
            partner_id=self.partner_id,
            memo=self.memo,
            created_by=user or self.created_by
        )

        # Create journal entry lines
        if self.voucher_type == 'receipt':
            # Debit payment method account, credit partner account

            # Debit line (payment method)
            if self.payment_method.payment_type == 'cash':
                debit_account = self.payment_method.cash_register.account
            elif self.payment_method.payment_type in ['bank', 'check']:
                debit_account = self.payment_method.bank_account.account
            else:
                debit_account = self.payment_method.account

            JournalEntryLine.objects.create(
                entry=entry,
                account=debit_account,
                name=f"Receipt from {self.partner_name}",
                debit=self.amount,
                credit=0,
                partner_name=self.partner_name,
                partner_type=self.partner_type,
                partner_id=self.partner_id
            )

            # Credit line (partner)
            # In a real system, we would get the partner's account from the partner record
            # For now, we'll use a placeholder account
            credit_account = Account.objects.filter(
                chart__company=self.company,
                type__category='asset',
                code__startswith='1110'  # Accounts Receivable
            ).first()

            if not credit_account:
                raise ValidationError(_('No accounts receivable account found'))

            JournalEntryLine.objects.create(
                entry=entry,
                account=credit_account,
                name=f"Receipt from {self.partner_name}",
                debit=0,
                credit=self.amount,
                partner_name=self.partner_name,
                partner_type=self.partner_type,
                partner_id=self.partner_id
            )
        else:  # payment
            # Debit partner account, credit payment method account

            # Debit line (partner)
            # In a real system, we would get the partner's account from the partner record
            # For now, we'll use a placeholder account
            debit_account = Account.objects.filter(
                chart__company=self.company,
                type__category='liability',
                code__startswith='2110'  # Accounts Payable
            ).first()

            if not debit_account:
                raise ValidationError(_('No accounts payable account found'))

            JournalEntryLine.objects.create(
                entry=entry,
                account=debit_account,
                name=f"Payment to {self.partner_name}",
                debit=self.amount,
                credit=0,
                partner_name=self.partner_name,
                partner_type=self.partner_type,
                partner_id=self.partner_id
            )

            # Credit line (payment method)
            if self.payment_method.payment_type == 'cash':
                credit_account = self.payment_method.cash_register.account
            elif self.payment_method.payment_type in ['bank', 'check']:
                credit_account = self.payment_method.bank_account.account
            else:
                credit_account = self.payment_method.account

            JournalEntryLine.objects.create(
                entry=entry,
                account=credit_account,
                name=f"Payment to {self.partner_name}",
                debit=0,
                credit=self.amount,
                partner_name=self.partner_name,
                partner_type=self.partner_type,
                partner_id=self.partner_id
            )

        # Link journal entry to voucher
        self.journal_entry = entry
        self.save()

        return entry

    def post(self, user=None):
        """Post the voucher"""
        if self.state != 'draft':
            raise ValidationError(_('Only draft vouchers can be posted'))

        # Create journal entry if not exists
        if not self.journal_entry:
            self.create_journal_entry(user)

        # Post journal entry
        self.journal_entry.post(user)

        # Update voucher state
        self.state = 'posted'
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save()

    def cancel(self):
        """Cancel the voucher"""
        if self.state != 'posted':
            raise ValidationError(_('Only posted vouchers can be cancelled'))

        # Cancel journal entry
        if self.journal_entry:
            self.journal_entry.cancel()

        # Update voucher state
        self.state = 'cancelled'
        self.save()


class AssetCategory(models.Model):
    """Asset category model"""
    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='asset_categories', verbose_name=_('Company'))

    # Default accounts
    asset_account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='asset_categories',
                                     verbose_name=_('Asset Account'))
    depreciation_account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='depreciation_asset_categories',
                                           verbose_name=_('Depreciation Account'))
    accumulated_depreciation_account = models.ForeignKey(Account, on_delete=models.PROTECT,
                                                       related_name='accumulated_depreciation_asset_categories',
                                                       verbose_name=_('Accumulated Depreciation Account'))

    # Default depreciation settings
    depreciation_rate = models.DecimalField(_('Depreciation Rate (%)'), max_digits=5, decimal_places=2, default=0)
    useful_life_years = models.PositiveIntegerField(_('Useful Life (Years)'), default=0)

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_asset_categories',
                                  verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Asset Category')
        verbose_name_plural = _('Asset Categories')
        unique_together = ('code', 'company')

    def __str__(self):
        return f"{self.code} - {self.name}"

    def clean(self):
        # Validate accounts
        if self.asset_account and self.asset_account.type.category != 'asset':
            raise ValidationError(_('Asset account must be an asset account'))

        if self.depreciation_account and self.depreciation_account.type.category != 'expense':
            raise ValidationError(_('Depreciation account must be an expense account'))

        if self.accumulated_depreciation_account and self.accumulated_depreciation_account.type.category != 'asset':
            raise ValidationError(_('Accumulated depreciation account must be an asset account'))

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)


class DepreciationMethod(models.Model):
    """Depreciation method model"""
    METHOD_TYPES = [
        ('straight_line', _('Straight Line')),
        ('declining_balance', _('Declining Balance')),
        ('sum_of_years_digits', _('Sum of Years Digits')),
        ('units_of_production', _('Units of Production')),
        ('custom', _('Custom')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    method_type = models.CharField(_('Method Type'), max_length=30, choices=METHOD_TYPES)

    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='depreciation_methods', verbose_name=_('Company'))

    # For declining balance method
    factor = models.DecimalField(_('Factor'), max_digits=5, decimal_places=2, default=2.0,
                               help_text=_('Factor for declining balance method (e.g., 2.0 for double declining)'))

    is_active = models.BooleanField(_('Active'), default=True)
    notes = models.TextField(_('Notes'), blank=True)

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Depreciation Method')
        verbose_name_plural = _('Depreciation Methods')
        unique_together = ('code', 'company')

    def __str__(self):
        return f"{self.code} - {self.name}"

    def calculate_depreciation(self, asset_cost, salvage_value, useful_life, period, accumulated_depreciation=0):
        """
        Calculate depreciation amount for a period

        Parameters:
        - asset_cost: Original cost of the asset
        - salvage_value: Expected salvage value at the end of useful life
        - useful_life: Useful life in periods (months, years, etc.)
        - period: Current period (1-based)
        - accumulated_depreciation: Accumulated depreciation so far

        Returns:
        - Depreciation amount for the period
        """
        depreciable_cost = asset_cost - salvage_value

        if self.method_type == 'straight_line':
            # Straight Line: Equal depreciation each period
            return depreciable_cost / useful_life if useful_life > 0 else 0

        elif self.method_type == 'declining_balance':
            # Declining Balance: Depreciation based on remaining book value
            if useful_life <= 0:
                return 0

            rate = self.factor / useful_life
            book_value = asset_cost - accumulated_depreciation
            depreciation = book_value * rate

            # Switch to straight-line if it gives higher depreciation
            remaining_periods = useful_life - period + 1
            if remaining_periods > 0:
                straight_line = (book_value - salvage_value) / remaining_periods
                return max(depreciation, straight_line)
            return depreciation

        elif self.method_type == 'sum_of_years_digits':
            # Sum of Years Digits: Accelerated depreciation
            if useful_life <= 0 or period > useful_life:
                return 0

            sum_of_years = (useful_life * (useful_life + 1)) / 2
            depreciation_rate = (useful_life - period + 1) / sum_of_years
            return depreciable_cost * depreciation_rate

        elif self.method_type == 'units_of_production':
            # Units of Production: Based on actual usage
            # This requires additional data about estimated total units and actual units
            # For simplicity, we'll return 0 here
            return 0

        else:  # custom
            # Custom method requires specific implementation
            return 0


def generate_uuid():
    return str(uuid.uuid4())

class Asset(models.Model):
    """Asset model"""
    STATES = [
        ('draft', _('Draft')),
        ('active', _('Active')),
        ('fully_depreciated', _('Fully Depreciated')),
        ('disposed', _('Disposed')),
        ('sold', _('Sold')),
    ]

    DEPRECIATION_FREQUENCIES = [
        ('monthly', _('Monthly')),
        ('quarterly', _('Quarterly')),
        ('semi_annual', _('Semi-Annual')),
        ('annual', _('Annual')),
    ]

    name = models.CharField(_('Name'), max_length=100)
    code = models.CharField(_('Code'), max_length=20)
    reference = models.CharField(_('Reference'), max_length=50, unique=True, default=generate_uuid)

    category = models.ForeignKey(AssetCategory, on_delete=models.PROTECT, related_name='assets', verbose_name=_('Category'))
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='assets', verbose_name=_('Company'))
    branch = models.ForeignKey(Branch, on_delete=models.CASCADE, related_name='assets', verbose_name=_('Branch'))

    # Asset details
    purchase_date = models.DateField(_('Purchase Date'))
    in_service_date = models.DateField(_('In Service Date'))
    purchase_value = models.DecimalField(_('Purchase Value'), max_digits=18, decimal_places=2)
    salvage_value = models.DecimalField(_('Salvage Value'), max_digits=18, decimal_places=2, default=0)

    # Depreciation settings
    depreciation_method = models.ForeignKey(DepreciationMethod, on_delete=models.PROTECT, related_name='assets',
                                          verbose_name=_('Depreciation Method'))
    useful_life_years = models.PositiveIntegerField(_('Useful Life (Years)'))
    depreciation_frequency = models.CharField(_('Depreciation Frequency'), max_length=20,
                                            choices=DEPRECIATION_FREQUENCIES, default='monthly')

    # Accounts (can override category defaults)
    asset_account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='assets',
                                     verbose_name=_('Asset Account'), null=True, blank=True)
    depreciation_account = models.ForeignKey(Account, on_delete=models.PROTECT, related_name='depreciation_assets',
                                           verbose_name=_('Depreciation Account'), null=True, blank=True)
    accumulated_depreciation_account = models.ForeignKey(Account, on_delete=models.PROTECT,
                                                       related_name='accumulated_depreciation_assets',
                                                       verbose_name=_('Accumulated Depreciation Account'),
                                                       null=True, blank=True)

    # Status
    state = models.CharField(_('State'), max_length=20, choices=STATES, default='draft')
    is_active = models.BooleanField(_('Active'), default=True)

    # Additional information
    description = models.TextField(_('Description'), blank=True)
    location = models.CharField(_('Location'), max_length=100, blank=True)
    serial_number = models.CharField(_('Serial Number'), max_length=50, blank=True)
    model = models.CharField(_('Model'), max_length=50, blank=True)
    manufacturer = models.CharField(_('Manufacturer'), max_length=100, blank=True)
    warranty_expiry = models.DateField(_('Warranty Expiry'), null=True, blank=True)

    # Disposal information
    disposal_date = models.DateField(_('Disposal Date'), null=True, blank=True)
    disposal_value = models.DecimalField(_('Disposal Value'), max_digits=18, decimal_places=2, null=True, blank=True)
    disposal_reason = models.TextField(_('Disposal Reason'), blank=True)

    # Journal entry for asset acquisition
    acquisition_journal_entry = models.ForeignKey(JournalEntry, on_delete=models.SET_NULL, null=True, blank=True,
                                                related_name='acquired_assets', verbose_name=_('Acquisition Journal Entry'))

    # Journal entry for asset disposal
    disposal_journal_entry = models.ForeignKey(JournalEntry, on_delete=models.SET_NULL, null=True, blank=True,
                                             related_name='disposed_assets', verbose_name=_('Disposal Journal Entry'))

    # Tracking fields
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_assets',
                                  verbose_name=_('Created By'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Asset')
        verbose_name_plural = _('Assets')
        unique_together = ('code', 'company')

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def current_value(self):
        """Calculate current book value of the asset"""
        accumulated_depreciation = self.depreciations.filter(state='posted').aggregate(
            total=models.Sum('amount'))['total'] or 0
        return self.purchase_value - accumulated_depreciation

    @property
    def depreciation_periods(self):
        """Calculate total number of depreciation periods"""
        if self.depreciation_frequency == 'monthly':
            return self.useful_life_years * 12
        elif self.depreciation_frequency == 'quarterly':
            return self.useful_life_years * 4
        elif self.depreciation_frequency == 'semi_annual':
            return self.useful_life_years * 2
        else:  # annual
            return self.useful_life_years

    @property
    def remaining_periods(self):
        """Calculate remaining depreciation periods"""
        completed_periods = self.depreciations.filter(state='posted').count()
        return max(0, self.depreciation_periods - completed_periods)

    @property
    def next_depreciation_date(self):
        """Calculate the next depreciation date"""
        last_depreciation = self.depreciations.filter(state='posted').order_by('-date').first()

        if not last_depreciation:
            # If no depreciation has been posted yet, start from in_service_date
            start_date = self.in_service_date
        else:
            start_date = last_depreciation.date

        if self.depreciation_frequency == 'monthly':
            return start_date + timezone.timedelta(days=30)
        elif self.depreciation_frequency == 'quarterly':
            return start_date + timezone.timedelta(days=90)
        elif self.depreciation_frequency == 'semi_annual':
            return start_date + timezone.timedelta(days=180)
        else:  # annual
            return start_date + timezone.timedelta(days=365)

    def clean(self):
        # Validate dates
        if self.purchase_date and self.in_service_date and self.purchase_date > self.in_service_date:
            raise ValidationError(_('Purchase date must be before or equal to in-service date'))

        # Validate disposal
        if self.state in ['disposed', 'sold'] and not self.disposal_date:
            raise ValidationError(_('Disposal date is required for disposed or sold assets'))

        # Validate accounts
        if not self.asset_account:
            self.asset_account = self.category.asset_account

        if not self.depreciation_account:
            self.depreciation_account = self.category.depreciation_account

        if not self.accumulated_depreciation_account:
            self.accumulated_depreciation_account = self.category.accumulated_depreciation_account

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def create_acquisition_journal_entry(self, user=None):
        """Create journal entry for asset acquisition"""
        if self.acquisition_journal_entry:
            raise ValidationError(_('Acquisition journal entry already exists for this asset'))

        # Get or create asset journal
        journal, created = Journal.objects.get_or_create(
            company=self.company,
            type='asset',
            defaults={
                'name': _('Asset Journal'),
                'code': 'ASSET'
            }
        )

        # Create journal entry
        entry = JournalEntry.objects.create(
            name=f"Asset Acquisition: {self.name}",
            reference=f"ASSET-ACQ-{self.code}",
            date=self.purchase_date,
            journal=journal,
            fiscal_year=FiscalYear.objects.filter(
                company=self.company,
                start_date__lte=self.purchase_date,
                end_date__gte=self.purchase_date,
                state='open'
            ).first(),
            company=self.company,
            branch=self.branch,
            memo=f"Acquisition of asset: {self.name}",
            created_by=user or self.created_by
        )

        # Create journal entry lines
        # Debit asset account
        JournalEntryLine.objects.create(
            entry=entry,
            account=self.asset_account,
            name=f"Asset Acquisition: {self.name}",
            debit=self.purchase_value,
            credit=0
        )

        # Credit cash/payable account (placeholder)
        # In a real system, we would get the payment account from the acquisition details
        credit_account = Account.objects.filter(
            chart__company=self.company,
            type__category='liability',
            code__startswith='2110'  # Accounts Payable
        ).first()

        if not credit_account:
            raise ValidationError(_('No accounts payable account found'))

        JournalEntryLine.objects.create(
            entry=entry,
            account=credit_account,
            name=f"Asset Acquisition: {self.name}",
            debit=0,
            credit=self.purchase_value
        )

        # Link journal entry to asset
        self.acquisition_journal_entry = entry
        self.save()

        return entry

    def create_disposal_journal_entry(self, user=None):
        """Create journal entry for asset disposal"""
        if not self.disposal_date or not self.disposal_value:
            raise ValidationError(_('Disposal date and value are required for creating disposal journal entry'))

        if self.disposal_journal_entry:
            raise ValidationError(_('Disposal journal entry already exists for this asset'))

        # Calculate accumulated depreciation
        accumulated_depreciation = self.depreciations.filter(state='posted').aggregate(
            total=models.Sum('amount'))['total'] or 0

        # Calculate gain/loss on disposal
        book_value = self.purchase_value - accumulated_depreciation
        gain_loss = self.disposal_value - book_value

        # Get or create asset journal
        journal, created = Journal.objects.get_or_create(
            company=self.company,
            type='asset',
            defaults={
                'name': _('Asset Journal'),
                'code': 'ASSET'
            }
        )

        # Create journal entry
        entry = JournalEntry.objects.create(
            name=f"Asset Disposal: {self.name}",
            reference=f"ASSET-DISP-{self.code}",
            date=self.disposal_date,
            journal=journal,
            fiscal_year=FiscalYear.objects.filter(
                company=self.company,
                start_date__lte=self.disposal_date,
                end_date__gte=self.disposal_date,
                state='open'
            ).first(),
            company=self.company,
            branch=self.branch,
            memo=f"Disposal of asset: {self.name}",
            created_by=user or self.created_by
        )

        # Create journal entry lines
        # Debit accumulated depreciation
        JournalEntryLine.objects.create(
            entry=entry,
            account=self.accumulated_depreciation_account,
            name=f"Asset Disposal: {self.name} - Accumulated Depreciation",
            debit=accumulated_depreciation,
            credit=0
        )

        # Debit cash/receivable for disposal value
        cash_account = Account.objects.filter(
            chart__company=self.company,
            type__category='asset',
            code__startswith='1010'  # Cash
        ).first()

        if not cash_account:
            raise ValidationError(_('No cash account found'))

        JournalEntryLine.objects.create(
            entry=entry,
            account=cash_account,
            name=f"Asset Disposal: {self.name} - Proceeds",
            debit=self.disposal_value,
            credit=0
        )

        # Credit asset account for original cost
        JournalEntryLine.objects.create(
            entry=entry,
            account=self.asset_account,
            name=f"Asset Disposal: {self.name} - Original Cost",
            debit=0,
            credit=self.purchase_value
        )

        # Debit/Credit gain/loss account
        if gain_loss != 0:
            # Get gain/loss account
            if gain_loss > 0:
                # Gain on disposal
                gl_account = Account.objects.filter(
                    chart__company=self.company,
                    type__category='revenue',
                    code__startswith='4'  # Revenue
                ).first()

                if not gl_account:
                    raise ValidationError(_('No revenue account found for gain on disposal'))

                JournalEntryLine.objects.create(
                    entry=entry,
                    account=gl_account,
                    name=f"Asset Disposal: {self.name} - Gain on Disposal",
                    debit=0,
                    credit=gain_loss
                )
            else:
                # Loss on disposal
                gl_account = Account.objects.filter(
                    chart__company=self.company,
                    type__category='expense',
                    code__startswith='5'  # Expense
                ).first()

                if not gl_account:
                    raise ValidationError(_('No expense account found for loss on disposal'))

                JournalEntryLine.objects.create(
                    entry=entry,
                    account=gl_account,
                    name=f"Asset Disposal: {self.name} - Loss on Disposal",
                    debit=abs(gain_loss),
                    credit=0
                )

        # Link journal entry to asset
        self.disposal_journal_entry = entry
        self.save()

        return entry

    def compute_depreciation_board(self):
        """Compute depreciation schedule for the asset"""
        # Calculate total number of periods
        periods = self.depreciation_periods

        # Calculate depreciation for each period
        result = []
        accumulated_depreciation = 0

        for period in range(1, periods + 1):
            # Calculate depreciation amount
            amount = self.depreciation_method.calculate_depreciation(
                self.purchase_value,
                self.salvage_value,
                periods,
                period,
                accumulated_depreciation
            )

            # Calculate depreciation date
            if self.depreciation_frequency == 'monthly':
                date = self.in_service_date + timezone.timedelta(days=30 * period)
            elif self.depreciation_frequency == 'quarterly':
                date = self.in_service_date + timezone.timedelta(days=90 * period)
            elif self.depreciation_frequency == 'semi_annual':
                date = self.in_service_date + timezone.timedelta(days=180 * period)
            else:  # annual
                date = self.in_service_date + timezone.timedelta(days=365 * period)

            # Add to result
            result.append({
                'period': period,
                'date': date,
                'amount': amount,
                'accumulated_depreciation': accumulated_depreciation + amount,
                'remaining_value': self.purchase_value - (accumulated_depreciation + amount)
            })

            accumulated_depreciation += amount

        return result


class Depreciation(models.Model):
    """Depreciation model for asset depreciation entries"""
    STATES = [
        ('draft', _('Draft')),
        ('posted', _('Posted')),
        ('cancelled', _('Cancelled')),
    ]

    asset = models.ForeignKey(Asset, on_delete=models.CASCADE, related_name='depreciations', verbose_name=_('Asset'))
    date = models.DateField(_('Date'))
    period = models.PositiveIntegerField(_('Period'))
    amount = models.DecimalField(_('Amount'), max_digits=18, decimal_places=2)

    # Accumulated values
    accumulated_depreciation = models.DecimalField(_('Accumulated Depreciation'), max_digits=18, decimal_places=2)
    remaining_value = models.DecimalField(_('Remaining Value'), max_digits=18, decimal_places=2)

    # Journal entry created for this depreciation
    journal_entry = models.ForeignKey(JournalEntry, on_delete=models.SET_NULL, null=True, blank=True,
                                     related_name='depreciations', verbose_name=_('Journal Entry'))

    state = models.CharField(_('State'), max_length=20, choices=STATES, default='draft')

    # Tracking fields
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='created_depreciations',
                                  verbose_name=_('Created By'))
    posted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True,
                                 related_name='posted_depreciations', verbose_name=_('Posted By'))

    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    posted_at = models.DateTimeField(_('Posted At'), null=True, blank=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    class Meta:
        verbose_name = _('Depreciation')
        verbose_name_plural = _('Depreciations')
        unique_together = ('asset', 'period')
        ordering = ['asset', 'period']

    def __str__(self):
        return f"{self.asset.name} - Period {self.period}"

    def create_journal_entry(self, user=None):
        """Create journal entry for this depreciation"""
        if self.journal_entry:
            raise ValidationError(_('Journal entry already exists for this depreciation'))

        # Get or create asset journal
        journal, _ = Journal.objects.get_or_create(
            company=self.asset.company,
            type='asset',
            defaults={
                'name': _('Asset Journal'),
                'code': 'ASSET'
            }
        )

        # Create journal entry
        entry = JournalEntry.objects.create(
            name=f"Asset Depreciation: {self.asset.name} - Period {self.period}",
            reference=f"DEPR-{self.asset.code}-{self.period}",
            date=self.date,
            journal=journal,
            fiscal_year=FiscalYear.objects.filter(
                company=self.asset.company,
                start_date__lte=self.date,
                end_date__gte=self.date,
                state='open'
            ).first(),
            company=self.asset.company,
            branch=self.asset.branch,
            memo=f"Depreciation of asset: {self.asset.name} for period {self.period}",
            created_by=user or self.created_by
        )

        # Create journal entry lines
        # Debit depreciation expense
        JournalEntryLine.objects.create(
            entry=entry,
            account=self.asset.depreciation_account,
            name=f"Depreciation Expense: {self.asset.name} - Period {self.period}",
            debit=self.amount,
            credit=0
        )

        # Credit accumulated depreciation
        JournalEntryLine.objects.create(
            entry=entry,
            account=self.asset.accumulated_depreciation_account,
            name=f"Accumulated Depreciation: {self.asset.name} - Period {self.period}",
            debit=0,
            credit=self.amount
        )

        # Link journal entry to depreciation
        self.journal_entry = entry
        self.save()

        return entry

    def post(self, user=None):
        """Post the depreciation"""
        if self.state != 'draft':
            raise ValidationError(_('Only draft depreciations can be posted'))

        # Create journal entry if not exists
        if not self.journal_entry:
            self.create_journal_entry(user)

        # Post journal entry
        self.journal_entry.post(user)

        # Update depreciation state
        self.state = 'posted'
        self.posted_by = user
        self.posted_at = timezone.now()
        self.save()

        # Check if asset is fully depreciated
        if self.remaining_value <= self.asset.salvage_value:
            self.asset.state = 'fully_depreciated'
            self.asset.save()

    def cancel(self):
        """Cancel the depreciation"""
        if self.state != 'posted':
            raise ValidationError(_('Only posted depreciations can be cancelled'))

        # Cancel journal entry
        if self.journal_entry:
            self.journal_entry.cancel()

        # Update depreciation state
        self.state = 'cancelled'
        self.save()

        # Update asset state if needed
        if self.asset.state == 'fully_depreciated':
            # Check if there are other posted depreciations
            if self.asset.depreciations.filter(state='posted').count() > 0:
                self.asset.state = 'active'
                self.asset.save()
