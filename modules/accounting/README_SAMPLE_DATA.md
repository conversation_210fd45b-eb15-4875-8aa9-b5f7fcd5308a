# البيانات التجريبية لنظام المحاسبة

## نظرة عامة

تم إنشاء مجموعة شاملة من البيانات التجريبية لنظام المحاسبة لتسهيل الاختبار والتطوير. تشمل هذه البيانات جميع المكونات الأساسية للنظام المحاسبي.

## كيفية إنشاء البيانات التجريبية

```bash
python manage.py create_accounting_sample_data --company-id=1
```

## البيانات المُنشأة

### 1. دليل الحسابات
- **دليل الحسابات الرئيسي**: يحتوي على جميع الحسابات المطلوبة
- **أنواع الحسابات**: 22 نوع حساب مختلف (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات)
- **الحسابات**: 57 حساب موزع على 5 مستويات هرمية

### 2. الحسابات الرئيسية (المستوى 1)
- النقدية والبنوك (11)
- العملاء والمدينون (12)
- المخزون (13)
- الأصول الثابتة (14)
- الموردون والدائنون (21)
- رأس المال (30)
- الإيرادات (40)
- المصروفات التشغيلية (51)

### 3. حسابات المعاملات (المستوى 5)
#### النقدية والبنوك:
- 1101: الخزينة الرئيسية
- 1102: البنك الأهلي المصري
- 1103: بنك مصر
- 1104: البنك التجاري الدولي

#### العملاء:
- 1201: العملاء
- 1202: أوراق القبض
- 1203: مدينون متنوعون

#### الموردون:
- 2101: الموردون
- 2102: أوراق الدفع
- 2103: دائنون متنوعون

### 4. الحسابات البنكية (5 حسابات)
- حساب جاري - البنك الأهلي المصري
- حساب توفير - بنك مصر
- حساب بالدولار - البنك التجاري الدولي
- حساب استثماري - بنك القاهرة

### 5. الخزائن (4 خزائن)
- الخزينة الرئيسية
- خزينة المبيعات
- خزينة المشتريات

### 6. دفاتر الشيكات (4 دفاتر)
- دفتر شيكات البنك الأهلي (1000001-1000050)
- دفتر شيكات بنك مصر (2000001-2000050)
- دفتر شيكات البنك التجاري (3000001-3000050)

### 7. الشيكات (18 شيك)
#### الشيكات الصادرة (11 شيك):
- شيكات لشركة الكهرباء
- شيكات لشركة المياه
- شيكات لشركة النور للتجارة

#### الشيكات الواردة (7 شيكات):
- شيكات من أحمد محمد علي
- شيكات من شركة الأمل التجارية

### 8. دفاتر اليومية (5 دفاتر)
- دفتر اليومية العام (GJ)
- دفتر المبيعات (SJ)
- دفتر المشتريات (PJ)
- دفتر النقدية (CJ)
- دفتر البنك (BJ)

### 9. قيود اليومية (8 قيود)
- رأس المال الافتتاحي (1,500,000 جنيه)
- مبيعات نقدية (50,000 جنيه)
- مبيعات آجلة للعميل أحمد محمد (75,000 جنيه)
- مشتريات من المورد شركة النور (30,000 جنيه)
- دفع مرتبات الموظفين (80,000 جنيه)
- دفع إيجار المكتب (25,000 جنيه)
- تحصيل من العميل أحمد محمد (40,000 جنيه)

### 10. السندات (9 سندات)
#### سندات القبض (4 سندات):
- RV001: تحصيل نقدي من أحمد محمد علي (75,000 جنيه)
- RV002: تحويل بنكي من شركة الأمل (120,000 جنيه)
- RV003: شيك من محمد أحمد سالم (85,000 جنيه)

#### سندات الدفع (5 سندات):
- PV001: دفع لشركة النور (30,000 جنيه)
- PV002: دفع فاتورة كهرباء (25,000 جنيه)
- PV003: دفع فاتورة مياه (15,000 جنيه)
- PV004: دفع مرتبات (80,000 جنيه)

### 11. الأصول الثابتة (6 أصول)
- مبنى المكتب الرئيسي (2,000,000 جنيه)
- آلة طباعة ديجيتال (150,000 جنيه)
- مكاتب إدارية (50,000 جنيه)
- سيارة تويوتا كامري (400,000 جنيه)
- أجهزة كمبيوتر مكتبية (80,000 جنيه)

### 12. فئات الأصول (6 فئات)
- مباني وإنشاءات (معدل إهلاك 5% - 20 سنة)
- آلات ومعدات (معدل إهلاك 10% - 10 سنوات)
- أثاث وتجهيزات (معدل إهلاك 20% - 5 سنوات)
- سيارات ومركبات (معدل إهلاك 25% - 4 سنوات)
- أجهزة كمبيوتر (معدل إهلاك 33.33% - 3 سنوات)

### 13. طرق الإهلاك (4 طرق)
- القسط الثابت
- الرصيد المتناقص
- مجموع سنوات الاستخدام

### 14. طرق الدفع (5 طرق)
- نقدي
- شيك
- تحويل بنكي
- بطاقة ائتمان

### 15. حسابات العملاء والموردين التفصيلية
#### العملاء:
- أحمد محمد علي (تاجر تجزئة)
- شركة الأمل التجارية
- محمد أحمد سالم (تاجر جملة)
- شركة النجاح للاستيراد
- فاطمة حسن محمود (تاجرة تجزئة)

#### الموردون:
- شركة النور للتجارة (بضائع عامة)
- شركة الكهرباء (خدمات كهرباء)
- شركة المياه (خدمات مياه)
- شركة الاتصالات (خدمات اتصالات)
- شركة التوريدات الحديثة (مواد خام)

## ملاحظات مهمة

1. **التوازن المحاسبي**: جميع القيود متوازنة (المدين = الدائن)
2. **التسلسل الهرمي**: الحسابات منظمة في 5 مستويات
3. **المعاملات اليدوية**: فقط حسابات المستوى 5 تسمح بالمعاملات اليدوية
4. **التواريخ**: البيانات موزعة على فترات زمنية مختلفة لمحاكاة الواقع
5. **العملة**: جميع المبالغ بالجنيه المصري

## الاستخدام

يمكن استخدام هذه البيانات لـ:
- اختبار وظائف النظام المحاسبي
- التدريب على استخدام النظام
- عرض إمكانيات النظام للعملاء
- تطوير تقارير جديدة
- اختبار الأداء

## إعادة إنشاء البيانات

لإعادة إنشاء البيانات التجريبية، قم بحذف البيانات الموجودة أولاً ثم تشغيل الأمر مرة أخرى:

```bash
# حذف البيانات الموجودة (اختياري)
python manage.py flush

# إنشاء البيانات التجريبية
python manage.py create_accounting_sample_data --company-id=1
```
