"""
اختبارات وظيفة اختيار الشيك في السندات
"""

from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from companies.models import Company, Branch
from modules.accounting.models import (
    CheckBook, Check, PaymentMethod, Voucher, BankAccount, Bank,
    Account, AccountChart, AccountType, FiscalYear
)
from decimal import Decimal
from datetime import date


class CheckSelectionTestCase(TestCase):
    """اختبار وظيفة اختيار الشيك في السندات"""

    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء مستخدم للاختبار
        User = get_user_model()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123',
            email='<EMAIL>'
        )

        # إنشاء شركة وفرع
        self.company = Company.objects.create(
            name='شركة اختبار',
            code='TEST001',
            email='<EMAIL>'
        )

        self.branch = Branch.objects.create(
            name='الفرع الرئيسي',
            code='MAIN',
            company=self.company
        )

        # إنشاء سنة مالية
        self.fiscal_year = FiscalYear.objects.create(
            name='السنة المالية 2024',
            code='FY2024',
            start_date=date(2024, 1, 1),
            end_date=date(2024, 12, 31),
            company=self.company,
            is_active=True
        )

        # إنشاء بنك وحساب بنكي
        self.bank = Bank.objects.create(
            name='البنك الأهلي المصري',
            code='NBE',
            swift_code='NBEGEGCX'
        )

        # إنشاء دليل الحسابات ونوع الحساب
        self.chart = AccountChart.objects.create(
            name='دليل الحسابات الرئيسي',
            code='MAIN',
            company=self.company
        )

        self.account_type = AccountType.objects.create(
            name='النقدية والبنوك',
            code='1100',
            category='asset',
            company=self.company
        )

        # إنشاء حساب جذر أولاً
        self.root_account = Account.objects.create(
            chart=self.chart,
            code='1000',
            name='الأصول',
            type=self.account_type,
            level=1,
            is_active=True,
            allow_manual_transactions=False
        )

        # إنشاء حسابات متدرجة للوصول للمستوى 5
        self.level2_account = Account.objects.create(
            chart=self.chart,
            code='1100',
            name='النقدية والبنوك',
            type=self.account_type,
            parent=self.root_account,
            level=2,
            is_active=True,
            allow_manual_transactions=False
        )

        self.level3_account = Account.objects.create(
            chart=self.chart,
            code='1110',
            name='البنوك',
            type=self.account_type,
            parent=self.level2_account,
            level=3,
            is_active=True,
            allow_manual_transactions=False
        )

        self.level4_account = Account.objects.create(
            chart=self.chart,
            code='1111',
            name='البنك الأهلي',
            type=self.account_type,
            parent=self.level3_account,
            level=4,
            is_active=True,
            allow_manual_transactions=False
        )

        self.bank_account_record = Account.objects.create(
            chart=self.chart,
            code='1111001',
            name='البنك الأهلي المصري - حساب جاري',
            type=self.account_type,
            parent=self.level4_account,
            level=5,
            is_active=True,
            allow_manual_transactions=True
        )

        self.bank_account = BankAccount.objects.create(
            name='حساب جاري - البنك الأهلي',
            number='*********',
            bank=self.bank,
            account=self.bank_account_record,
            company=self.company,
            branch=self.branch,
            currency='EGP',
            is_active=True
        )

        # إنشاء دفتر شيكات
        self.checkbook = CheckBook.objects.create(
            name='دفتر شيكات البنك الأهلي - 001',
            code='NBE_CHK_001',
            bank_account=self.bank_account,
            start_number='1000001',
            end_number='1000050',
            next_number='1000001',
            is_active=True
        )

        # إنشاء طريقة دفع بالشيك
        self.check_payment_method = PaymentMethod.objects.create(
            name='شيك',
            code='CHECK',
            payment_type='check',
            account=self.bank_account_record,
            bank_account=self.bank_account,
            company=self.company,
            is_active=True
        )

        # إنشاء خزينة للطريقة النقدية
        from modules.accounting.models import CashRegister

        self.cash_register = CashRegister.objects.create(
            name='الخزينة الرئيسية',
            code='MAIN_CASH',
            company=self.company,
            branch=self.branch,
            account=self.bank_account_record,
            is_active=True
        )

        # إنشاء طريقة دفع نقدي
        self.cash_payment_method = PaymentMethod.objects.create(
            name='نقدي',
            code='CASH',
            payment_type='cash',
            account=self.bank_account_record,
            cash_register=self.cash_register,
            company=self.company,
            is_active=True
        )

        # إنشاء شيكات فارغة للاختبار
        self.blank_check1 = Check.objects.create(
            number='1000001',
            amount=0,
            date=date.today(),
            due_date=date.today(),
            check_type='outgoing',
            state='draft',
            check_book=self.checkbook,
            bank_account=self.bank_account,
            company=self.company,
            branch=self.branch,
            partner_name='',
            partner_type='supplier',
            memo='شيك فارغ للاختبار'
        )

        self.blank_check2 = Check.objects.create(
            number='1000002',
            amount=0,
            date=date.today(),
            due_date=date.today(),
            check_type='outgoing',
            state='draft',
            check_book=self.checkbook,
            bank_account=self.bank_account,
            company=self.company,
            branch=self.branch,
            partner_name='',
            partner_type='supplier',
            memo='شيك فارغ للاختبار'
        )

        # تحديث رقم الشيك التالي في دفتر الشيكات
        self.checkbook.next_number = '1000003'
        self.checkbook.save()

        # إنشاء عميل للاختبار
        self.client = Client()
        self.client.login(username='testuser', password='testpass123')

        # إعداد session للشركة والفرع
        session = self.client.session
        session['active_company_id'] = self.company.id
        session['active_branch_id'] = self.branch.id
        session.save()

    def test_payment_voucher_form_displays_check_options(self):
        """اختبار عرض خيارات الشيك في نموذج سند الصرف"""
        url = reverse('accounting:payment_voucher_create')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'check_option')
        self.assertContains(response, 'check_book')
        self.assertContains(response, 'existing_check')

    def test_create_payment_voucher_with_new_check(self):
        """اختبار إنشاء سند صرف مع شيك جديد"""
        url = reverse('accounting:payment_voucher_create')

        data = {
            'number': 'PV001',
            'date': date.today(),
            'amount': Decimal('5000.00'),
            'branch': self.branch.id,
            'partner_name': 'مورد تجريبي',
            'partner_type': 'supplier',
            'payment_method': self.check_payment_method.id,
            'check_option': 'new',
            'check_book': self.checkbook.id,
            'memo': 'سند صرف تجريبي'
        }

        response = self.client.post(url, data)

        # التحقق من إنشاء السند بنجاح
        self.assertEqual(response.status_code, 302)  # Redirect after successful creation

        # التحقق من إنشاء السند
        voucher = Voucher.objects.filter(number='PV001').first()
        self.assertIsNotNone(voucher)
        self.assertEqual(voucher.amount, Decimal('5000.00'))

        # التحقق من إنشاء الشيك
        self.assertIsNotNone(voucher.payment_check)
        self.assertEqual(voucher.payment_check.amount, Decimal('5000.00'))
        self.assertEqual(voucher.payment_check.partner_name, 'مورد تجريبي')

    def test_create_payment_voucher_with_existing_check(self):
        """اختبار إنشاء سند صرف مع شيك موجود"""
        url = reverse('accounting:payment_voucher_create')

        data = {
            'number': 'PV002',
            'date': date.today(),
            'amount': Decimal('3000.00'),
            'branch': self.branch.id,
            'partner_name': 'مورد آخر',
            'partner_type': 'supplier',
            'payment_method': self.check_payment_method.id,
            'check_option': 'existing',
            'existing_check': self.blank_check1.id,
            'memo': 'سند صرف بشيك موجود'
        }

        response = self.client.post(url, data)

        # التحقق من إنشاء السند بنجاح
        self.assertEqual(response.status_code, 302)

        # التحقق من إنشاء السند
        voucher = Voucher.objects.filter(number='PV002').first()
        self.assertIsNotNone(voucher)
        self.assertEqual(voucher.amount, Decimal('3000.00'))

        # التحقق من استخدام الشيك الموجود
        self.assertEqual(voucher.payment_check.id, self.blank_check1.id)

        # التحقق من تحديث بيانات الشيك
        updated_check = Check.objects.get(id=self.blank_check1.id)
        self.assertEqual(updated_check.amount, Decimal('3000.00'))
        self.assertEqual(updated_check.partner_name, 'مورد آخر')
        self.assertEqual(updated_check.state, 'pending')

    def test_form_validation_check_required_fields(self):
        """اختبار التحقق من الحقول المطلوبة للشيك"""
        url = reverse('accounting:payment_voucher_create')

        # اختبار عدم تحديد دفتر شيكات عند اختيار شيك جديد
        data = {
            'number': 'PV003',
            'date': date.today(),
            'amount': Decimal('1000.00'),
            'branch': self.branch.id,
            'partner_name': 'مورد',
            'partner_type': 'supplier',
            'payment_method': self.check_payment_method.id,
            'check_option': 'new',
            # check_book غير محدد
            'memo': 'اختبار التحقق'
        }

        response = self.client.post(url, data)

        # يجب أن يفشل التحقق
        self.assertEqual(response.status_code, 200)  # يعود للنموذج مع أخطاء

        # اختبار عدم تحديد شيك موجود عند اختيار شيك موجود
        data['check_option'] = 'existing'
        # existing_check غير محدد

        response = self.client.post(url, data)
        self.assertEqual(response.status_code, 200)  # يعود للنموذج مع أخطاء
