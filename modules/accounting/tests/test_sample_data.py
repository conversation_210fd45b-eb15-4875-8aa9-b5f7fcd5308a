"""
اختبارات البيانات التجريبية لنظام المحاسبة
"""

from django.test import TestCase
from django.core.management import call_command
from companies.models import Company
from branches.models import Branch
from modules.accounting.models import (
    AccountChart, AccountType, Account, BankAccount, CashRegister,
    CheckBook, Check, Journal, JournalEntry, JournalEntryLine,
    Voucher, Asset, AssetCategory, DepreciationMethod, PaymentMethod,
    FiscalYear
)
from decimal import Decimal


class SampleDataTestCase(TestCase):
    """اختبار البيانات التجريبية"""
    
    def setUp(self):
        """إعداد البيانات للاختبار"""
        # إنشاء شركة للاختبار
        self.company = Company.objects.create(
            name='شركة اختبار',
            code='TEST001',
            email='<EMAIL>'
        )
        
        # إنشاء فرع للاختبار
        self.branch = Branch.objects.create(
            name='الفرع الرئيسي',
            code='MAIN',
            company=self.company
        )
        
        # إنشاء البيانات التجريبية
        call_command('create_accounting_sample_data', company_id=self.company.id)
    
    def test_account_chart_creation(self):
        """اختبار إنشاء دليل الحسابات"""
        charts = AccountChart.objects.filter(company=self.company)
        self.assertGreaterEqual(charts.count(), 1)
        
        main_chart = charts.first()
        self.assertIsNotNone(main_chart)
        self.assertEqual(main_chart.company, self.company)
    
    def test_account_types_creation(self):
        """اختبار إنشاء أنواع الحسابات"""
        account_types = AccountType.objects.filter(company=self.company)
        self.assertGreaterEqual(account_types.count(), 20)
        
        # التحقق من وجود الأنواع الأساسية
        cash_type = account_types.filter(code='1100').first()
        self.assertIsNotNone(cash_type)
        self.assertEqual(cash_type.category, 'asset')
    
    def test_accounts_hierarchy(self):
        """اختبار التسلسل الهرمي للحسابات"""
        chart = AccountChart.objects.filter(company=self.company).first()
        accounts = Account.objects.filter(chart=chart)
        
        # التحقق من وجود حسابات المستوى 1
        level_1_accounts = accounts.filter(level=1)
        self.assertGreaterEqual(level_1_accounts.count(), 5)
        
        # التحقق من وجود حسابات المستوى 5
        level_5_accounts = accounts.filter(level=5)
        self.assertGreaterEqual(level_5_accounts.count(), 15)
        
        # التحقق من أن حسابات المستوى 5 فقط تسمح بالمعاملات اليدوية
        for account in level_5_accounts:
            self.assertTrue(account.allow_manual_transactions)
        
        for account in level_1_accounts:
            self.assertFalse(account.allow_manual_transactions)
    
    def test_bank_accounts_creation(self):
        """اختبار إنشاء الحسابات البنكية"""
        bank_accounts = BankAccount.objects.filter(company=self.company)
        self.assertGreaterEqual(bank_accounts.count(), 3)
        
        # التحقق من وجود حساب البنك الأهلي
        nbe_account = bank_accounts.filter(bank__name__contains='الأهلي').first()
        self.assertIsNotNone(nbe_account)
    
    def test_cash_registers_creation(self):
        """اختبار إنشاء الخزائن"""
        cash_registers = CashRegister.objects.filter(company=self.company)
        self.assertGreaterEqual(cash_registers.count(), 3)
        
        # التحقق من وجود الخزينة الرئيسية
        main_register = cash_registers.filter(code='MAIN_CASH').first()
        self.assertIsNotNone(main_register)
    
    def test_checkbooks_creation(self):
        """اختبار إنشاء دفاتر الشيكات"""
        checkbooks = CheckBook.objects.filter(bank_account__company=self.company)
        self.assertGreaterEqual(checkbooks.count(), 3)
        
        # التحقق من تسلسل أرقام الشيكات
        for checkbook in checkbooks:
            self.assertIsNotNone(checkbook.start_number)
            self.assertIsNotNone(checkbook.end_number)
            self.assertIsNotNone(checkbook.next_number)
            self.assertGreaterEqual(int(checkbook.next_number), int(checkbook.start_number))
            self.assertLessEqual(int(checkbook.next_number), int(checkbook.end_number))
    
    def test_checks_creation(self):
        """اختبار إنشاء الشيكات"""
        checks = Check.objects.filter(company=self.company)
        self.assertGreaterEqual(checks.count(), 5)
        
        # التحقق من وجود شيكات صادرة وواردة
        outgoing_checks = checks.filter(check_type='outgoing')
        incoming_checks = checks.filter(check_type='incoming')
        
        self.assertGreater(outgoing_checks.count(), 0)
        self.assertGreater(incoming_checks.count(), 0)
    
    def test_journals_creation(self):
        """اختبار إنشاء دفاتر اليومية"""
        journals = Journal.objects.filter(company=self.company)
        self.assertGreaterEqual(journals.count(), 5)
        
        # التحقق من وجود دفتر اليومية العام
        general_journal = journals.filter(code='GJ').first()
        self.assertIsNotNone(general_journal)
        self.assertEqual(general_journal.type, 'general')
    
    def test_journal_entries_balance(self):
        """اختبار توازن قيود اليومية"""
        entries = JournalEntry.objects.filter(company=self.company)
        self.assertGreaterEqual(entries.count(), 5)
        
        for entry in entries:
            lines = entry.lines.all()
            total_debit = sum(line.debit for line in lines)
            total_credit = sum(line.credit for line in lines)
            
            # التحقق من التوازن المحاسبي
            self.assertEqual(total_debit, total_credit, 
                           f"Entry {entry.name} is not balanced: Debit={total_debit}, Credit={total_credit}")
    
    def test_vouchers_creation(self):
        """اختبار إنشاء السندات"""
        vouchers = Voucher.objects.filter(company=self.company)
        self.assertGreaterEqual(vouchers.count(), 5)
        
        # التحقق من وجود سندات قبض ودفع
        receipt_vouchers = vouchers.filter(voucher_type='receipt')
        payment_vouchers = vouchers.filter(voucher_type='payment')
        
        self.assertGreater(receipt_vouchers.count(), 0)
        self.assertGreater(payment_vouchers.count(), 0)
    
    def test_assets_creation(self):
        """اختبار إنشاء الأصول الثابتة"""
        assets = Asset.objects.filter(company=self.company)
        self.assertGreaterEqual(assets.count(), 5)
        
        # التحقق من صحة بيانات الأصول
        for asset in assets:
            self.assertGreater(asset.purchase_value, 0)
            self.assertGreaterEqual(asset.salvage_value, 0)
            self.assertLess(asset.salvage_value, asset.purchase_value)
            self.assertGreater(asset.useful_life_years, 0)
    
    def test_asset_categories_creation(self):
        """اختبار إنشاء فئات الأصول"""
        categories = AssetCategory.objects.filter(company=self.company)
        self.assertGreaterEqual(categories.count(), 5)
        
        # التحقق من معدلات الإهلاك
        for category in categories:
            self.assertGreater(category.depreciation_rate, 0)
            self.assertLessEqual(category.depreciation_rate, 100)
            self.assertGreater(category.useful_life_years, 0)
    
    def test_depreciation_methods_creation(self):
        """اختبار إنشاء طرق الإهلاك"""
        methods = DepreciationMethod.objects.filter(company=self.company)
        self.assertGreaterEqual(methods.count(), 3)
        
        # التحقق من وجود طريقة القسط الثابت
        straight_line = methods.filter(code='STRAIGHT_LINE').first()
        self.assertIsNotNone(straight_line)
        self.assertEqual(straight_line.method_type, 'straight_line')
    
    def test_payment_methods_creation(self):
        """اختبار إنشاء طرق الدفع"""
        payment_methods = PaymentMethod.objects.filter(company=self.company)
        self.assertGreaterEqual(payment_methods.count(), 4)
        
        # التحقق من وجود الطرق الأساسية
        cash_method = payment_methods.filter(code='CASH').first()
        check_method = payment_methods.filter(code='CHECK').first()
        
        self.assertIsNotNone(cash_method)
        self.assertIsNotNone(check_method)
    
    def test_fiscal_year_creation(self):
        """اختبار إنشاء السنة المالية"""
        fiscal_years = FiscalYear.objects.filter(company=self.company)
        self.assertGreaterEqual(fiscal_years.count(), 1)
        
        # التحقق من وجود سنة مالية نشطة
        active_year = fiscal_years.filter(is_active=True).first()
        self.assertIsNotNone(active_year)
    
    def test_customer_supplier_accounts(self):
        """اختبار حسابات العملاء والموردين التفصيلية"""
        chart = AccountChart.objects.filter(company=self.company).first()
        
        # حسابات العملاء التفصيلية
        customer_accounts = Account.objects.filter(
            chart=chart,
            code__startswith='1201'
        )
        self.assertGreaterEqual(customer_accounts.count(), 3)
        
        # حسابات الموردين التفصيلية
        supplier_accounts = Account.objects.filter(
            chart=chart,
            code__startswith='2101'
        )
        self.assertGreaterEqual(supplier_accounts.count(), 3)
    
    def test_data_integrity(self):
        """اختبار تكامل البيانات"""
        # التحقق من أن جميع الحسابات لها أنواع صحيحة
        chart = AccountChart.objects.filter(company=self.company).first()
        accounts = Account.objects.filter(chart=chart)
        
        for account in accounts:
            self.assertIsNotNone(account.type)
            self.assertEqual(account.type.company, self.company)
        
        # التحقق من أن جميع القيود لها سنة مالية صحيحة
        entries = JournalEntry.objects.filter(company=self.company)
        for entry in entries:
            self.assertIsNotNone(entry.fiscal_year)
            self.assertEqual(entry.fiscal_year.company, self.company)
        
        # التحقق من أن جميع الشيكات الصادرة لها دفاتر شيكات
        outgoing_checks = Check.objects.filter(
            company=self.company,
            check_type='outgoing'
        )
        for check in outgoing_checks:
            self.assertIsNotNone(check.check_book)
            self.assertEqual(check.check_book.bank_account.company, self.company)


class SampleDataPerformanceTestCase(TestCase):
    """اختبار أداء البيانات التجريبية"""
    
    def test_data_creation_performance(self):
        """اختبار أداء إنشاء البيانات"""
        import time
        
        # إنشاء شركة للاختبار
        company = Company.objects.create(
            name='شركة اختبار الأداء',
            code='PERF001',
            email='<EMAIL>'
        )
        
        # قياس وقت إنشاء البيانات
        start_time = time.time()
        call_command('create_accounting_sample_data', company_id=company.id)
        end_time = time.time()
        
        creation_time = end_time - start_time
        
        # يجب أن يكتمل إنشاء البيانات في أقل من 30 ثانية
        self.assertLess(creation_time, 30, 
                       f"Data creation took {creation_time:.2f} seconds, which is too long")
        
        print(f"Sample data creation completed in {creation_time:.2f} seconds")
