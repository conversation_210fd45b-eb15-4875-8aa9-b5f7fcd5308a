# وظيفة اختيار الشيك في سندات الصرف

## نظرة عامة

تم تطوير وظيفة جديدة تتيح للمستخدم اختيار الشيك المحدد من دفتر الشيكات عند إنشاء سند صرف بطريقة دفع "شيك". هذه الوظيفة تحسن من دقة إدارة الشيكات وتتيح استخدام شيكات محددة مسبقاً.

## الميزات الجديدة

### 1. خيارات الشيك
عند اختيار طريقة دفع "شيك" في سند الصرف، يظهر للمستخدم خيارين:

- **إنشاء شيك جديد**: إنشاء شيك جديد من دفتر شيكات محدد
- **استخدام شيك موجود**: اختيار شيك فارغ موجود مسبقاً

### 2. إنشاء شيك جديد
- اختيار دفتر الشيكات المطلوب
- يتم إنشاء شيك جديد تلقائياً برقم الشيك التالي
- يتم ربط الشيك بسند الصرف

### 3. استخدام شيك موجود
- عرض قائمة بالشيكات الفارغة المتاحة
- اختيار الشيك المطلوب من القائمة
- تحديث بيانات الشيك بمعلومات السند

## التحديثات التقنية

### 1. النماذج (Forms)
تم تحديث `VoucherForm` في `modules/accounting/forms.py`:

```python
# حقول جديدة
check_option = forms.ChoiceField(...)  # خيار الشيك
check_book = forms.ModelChoiceField(...)  # دفتر الشيكات
existing_check = forms.ModelChoiceField(...)  # الشيك الموجود
```

### 2. العروض (Views)
تم تحديث `payment_voucher_create` في `modules/accounting/views.py`:

- معالجة خيارات الشيك الجديدة
- إنشاء شيك جديد أو استخدام شيك موجود
- التحقق من صحة البيانات

### 3. القوالب (Templates)
تم تحديث `payment_voucher_form.html`:

- إضافة واجهة اختيار خيارات الشيك
- JavaScript لإدارة عرض الخيارات
- تحسين التصميم والتفاعل

### 4. أوامر الإدارة
تم إنشاء أمر جديد `create_blank_checks`:

```bash
python manage.py create_blank_checks --company-id=1 --count=5
```

## كيفية الاستخدام

### 1. إنشاء شيكات فارغة للاختبار
```bash
python manage.py create_blank_checks --company-id=1 --count=10
```

### 2. إنشاء سند صرف بشيك جديد
1. انتقل إلى صفحة إنشاء سند صرف
2. اختر طريقة دفع "شيك"
3. اختر "إنشاء شيك جديد"
4. حدد دفتر الشيكات المطلوب
5. أكمل بيانات السند واحفظ

### 3. إنشاء سند صرف بشيك موجود
1. انتقل إلى صفحة إنشاء سند صرف
2. اختر طريقة دفع "شيك"
3. اختر "استخدام شيك موجود"
4. حدد الشيك من القائمة
5. أكمل بيانات السند واحفظ

## الاختبارات

تم إنشاء مجموعة اختبارات شاملة في `modules/accounting/tests/test_check_selection.py`:

- اختبار عرض خيارات الشيك
- اختبار إنشاء سند بشيك جديد
- اختبار إنشاء سند بشيك موجود
- اختبار التحقق من الحقول المطلوبة

## الملفات المحدثة

### الملفات الأساسية
- `modules/accounting/forms.py` - تحديث نموذج السند
- `modules/accounting/views.py` - تحديث منطق المعالجة
- `templates/modules/accounting/payment_voucher_form.html` - تحديث الواجهة

### ملفات جديدة
- `modules/accounting/management/commands/create_blank_checks.py` - أمر إنشاء شيكات فارغة
- `modules/accounting/tests/test_check_selection.py` - اختبارات الوظيفة
- `modules/accounting/docs/CHECK_SELECTION_FEATURE.md` - هذا الملف

## المتطلبات

- Django 4.2+
- Python 3.8+
- قاعدة بيانات مع بيانات الشركة والفروع
- دفاتر شيكات نشطة

## الملاحظات

1. **الأمان**: يتم التحقق من صحة البيانات في النموذج والعرض
2. **الأداء**: يتم تحميل الشيكات المتاحة فقط للشركة النشطة
3. **سهولة الاستخدام**: واجهة بديهية مع إرشادات واضحة
4. **التوافق**: متوافق مع النظام الحالي دون كسر الوظائف الموجودة

## المشاكل المعروفة

1. قد تحتاج لإعادة تحميل الصفحة لتحديث قائمة الشيكات المتاحة
2. لا يتم التحقق من تداخل استخدام الشيكات في الوقت الفعلي

## التطوير المستقبلي

1. إضافة AJAX لتحديث قوائم الشيكات ديناميكياً
2. إضافة تنبيهات عند نفاد الشيكات في دفتر معين
3. إضافة تقارير استخدام الشيكات
4. دعم طباعة الشيكات مباشرة من النظام

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
