# دليل استخدام نظام المحاسبة

## البدء السريع

### 1. إنشاء البيانات التجريبية
```bash
python manage.py create_accounting_sample_data --company-id=1
```

### 2. تشغيل الخادم
```bash
python manage.py runserver
```

### 3. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost:8000`

## الوظائف الأساسية

### إدارة الحسابات

#### عرض دليل الحسابات
```python
from modules.accounting.models import Account, AccountChart

# عرض جميع الحسابات
company_id = 1
chart = AccountChart.objects.filter(company_id=company_id).first()
accounts = Account.objects.filter(chart=chart)

for account in accounts:
    print(f"{account.code} - {account.name} (المستوى {account.level})")
```

#### إنشاء حساب جديد
```python
from modules.accounting.models import Account, AccountType, AccountChart

chart = AccountChart.objects.filter(company_id=1).first()
account_type = AccountType.objects.filter(company_id=1, code='1100').first()

new_account = Account.objects.create(
    chart=chart,
    code='1105',
    name='بنك جديد',
    type=account_type,
    level=5,
    is_active=True,
    allow_manual_transactions=True,
    reconcilable=True
)
```

### إدارة قيود اليومية

#### إنشاء قيد يومية جديد
```python
from modules.accounting.models import JournalEntry, JournalEntryLine, Journal, FiscalYear
from companies.models import Company
from branches.models import Branch
from datetime import date

company = Company.objects.get(id=1)
branch = Branch.objects.filter(company=company).first()
fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()
journal = Journal.objects.filter(company=company, code='GJ').first()

# إنشاء القيد
entry = JournalEntry.objects.create(
    name='قيد تجريبي',
    date=date.today(),
    journal=journal,
    fiscal_year=fiscal_year,
    company=company,
    branch=branch,
    memo='قيد تجريبي للاختبار'
)

# إضافة سطور القيد
cash_account = Account.objects.filter(chart__company=company, code='1101').first()
sales_account = Account.objects.filter(chart__company=company, code='4001').first()

# سطر المدين
JournalEntryLine.objects.create(
    entry=entry,
    account=cash_account,
    name='مبيعات نقدية',
    debit=1000,
    credit=0
)

# سطر الدائن
JournalEntryLine.objects.create(
    entry=entry,
    account=sales_account,
    name='إيرادات مبيعات',
    debit=0,
    credit=1000
)

# ترحيل القيد
entry.post()
```

### إدارة الشيكات

#### إنشاء شيك صادر
```python
from modules.accounting.models import Check, CheckBook

checkbook = CheckBook.objects.filter(bank_account__company_id=1).first()
check_number = checkbook.get_next_check_number()

check = Check.objects.create(
    number=check_number,
    amount=5000,
    date=date.today(),
    due_date=date.today(),
    check_type='outgoing',
    state='pending',
    check_book=checkbook,
    bank_account=checkbook.bank_account,
    company_id=1,
    branch=Branch.objects.filter(company_id=1).first(),
    partner_name='مورد جديد',
    partner_type='supplier',
    memo='دفع فاتورة'
)
```

#### إنشاء شيك وارد
```python
bank_account = BankAccount.objects.filter(company_id=1).first()

incoming_check = Check.objects.create(
    number='7777777',
    amount=10000,
    date=date.today(),
    due_date=date.today(),
    check_type='incoming',
    state='received',
    bank_name='بنك خارجي',
    account_number='**********',
    bank_account=bank_account,
    company_id=1,
    branch=Branch.objects.filter(company_id=1).first(),
    partner_name='عميل جديد',
    partner_type='customer',
    memo='دفعة من العميل'
)
```

### إدارة السندات

#### إنشاء سند قبض
```python
from modules.accounting.models import Voucher, PaymentMethod

payment_method = PaymentMethod.objects.filter(company_id=1, code='CASH').first()
fiscal_year = FiscalYear.objects.filter(company_id=1, is_active=True).first()

receipt_voucher = Voucher.objects.create(
    number='RV999',
    date=date.today(),
    voucher_type='receipt',
    amount=15000,
    company_id=1,
    fiscal_year=fiscal_year,
    branch=Branch.objects.filter(company_id=1).first(),
    partner_name='عميل جديد',
    partner_type='customer',
    payment_method=payment_method,
    memo='تحصيل من العميل',
    state='posted'
)
```

#### إنشاء سند دفع
```python
payment_voucher = Voucher.objects.create(
    number='PV999',
    date=date.today(),
    voucher_type='payment',
    amount=8000,
    company_id=1,
    fiscal_year=fiscal_year,
    branch=Branch.objects.filter(company_id=1).first(),
    partner_name='مورد جديد',
    partner_type='supplier',
    payment_method=payment_method,
    memo='دفع للمورد',
    state='posted'
)
```

### إدارة الأصول الثابتة

#### إنشاء أصل جديد
```python
from modules.accounting.models import Asset, AssetCategory, DepreciationMethod

category = AssetCategory.objects.filter(company_id=1, code='COMPUTERS').first()
depreciation_method = DepreciationMethod.objects.filter(company_id=1, code='STRAIGHT_LINE').first()

asset = Asset.objects.create(
    code='LAPTOP001',
    name='لابتوب جديد',
    category=category,
    company_id=1,
    branch=Branch.objects.filter(company_id=1).first(),
    purchase_date=date.today(),
    in_service_date=date.today(),
    purchase_value=25000,
    salvage_value=2000,
    depreciation_method=depreciation_method,
    useful_life_years=3,
    depreciation_frequency='monthly',
    state='active',
    description='لابتوب للموظفين',
    location='المكتب الرئيسي'
)
```

## التقارير

### تقرير ميزان المراجعة
```python
from modules.accounting.models import Account, JournalEntryLine
from django.db.models import Sum

def trial_balance_report(company_id, date_from=None, date_to=None):
    accounts = Account.objects.filter(
        chart__company_id=company_id,
        level=5  # حسابات المعاملات فقط
    )
    
    report_data = []
    
    for account in accounts:
        lines = JournalEntryLine.objects.filter(
            account=account,
            entry__state='posted'
        )
        
        if date_from:
            lines = lines.filter(entry__date__gte=date_from)
        if date_to:
            lines = lines.filter(entry__date__lte=date_to)
        
        total_debit = lines.aggregate(Sum('debit'))['debit__sum'] or 0
        total_credit = lines.aggregate(Sum('credit'))['credit__sum'] or 0
        
        if total_debit != 0 or total_credit != 0:
            report_data.append({
                'account_code': account.code,
                'account_name': account.name,
                'debit': total_debit,
                'credit': total_credit,
                'balance': total_debit - total_credit
            })
    
    return report_data
```

### تقرير كشف حساب
```python
def account_statement(account_id, date_from=None, date_to=None):
    account = Account.objects.get(id=account_id)
    lines = JournalEntryLine.objects.filter(
        account=account,
        entry__state='posted'
    ).order_by('entry__date', 'id')
    
    if date_from:
        lines = lines.filter(entry__date__gte=date_from)
    if date_to:
        lines = lines.filter(entry__date__lte=date_to)
    
    running_balance = 0
    statement_data = []
    
    for line in lines:
        running_balance += line.debit - line.credit
        statement_data.append({
            'date': line.entry.date,
            'description': line.name,
            'reference': line.entry.reference,
            'debit': line.debit,
            'credit': line.credit,
            'balance': running_balance
        })
    
    return statement_data
```

## نصائح مهمة

1. **التوازن المحاسبي**: تأكد دائماً من أن مجموع المدين = مجموع الدائن في كل قيد
2. **المستويات**: استخدم حسابات المستوى 5 فقط للمعاملات اليدوية
3. **التواريخ**: تأكد من أن التواريخ ضمن السنة المالية النشطة
4. **الترحيل**: قم بترحيل القيود بعد التأكد من صحتها
5. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية قبل إجراء تغييرات كبيرة

## استكشاف الأخطاء

### خطأ: "Only transaction accounts can allow manual transactions"
**الحل**: استخدم حساب من المستوى 5 أو قم بتعديل إعدادات الحساب

### خطأ: "Journal entry must be balanced"
**الحل**: تأكد من أن مجموع المدين = مجموع الدائن

### خطأ: "Date is outside fiscal year"
**الحل**: تأكد من أن تاريخ المعاملة ضمن السنة المالية النشطة

## الدعم

للحصول على المساعدة:
1. راجع هذا الدليل
2. تحقق من ملف README_SAMPLE_DATA.md
3. راجع التوثيق في الكود
4. اتصل بفريق التطوير
