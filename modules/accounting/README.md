# موديول المحاسبة (Accounting Module)

## نظرة عامة (Overview)

موديول المحاسبة هو جزء أساسي من نظام ERP، ويوفر وظائف محاسبية شاملة لإدارة العمليات المالية للشركة. يتضمن الموديول إدارة دليل الحسابات، والقيود المحاسبية، والسنوات المالية، والخزينة، والبنوك، والشيكات، والأصول، وإعداد التقارير المالية.

## الميزات الرئيسية (Key Features)

### دليل الحسابات (Chart of Accounts)
- إنشاء وإدارة دليل حسابات شجري متعدد المستويات
- دعم أنواع الحسابات المختلفة (أصول، خصوم، إيرادات، مصروفات، حقوق ملكية)
- تخصيص دليل الحسابات حسب احتياجات الشركة

### السنة المالية (Fiscal Year)
- إنشاء وإدارة السنوات المالية
- فتح وإغلاق السنوات المالية
- ترحيل الأرصدة بين السنوات المالية

### القيود المحاسبية (Journal Entries)
- إنشاء وإدارة دفاتر اليومية المختلفة
- إنشاء قيود محاسبية يدوية
- ترحيل وإلغاء القيود المحاسبية
- التحقق من توازن القيود المحاسبية

### الخزينة والبنوك (Cash and Banks)
- إدارة الخزن والحسابات البنكية
- إنشاء وإدارة دفاتر الشيكات
- متابعة الشيكات الصادرة والواردة حسب الحالة
- إنشاء سندات القبض والصرف

### الأصول (Assets)
- إدارة الأصول الثابتة
- حساب الإهلاكات بطرق مختلفة
- إنشاء قيود الإهلاك بشكل تلقائي
- متابعة القيمة الدفترية للأصول

### التقارير المالية (Financial Reports)
- ميزان المراجعة
- كشف حساب
- قائمة الدخل
- الميزانية العمومية
- تقارير العملاء والموردين
- تقارير البنوك والخزينة
- تقارير الشيكات
- تقارير الأصول

## المتطلبات (Requirements)

- Django 3.2+
- Python 3.8+
- django-mptt (للهياكل الشجرية)

## التثبيت (Installation)

1. تأكد من إضافة 'mptt' و 'modules.accounting' إلى INSTALLED_APPS في ملف settings.py:
```python
INSTALLED_APPS = [
    # ...
    'mptt',
    'modules.accounting',
    # ...
]
```

2. قم بتنفيذ الهجرات:
```bash
python manage.py makemigrations
python manage.py migrate
```

3. قم بتشغيل الخادم:
```bash
python manage.py runserver
```

## الاستخدام (Usage)

1. انتقل إلى لوحة تحكم المحاسبة: `/accounting/`
2. قم بإنشاء سنة مالية جديدة
3. قم بإنشاء دليل الحسابات
4. قم بإعداد الخزن والحسابات البنكية
5. ابدأ في إنشاء القيود المحاسبية وسندات القبض والصرف

## المساهمة (Contributing)

نرحب بالمساهمات لتحسين موديول المحاسبة. يرجى اتباع الخطوات التالية:

1. قم بعمل fork للمشروع
2. قم بإنشاء فرع جديد للميزة أو الإصلاح
3. قم بتنفيذ التغييرات
4. قم بإرسال طلب سحب (pull request)

## الترخيص (License)

هذا الموديول مرخص بموجب [رخصة MIT](LICENSE).
