from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import (
    FiscalYear, AccountType, AccountChart, Account, AccountBalance,
    CashRegister, Bank, BankAccount, CheckBook, Check,
    Journal, JournalEntry, JournalEntryLine, PaymentMethod, Voucher,
    AssetCategory, DepreciationMethod, Asset, Depreciation
)


@admin.register(FiscalYear)
class FiscalYearAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'company', 'start_date', 'end_date', 'state', 'is_active')
    list_filter = ('company', 'state', 'is_active')
    search_fields = ('name', 'code')
    ordering = ('-start_date',)
    date_hierarchy = 'start_date'
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'category', 'is_debit_balance', 'company')
    list_filter = ('company', 'category', 'is_debit_balance')
    search_fields = ('name', 'code')
    ordering = ('code',)


@admin.register(AccountChart)
class AccountChartAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'company', 'is_active')
    list_filter = ('company', 'is_active')
    search_fields = ('name', 'code')
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'chart', 'type', 'level', 'parent', 'is_active')
    list_filter = ('chart', 'type', 'level', 'is_active')
    search_fields = ('code', 'name')
    ordering = ('chart', 'code')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(AccountBalance)
class AccountBalanceAdmin(admin.ModelAdmin):
    list_display = ('account', 'fiscal_year', 'branch', 'opening_balance', 'current_balance', 'closing_balance')
    list_filter = ('fiscal_year', 'branch')
    search_fields = ('account__code', 'account__name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(CashRegister)
class CashRegisterAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'company', 'branch', 'account', 'is_active')
    list_filter = ('company', 'branch', 'is_active')
    search_fields = ('name', 'code')
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(Bank)
class BankAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'swift_code', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'code', 'swift_code')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(BankAccount)
class BankAccountAdmin(admin.ModelAdmin):
    list_display = ('name', 'number', 'bank', 'company', 'branch', 'account_type', 'currency', 'is_active')
    list_filter = ('bank', 'company', 'branch', 'account_type', 'currency', 'is_active')
    search_fields = ('name', 'number', 'iban')
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(CheckBook)
class CheckBookAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'bank_account', 'start_number', 'end_number', 'next_number', 'is_active')
    list_filter = ('bank_account__bank', 'bank_account__company', 'is_active')
    search_fields = ('name', 'code', 'bank_account__number')
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(Check)
class CheckAdmin(admin.ModelAdmin):
    list_display = ('number', 'amount', 'date', 'due_date', 'check_type', 'state', 'bank_account', 'partner_name')
    list_filter = ('check_type', 'state', 'bank_account__bank', 'bank_account__company')
    search_fields = ('number', 'reference', 'partner_name')
    date_hierarchy = 'date'
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(Journal)
class JournalAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'type', 'company', 'is_active')
    list_filter = ('type', 'company', 'is_active')
    search_fields = ('name', 'code')
    readonly_fields = ('created_by', 'created_at', 'updated_at')


class JournalEntryLineInline(admin.TabularInline):
    model = JournalEntryLine
    extra = 1
    fields = ('account', 'name', 'debit', 'credit', 'partner_name', 'partner_type', 'reconciled')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    list_display = ('reference', 'name', 'date', 'journal', 'company', 'branch', 'state')
    list_filter = ('journal', 'company', 'branch', 'state')
    search_fields = ('reference', 'name')
    date_hierarchy = 'date'
    readonly_fields = ('created_by', 'posted_by', 'created_at', 'posted_at', 'updated_at')
    inlines = [JournalEntryLineInline]


@admin.register(JournalEntryLine)
class JournalEntryLineAdmin(admin.ModelAdmin):
    list_display = ('entry', 'account', 'name', 'debit', 'credit', 'reconciled')
    list_filter = ('entry__journal', 'entry__company', 'reconciled')
    search_fields = ('entry__reference', 'account__code', 'account__name', 'name')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(PaymentMethod)
class PaymentMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'payment_type', 'company', 'is_active')
    list_filter = ('payment_type', 'company', 'is_active')
    search_fields = ('name', 'code')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Voucher)
class VoucherAdmin(admin.ModelAdmin):
    list_display = ('number', 'reference', 'date', 'voucher_type', 'state', 'amount', 'company', 'branch')
    list_filter = ('voucher_type', 'state', 'company', 'branch')
    search_fields = ('number', 'reference', 'partner_name')
    date_hierarchy = 'date'
    readonly_fields = ('created_by', 'posted_by', 'created_at', 'posted_at', 'updated_at')


@admin.register(AssetCategory)
class AssetCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'company', 'depreciation_rate', 'useful_life_years', 'is_active')
    list_filter = ('company', 'is_active')
    search_fields = ('name', 'code')
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(DepreciationMethod)
class DepreciationMethodAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'method_type', 'company', 'factor', 'is_active')
    list_filter = ('method_type', 'company', 'is_active')
    search_fields = ('name', 'code')
    readonly_fields = ('created_at', 'updated_at')


@admin.register(Asset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'category', 'company', 'branch', 'purchase_date', 'purchase_value', 'current_value', 'state')
    list_filter = ('category', 'company', 'branch', 'state', 'is_active')
    search_fields = ('name', 'code', 'reference', 'serial_number')
    date_hierarchy = 'purchase_date'
    readonly_fields = ('created_by', 'created_at', 'updated_at')


@admin.register(Depreciation)
class DepreciationAdmin(admin.ModelAdmin):
    list_display = ('asset', 'date', 'period', 'amount', 'accumulated_depreciation', 'remaining_value', 'state')
    list_filter = ('asset__category', 'asset__company', 'state')
    search_fields = ('asset__name', 'asset__code')
    date_hierarchy = 'date'
    readonly_fields = ('created_by', 'posted_by', 'created_at', 'posted_at', 'updated_at')
