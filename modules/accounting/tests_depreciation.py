"""
Tests for the depreciation functionality in the accounting module.
"""
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
import datetime

from companies.models import Company, Branch
from .models import (
    AssetCategory, DepreciationMethod, Asset, Depreciation,
    Account, AccountChart, AccountType, FiscalYear, Journal, JournalEntry
)

User = get_user_model()


class DepreciationTestCase(TestCase):
    """Test case for depreciation functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a superuser
        self.user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='password123'
        )

        # Create a company
        self.company = Company.objects.create(
            name='Test Company',
            code='TC',
            registration_number='123456',
            tax_number='987654',
            address='Test Address',
            phone='*********',
            email='<EMAIL>',
            website='www.example.com',
            is_active=True
        )

        # Create a branch
        self.branch = Branch.objects.create(
            name='Main Branch',
            code='MB',
            company=self.company,
            address='Branch Address',
            phone='*********',
            email='<EMAIL>',
            is_active=True
        )

        # Create a fiscal year
        self.fiscal_year = FiscalYear.objects.create(
            name='Fiscal Year 2025',
            code='FY2025',
            company=self.company,
            start_date=datetime.date(2025, 1, 1),
            end_date=datetime.date(2025, 12, 31),
            state='open',
            is_active=True,
            created_by=self.user
        )

        # Create account chart
        self.chart = AccountChart.objects.create(
            name='Standard Chart',
            code='STD',
            company=self.company,
            is_active=True,
            created_by=self.user
        )

        # Create account types
        self.asset_type = AccountType.objects.create(
            name='Asset',
            code='ASSET',
            category='asset',
            is_debit_balance=True,
            company=self.company
        )

        self.expense_type = AccountType.objects.create(
            name='Expense',
            code='EXP',
            category='expense',
            is_debit_balance=True,
            company=self.company
        )

        # Create accounts
        self.asset_account = Account.objects.create(
            chart=self.chart,
            code='1200',
            name='Fixed Assets',
            type=self.asset_type,
            level=1,
            is_active=True,
            allow_manual_transactions=True
        )

        self.depreciation_account = Account.objects.create(
            chart=self.chart,
            code='5100',
            name='Depreciation Expense',
            type=self.expense_type,
            level=1,
            is_active=True,
            allow_manual_transactions=True
        )

        self.accumulated_depreciation_account = Account.objects.create(
            chart=self.chart,
            code='1210',
            name='Accumulated Depreciation',
            type=self.asset_type,
            level=1,
            is_active=True,
            allow_manual_transactions=True
        )

        # Create asset category
        self.asset_category = AssetCategory.objects.create(
            name='Computers',
            code='COMP',
            company=self.company,
            asset_account=self.asset_account,
            depreciation_account=self.depreciation_account,
            accumulated_depreciation_account=self.accumulated_depreciation_account,
            depreciation_rate=20.0,
            useful_life_years=5,
            is_active=True
        )

        # Create depreciation method
        self.depreciation_method = DepreciationMethod.objects.create(
            name='Straight Line',
            code='SL',
            method_type='straight_line',
            company=self.company,
            is_active=True
        )

        # Create asset
        self.asset = Asset.objects.create(
            name='Laptop',
            code='LAP001',
            category=self.asset_category,
            company=self.company,
            branch=self.branch,
            purchase_date=timezone.now().date(),
            in_service_date=timezone.now().date(),
            purchase_value=1000.00,
            salvage_value=100.00,
            depreciation_method=self.depreciation_method,
            useful_life_years=5,
            depreciation_frequency='monthly',
            asset_account=self.asset_account,
            depreciation_account=self.depreciation_account,
            accumulated_depreciation_account=self.accumulated_depreciation_account,
            state='active',
            is_active=True,
            created_by=self.user
        )

        # Create client
        self.client = Client()
        self.client.login(username='admin', password='password123')

    def test_asset_detail_view(self):
        """Test the asset detail view."""
        url = reverse('accounting:asset_detail', args=[self.asset.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'modules/accounting/asset_detail.html')
        self.assertEqual(response.context['asset'], self.asset)

    def test_asset_depreciation_board_view(self):
        """Test the asset depreciation board view."""
        url = reverse('accounting:asset_depreciation_board', args=[self.asset.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'modules/accounting/asset_depreciation_board.html')
        self.assertEqual(response.context['asset'], self.asset)
        self.assertIn('depreciation_board', response.context)

    def test_asset_create_depreciation(self):
        """Test creating depreciation for an asset."""
        url = reverse('accounting:asset_create_depreciation', args=[self.asset.id])
        response = self.client.get(url)
        self.assertRedirects(response, reverse('accounting:asset_detail', args=[self.asset.id]))

        # Check that depreciation records were created
        depreciations = Depreciation.objects.filter(asset=self.asset)
        self.assertEqual(depreciations.count(), 60)  # 5 years * 12 months

    def test_depreciation_detail_view(self):
        """Test the depreciation detail view."""
        # First create a depreciation
        depreciation = Depreciation.objects.create(
            asset=self.asset,
            date=timezone.now().date(),
            period=1,
            amount=15.00,
            accumulated_depreciation=15.00,
            remaining_value=985.00,
            state='draft',
            created_by=self.user
        )

        url = reverse('accounting:depreciation_detail', args=[depreciation.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'modules/accounting/depreciation_detail.html')
        self.assertEqual(response.context['depreciation'], depreciation)

    def test_depreciation_post(self):
        """Test posting a depreciation."""
        # Create a journal for asset operations
        journal = Journal.objects.create(
            name='Asset Journal',
            code='ASSET',
            type='asset',
            company=self.company,
            is_active=True,
            created_by=self.user
        )

        # Create a depreciation
        depreciation = Depreciation.objects.create(
            asset=self.asset,
            date=timezone.now().date(),
            period=1,
            amount=15.00,
            accumulated_depreciation=15.00,
            remaining_value=985.00,
            state='draft',
            created_by=self.user
        )

        url = reverse('accounting:depreciation_post', args=[depreciation.id])
        response = self.client.get(url)
        self.assertRedirects(response, reverse('accounting:depreciation_detail', args=[depreciation.id]))

        # Refresh from database
        depreciation.refresh_from_db()
        self.assertEqual(depreciation.state, 'posted')
        self.assertIsNotNone(depreciation.journal_entry)
        self.assertEqual(depreciation.journal_entry.state, 'posted')

    def test_depreciation_cancel(self):
        """Test cancelling a depreciation."""
        # Create a journal for asset operations
        journal = Journal.objects.create(
            name='Asset Journal',
            code='ASSET',
            type='asset',
            company=self.company,
            is_active=True,
            created_by=self.user
        )

        # Create a depreciation and post it
        depreciation = Depreciation.objects.create(
            asset=self.asset,
            date=timezone.now().date(),
            period=1,
            amount=15.00,
            accumulated_depreciation=15.00,
            remaining_value=985.00,
            state='posted',
            created_by=self.user,
            posted_by=self.user,
            posted_at=timezone.now()
        )

        # Create a journal entry for the depreciation
        entry = JournalEntry.objects.create(
            name=f"Asset Depreciation: {self.asset.name} - Period 1",
            reference=f"DEPR-{self.asset.code}-1",
            date=depreciation.date,
            journal=journal,
            fiscal_year=self.fiscal_year,
            company=self.company,
            branch=self.branch,
            state='posted',
            created_by=self.user
        )
        depreciation.journal_entry = entry
        depreciation.save()

        url = reverse('accounting:depreciation_cancel', args=[depreciation.id])
        response = self.client.get(url)
        self.assertRedirects(response, reverse('accounting:depreciation_detail', args=[depreciation.id]))

        # Refresh from database
        depreciation.refresh_from_db()
        self.assertEqual(depreciation.state, 'cancelled')
