from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Sum, Q
from django.http import JsonResponse
from django.views.decorators.http import require_GET
from django.core.paginator import Paginator
from django import forms
from django.forms import inlineformset_factory, HiddenInput
from django.core.exceptions import ValidationError
import datetime

from .models import (
    FiscalYear, AccountType, AccountChart, Account, AccountBalance,
    CashRegister, Bank, BankAccount, CheckBook, Check,
    Journal, JournalEntry, JournalEntryLine, PaymentMethod, Voucher,
    AssetCategory, DepreciationMethod, Asset, Depreciation
)
from .forms import (
    FiscalYearForm, AccountTypeForm, AccountChartForm, AccountForm,
    CashRegisterForm, BankForm, BankAccountForm, CheckBookForm, CheckForm,
    JournalForm, JournalEntryForm, JournalEntryLineForm, JournalEntryLineFormSet, PaymentMethodForm, VoucherForm,
    AssetCategoryForm, DepreciationMethodForm, AssetForm, AssetDisposeForm, DepreciationForm
)


@login_required
def dashboard(request):
    """Accounting dashboard view"""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    if not fiscal_year:
        messages.warning(request, _('No active fiscal year found. Please create and activate a fiscal year.'))

    # Get recent journal entries
    recent_entries = JournalEntry.objects.filter(
        company=company,
        branch=branch
    ).order_by('-date', '-id')[:10]

    # Get account balances
    if fiscal_year:
        # Accounts receivable
        accounts_receivable = AccountBalance.objects.filter(
            account__type__category='asset',
            account__code__startswith='1110',  # Accounts Receivable
            fiscal_year=fiscal_year,
            branch=branch
        ).aggregate(total=Sum('closing_debit') - Sum('closing_credit'))['total'] or 0

        # Accounts payable
        accounts_payable = AccountBalance.objects.filter(
            account__type__category='liability',
            account__code__startswith='2110',  # Accounts Payable
            fiscal_year=fiscal_year,
            branch=branch
        ).aggregate(total=Sum('closing_credit') - Sum('closing_debit'))['total'] or 0

        # Bank balance
        bank_balance = AccountBalance.objects.filter(
            account__type__category='asset',
            account__code__startswith='1020',  # Bank Accounts
            fiscal_year=fiscal_year,
            branch=branch
        ).aggregate(total=Sum('closing_debit') - Sum('closing_credit'))['total'] or 0

        # Revenue (MTD)
        current_month = timezone.now().month
        current_year = timezone.now().year
        revenue_mtd = JournalEntryLine.objects.filter(
            entry__company=company,
            entry__branch=branch,
            entry__state='posted',
            entry__date__month=current_month,
            entry__date__year=current_year,
            account__type__category='revenue'
        ).aggregate(total=Sum('credit') - Sum('debit'))['total'] or 0
    else:
        accounts_receivable = 0
        accounts_payable = 0
        bank_balance = 0
        revenue_mtd = 0

    context = {
        'recent_entries': recent_entries,
        'accounts_receivable': accounts_receivable,
        'accounts_payable': accounts_payable,
        'bank_balance': bank_balance,
        'revenue_mtd': revenue_mtd,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/dashboard.html', context)


# Placeholder views for all URLs defined in urls.py
# These will be implemented in detail later

# Chart of Accounts views
@login_required
def chart_list(request):
    """Chart of accounts list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get charts of accounts
    charts = AccountChart.objects.filter(company=company)

    # Get accounts
    accounts = Account.objects.filter(chart__company=company).select_related('chart', 'type')

    context = {
        'charts': charts,
        'accounts': accounts,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/chart_list.html', context)

@login_required
def chart_create(request):
    """Create a new chart of accounts."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    initial_data = {'company': company, 'is_active': True}
    form = AccountChartForm(request.POST or None, initial=initial_data)

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    if request.method == 'POST':
        if form.is_valid():
            chart = form.save(commit=False)
            # Asignar el usuario actual como creador si el usuario está autenticado
            if request.user.is_authenticated:
                chart.created_by = request.user
            chart.save()
            messages.success(request, _('Chart of accounts created successfully.'))
            return redirect('accounting:chart_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/chart_form.html', context)

@login_required
def chart_detail(request, chart_id):
    """View chart of accounts details."""
    try:
        chart = AccountChart.objects.get(id=chart_id)
    except AccountChart.DoesNotExist:
        messages.error(request, _('Chart of accounts not found.'))
        return redirect('accounting:chart_list')

    # Get accounts in this chart
    accounts = Account.objects.filter(chart=chart).select_related('type', 'parent')

    # Get account balances
    account_balances = {}
    balances = AccountBalance.objects.filter(account__in=accounts)
    for balance in balances:
        account_balances[balance.account_id] = balance.balance

    # Add balance to accounts
    for account in accounts:
        account.balance = account_balances.get(account.id, 0)

    context = {
        'chart': chart,
        'accounts': accounts,
    }

    return render(request, 'modules/accounting/chart_detail.html', context)

@login_required
def chart_edit(request, chart_id):
    """Edit an existing chart of accounts."""
    try:
        chart = AccountChart.objects.get(id=chart_id)
    except AccountChart.DoesNotExist:
        messages.error(request, _('Chart of accounts not found.'))
        return redirect('accounting:chart_list')

    # Create form instance
    form = AccountChartForm(request.POST or None, instance=chart)

    # Hide company field
    form.fields['company'].widget = HiddenInput()

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Chart of accounts updated successfully.'))
            return redirect('accounting:chart_list')

    context = {
        'form': form,
        'chart': chart,
        'company': chart.company,
    }

    return render(request, 'modules/accounting/chart_form.html', context)

@login_required
def chart_delete(request, chart_id):
    """Delete a chart of accounts."""
    try:
        chart = AccountChart.objects.get(id=chart_id)
    except AccountChart.DoesNotExist:
        messages.error(request, _('Chart of accounts not found.'))
        return redirect('accounting:chart_list')

    # Check if chart has accounts
    if chart.accounts.exists():
        messages.error(request, _('Cannot delete chart of accounts that has accounts. Please delete all accounts first.'))
        return redirect('accounting:chart_list')

    # Delete the chart
    chart_name = chart.name
    chart.delete()
    messages.success(request, _('Chart of accounts "{}" deleted successfully.').format(chart_name))

    return redirect('accounting:chart_list')

# Account Type views
@login_required
def account_type_list(request):
    """Account type list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    search_name = request.GET.get('name')
    category = request.GET.get('category')
    report_type = request.GET.get('report_type')
    is_active = request.GET.get('is_active')

    # Get account types for the current company
    account_types = AccountType.objects.filter(company=company)

    # Apply filters
    if search_name:
        account_types = account_types.filter(name__icontains=search_name)

    if category:
        account_types = account_types.filter(category=category)

    if report_type:
        account_types = account_types.filter(report_type=report_type)

    if is_active is not None and is_active != '':
        is_active_bool = is_active == '1'
        account_types = account_types.filter(is_active=is_active_bool)

    # Order by category and name
    account_types = account_types.order_by('category', 'name')

    context = {
        'account_types': account_types,
        'search_name': search_name,
        'selected_category': category,
        'selected_report_type': report_type,
        'selected_is_active': is_active,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/account_type_list.html', context)

@login_required
def account_type_create(request):
    """Create a new account type."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    initial_data = {'company': company, 'is_active': True}
    form = AccountTypeForm(request.POST or None, initial=initial_data)

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    if request.method == 'POST':
        if form.is_valid():
            account_type = form.save(commit=False)
            account_type.created_by = request.user
            account_type.save()
            messages.success(request, _('Account type created successfully.'))
            return redirect('accounting:account_type_list')

    context = {
        'form': form,
    }

    return render(request, 'modules/accounting/account_type_form.html', context)

@login_required
def account_type_detail(request, account_type_id):
    """View account type details."""
    try:
        account_type = AccountType.objects.get(id=account_type_id)
    except AccountType.DoesNotExist:
        messages.error(request, _('Account type not found.'))
        return redirect('accounting:account_type_list')

    # Get accounts of this type
    accounts = Account.objects.filter(type=account_type).select_related('chart')

    context = {
        'account_type': account_type,
        'accounts': accounts,
    }

    return render(request, 'modules/accounting/account_type_detail.html', context)

@login_required
def account_type_edit(request, account_type_id):
    """Edit an existing account type."""
    try:
        account_type = AccountType.objects.get(id=account_type_id)
    except AccountType.DoesNotExist:
        messages.error(request, _('Account type not found.'))
        return redirect('accounting:account_type_list')

    # Create form instance
    form = AccountTypeForm(request.POST or None, instance=account_type)

    # Hide company field
    form.fields['company'].widget = HiddenInput()

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Account type updated successfully.'))
            return redirect('accounting:account_type_list')

    context = {
        'form': form,
        'account_type': account_type,
    }

    return render(request, 'modules/accounting/account_type_form.html', context)

@login_required
def account_type_delete(request, account_type_id):
    """Delete an account type."""
    try:
        account_type = AccountType.objects.get(id=account_type_id)
    except AccountType.DoesNotExist:
        messages.error(request, _('Account type not found.'))
        return redirect('accounting:account_type_list')

    # Check if account type has accounts
    if Account.objects.filter(type=account_type).exists():
        messages.error(request, _('Cannot delete account type that has accounts. Please delete all accounts first.'))
        return redirect('accounting:account_type_list')

    # Delete the account type
    account_type_name = account_type.name
    account_type.delete()
    messages.success(request, _('Account type "{}" deleted successfully.').format(account_type_name))

    return redirect('accounting:account_type_list')

# Account views
@login_required
def account_list(request):
    """Account list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    chart_id = request.GET.get('chart')
    type_id = request.GET.get('type')
    search_name = request.GET.get('name')
    search_code = request.GET.get('code')

    # Get accounts
    accounts = Account.objects.filter(
        chart__company=company
    ).select_related('chart', 'type', 'parent')

    # Apply filters
    if chart_id:
        try:
            chart_id = int(chart_id)
            accounts = accounts.filter(chart_id=chart_id)
        except (ValueError, TypeError):
            pass

    if type_id:
        try:
            type_id = int(type_id)
            accounts = accounts.filter(type_id=type_id)
        except (ValueError, TypeError):
            pass

    if search_name:
        accounts = accounts.filter(name__icontains=search_name)

    if search_code:
        accounts = accounts.filter(code__icontains=search_code)

    # Get account balances
    account_balances = {}
    balances = AccountBalance.objects.filter(
        account__in=accounts,
        branch=branch
    )
    for balance in balances:
        account_balances[balance.account_id] = balance.closing_balance

    # Add balance to accounts
    for account in accounts:
        account.balance = account_balances.get(account.id, 0)

    # Get charts and account types for filter dropdowns
    charts = AccountChart.objects.filter(company=company)
    account_types = AccountType.objects.filter(company=company)

    context = {
        'accounts': accounts,
        'charts': charts,
        'account_types': account_types,
        'selected_chart': chart_id,
        'selected_type': type_id,
        'search_name': search_name,
        'search_code': search_code,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/account_list.html', context)

@login_required
def account_create(request):
    """Create a new account."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get initial data from query parameters
    chart_id = request.GET.get('chart')
    type_id = request.GET.get('type')
    parent_id = request.GET.get('parent')
    level = request.GET.get('level', 5)  # Default to transaction level

    # Prepare initial data
    initial_data = {
        'is_active': True,
        'level': level,
    }

    # Get chart if provided
    if chart_id:
        try:
            chart = AccountChart.objects.get(id=chart_id, company=company)
            initial_data['chart'] = chart
        except (AccountChart.DoesNotExist, ValueError, TypeError):
            pass
    else:
        # Use active chart if available
        active_chart = AccountChart.objects.filter(company=company, is_active=True).first()
        if active_chart:
            initial_data['chart'] = active_chart

    # Get account type if provided
    if type_id:
        try:
            account_type = AccountType.objects.get(id=type_id, company=company)
            initial_data['type'] = account_type
        except (AccountType.DoesNotExist, ValueError, TypeError):
            pass

    # Get parent account if provided
    if parent_id:
        try:
            parent = Account.objects.get(id=parent_id)
            initial_data['parent'] = parent
            # Set chart to parent's chart
            initial_data['chart'] = parent.chart
            # Set level to parent's level + 1
            initial_data['level'] = min(parent.level + 1, 5)
        except (Account.DoesNotExist, ValueError, TypeError):
            pass

    # Create form instance
    form = AccountForm(request.POST or None, initial=initial_data)

    # Filter chart choices to company's charts
    form.fields['chart'].queryset = AccountChart.objects.filter(company=company)

    # Filter account type choices to company's account types
    form.fields['type'].queryset = AccountType.objects.filter(company=company)

    # Filter parent account choices to accounts from the same chart
    if 'chart' in initial_data:
        form.fields['parent'].queryset = Account.objects.filter(chart=initial_data['chart'])
    else:
        form.fields['parent'].queryset = Account.objects.none()

    # Add data attributes to parent options for JavaScript filtering
    for parent_option in form.fields['parent'].queryset:
        parent_option.chart_id = str(parent_option.chart_id)
        parent_option.level = str(parent_option.level)

    if request.method == 'POST':
        if form.is_valid():
            account = form.save(commit=False)
            account.save()
            messages.success(request, _('Account created successfully.'))
            return redirect('accounting:account_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/account_form.html', context)

@login_required
def account_detail(request, account_id):
    """View account details."""
    try:
        account = Account.objects.get(id=account_id)
    except Account.DoesNotExist:
        messages.error(request, _('Account not found.'))
        return redirect('accounting:account_list')

    # Get account balance
    try:
        balance = AccountBalance.objects.get(
            account=account,
            branch_id=request.session.get('active_branch_id')
        )
        account.balance = balance.closing_balance
    except AccountBalance.DoesNotExist:
        account.balance = 0

    # Get child accounts
    children = Account.objects.filter(parent=account).select_related('type')

    # Get child account balances
    child_balances = {}
    balances = AccountBalance.objects.filter(
        account__in=children,
        branch_id=request.session.get('active_branch_id')
    )
    for balance in balances:
        child_balances[balance.account_id] = balance.closing_balance

    # Add balance to child accounts
    for child in children:
        child.balance = child_balances.get(child.id, 0)

    # Get recent journal entries for this account
    journal_entries = JournalEntryLine.objects.filter(
        account=account
    ).select_related('entry').order_by('-entry__date', '-entry__id')[:10]

    # Get total count of journal entries for this account
    journal_entries_count = JournalEntryLine.objects.filter(account=account).count()

    context = {
        'account': account,
        'children': children,
        'journal_entries': journal_entries,
        'journal_entries_count': journal_entries_count,
    }

    return render(request, 'modules/accounting/account_detail.html', context)

@login_required
def account_edit(request, account_id):
    """Edit an existing account."""
    try:
        account = Account.objects.get(id=account_id)
    except Account.DoesNotExist:
        messages.error(request, _('Account not found.'))
        return redirect('accounting:account_list')

    # Create form instance
    form = AccountForm(request.POST or None, instance=account)

    # Filter chart choices to company's charts
    form.fields['chart'].queryset = AccountChart.objects.filter(company=account.chart.company)

    # Filter account type choices to company's account types
    form.fields['type'].queryset = AccountType.objects.filter(company=account.chart.company)

    # Filter parent account choices to accounts from the same chart
    form.fields['parent'].queryset = Account.objects.filter(chart=account.chart).exclude(id=account.id)

    # Add data attributes to parent options for JavaScript filtering
    for parent_option in form.fields['parent'].queryset:
        parent_option.chart_id = str(parent_option.chart_id)
        parent_option.level = str(parent_option.level)

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Account updated successfully.'))
            return redirect('accounting:account_list')

    context = {
        'form': form,
        'account': account,
        'company': account.chart.company,
    }

    return render(request, 'modules/accounting/account_form.html', context)

@login_required
def account_delete(request, account_id):
    """Delete an account."""
    try:
        account = Account.objects.get(id=account_id)
    except Account.DoesNotExist:
        messages.error(request, _('Account not found.'))
        return redirect('accounting:account_list')

    # Check if account has children
    if account.children.exists():
        messages.error(request, _('Cannot delete account that has child accounts. Please delete all child accounts first.'))
        return redirect('accounting:account_list')

    # Check if account has journal entries
    if JournalEntryLine.objects.filter(account=account).exists():
        messages.error(request, _('Cannot delete account that has journal entries. Please delete all journal entries first.'))
        return redirect('accounting:account_list')

    # Delete the account
    account_name = account.name
    account.delete()
    messages.success(request, _('Account "{}" deleted successfully.').format(account_name))

    return redirect('accounting:account_list')

# Fiscal Year views
@login_required
def fiscal_year_list(request):
    """Fiscal year list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    name = request.GET.get('name')
    state = request.GET.get('state')
    is_active = request.GET.get('is_active')

    # Get fiscal years
    fiscal_years = FiscalYear.objects.filter(company=company)

    # Apply filters
    if name:
        fiscal_years = fiscal_years.filter(name__icontains=name)

    if state:
        fiscal_years = fiscal_years.filter(state=state)

    if is_active:
        is_active_bool = is_active == '1'
        fiscal_years = fiscal_years.filter(is_active=is_active_bool)

    # Order by start date (descending)
    fiscal_years = fiscal_years.order_by('-start_date')

    context = {
        'fiscal_years': fiscal_years,
        'search_name': name,
        'selected_state': state,
        'selected_active': is_active,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/fiscal_year_list.html', context)

@login_required
def fiscal_year_create(request):
    """Create a new fiscal year."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    if request.method == 'POST':
        form = FiscalYearForm(request.POST)
        # Set company manually to avoid RelatedObjectDoesNotExist error
        form.instance.company = company
    else:
        form = FiscalYearForm(initial={'company': company})

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    if request.method == 'POST':
        if form.is_valid():
            fiscal_year = form.save(commit=False)
            fiscal_year.company = company
            fiscal_year.created_by = request.user

            # If this fiscal year is active, deactivate other fiscal years
            if fiscal_year.is_active:
                FiscalYear.objects.filter(company=company, is_active=True).update(is_active=False)

            fiscal_year.save()
            messages.success(request, _('Fiscal year created successfully.'))
            return redirect('accounting:fiscal_year_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/fiscal_year_form.html', context)

@login_required
def fiscal_year_detail(request, fiscal_year_id):
    """View fiscal year details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get fiscal year
    try:
        fiscal_year = FiscalYear.objects.get(id=fiscal_year_id, company=company)
    except FiscalYear.DoesNotExist:
        messages.error(request, _('Fiscal year not found.'))
        return redirect('accounting:fiscal_year_list')

    # Get statistics
    # Journal entries count
    journal_entries_count = JournalEntry.objects.filter(
        fiscal_year=fiscal_year,
        company=company
    ).count()

    # Recent journal entries
    journal_entries = JournalEntry.objects.filter(
        fiscal_year=fiscal_year,
        company=company
    ).select_related('journal').order_by('-date', '-id')[:5]

    # Add amount to journal entries
    for entry in journal_entries:
        entry.amount = entry.lines.aggregate(Sum('debit'))['debit__sum'] or 0

    # Receipts count
    receipts_count = Voucher.objects.filter(
        fiscal_year=fiscal_year,
        company=company,
        voucher_type='receipt'
    ).count()

    # Payments count
    payments_count = Voucher.objects.filter(
        fiscal_year=fiscal_year,
        company=company,
        voucher_type='payment'
    ).count()

    # Checks count
    checks_count = Check.objects.filter(
        company=company
    ).count()

    context = {
        'fiscal_year': fiscal_year,
        'journal_entries_count': journal_entries_count,
        'journal_entries': journal_entries,
        'receipts_count': receipts_count,
        'payments_count': payments_count,
        'checks_count': checks_count,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/fiscal_year_detail.html', context)

@login_required
def fiscal_year_edit(request, fiscal_year_id):
    """Edit an existing fiscal year."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get fiscal year
    try:
        fiscal_year = FiscalYear.objects.get(id=fiscal_year_id, company=company)
    except FiscalYear.DoesNotExist:
        messages.error(request, _('Fiscal year not found.'))
        return redirect('accounting:fiscal_year_list')

    # Check if fiscal year is in draft state
    if fiscal_year.state != 'draft':
        messages.error(request, _('Only draft fiscal years can be edited.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Create form instance
    form = FiscalYearForm(request.POST or None, instance=fiscal_year)

    if request.method == 'POST':
        if form.is_valid():
            fiscal_year = form.save(commit=False)
            fiscal_year.updated_by = request.user

            # If this fiscal year is active, deactivate other fiscal years
            if fiscal_year.is_active:
                FiscalYear.objects.filter(company=company, is_active=True).exclude(id=fiscal_year.id).update(is_active=False)

            fiscal_year.save()
            messages.success(request, _('Fiscal year updated successfully.'))
            return redirect('accounting:fiscal_year_list')

    context = {
        'form': form,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/fiscal_year_form.html', context)

@login_required
def fiscal_year_open(request, fiscal_year_id):
    """Open a fiscal year."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get fiscal year
    try:
        fiscal_year = FiscalYear.objects.get(id=fiscal_year_id, company=company)
    except FiscalYear.DoesNotExist:
        messages.error(request, _('Fiscal year not found.'))
        return redirect('accounting:fiscal_year_list')

    # Check if fiscal year is in draft state
    if fiscal_year.state != 'draft':
        messages.error(request, _('Only draft fiscal years can be opened.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Open fiscal year
    fiscal_year.state = 'open'
    fiscal_year.save()

    # If this fiscal year is active, deactivate other fiscal years
    if fiscal_year.is_active:
        FiscalYear.objects.filter(company=company, is_active=True).exclude(id=fiscal_year.id).update(is_active=False)

    messages.success(request, _('Fiscal year opened successfully.'))
    return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

@login_required
def fiscal_year_close(request, fiscal_year_id):
    """Close a fiscal year."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get fiscal year
    try:
        fiscal_year = FiscalYear.objects.get(id=fiscal_year_id, company=company)
    except FiscalYear.DoesNotExist:
        messages.error(request, _('Fiscal year not found.'))
        return redirect('accounting:fiscal_year_list')

    # Check if fiscal year is in open state
    if fiscal_year.state != 'open':
        messages.error(request, _('Only open fiscal years can be closed.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Check if there is a next fiscal year
    next_fiscal_year = FiscalYear.objects.filter(
        company=company,
        start_date__gt=fiscal_year.end_date
    ).order_by('start_date').first()

    if not next_fiscal_year:
        messages.error(request, _('No next fiscal year found. Please create a new fiscal year before closing this one.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Check if next fiscal year is in draft or open state
    if next_fiscal_year.state not in ['draft', 'open']:
        messages.error(request, _('Next fiscal year is not in draft or open state.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Get all accounts
    accounts = Account.objects.filter(
        chart__company=company,
        is_active=True
    ).select_related('type')

    # Create closing journal entry
    closing_journal = Journal.objects.filter(
        company=company,
        type='closing'
    ).first()

    if not closing_journal:
        messages.error(request, _('No closing journal found. Please create a closing journal first.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Create closing journal entry
    closing_entry = JournalEntry.objects.create(
        name=_('Closing Entry for {}').format(fiscal_year.name),
        date=fiscal_year.end_date,
        journal=closing_journal,
        reference=_('CLOSE-{}').format(fiscal_year.code),
        state='posted',
        company=company,
        branch=branch,
        fiscal_year=fiscal_year,
        memo=_('Closing entry for fiscal year {}').format(fiscal_year.name),
        created_by=request.user,
        posted_by=request.user,
        posted_date=timezone.now(),
    )

    # Create opening journal entry for next fiscal year
    opening_journal = Journal.objects.filter(
        company=company,
        type='opening'
    ).first()

    if not opening_journal:
        messages.error(request, _('No opening journal found. Please create an opening journal first.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Create opening journal entry
    opening_entry = JournalEntry.objects.create(
        name=_('Opening Entry for {}').format(next_fiscal_year.name),
        date=next_fiscal_year.start_date,
        journal=opening_journal,
        reference=_('OPEN-{}').format(next_fiscal_year.code),
        state='posted',
        company=company,
        branch=branch,
        fiscal_year=next_fiscal_year,
        memo=_('Opening entry for fiscal year {}').format(next_fiscal_year.name),
        created_by=request.user,
        posted_by=request.user,
        posted_date=timezone.now(),
    )

    # Get retained earnings account
    retained_earnings_account = Account.objects.filter(
        chart__company=company,
        type__name='Retained Earnings'
    ).first()

    if not retained_earnings_account:
        messages.error(request, _('No retained earnings account found. Please create a retained earnings account first.'))
        return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

    # Process each account
    total_debit = 0
    total_credit = 0

    for account in accounts:
        # Skip retained earnings account
        if account.id == retained_earnings_account.id:
            continue

        # Get account balance
        try:
            account_balance = AccountBalance.objects.get(
                account=account,
                branch=branch
            )
            balance = account_balance.closing_balance
        except AccountBalance.DoesNotExist:
            balance = 0

        # Skip accounts with zero balance
        if abs(balance) < 0.01:
            continue

        # Create closing journal entry line
        if account.type.is_debit_balance:
            # For debit balance accounts (assets, expenses)
            if balance > 0:
                # Credit the account to close it
                JournalEntryLine.objects.create(
                    journal_entry=closing_entry,
                    account=account,
                    name=_('Closing balance for {}').format(account.name),
                    debit=0,
                    credit=balance,
                )
                total_credit += balance
            elif balance < 0:
                # Debit the account to close it
                JournalEntryLine.objects.create(
                    journal_entry=closing_entry,
                    account=account,
                    name=_('Closing balance for {}').format(account.name),
                    debit=abs(balance),
                    credit=0,
                )
                total_debit += abs(balance)
        else:
            # For credit balance accounts (liabilities, equity, revenue)
            if balance > 0:
                # Debit the account to close it
                JournalEntryLine.objects.create(
                    journal_entry=closing_entry,
                    account=account,
                    name=_('Closing balance for {}').format(account.name),
                    debit=balance,
                    credit=0,
                )
                total_debit += balance
            elif balance < 0:
                # Credit the account to close it
                JournalEntryLine.objects.create(
                    journal_entry=closing_entry,
                    account=account,
                    name=_('Closing balance for {}').format(account.name),
                    debit=0,
                    credit=abs(balance),
                )
                total_credit += abs(balance)

        # Create opening journal entry line for balance sheet accounts
        if account.type.report_type in ['asset', 'liability', 'equity']:
            # Create opening journal entry line
            if account.type.is_debit_balance:
                # For debit balance accounts (assets)
                if balance > 0:
                    # Debit the account to open it
                    JournalEntryLine.objects.create(
                        journal_entry=opening_entry,
                        account=account,
                        name=_('Opening balance for {}').format(account.name),
                        debit=balance,
                        credit=0,
                    )
                elif balance < 0:
                    # Credit the account to open it
                    JournalEntryLine.objects.create(
                        journal_entry=opening_entry,
                        account=account,
                        name=_('Opening balance for {}').format(account.name),
                        debit=0,
                        credit=abs(balance),
                    )
            else:
                # For credit balance accounts (liabilities, equity)
                if balance > 0:
                    # Credit the account to open it
                    JournalEntryLine.objects.create(
                        journal_entry=opening_entry,
                        account=account,
                        name=_('Opening balance for {}').format(account.name),
                        debit=0,
                        credit=balance,
                    )
                elif balance < 0:
                    # Debit the account to open it
                    JournalEntryLine.objects.create(
                        journal_entry=opening_entry,
                        account=account,
                        name=_('Opening balance for {}').format(account.name),
                        debit=abs(balance),
                        credit=0,
                    )

    # Create retained earnings line for closing entry
    if total_debit > total_credit:
        # Credit retained earnings
        JournalEntryLine.objects.create(
            journal_entry=closing_entry,
            account=retained_earnings_account,
            name=_('Retained earnings for {}').format(fiscal_year.name),
            debit=0,
            credit=total_debit - total_credit,
        )
    elif total_credit > total_debit:
        # Debit retained earnings
        JournalEntryLine.objects.create(
            journal_entry=closing_entry,
            account=retained_earnings_account,
            name=_('Retained earnings for {}').format(fiscal_year.name),
            debit=total_credit - total_debit,
            credit=0,
        )

    # Create retained earnings line for opening entry
    retained_earnings_balance = 0
    try:
        retained_earnings_balance_obj = AccountBalance.objects.get(
            account=retained_earnings_account,
            branch=branch
        )
        retained_earnings_balance = retained_earnings_balance_obj.closing_balance
    except AccountBalance.DoesNotExist:
        pass

    # Add the difference from closing entry
    if total_debit > total_credit:
        retained_earnings_balance += (total_debit - total_credit)
    elif total_credit > total_debit:
        retained_earnings_balance -= (total_credit - total_debit)

    # Create opening journal entry line for retained earnings
    if retained_earnings_balance > 0:
        # Credit retained earnings
        JournalEntryLine.objects.create(
            journal_entry=opening_entry,
            account=retained_earnings_account,
            name=_('Opening retained earnings for {}').format(next_fiscal_year.name),
            debit=0,
            credit=retained_earnings_balance,
        )
    elif retained_earnings_balance < 0:
        # Debit retained earnings
        JournalEntryLine.objects.create(
            journal_entry=opening_entry,
            account=retained_earnings_account,
            name=_('Opening retained earnings for {}').format(next_fiscal_year.name),
            debit=abs(retained_earnings_balance),
            credit=0,
        )

    # Close fiscal year
    fiscal_year.state = 'closed'
    fiscal_year.closed_by = request.user
    fiscal_year.closed_date = timezone.now()
    fiscal_year.save()

    messages.success(request, _('Fiscal year closed successfully. Closing balances transferred to next fiscal year.'))
    return redirect('accounting:fiscal_year_detail', fiscal_year_id=fiscal_year.id)

# Cash Register views
@login_required
def cash_register_list(request):
    """Cash register list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    location = request.GET.get('location')
    currency = request.GET.get('currency')
    is_active = request.GET.get('is_active')

    # Get cash registers
    cash_registers = CashRegister.objects.filter(
        company=company,
        branch=branch
    ).select_related('account')

    # Apply filters
    # Location filter removed as the field doesn't exist in the model

    if currency:
        cash_registers = cash_registers.filter(currency=currency)

    if is_active is not None and is_active != '':
        is_active_bool = is_active == '1'
        cash_registers = cash_registers.filter(is_active=is_active_bool)

    # Order by code
    cash_registers = cash_registers.order_by('code')

    context = {
        'cash_registers': cash_registers,
        'selected_location': location,
        'selected_currency': currency,
        'selected_is_active': is_active,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/cash_register_list.html', context)

@login_required
def cash_register_create(request):
    """Create a new cash register."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    initial_data = {'company': company, 'branch': branch, 'is_active': True}
    form = CashRegisterForm(request.POST or None, initial=initial_data)

    # Hide company and branch fields and set them automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['branch'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].initial = branch

    # Filter account choices to asset accounts that allow manual transactions
    form.fields['account'].queryset = Account.objects.filter(
        chart__company=company,
        type__category='asset',
        allow_manual_transactions=True,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            cash_register = form.save(commit=False)
            cash_register.created_by = request.user
            cash_register.save()
            messages.success(request, _('Cash register created successfully.'))
            return redirect('accounting:cash_register_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/cash_register_form.html', context)

@login_required
def cash_register_detail(request, cash_register_id):
    """View cash register details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get cash register
    try:
        cash_register = CashRegister.objects.select_related('account').get(
            id=cash_register_id,
            company=company
        )
    except CashRegister.DoesNotExist:
        messages.error(request, _('Cash register not found.'))
        return redirect('accounting:cash_register_list')

    # Get related journal entries
    journal_entries = JournalEntry.objects.filter(
        lines__account=cash_register.account
    ).distinct().order_by('-date')[:10]  # Get the 10 most recent entries

    context = {
        'cash_register': cash_register,
        'journal_entries': journal_entries,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/cash_register_detail.html', context)

@login_required
def cash_register_edit(request, cash_register_id):
    """Edit an existing cash register."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get cash register
    try:
        cash_register = CashRegister.objects.select_related('account').get(
            id=cash_register_id,
            company=company
        )
    except CashRegister.DoesNotExist:
        messages.error(request, _('Cash register not found.'))
        return redirect('accounting:cash_register_list')

    # Create form instance
    form = CashRegisterForm(request.POST or None, instance=cash_register)

    # Hide company and branch fields and set them automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['branch'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].initial = branch

    # Filter account choices to asset accounts that allow manual transactions
    form.fields['account'].queryset = Account.objects.filter(
        chart__company=company,
        type__category='asset',
        allow_manual_transactions=True,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Cash register updated successfully.'))
            return redirect('accounting:cash_register_detail', cash_register_id=cash_register.id)

    context = {
        'form': form,
        'cash_register': cash_register,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/cash_register_form.html', context)

# Bank views
@login_required
def bank_list(request):
    """List all banks."""
    # Get all banks
    banks = Bank.objects.all().order_by('name')

    # Filter by search query if provided
    search_query = request.GET.get('search')
    if search_query:
        from django.db.models import Q
        banks = banks.filter(
            Q(name__icontains=search_query) |
            Q(code__icontains=search_query) |
            Q(swift_code__icontains=search_query)
        )

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter == 'active':
        banks = banks.filter(is_active=True)
    elif status_filter == 'inactive':
        banks = banks.filter(is_active=False)

    context = {
        'banks': banks,
        'search_query': search_query,
        'status_filter': status_filter,
    }

    return render(request, 'modules/accounting/bank_list.html', context)

@login_required
def bank_create(request):
    """Create a new bank."""
    form = BankForm(request.POST or None)

    if request.method == 'POST':
        if form.is_valid():
            bank = form.save()
            messages.success(request, _('Bank created successfully.'))
            return redirect('accounting:bank_detail', bank_id=bank.id)

    context = {
        'form': form,
        'is_edit': False,
    }

    return render(request, 'modules/accounting/bank_form.html', context)

@login_required
def bank_detail(request, bank_id):
    """Bank detail view."""
    try:
        bank = Bank.objects.get(id=bank_id)
    except Bank.DoesNotExist:
        messages.error(request, _('Bank not found.'))
        return redirect('accounting:bank_list')

    # Get bank accounts for this bank
    bank_accounts = BankAccount.objects.filter(bank=bank).select_related('company', 'branch', 'account')

    context = {
        'bank': bank,
        'bank_accounts': bank_accounts,
    }

    return render(request, 'modules/accounting/bank_detail.html', context)

@login_required
def bank_edit(request, bank_id):
    """Edit an existing bank."""
    try:
        bank = Bank.objects.get(id=bank_id)
    except Bank.DoesNotExist:
        messages.error(request, _('Bank not found.'))
        return redirect('accounting:bank_list')

    form = BankForm(request.POST or None, instance=bank)

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Bank updated successfully.'))
            return redirect('accounting:bank_detail', bank_id=bank.id)

    context = {
        'form': form,
        'bank': bank,
        'is_edit': True,
    }

    return render(request, 'modules/accounting/bank_form.html', context)

# Bank Account views
@login_required
def bank_account_list(request):
    """Bank account list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    bank_id = request.GET.get('bank')
    account_type = request.GET.get('account_type')
    currency = request.GET.get('currency')
    is_active = request.GET.get('is_active')

    # Get banks
    banks = Bank.objects.filter(is_active=True)

    # Get bank accounts
    bank_accounts = BankAccount.objects.filter(
        company=company,
        branch=branch
    ).select_related('bank', 'account')

    # Apply filters
    if bank_id:
        bank_accounts = bank_accounts.filter(bank_id=bank_id)

    if account_type:
        bank_accounts = bank_accounts.filter(account_type=account_type)

    if currency:
        bank_accounts = bank_accounts.filter(currency=currency)

    if is_active is not None and is_active != '':
        is_active_bool = is_active == '1'
        bank_accounts = bank_accounts.filter(is_active=is_active_bool)

    # Order by bank name and account number
    bank_accounts = bank_accounts.order_by('bank__name', 'number')

    context = {
        'bank_accounts': bank_accounts,
        'banks': banks,
        'selected_bank': bank_id,
        'selected_account_type': account_type,
        'selected_currency': currency,
        'selected_is_active': is_active,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/bank_account_list.html', context)

@login_required
def bank_account_create(request):
    """Create a new bank account."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    initial_data = {'company': company, 'branch': branch, 'is_active': True}
    form = BankAccountForm(request.POST or None, initial=initial_data)

    # Hide company and branch fields and set them automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['branch'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].initial = branch

    # Filter bank choices to active banks
    form.fields['bank'].queryset = Bank.objects.filter(is_active=True)

    # Filter account choices to asset accounts that allow manual transactions
    form.fields['account'].queryset = Account.objects.filter(
        chart__company=company,
        type__category='asset',
        allow_manual_transactions=True,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            bank_account = form.save(commit=False)
            bank_account.created_by = request.user
            bank_account.save()
            messages.success(request, _('Bank account created successfully.'))
            return redirect('accounting:bank_account_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/bank_account_form.html', context)

@login_required
def bank_account_detail(request, bank_account_id):
    """View bank account details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get bank account
    try:
        bank_account = BankAccount.objects.get(id=bank_account_id, company=company)
    except BankAccount.DoesNotExist:
        messages.error(request, _('Bank account not found.'))
        return redirect('accounting:bank_account_list')

    context = {
        'bank_account': bank_account,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/bank_account_detail.html', context)

@login_required
def bank_account_edit(request, bank_account_id):
    """Edit an existing bank account."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get bank account
    try:
        bank_account = BankAccount.objects.get(id=bank_account_id, company=company)
    except BankAccount.DoesNotExist:
        messages.error(request, _('Bank account not found.'))
        return redirect('accounting:bank_account_list')

    # Create form instance
    form = BankAccountForm(request.POST or None, instance=bank_account)

    # Hide company and branch fields
    form.fields['company'].widget = HiddenInput()
    form.fields['branch'].widget = HiddenInput()

    # Filter bank choices to active banks
    form.fields['bank'].queryset = Bank.objects.filter(is_active=True)

    # Filter account choices to asset accounts that allow manual transactions
    form.fields['account'].queryset = Account.objects.filter(
        chart__company=company,
        type__category='asset',
        allow_manual_transactions=True,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Bank account updated successfully.'))
            return redirect('accounting:bank_account_list')

    context = {
        'form': form,
        'bank_account': bank_account,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/bank_account_form.html', context)

# Check Book views
@login_required
def check_book_list(request):
    """List all check books."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    search_name = request.GET.get('name')
    bank_account_id = request.GET.get('bank_account')
    is_active = request.GET.get('is_active')

    # Get check books for the current company
    check_books = CheckBook.objects.filter(
        bank_account__company=company
    ).select_related('bank_account', 'bank_account__bank')

    # Apply filters
    if search_name:
        check_books = check_books.filter(
            Q(name__icontains=search_name) |
            Q(code__icontains=search_name)
        )

    if bank_account_id:
        check_books = check_books.filter(bank_account_id=bank_account_id)

    if is_active is not None and is_active != '':
        is_active_bool = is_active == '1'
        check_books = check_books.filter(is_active=is_active_bool)

    # Order by bank account and name
    check_books = check_books.order_by('bank_account__name', 'name')

    # Get all bank accounts for filter dropdown
    bank_accounts = BankAccount.objects.filter(company=company, is_active=True)

    context = {
        'check_books': check_books,
        'bank_accounts': bank_accounts,
        'search_name': search_name,
        'selected_bank_account_id': bank_account_id,
        'selected_is_active': is_active,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_book_list.html', context)

@login_required
def check_book_create(request):
    """Create a new check book."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get bank account ID from query parameters (if coming from bank account detail page)
    bank_account_id = request.GET.get('bank_account')
    bank_account = None

    if bank_account_id:
        try:
            bank_account = BankAccount.objects.get(id=bank_account_id, company=company)
        except BankAccount.DoesNotExist:
            pass

    # Create form instance
    initial_data = {'is_active': True}
    if bank_account:
        initial_data['bank_account'] = bank_account

    form = CheckBookForm(request.POST or None, initial=initial_data)

    # Filter bank account choices to active bank accounts for this company
    form.fields['bank_account'].queryset = BankAccount.objects.filter(
        company=company,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            try:
                check_book = form.save(commit=False)
                check_book.created_by = request.user

                # Calculate initial values for used_count and remaining_count
                if check_book.start_number and check_book.end_number:
                    # Extract numeric parts
                    start_numeric = ''.join(filter(str.isdigit, check_book.start_number))
                    end_numeric = ''.join(filter(str.isdigit, check_book.end_number))

                    try:
                        start_num = int(start_numeric)
                        end_num = int(end_numeric)
                        check_book.remaining_count = end_num - start_num + 1
                    except ValueError:
                        # Will be caught in clean() method
                        pass

                check_book.save()
                messages.success(request, _('Check book created successfully.'))
                return redirect('accounting:check_book_list')
            except ValidationError as e:
                for error in e.messages:
                    messages.error(request, error)

    context = {
        'form': form,
        'bank_account': bank_account,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_book_form.html', context)

@login_required
def check_book_detail(request, check_book_id):
    """View check book details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get check book
    try:
        check_book = CheckBook.objects.select_related('bank_account', 'bank_account__bank').get(
            id=check_book_id,
            bank_account__company=company
        )
    except CheckBook.DoesNotExist:
        messages.error(request, _('Check book not found.'))
        return redirect('accounting:check_book_list')

    # Get checks for this check book
    checks = Check.objects.filter(check_book=check_book).order_by('number')

    context = {
        'check_book': check_book,
        'checks': checks,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_book_detail.html', context)

@login_required
def check_book_edit(request, check_book_id):
    """Edit an existing check book."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get check book
    try:
        check_book = CheckBook.objects.select_related('bank_account').get(
            id=check_book_id,
            bank_account__company=company
        )
    except CheckBook.DoesNotExist:
        messages.error(request, _('Check book not found.'))
        return redirect('accounting:check_book_list')

    # Create form instance
    form = CheckBookForm(request.POST or None, instance=check_book)

    # Filter bank account choices to active bank accounts for this company
    form.fields['bank_account'].queryset = BankAccount.objects.filter(
        company=company,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Check book updated successfully.'))
            return redirect('accounting:check_book_detail', check_book_id=check_book.id)

    context = {
        'form': form,
        'check_book': check_book,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_book_form.html', context)

# Check views
@login_required
def check_list(request):
    """Check list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    check_type = request.GET.get('check_type')
    state = request.GET.get('state')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # Get checks
    checks = Check.objects.filter(
        company=company,
        branch=branch
    ).select_related('bank_account')

    # Apply filters
    if check_type:
        checks = checks.filter(check_type=check_type)

    if state:
        checks = checks.filter(state=state)

    if date_from:
        checks = checks.filter(issue_date__gte=date_from)

    if date_to:
        checks = checks.filter(issue_date__lte=date_to)

    # Order by due date and number
    checks = checks.order_by('due_date', 'number')

    context = {
        'checks': checks,
        'selected_type': check_type,
        'selected_state': state,
        'date_from': date_from,
        'date_to': date_to,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_list.html', context)

@login_required
def check_create(request):
    """Create a new check."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get check book ID from query parameters (if coming from check book detail page)
    check_book_id = request.GET.get('check_book')
    check_book = None

    if check_book_id:
        try:
            check_book = CheckBook.objects.get(id=check_book_id, bank_account__company=company)
        except CheckBook.DoesNotExist:
            pass

    # Create form instance
    initial_data = {
        'company': company,
        'branch': branch,
        'date': timezone.now().date(),
        'due_date': timezone.now().date(),
        'check_type': 'outgoing',
        'state': 'draft'
    }

    if check_book:
        initial_data['check_book'] = check_book
        initial_data['bank_account'] = check_book.bank_account
        # Use the get_next_check_number method to get and update the next number
        initial_data['number'] = check_book.next_number

    form = CheckForm(request.POST or None, request.FILES or None, initial=initial_data)

    # Hide company and branch fields and set them automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['branch'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].initial = branch

    # Filter bank account choices to active bank accounts for this company
    form.fields['bank_account'].queryset = BankAccount.objects.filter(
        company=company,
        is_active=True
    )

    # Filter check book choices based on selected bank account
    if check_book:
        form.fields['check_book'].queryset = CheckBook.objects.filter(
            bank_account=check_book.bank_account,
            is_active=True
        )
    else:
        form.fields['check_book'].queryset = CheckBook.objects.filter(
            bank_account__company=company,
            is_active=True
        )

    if request.method == 'POST':
        if form.is_valid():
            check = form.save(commit=False)
            check.created_by = request.user

            # Update check book next number if this is an outgoing check
            if check.check_type == 'outgoing' and check.check_book:
                # Use the get_next_check_number method to update the check book
                check.check_book.get_next_check_number()

            check.save()
            messages.success(request, _('Check created successfully.'))
            return redirect('accounting:check_detail', check_id=check.id)

    context = {
        'form': form,
        'check_book': check_book,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_form.html', context)

@login_required
def check_detail(request, check_id):
    """View check details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get check
    try:
        check = Check.objects.select_related('check_book', 'bank_account', 'bank_account__bank').get(
            id=check_id,
            company=company
        )
    except Check.DoesNotExist:
        messages.error(request, _('Check not found.'))
        return redirect('accounting:check_list')

    # Get related journal entries
    journal_entries = JournalEntry.objects.filter(
        Q(check_payment=check) | Q(check_collection=check) | Q(check_deposit=check) | Q(check_bounce=check)
    ).order_by('-date')

    context = {
        'check': check,
        'journal_entries': journal_entries,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_detail.html', context)

@login_required
def check_edit(request, check_id):
    """Edit an existing check."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get check
    try:
        check = Check.objects.select_related('check_book', 'bank_account').get(
            id=check_id,
            company=company
        )
    except Check.DoesNotExist:
        messages.error(request, _('Check not found.'))
        return redirect('accounting:check_list')

    # Only draft checks can be edited
    if check.state != 'draft':
        messages.error(request, _('Only draft checks can be edited.'))
        return redirect('accounting:check_detail', check_id=check.id)

    # Create form instance
    form = CheckForm(request.POST or None, request.FILES or None, instance=check)

    # Hide company and branch fields and set them automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['branch'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].initial = branch

    # Filter bank account choices to active bank accounts for this company
    form.fields['bank_account'].queryset = BankAccount.objects.filter(
        company=company,
        is_active=True
    )

    # Filter check book choices based on selected bank account
    form.fields['check_book'].queryset = CheckBook.objects.filter(
        bank_account=check.bank_account,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Check updated successfully.'))
            return redirect('accounting:check_detail', check_id=check.id)

    context = {
        'form': form,
        'check': check,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/check_form.html', context)

@login_required
def check_change_state(request, check_id):
    """Change the state of a check."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get check
    try:
        check = Check.objects.select_related('check_book', 'bank_account').get(
            id=check_id,
            company=company
        )
    except Check.DoesNotExist:
        messages.error(request, _('Check not found.'))
        return redirect('accounting:check_list')

    # Get new state from query parameters
    new_state = request.GET.get('state')
    if not new_state:
        messages.error(request, _('No state specified.'))
        return redirect('accounting:check_detail', check_id=check.id)

    # Validate state transition
    valid_transitions = {
        'draft': ['registered', 'cancelled'],
        'registered': ['collected', 'deposited', 'bounced', 'cancelled'],
        'pending': ['collected', 'deposited', 'bounced', 'cancelled'],
        'collected': [],
        'deposited': [],
        'bounced': [],
        'cancelled': []
    }

    if new_state not in valid_transitions.get(check.state, []):
        messages.error(request, _('Invalid state transition from {} to {}.').format(check.state, new_state))
        return redirect('accounting:check_detail', check_id=check.id)

    # Update check state
    check.state = new_state
    check.save()

    # Create journal entry for state change if needed
    if new_state in ['collected', 'deposited', 'bounced']:
        # Get fiscal year
        fiscal_year = FiscalYear.objects.filter(
            company=company,
            start_date__lte=timezone.now().date(),
            end_date__gte=timezone.now().date(),
            is_active=True
        ).first()

        if not fiscal_year:
            messages.error(request, _('No active fiscal year found for the current date.'))
            return redirect('accounting:check_detail', check_id=check.id)

        # Get journal
        journal = Journal.objects.filter(
            company=company,
            type='bank',
            is_active=True
        ).first()

        if not journal:
            messages.error(request, _('No active bank journal found.'))
            return redirect('accounting:check_detail', check_id=check.id)

        # Create journal entry
        journal_entry = JournalEntry.objects.create(
            company=company,
            branch=branch,
            journal=journal,
            fiscal_year=fiscal_year,
            date=timezone.now().date(),
            reference=f"Check {check.number} - {new_state.capitalize()}",
            name=f"Check {check.number} - {new_state.capitalize()}",
            state='draft',
            created_by=request.user
        )

        # Set the appropriate check field in the journal entry
        if new_state == 'collected':
            journal_entry.check_collection = check
        elif new_state == 'deposited':
            journal_entry.check_deposit = check
        elif new_state == 'bounced':
            journal_entry.check_bounce = check
        journal_entry.save()

        # Create journal entry lines based on check type and new state
        if check.check_type == 'outgoing':
            if new_state == 'collected':
                # Debit: Bank Account
                JournalEntryLine.objects.create(
                    entry=journal_entry,
                    account=check.bank_account.account,
                    name=f"Check {check.number} - Collected",
                    debit=check.amount,
                    credit=0,
                    partner_type=check.partner_type,
                    partner_name=check.partner_name
                )
                # Credit: Payable Account
                payable_account = Account.objects.filter(
                    chart__company=company,
                    type__category='liability',
                    is_active=True
                ).first()
                if payable_account:
                    JournalEntryLine.objects.create(
                        entry=journal_entry,
                        account=payable_account,
                        name=f"Check {check.number} - Collected",
                        debit=0,
                        credit=check.amount,
                        partner_type=check.partner_type,
                        partner_name=check.partner_name
                    )
            elif new_state == 'bounced':
                # Debit: Payable Account
                payable_account = Account.objects.filter(
                    chart__company=company,
                    type__category='liability',
                    is_active=True
                ).first()
                if payable_account:
                    JournalEntryLine.objects.create(
                        entry=journal_entry,
                        account=payable_account,
                        name=f"Check {check.number} - Bounced",
                        debit=check.amount,
                        credit=0,
                        partner_type=check.partner_type,
                        partner_name=check.partner_name
                    )
                # Credit: Bank Account
                JournalEntryLine.objects.create(
                    entry=journal_entry,
                    account=check.bank_account.account,
                    name=f"Check {check.number} - Bounced",
                    debit=0,
                    credit=check.amount,
                    partner_type=check.partner_type,
                    partner_name=check.partner_name
                )
        else:  # incoming check
            if new_state == 'deposited':
                # Debit: Bank Account
                JournalEntryLine.objects.create(
                    entry=journal_entry,
                    account=check.bank_account.account,
                    name=f"Check {check.number} - Deposited",
                    debit=check.amount,
                    credit=0,
                    partner_type=check.partner_type,
                    partner_name=check.partner_name
                )
                # Credit: Receivable Account
                receivable_account = Account.objects.filter(
                    chart__company=company,
                    type__category='asset',
                    is_active=True
                ).first()
                if receivable_account:
                    JournalEntryLine.objects.create(
                        entry=journal_entry,
                        account=receivable_account,
                        name=f"Check {check.number} - Deposited",
                        debit=0,
                        credit=check.amount,
                        partner_type=check.partner_type,
                        partner_name=check.partner_name
                    )
            elif new_state == 'bounced':
                # Debit: Receivable Account
                receivable_account = Account.objects.filter(
                    chart__company=company,
                    type__category='asset',
                    is_active=True
                ).first()
                if receivable_account:
                    JournalEntryLine.objects.create(
                        entry=journal_entry,
                        account=receivable_account,
                        name=f"Check {check.number} - Bounced",
                        debit=check.amount,
                        credit=0,
                        partner_type=check.partner_type,
                        partner_name=check.partner_name
                    )
                # Credit: Bank Account
                JournalEntryLine.objects.create(
                    entry=journal_entry,
                    account=check.bank_account.account,
                    name=f"Check {check.number} - Bounced",
                    debit=0,
                    credit=check.amount,
                    partner_type=check.partner_type,
                    partner_name=check.partner_name
                )

        # Post journal entry
        journal_entry.state = 'posted'
        journal_entry.posted_by = request.user
        journal_entry.posted_at = timezone.now()
        journal_entry.save()

        messages.success(request, _('Check state changed to {} and journal entry created.').format(new_state))
    else:
        messages.success(request, _('Check state changed to {}.').format(new_state))

    return redirect('accounting:check_detail', check_id=check.id)

# Journal views
@login_required
def journal_list(request):
    """List all journals."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    search_name = request.GET.get('name', '')
    selected_type = request.GET.get('type', '')
    selected_is_active = request.GET.get('is_active', '')

    # Build query
    query = Q(company=company)

    if search_name:
        query &= Q(name__icontains=search_name) | Q(code__icontains=search_name)

    if selected_type:
        query &= Q(type=selected_type)

    if selected_is_active == '1':
        query &= Q(is_active=True)
    elif selected_is_active == '0':
        query &= Q(is_active=False)

    # Get journals
    journals = Journal.objects.filter(query).select_related('default_debit_account', 'default_credit_account').order_by('name')

    context = {
        'journals': journals,
        'company': company,
        'branch': branch,
        'search_name': search_name,
        'selected_type': selected_type,
        'selected_is_active': selected_is_active,
        'journal_types': Journal.JOURNAL_TYPES,
    }

    return render(request, 'modules/accounting/journal_list.html', context)

@login_required
def journal_create(request):
    """Create a new journal."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    initial_data = {'company': company, 'is_active': True, 'auto_numbering': True}
    form = JournalForm(request.POST or None, initial=initial_data)

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    # Filter account choices to accounts that allow manual transactions
    active_accounts = Account.objects.filter(
        chart__company=company,
        allow_manual_transactions=True,
        is_active=True
    )

    form.fields['default_debit_account'].queryset = active_accounts
    form.fields['default_credit_account'].queryset = active_accounts

    if request.method == 'POST':
        if form.is_valid():
            journal = form.save(commit=False)
            journal.created_by = request.user
            journal.save()
            messages.success(request, _('Journal created successfully.'))
            return redirect('accounting:journal_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_form.html', context)

@login_required
def journal_detail(request, journal_id):
    """View journal details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get journal
    try:
        journal = Journal.objects.select_related('default_debit_account', 'default_credit_account').get(
            id=journal_id,
            company=company
        )
    except Journal.DoesNotExist:
        messages.error(request, _('Journal not found.'))
        return redirect('accounting:journal_list')

    # Get related journal entries
    journal_entries = JournalEntry.objects.filter(
        journal=journal
    ).order_by('-date')[:10]  # Get the 10 most recent entries

    context = {
        'journal': journal,
        'journal_entries': journal_entries,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_detail.html', context)

@login_required
def journal_edit(request, journal_id):
    """Edit an existing journal."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get journal
    try:
        journal = Journal.objects.select_related('account').get(
            id=journal_id,
            company=company
        )
    except Journal.DoesNotExist:
        messages.error(request, _('Journal not found.'))
        return redirect('accounting:journal_list')

    # Create form instance
    form = JournalForm(request.POST or None, instance=journal)

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    # Filter account choices to accounts that allow manual transactions
    active_accounts = Account.objects.filter(
        chart__company=company,
        allow_manual_transactions=True,
        is_active=True
    )

    form.fields['default_debit_account'].queryset = active_accounts
    form.fields['default_credit_account'].queryset = active_accounts

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Journal updated successfully.'))
            return redirect('accounting:journal_detail', journal_id=journal.id)

    context = {
        'form': form,
        'journal': journal,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_form.html', context)

# Journal Entry views
@login_required
def journal_entry_list(request):
    """Journal entry list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get filter parameters
    journal_id = request.GET.get('journal')
    state = request.GET.get('state')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # Get journals
    journals = Journal.objects.filter(company=company, is_active=True)

    # Get journal entries
    journal_entries = JournalEntry.objects.filter(
        company=company,
        branch=branch
    ).select_related('journal').prefetch_related('lines')

    # Apply filters
    if journal_id:
        journal_entries = journal_entries.filter(journal_id=journal_id)

    if state:
        journal_entries = journal_entries.filter(state=state)

    if date_from:
        journal_entries = journal_entries.filter(date__gte=date_from)

    if date_to:
        journal_entries = journal_entries.filter(date__lte=date_to)

    # Order by date and reference
    journal_entries = journal_entries.order_by('-date', '-reference')

    context = {
        'journal_entries': journal_entries,
        'journals': journals,
        'selected_journal': journal_id,
        'selected_state': state,
        'date_from': date_from,
        'date_to': date_to,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_entry_list.html', context)

@login_required
def journal_entry_create(request):
    """Create a new journal entry."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get initial data from query parameters
    journal_id = request.GET.get('journal')
    date = request.GET.get('date', timezone.now().date())
    reference = request.GET.get('reference')

    # Prepare initial data
    initial_data = {
        'date': date,
        'reference': reference,
        'state': 'draft',
        'company': company,
        'branch': branch,
    }

    # Get journal if provided
    if journal_id:
        try:
            journal = Journal.objects.get(id=journal_id, company=company)
            initial_data['journal'] = journal
        except (Journal.DoesNotExist, ValueError, TypeError):
            pass

    # Create form instance
    form = JournalEntryForm(request.POST or None, initial=initial_data)

    # Filter journal choices to company's journals
    form.fields['journal'].queryset = Journal.objects.filter(company=company, is_active=True)

    # Create formset instance
    JournalEntryLineFormSet = inlineformset_factory(
        JournalEntry, JournalEntryLine,
        form=JournalEntryLineForm,
        extra=2, can_delete=True, min_num=1, validate_min=True
    )
    formset = JournalEntryLineFormSet(request.POST or None, instance=JournalEntry())

    # Filter account choices in formset
    for line_form in formset:
        line_form.fields['account'].queryset = Account.objects.filter(
            chart__company=company,
            is_active=True,
            allow_manual_transactions=True
        ).select_related('type')

    if request.method == 'POST':
        if form.is_valid() and formset.is_valid():
            # Save journal entry
            journal_entry = form.save(commit=False)
            journal_entry.company = company
            journal_entry.branch = branch
            journal_entry.created_by = request.user
            journal_entry.save()

            # Save journal entry lines
            formset.instance = journal_entry
            formset.save()

            messages.success(request, _('Journal entry created successfully.'))
            return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

    context = {
        'form': form,
        'formset': formset,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_entry_form.html', context)

@login_required
def journal_entry_detail(request, journal_entry_id):
    """View journal entry details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get journal entry
    try:
        journal_entry = JournalEntry.objects.get(id=journal_entry_id, company=company)
    except JournalEntry.DoesNotExist:
        messages.error(request, _('Journal entry not found.'))
        return redirect('accounting:journal_entry_list')

    # Get journal entry lines
    journal_entry_lines = journal_entry.lines.all().select_related('account')

    context = {
        'journal_entry': journal_entry,
        'journal_entry_lines': journal_entry_lines,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_entry_detail.html', context)

@login_required
def journal_entry_edit(request, journal_entry_id):
    """Edit an existing journal entry."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get journal entry
    try:
        journal_entry = JournalEntry.objects.get(id=journal_entry_id, company=company)
    except JournalEntry.DoesNotExist:
        messages.error(request, _('Journal entry not found.'))
        return redirect('accounting:journal_entry_list')

    # Check if journal entry is in draft state
    if journal_entry.state != 'draft':
        messages.error(request, _('Only draft journal entries can be edited.'))
        return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

    # Create form instance
    form = JournalEntryForm(request.POST or None, instance=journal_entry)

    # Filter journal choices to company's journals
    form.fields['journal'].queryset = Journal.objects.filter(company=company, is_active=True)

    # Create formset instance
    JournalEntryLineFormSet = inlineformset_factory(
        JournalEntry, JournalEntryLine,
        form=JournalEntryLineForm,
        extra=1, can_delete=True, min_num=1, validate_min=True
    )
    formset = JournalEntryLineFormSet(request.POST or None, instance=journal_entry)

    # Filter account choices in formset
    for line_form in formset:
        line_form.fields['account'].queryset = Account.objects.filter(
            chart__company=company,
            is_active=True,
            allow_manual_transactions=True
        ).select_related('type')

    if request.method == 'POST':
        if form.is_valid() and formset.is_valid():
            # Save journal entry
            journal_entry = form.save(commit=False)
            journal_entry.updated_by = request.user
            journal_entry.save()

            # Save journal entry lines
            formset.save()

            messages.success(request, _('Journal entry updated successfully.'))
            return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

    context = {
        'form': form,
        'formset': formset,
        'journal_entry': journal_entry,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/journal_entry_form.html', context)

@login_required
def journal_entry_post(request, journal_entry_id):
    """Post a journal entry."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get journal entry
    try:
        journal_entry = JournalEntry.objects.get(id=journal_entry_id, company=company)
    except JournalEntry.DoesNotExist:
        messages.error(request, _('Journal entry not found.'))
        return redirect('accounting:journal_entry_list')

    # Check if journal entry is in draft state
    if journal_entry.state != 'draft':
        messages.error(request, _('Only draft journal entries can be posted.'))
        return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

    # Check if journal entry is balanced
    debit_sum = journal_entry.lines.aggregate(Sum('debit'))['debit__sum'] or 0
    credit_sum = journal_entry.lines.aggregate(Sum('credit'))['credit__sum'] or 0

    if abs(debit_sum - credit_sum) > 0.01:
        messages.error(request, _('Journal entry is not balanced. Debit: {}, Credit: {}.').format(debit_sum, credit_sum))
        return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

    # Post journal entry
    journal_entry.state = 'posted'
    journal_entry.posted_by = request.user
    journal_entry.posted_date = timezone.now()
    journal_entry.save()

    # Update account balances
    for line in journal_entry.lines.all():
        # Get or create account balance
        account_balance, created = AccountBalance.objects.get_or_create(
            account=line.account,
            branch=branch,
            defaults={
                'opening_balance': 0,
                'closing_balance': 0,
            }
        )

        # Update balance based on account type
        if line.account.type.is_debit_balance:
            # For debit balance accounts (assets, expenses)
            account_balance.closing_balance += line.debit - line.credit
        else:
            # For credit balance accounts (liabilities, equity, revenue)
            account_balance.closing_balance += line.credit - line.debit

        account_balance.save()

    messages.success(request, _('Journal entry posted successfully.'))
    return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

@login_required
def journal_entry_cancel(request, journal_entry_id):
    """Cancel a journal entry."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get journal entry
    try:
        journal_entry = JournalEntry.objects.get(id=journal_entry_id, company=company)
    except JournalEntry.DoesNotExist:
        messages.error(request, _('Journal entry not found.'))
        return redirect('accounting:journal_entry_list')

    # Check if journal entry is in posted state
    if journal_entry.state != 'posted':
        messages.error(request, _('Only posted journal entries can be cancelled.'))
        return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

    # Create reversal journal entry
    reversal_entry = JournalEntry.objects.create(
        name=_('Reversal of {}').format(journal_entry.name),
        date=timezone.now().date(),
        journal=journal_entry.journal,
        reference=_('REV-{}').format(journal_entry.reference),
        state='posted',
        company=company,
        branch=branch,
        fiscal_year=journal_entry.fiscal_year,
        memo=_('Reversal entry for {}').format(journal_entry.reference),
        created_by=request.user,
        posted_by=request.user,
        posted_date=timezone.now(),
        reversed_entry=journal_entry,
    )

    # Create reversal lines
    for line in journal_entry.lines.all():
        JournalEntryLine.objects.create(
            journal_entry=reversal_entry,
            account=line.account,
            name=_('Reversal of {}').format(line.name or ''),
            debit=line.credit,
            credit=line.debit,
            partner_name=line.partner_name,
            partner_type=line.partner_type,
            partner_id=line.partner_id,
        )

    # Update account balances for reversal entry
    for line in reversal_entry.lines.all():
        # Get or create account balance
        account_balance, created = AccountBalance.objects.get_or_create(
            account=line.account,
            branch=branch,
            defaults={
                'opening_balance': 0,
                'closing_balance': 0,
            }
        )

        # Update balance based on account type
        if line.account.type.is_debit_balance:
            # For debit balance accounts (assets, expenses)
            account_balance.closing_balance += line.debit - line.credit
        else:
            # For credit balance accounts (liabilities, equity, revenue)
            account_balance.closing_balance += line.credit - line.debit

        account_balance.save()

    # Mark original entry as cancelled
    journal_entry.state = 'cancelled'
    journal_entry.cancelled_by = request.user
    journal_entry.cancelled_date = timezone.now()
    journal_entry.reversal_entry = reversal_entry
    journal_entry.save()

    messages.success(request, _('Journal entry cancelled successfully. Reversal entry created.'))
    return redirect('accounting:journal_entry_detail', journal_entry_id=journal_entry.id)

# Voucher views
@login_required
def voucher_list(request):
    """Voucher list view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get filter parameters
    voucher_type = request.GET.get('voucher_type')
    payment_method_id = request.GET.get('payment_method')
    state = request.GET.get('state')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')

    # Get payment methods
    payment_methods = PaymentMethod.objects.filter(company=company, is_active=True)

    # Get vouchers
    vouchers = Voucher.objects.filter(
        company=company,
        branch=branch
    ).select_related('payment_method')

    # Apply filters
    if voucher_type:
        vouchers = vouchers.filter(voucher_type=voucher_type)

    if payment_method_id:
        vouchers = vouchers.filter(payment_method_id=payment_method_id)

    if state:
        vouchers = vouchers.filter(state=state)

    if date_from:
        vouchers = vouchers.filter(date__gte=date_from)

    if date_to:
        vouchers = vouchers.filter(date__lte=date_to)

    # Order by date and reference
    vouchers = vouchers.order_by('-date', '-reference')

    context = {
        'vouchers': vouchers,
        'payment_methods': payment_methods,
        'selected_type': voucher_type,
        'selected_method': payment_method_id,
        'selected_state': state,
        'date_from': date_from,
        'date_to': date_to,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/voucher_list.html', context)

@login_required
def payment_voucher_list(request):
    """List payment vouchers only."""
    # Get current company and branch
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get filter parameters
    payment_method_id = request.GET.get('payment_method', '')
    state = request.GET.get('state', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Get payment vouchers for the current company
    vouchers = Voucher.objects.filter(
        company=company,
        voucher_type='payment'
    ).select_related('payment_method', 'branch')

    # Get payment methods for filter
    payment_methods = PaymentMethod.objects.filter(company=company).order_by('name')

    # Apply filters
    if payment_method_id:
        vouchers = vouchers.filter(payment_method_id=payment_method_id)

    if state:
        vouchers = vouchers.filter(state=state)

    if date_from:
        vouchers = vouchers.filter(date__gte=date_from)

    if date_to:
        vouchers = vouchers.filter(date__lte=date_to)

    # Order by date and reference
    vouchers = vouchers.order_by('-date', '-reference')

    context = {
        'vouchers': vouchers,
        'payment_methods': payment_methods,
        'selected_method': payment_method_id,
        'selected_state': state,
        'date_from': date_from,
        'date_to': date_to,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/payment_voucher_list.html', context)

@login_required
def receipt_voucher_list(request):
    """List receipt vouchers only."""
    # Get current company and branch
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get filter parameters
    payment_method_id = request.GET.get('payment_method', '')
    state = request.GET.get('state', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')

    # Get receipt vouchers for the current company
    vouchers = Voucher.objects.filter(
        company=company,
        voucher_type='receipt'
    ).select_related('payment_method', 'branch')

    # Get payment methods for filter
    payment_methods = PaymentMethod.objects.filter(company=company).order_by('name')

    # Apply filters
    if payment_method_id:
        vouchers = vouchers.filter(payment_method_id=payment_method_id)

    if state:
        vouchers = vouchers.filter(state=state)

    if date_from:
        vouchers = vouchers.filter(date__gte=date_from)

    if date_to:
        vouchers = vouchers.filter(date__lte=date_to)

    # Order by date and reference
    vouchers = vouchers.order_by('-date', '-reference')

    context = {
        'vouchers': vouchers,
        'payment_methods': payment_methods,
        'selected_method': payment_method_id,
        'selected_state': state,
        'date_from': date_from,
        'date_to': date_to,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/receipt_voucher_list.html', context)

def _get_company_branch(request):
    """Helper function to get current company and branch."""
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    return company, branch, None
                else:
                    return None, None, _('No branches found for the default company.')
            else:
                return None, None, _('No companies found in the system.')
        else:
            return None, None, _('Please select a company and branch first.')

    return company, branch, None

def _get_or_create_fiscal_year(request, company):
    """Helper function to get or create fiscal year."""
    # Get current fiscal year
    current_date = timezone.now().date()
    fiscal_year = FiscalYear.objects.filter(
        company=company,
        start_date__lte=current_date,
        end_date__gte=current_date,
        state='open'
    ).first()

    if not fiscal_year:
        # Check if there's a draft fiscal year that covers the current date
        draft_fiscal_year = FiscalYear.objects.filter(
            company=company,
            start_date__lte=current_date,
            end_date__gte=current_date,
            state='draft'
        ).first()

        if draft_fiscal_year:
            # Open the draft fiscal year
            draft_fiscal_year.state = 'open'
            draft_fiscal_year.save()
            fiscal_year = draft_fiscal_year
            messages.info(request, _('A draft fiscal year has been opened for the current date.'))
        else:
            # Create a new fiscal year for the current year
            year = current_date.year
            start_date = datetime.date(year, 1, 1)
            end_date = datetime.date(year, 12, 31)

            # Check if there's any overlapping fiscal year
            overlapping = FiscalYear.objects.filter(
                company=company,
                start_date__lte=end_date,
                end_date__gte=start_date
            ).exists()

            if not overlapping:
                new_fiscal_year = FiscalYear.objects.create(
                    name=f"Fiscal Year {year}",
                    code=f"FY{year}",
                    company=company,
                    start_date=start_date,
                    end_date=end_date,
                    state='open',
                    is_active=True,
                    created_by=request.user
                )
                fiscal_year = new_fiscal_year
                messages.info(request, _('A new fiscal year has been created and opened for the current date.'))
            else:
                return None, _('No open fiscal year found for the current date, and there are overlapping fiscal years that prevent automatic creation.')

    return fiscal_year, None

@login_required
def voucher_create(request):
    """Create a new voucher (redirects to specific type)."""
    voucher_type = request.GET.get('type', 'payment')
    if voucher_type == 'receipt':
        return redirect('accounting:receipt_voucher_create')
    else:
        return redirect('accounting:payment_voucher_create')

@login_required
def payment_voucher_create(request):
    """Create a new payment voucher."""
    # Get current company and branch
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get or create fiscal year
    fiscal_year, error_message = _get_or_create_fiscal_year(request, company)
    if error_message:
        messages.warning(request, error_message)
        return redirect('accounting:fiscal_year_list')

    # Create form instance
    initial_data = {
        'date': timezone.now().date(),
        'branch': branch,
        'company': company,
        'voucher_type': 'payment',  # Set default type to payment
    }

    form = VoucherForm(request.POST or None, initial=initial_data)

    # Import models
    from companies.models import Company, Branch

    # Hide company field and voucher type field (always payment)
    form.fields['company'] = forms.ModelChoiceField(
        queryset=Company.objects.filter(id=company.id),
        initial=company,
        widget=forms.HiddenInput()
    )
    form.fields['voucher_type'] = forms.ChoiceField(
        choices=Voucher.VOUCHER_TYPES,
        initial='payment',
        widget=forms.HiddenInput()
    )

    # Filter branch choices to current company
    form.fields['branch'].queryset = Branch.objects.filter(company=company)

    # Filter payment method choices to current company
    form.fields['payment_method'].queryset = PaymentMethod.objects.filter(company=company)

    # Filter check choices to current company
    form.fields['payment_check'].queryset = Check.objects.filter(bank_account__company=company)
    form.fields['payment_check'].required = False

    if request.method == 'POST':
        if form.is_valid():
            voucher = form.save(commit=False)
            voucher.company = company
            voucher.created_by = request.user
            voucher.voucher_type = 'payment'  # Ensure it's a payment voucher

            # Automatically set the active fiscal year
            voucher.fiscal_year = fiscal_year

            # Handle check payment
            payment_check_value = request.POST.get('payment_check', '')
            if payment_check_value and payment_check_value.startswith('new_'):
                # This is a new check to be created
                try:
                    # Parse the special ID format: new_checkbook_id_check_number
                    _, checkbook_id, check_number = payment_check_value.split('_', 2)
                    checkbook = CheckBook.objects.get(id=checkbook_id, bank_account__company=company)

                    # Create a new check
                    check = Check.objects.create(
                        number=check_number,
                        amount=voucher.amount,
                        date=voucher.date,
                        due_date=voucher.date,  # Set due date same as voucher date by default
                        check_type='outgoing',
                        state='draft',
                        check_book=checkbook,
                        bank_account=checkbook.bank_account,
                        company=company,
                        branch=voucher.branch,
                        partner_name=voucher.partner_name,
                        partner_type=voucher.partner_type,
                        partner_id=voucher.partner_id,
                        memo=voucher.memo,
                        created_by=request.user
                    )

                    # Update the checkbook
                    checkbook.get_next_check_number()

                    # Set the check on the voucher
                    voucher.payment_check = check
                except (ValueError, CheckBook.DoesNotExist) as e:
                    messages.error(request, _('Error creating check: {}').format(str(e)))
                    return redirect('accounting:payment_voucher_create')

            voucher.save()

            messages.success(request, _('Payment voucher created successfully.'))
            return redirect('accounting:voucher_detail', voucher_id=voucher.id)

    context = {
        'form': form,
        'company': company,
        'branch': branch,
        'voucher_type': 'payment',
    }

    return render(request, 'modules/accounting/payment_voucher_form.html', context)

@login_required
def receipt_voucher_create(request):
    """Create a new receipt voucher."""
    # Get current company and branch
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get or create fiscal year
    fiscal_year, error_message = _get_or_create_fiscal_year(request, company)
    if error_message:
        messages.warning(request, error_message)
        return redirect('accounting:fiscal_year_list')

    # Create form instance
    initial_data = {
        'date': timezone.now().date(),
        'branch': branch,
        'company': company,
        'voucher_type': 'receipt',  # Set default type to receipt
    }

    form = VoucherForm(request.POST or None, initial=initial_data)

    # Import models
    from companies.models import Company, Branch

    # Hide company field and voucher type field (always receipt)
    form.fields['company'] = forms.ModelChoiceField(
        queryset=Company.objects.filter(id=company.id),
        initial=company,
        widget=forms.HiddenInput()
    )
    form.fields['voucher_type'] = forms.ChoiceField(
        choices=Voucher.VOUCHER_TYPES,
        initial='receipt',
        widget=forms.HiddenInput()
    )

    # Filter branch choices to current company
    form.fields['branch'].queryset = Branch.objects.filter(company=company)

    # Filter payment method choices to current company
    form.fields['payment_method'].queryset = PaymentMethod.objects.filter(company=company)

    # Hide check field for receipt vouchers
    form.fields['payment_check'].widget = forms.HiddenInput()
    form.fields['payment_check'].required = False

    if request.method == 'POST':
        if form.is_valid():
            voucher = form.save(commit=False)
            voucher.company = company
            voucher.created_by = request.user
            voucher.voucher_type = 'receipt'  # Ensure it's a receipt voucher

            # Automatically set the active fiscal year
            voucher.fiscal_year = fiscal_year

            voucher.save()

            messages.success(request, _('Receipt voucher created successfully.'))
            return redirect('accounting:voucher_detail', voucher_id=voucher.id)

    context = {
        'form': form,
        'company': company,
        'branch': branch,
        'voucher_type': 'receipt',
    }

    return render(request, 'modules/accounting/receipt_voucher_form.html', context)

@login_required
def voucher_detail(request, voucher_id):
    """Detail view for a voucher."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get voucher
    voucher = get_object_or_404(Voucher, id=voucher_id, company=company)

    context = {
        'voucher': voucher,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/voucher_detail.html', context)

@login_required
def voucher_edit(request, voucher_id):
    """Edit a voucher."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get voucher
    voucher = get_object_or_404(Voucher, id=voucher_id, company=company)

    # Check if voucher is in draft state
    if voucher.state != 'draft':
        messages.warning(request, _('Only draft vouchers can be edited.'))
        return redirect('accounting:voucher_detail', voucher_id=voucher.id)

    # Create form instance
    form = VoucherForm(request.POST or None, instance=voucher)

    # Hide company field
    form.fields['company'] = forms.ModelChoiceField(
        queryset=Company.objects.filter(id=company.id),
        initial=company,
        widget=forms.HiddenInput()
    )

    # Filter branch choices to current company
    form.fields['branch'].queryset = Branch.objects.filter(company=company)

    # Filter payment method choices to current company
    form.fields['payment_method'].queryset = PaymentMethod.objects.filter(company=company)

    # Filter check choices to current company
    form.fields['payment_check'].queryset = Check.objects.filter(bank_account__company=company)
    form.fields['payment_check'].required = False

    if request.method == 'POST':
        if form.is_valid():
            voucher = form.save(commit=False)
            voucher.company = company

            # Keep the existing fiscal year
            voucher.fiscal_year = voucher.fiscal_year

            voucher.save()

            messages.success(request, _('Voucher updated successfully.'))
            return redirect('accounting:voucher_detail', voucher_id=voucher.id)

    context = {
        'form': form,
        'voucher': voucher,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/voucher_form.html', context)

@login_required
def voucher_post(request, voucher_id):
    """Post a voucher."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get voucher
    voucher = get_object_or_404(Voucher, id=voucher_id, company=company)

    # Check if voucher is in draft state
    if voucher.state != 'draft':
        messages.warning(request, _('Only draft vouchers can be posted.'))
        return redirect('accounting:voucher_detail', voucher_id=voucher.id)

    try:
        # Post voucher
        voucher.post(user=request.user)
        messages.success(request, _('Voucher posted successfully.'))
    except Exception as e:
        messages.error(request, str(e))

    return redirect('accounting:voucher_detail', voucher_id=voucher.id)

@login_required
def voucher_cancel(request, voucher_id):
    """Cancel a voucher."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get voucher
    voucher = get_object_or_404(Voucher, id=voucher_id, company=company)

    # Check if voucher is in posted state
    if voucher.state != 'posted':
        messages.warning(request, _('Only posted vouchers can be cancelled.'))
        return redirect('accounting:voucher_detail', voucher_id=voucher.id)

    try:
        # Cancel voucher
        voucher.cancel()
        messages.success(request, _('Voucher cancelled successfully.'))
    except Exception as e:
        messages.error(request, str(e))

    return redirect('accounting:voucher_detail', voucher_id=voucher.id)

# Asset views
@login_required
def asset_list(request):
    """قائمة الأصول."""
    # الحصول على الشركة والفرع الحالي
    from companies.models import Company, Branch

    # محاولة الحصول على الشركة والفرع النشط من الجلسة
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # إذا كان المستخدم مدير النظام وهناك شركات في النظام، استخدم الشركة الأولى
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # تعيين الشركة والفرع النشط في الجلسة
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # الحصول على معلمات التصفية
    search_name = request.GET.get('name', '')
    selected_category = request.GET.get('category', '')
    selected_state = request.GET.get('state', '')
    selected_is_active = request.GET.get('is_active', '')

    # بناء الاستعلام
    query = Q(company=company)

    if search_name:
        query &= Q(name__icontains=search_name) | Q(code__icontains=search_name)

    if selected_category:
        query &= Q(category_id=selected_category)

    if selected_state:
        query &= Q(state=selected_state)

    if selected_is_active == '1':
        query &= Q(is_active=True)
    elif selected_is_active == '0':
        query &= Q(is_active=False)

    # الحصول على الأصول
    assets = Asset.objects.filter(query).select_related('category', 'depreciation_method').order_by('name')

    # الحصول على فئات الأصول
    categories = AssetCategory.objects.filter(company=company, is_active=True)

    context = {
        'assets': assets,
        'categories': categories,
        'company': company,
        'branch': branch,
        'search_name': search_name,
        'selected_category': selected_category,
        'selected_state': selected_state,
        'selected_is_active': selected_is_active,
        'asset_states': Asset.STATES,
    }

    return render(request, 'modules/accounting/asset_list.html', context)

@login_required
def asset_create(request):
    """إنشاء أصل جديد."""
    # الحصول على الشركة والفرع الحالي
    from companies.models import Company, Branch

    # محاولة الحصول على الشركة والفرع النشط من الجلسة
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # إذا كان المستخدم مدير النظام وهناك شركات في النظام، استخدم الشركة الأولى
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # تعيين الشركة والفرع النشط في الجلسة
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # إنشاء نموذج جديد
    initial_data = {
        'company': company,
        'branch': branch,
        'is_active': True,
        'purchase_date': timezone.now().date(),
        'in_service_date': timezone.now().date(),
        'state': 'draft'
    }
    form = AssetForm(request.POST or None, initial=initial_data)

    # إخفاء حقول الشركة والفرع وتعيينها تلقائيًا
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].widget = HiddenInput()
    form.fields['branch'].initial = branch

    # تصفية خيارات فئة الأصل وطريقة الإهلاك
    form.fields['category'].queryset = AssetCategory.objects.filter(
        company=company,
        is_active=True
    )

    form.fields['depreciation_method'].queryset = DepreciationMethod.objects.filter(
        company=company,
        is_active=True
    )

    # تصفية خيارات الحسابات
    active_accounts = Account.objects.filter(
        chart__company=company,
        is_active=True
    )

    # Get account types for filtering
    asset_types = AccountType.objects.filter(company=company, category='asset')
    expense_types = AccountType.objects.filter(company=company, category='expense')

    form.fields['asset_account'].queryset = active_accounts.filter(type__in=asset_types)
    form.fields['depreciation_account'].queryset = active_accounts.filter(type__in=expense_types)
    form.fields['accumulated_depreciation_account'].queryset = active_accounts.filter(type__in=asset_types)

    if request.method == 'POST':
        if form.is_valid():
            asset = form.save(commit=False)
            asset.created_by = request.user
            asset.save()
            messages.success(request, _('Asset created successfully.'))
            return redirect('accounting:asset_detail', asset_id=asset.id)

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/asset_form.html', context)

@login_required
def asset_detail(request, asset_id):
    """Detail view for an asset."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get asset
    asset = get_object_or_404(Asset, id=asset_id, company=company)

    # Get depreciations
    depreciations = Depreciation.objects.filter(asset=asset).order_by('period')

    # Get depreciation board
    depreciation_board = asset.compute_depreciation_board()

    context = {
        'asset': asset,
        'depreciations': depreciations,
        'depreciation_board': depreciation_board,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/asset_detail.html', context)

@login_required
def asset_edit(request, asset_id):
    """تعديل أصل موجود."""
    # الحصول على الشركة والفرع الحالي
    from companies.models import Company, Branch

    # محاولة الحصول على الشركة والفرع النشط من الجلسة
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # إذا كان المستخدم مدير النظام وهناك شركات في النظام، استخدم الشركة الأولى
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # تعيين الشركة والفرع النشط في الجلسة
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # الحصول على الأصل
    try:
        asset = Asset.objects.select_related('category', 'depreciation_method').get(
            id=asset_id,
            company=company
        )
    except Asset.DoesNotExist:
        messages.error(request, _('Asset not found.'))
        return redirect('accounting:asset_list')

    # التحقق من حالة الأصل
    if asset.state != 'draft':
        messages.warning(request, _('Only assets in draft state can be edited.'))
        return redirect('accounting:asset_detail', asset_id=asset.id)

    # إنشاء نموذج
    form = AssetForm(request.POST or None, instance=asset)

    # إخفاء حقول الشركة والفرع وتعيينها تلقائيًا
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company
    form.fields['branch'].widget = HiddenInput()
    form.fields['branch'].initial = branch

    # تصفية خيارات فئة الأصل وطريقة الإهلاك
    form.fields['category'].queryset = AssetCategory.objects.filter(
        company=company,
        is_active=True
    )

    form.fields['depreciation_method'].queryset = DepreciationMethod.objects.filter(
        company=company,
        is_active=True
    )

    # تصفية خيارات الحسابات
    active_accounts = Account.objects.filter(
        chart__company=company,
        is_active=True
    )

    # Get account types for filtering
    asset_types = AccountType.objects.filter(company=company, category='asset')
    expense_types = AccountType.objects.filter(company=company, category='expense')

    form.fields['asset_account'].queryset = active_accounts.filter(type__in=asset_types)
    form.fields['depreciation_account'].queryset = active_accounts.filter(type__in=expense_types)
    form.fields['accumulated_depreciation_account'].queryset = active_accounts.filter(type__in=asset_types)

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Asset updated successfully.'))
            return redirect('accounting:asset_detail', asset_id=asset.id)

    context = {
        'form': form,
        'asset': asset,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/asset_form.html', context)

@login_required
def asset_depreciation_board(request, asset_id):
    """Depreciation board view for an asset."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get asset
    asset = get_object_or_404(Asset, id=asset_id, company=company)

    # Get depreciation board
    depreciation_board = asset.compute_depreciation_board()

    context = {
        'asset': asset,
        'depreciation_board': depreciation_board,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/asset_depreciation_board.html', context)

@login_required
def asset_create_depreciation(request, asset_id):
    """Generate depreciation for an asset."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get asset
    asset = get_object_or_404(Asset, id=asset_id, company=company)

    # Check if depreciation already exists
    existing_depreciation = Depreciation.objects.filter(asset=asset).count()
    if existing_depreciation > 0:
        messages.warning(request, _('Depreciation already exists for this asset.'))
        return redirect('accounting:asset_detail', asset_id=asset.id)

    # Calculate depreciation amount
    depreciable_value = asset.purchase_value - asset.salvage_value

    # Determine number of periods based on frequency
    if asset.depreciation_frequency == 'monthly':
        periods = asset.useful_life_years * 12
        days_per_period = 30
    elif asset.depreciation_frequency == 'quarterly':
        periods = asset.useful_life_years * 4
        days_per_period = 90
    elif asset.depreciation_frequency == 'semi_annual':
        periods = asset.useful_life_years * 2
        days_per_period = 180
    else:  # annual
        periods = asset.useful_life_years
        days_per_period = 365

    # Calculate depreciation per period
    depreciation_per_period = depreciable_value / periods

    # Create depreciation records
    start_date = asset.in_service_date
    for i in range(periods):
        period_num = i + 1
        depreciation_date = start_date + datetime.timedelta(days=days_per_period * period_num)

        # Create depreciation record
        Depreciation.objects.create(
            asset=asset,
            date=depreciation_date,
            period=period_num,
            amount=depreciation_per_period,
            accumulated_depreciation=depreciation_per_period * period_num,
            remaining_value=asset.purchase_value - (depreciation_per_period * period_num),
            state='draft',
            created_by=request.user
        )

    messages.success(request, _('Depreciation schedule generated successfully.'))
    return redirect('accounting:asset_detail', asset_id=asset.id)

@login_required
def asset_dispose(request, asset_id):
    """Dispose an asset."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get asset
    asset = get_object_or_404(Asset, id=asset_id, company=company)

    if request.method == 'POST':
        # Get disposal details
        disposal_date = request.POST.get('disposal_date')
        disposal_value = request.POST.get('disposal_value', 0)
        disposal_reason = request.POST.get('disposal_reason', '')

        try:
            # Dispose asset
            asset.dispose(
                disposal_date=disposal_date,
                disposal_value=disposal_value,
                disposal_reason=disposal_reason,
                user=request.user
            )
            messages.success(request, _('Asset disposed successfully.'))
            return redirect('accounting:asset_detail', asset_id=asset.id)
        except Exception as e:
            messages.error(request, str(e))

    context = {
        'asset': asset,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/asset_dispose.html', context)

# Asset Category views
@login_required
def asset_category_list(request):
    return render(request, 'modules/accounting/asset_category_list.html', {})

@login_required
def asset_category_create(request):
    return render(request, 'modules/accounting/asset_category_form.html', {})

@login_required
def asset_category_detail(request, asset_category_id):
    return render(request, 'modules/accounting/asset_category_detail.html', {})

@login_required
def asset_category_edit(request, asset_category_id):
    return render(request, 'modules/accounting/asset_category_form.html', {})

# Depreciation Method views
@login_required
def depreciation_method_list(request):
    return render(request, 'modules/accounting/depreciation_method_list.html', {})

@login_required
def depreciation_method_create(request):
    return render(request, 'modules/accounting/depreciation_method_form.html', {})

@login_required
def depreciation_method_detail(request, depreciation_method_id):
    return render(request, 'modules/accounting/depreciation_method_detail.html', {})

@login_required
def depreciation_method_edit(request, depreciation_method_id):
    return render(request, 'modules/accounting/depreciation_method_form.html', {})

# API views
@login_required
@require_GET
def api_available_checks(request):
    """API endpoint to get available checks for a payment method."""
    payment_method_id = request.GET.get('payment_method_id')

    if not payment_method_id:
        return JsonResponse({'error': 'Payment method ID is required'}, status=400)

    try:
        # Get current company
        company_id = request.session.get('active_company_id')
        if not company_id:
            return JsonResponse({'error': 'No active company selected'}, status=400)

        # Get payment method
        payment_method = PaymentMethod.objects.get(id=payment_method_id, company_id=company_id)

        # Check if payment method is check type
        if payment_method.payment_type != 'check':
            return JsonResponse({'error': 'Payment method is not check type'}, status=400)

        # Get bank account from payment method
        bank_account = payment_method.bank_account
        if not bank_account:
            return JsonResponse({'error': 'No bank account associated with this payment method'}, status=400)

        # Get available check books
        check_books = CheckBook.objects.filter(
            bank_account=bank_account,
            is_active=True,
            remaining_count__gt=0
        )

        # Get available checks (not used in vouchers)
        available_checks = []

        for check_book in check_books:
            # Create a new check with the next number from the check book
            next_check_number = check_book.next_number

            check_data = {
                'id': f"new_{check_book.id}_{next_check_number}",  # Special ID for new checks
                'number': next_check_number,
                'checkbook_name': check_book.name,
                'bank_name': check_book.bank_account.bank.name,
                'is_new': True
            }

            available_checks.append(check_data)

        # Also get existing draft checks that are not used in vouchers
        existing_checks = Check.objects.filter(
            bank_account=bank_account,
            check_type='outgoing',
            state='draft'
        ).exclude(
            vouchers__isnull=False
        )

        for check in existing_checks:
            check_data = {
                'id': check.id,
                'number': check.number,
                'checkbook_name': check.check_book.name if check.check_book else 'N/A',
                'bank_name': check.bank_account.bank.name,
                'is_new': False
            }

            available_checks.append(check_data)

        return JsonResponse({
            'checks': available_checks
        })

    except PaymentMethod.DoesNotExist:
        return JsonResponse({'error': 'Payment method not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

# Depreciation views
@login_required
def depreciation_list(request):
    return render(request, 'modules/accounting/depreciation_list.html', {})

@login_required
def depreciation_detail(request, depreciation_id):
    """Detail view for a depreciation."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get depreciation
    depreciation = get_object_or_404(Depreciation, id=depreciation_id, asset__company=company)

    # Get journal entry if exists
    journal_entry = depreciation.journal_entry

    context = {
        'depreciation': depreciation,
        'asset': depreciation.asset,
        'journal_entry': journal_entry,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/depreciation_detail.html', context)

@login_required
def depreciation_post(request, depreciation_id):
    """Post a depreciation entry."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get depreciation
    depreciation = get_object_or_404(Depreciation, id=depreciation_id, asset__company=company)

    try:
        # Post depreciation
        depreciation.post(user=request.user)
        messages.success(request, _('Depreciation posted successfully.'))
    except Exception as e:
        messages.error(request, str(e))

    return redirect('accounting:depreciation_detail', depreciation_id=depreciation_id)

@login_required
def depreciation_cancel(request, depreciation_id):
    """Cancel a depreciation entry."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get depreciation
    depreciation = get_object_or_404(Depreciation, id=depreciation_id, asset__company=company)

    try:
        # Cancel depreciation
        depreciation.cancel()
        messages.success(request, _('Depreciation cancelled successfully.'))
    except Exception as e:
        messages.error(request, str(e))

    return redirect('accounting:depreciation_detail', depreciation_id=depreciation_id)

# Report views
@login_required
def reports_index(request):
    """Reports index page."""
    # Get current company and branch
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    context = {
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/index.html', context)

@login_required
def report_trial_balance(request):
    """Trial balance report view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    if not fiscal_year:
        messages.warning(request, _('No active fiscal year found. Please create and activate a fiscal year.'))
        return redirect('accounting:dashboard')

    # Get account balances
    account_balances = AccountBalance.objects.filter(
        fiscal_year=fiscal_year,
        branch=branch
    ).select_related('account', 'account__type')

    # Prepare data for report
    accounts_data = []
    total_debit = 0
    total_credit = 0

    for balance in account_balances:
        account = balance.account
        debit = balance.closing_debit
        credit = balance.closing_credit

        # Skip accounts with zero balance
        if debit == 0 and credit == 0:
            continue

        accounts_data.append({
            'code': account.code,
            'name': account.name,
            'type': account.type.name,
            'debit': debit,
            'credit': credit
        })

        total_debit += debit
        total_credit += credit

    context = {
        'accounts_data': accounts_data,
        'total_debit': total_debit,
        'total_credit': total_credit,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/trial_balance.html', context)

@login_required
def report_account_statement(request):
    """Account statement report view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    if not fiscal_year:
        messages.warning(request, _('No active fiscal year found. Please create and activate a fiscal year.'))
        return redirect('accounting:dashboard')

    # Get account
    account_id = request.GET.get('account_id')
    account = None
    transactions = []

    if account_id:
        try:
            account = Account.objects.get(id=account_id, chart__company=company)

            # Get journal entry lines for this account
            transactions = JournalEntryLine.objects.filter(
                account=account,
                entry__company=company,
                entry__branch=branch,
                entry__fiscal_year=fiscal_year,
                entry__state='posted'
            ).select_related('entry').order_by('entry__date', 'entry__id')
        except Account.DoesNotExist:
            messages.error(request, _('Account not found.'))

    # Get all accounts for dropdown
    accounts = Account.objects.filter(
        chart__company=company,
        is_active=True
    ).order_by('code')

    context = {
        'account': account,
        'accounts': accounts,
        'transactions': transactions,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/account_statement.html', context)

@login_required
def report_income_statement(request):
    """Income statement report view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    if not fiscal_year:
        messages.warning(request, _('No active fiscal year found. Please create and activate a fiscal year.'))
        return redirect('accounting:dashboard')

    # Get date range from request or use fiscal year
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')

    if not start_date:
        start_date = fiscal_year.start_date
    if not end_date:
        end_date = fiscal_year.end_date

    # Get revenue accounts
    revenue_accounts = Account.objects.filter(
        chart__company=company,
        type__category='revenue',
        is_active=True
    ).order_by('code')

    # Get expense accounts
    expense_accounts = Account.objects.filter(
        chart__company=company,
        type__category='expense',
        is_active=True
    ).order_by('code')

    # Calculate revenue
    revenue_data = []
    total_revenue = 0

    for account in revenue_accounts:
        # Get journal entry lines for this account
        lines = JournalEntryLine.objects.filter(
            account=account,
            entry__company=company,
            entry__branch=branch,
            entry__fiscal_year=fiscal_year,
            entry__state='posted',
            entry__date__gte=start_date,
            entry__date__lte=end_date
        )

        # Calculate balance
        debit = lines.aggregate(total=Sum('debit'))['total'] or 0
        credit = lines.aggregate(total=Sum('credit'))['total'] or 0
        balance = credit - debit  # Revenue accounts have credit balance

        if balance != 0:
            revenue_data.append({
                'code': account.code,
                'name': account.name,
                'balance': balance
            })
            total_revenue += balance

    # Calculate expenses
    expense_data = []
    total_expense = 0

    for account in expense_accounts:
        # Get journal entry lines for this account
        lines = JournalEntryLine.objects.filter(
            account=account,
            entry__company=company,
            entry__branch=branch,
            entry__fiscal_year=fiscal_year,
            entry__state='posted',
            entry__date__gte=start_date,
            entry__date__lte=end_date
        )

        # Calculate balance
        debit = lines.aggregate(total=Sum('debit'))['total'] or 0
        credit = lines.aggregate(total=Sum('credit'))['total'] or 0
        balance = debit - credit  # Expense accounts have debit balance

        if balance != 0:
            expense_data.append({
                'code': account.code,
                'name': account.name,
                'balance': balance
            })
            total_expense += balance

    # Calculate net income
    net_income = total_revenue - total_expense

    context = {
        'revenue_data': revenue_data,
        'expense_data': expense_data,
        'total_revenue': total_revenue,
        'total_expense': total_expense,
        'net_income': net_income,
        'start_date': start_date,
        'end_date': end_date,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/income_statement.html', context)

@login_required
def report_balance_sheet(request):
    """Balance sheet report view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    if not fiscal_year:
        messages.warning(request, _('No active fiscal year found. Please create and activate a fiscal year.'))
        return redirect('accounting:dashboard')

    # Get date from request or use fiscal year end date
    report_date = request.GET.get('report_date')
    if not report_date:
        report_date = fiscal_year.end_date

    # Get asset accounts
    asset_accounts = Account.objects.filter(
        chart__company=company,
        type__category='asset',
        is_active=True
    ).order_by('code')

    # Get liability accounts
    liability_accounts = Account.objects.filter(
        chart__company=company,
        type__category='liability',
        is_active=True
    ).order_by('code')

    # Get equity accounts
    equity_accounts = Account.objects.filter(
        chart__company=company,
        type__category='equity',
        is_active=True
    ).order_by('code')

    # Calculate assets
    asset_data = []
    total_assets = 0

    for account in asset_accounts:
        # Get account balance
        balance = AccountBalance.objects.filter(
            account=account,
            fiscal_year=fiscal_year,
            branch=branch
        ).first()

        if balance:
            account_balance = balance.closing_debit - balance.closing_credit
            if account_balance != 0:
                asset_data.append({
                    'code': account.code,
                    'name': account.name,
                    'balance': account_balance
                })
                total_assets += account_balance

    # Calculate liabilities
    liability_data = []
    total_liabilities = 0

    for account in liability_accounts:
        # Get account balance
        balance = AccountBalance.objects.filter(
            account=account,
            fiscal_year=fiscal_year,
            branch=branch
        ).first()

        if balance:
            account_balance = balance.closing_credit - balance.closing_debit
            if account_balance != 0:
                liability_data.append({
                    'code': account.code,
                    'name': account.name,
                    'balance': account_balance
                })
                total_liabilities += account_balance

    # Calculate equity
    equity_data = []
    total_equity = 0

    for account in equity_accounts:
        # Get account balance
        balance = AccountBalance.objects.filter(
            account=account,
            fiscal_year=fiscal_year,
            branch=branch
        ).first()

        if balance:
            account_balance = balance.closing_credit - balance.closing_debit
            if account_balance != 0:
                equity_data.append({
                    'code': account.code,
                    'name': account.name,
                    'balance': account_balance
                })
                total_equity += account_balance

    # Calculate retained earnings
    # Get revenue accounts
    revenue_accounts = Account.objects.filter(
        chart__company=company,
        type__category='revenue',
        is_active=True
    )

    # Get expense accounts
    expense_accounts = Account.objects.filter(
        chart__company=company,
        type__category='expense',
        is_active=True
    )

    # Calculate total revenue
    total_revenue = 0
    for account in revenue_accounts:
        balance = AccountBalance.objects.filter(
            account=account,
            fiscal_year=fiscal_year,
            branch=branch
        ).first()

        if balance:
            account_balance = balance.closing_credit - balance.closing_debit
            total_revenue += account_balance

    # Calculate total expense
    total_expense = 0
    for account in expense_accounts:
        balance = AccountBalance.objects.filter(
            account=account,
            fiscal_year=fiscal_year,
            branch=branch
        ).first()

        if balance:
            account_balance = balance.closing_debit - balance.closing_credit
            total_expense += account_balance

    # Calculate net income
    net_income = total_revenue - total_expense

    # Add net income to equity
    if net_income != 0:
        equity_data.append({
            'code': '',
            'name': _('Retained Earnings'),
            'balance': net_income
        })
        total_equity += net_income

    # Calculate total liabilities and equity
    total_liabilities_equity = total_liabilities + total_equity

    context = {
        'asset_data': asset_data,
        'liability_data': liability_data,
        'equity_data': equity_data,
        'total_assets': total_assets,
        'total_liabilities': total_liabilities,
        'total_equity': total_equity,
        'total_liabilities_equity': total_liabilities_equity,
        'report_date': report_date,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/balance_sheet.html', context)

@login_required
def report_customer_statement(request):
    return render(request, 'modules/accounting/reports/customer_statement.html', {})

@login_required
def report_vendor_statement(request):
    return render(request, 'modules/accounting/reports/vendor_statement.html', {})

@login_required
def report_bank_statement(request):
    return render(request, 'modules/accounting/reports/bank_statement.html', {})

@login_required
def report_cash_statement(request):
    return render(request, 'modules/accounting/reports/cash_statement.html', {})

@login_required
def report_check_status(request):
    return render(request, 'modules/accounting/reports/check_status.html', {})

@login_required
def report_asset_list(request):
    """Asset list report view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get assets
    assets = Asset.objects.filter(
        company=company,
        branch=branch,
        is_active=True
    ).select_related('category', 'depreciation_method')

    # Filter by category if provided
    category_id = request.GET.get('category_id')
    if category_id:
        try:
            category = AssetCategory.objects.get(id=category_id, company=company)
            assets = assets.filter(category=category)
        except AssetCategory.DoesNotExist:
            pass

    # Filter by state if provided
    state = request.GET.get('state')
    if state:
        assets = assets.filter(state=state)

    # Get asset categories for dropdown
    categories = AssetCategory.objects.filter(
        company=company,
        is_active=True
    ).order_by('name')

    # Calculate totals
    total_purchase_value = assets.aggregate(total=Sum('purchase_value'))['total'] or 0
    total_salvage_value = assets.aggregate(total=Sum('salvage_value'))['total'] or 0
    total_depreciated_value = 0
    total_current_value = 0

    for asset in assets:
        # Get latest depreciation
        latest_depreciation = Depreciation.objects.filter(
            asset=asset,
            state='posted'
        ).order_by('-period').first()

        if latest_depreciation:
            asset.depreciated_value = latest_depreciation.accumulated_depreciation
            asset.current_value = asset.purchase_value - asset.depreciated_value
        else:
            asset.depreciated_value = 0
            asset.current_value = asset.purchase_value

        total_depreciated_value += asset.depreciated_value
        total_current_value += asset.current_value

    context = {
        'assets': assets,
        'categories': categories,
        'selected_category_id': category_id,
        'selected_state': state,
        'total_purchase_value': total_purchase_value,
        'total_salvage_value': total_salvage_value,
        'total_depreciated_value': total_depreciated_value,
        'total_current_value': total_current_value,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/asset_list.html', context)

@login_required
def report_depreciation_schedule(request):
    """Depreciation schedule report view."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get assets
    assets = Asset.objects.filter(
        company=company,
        branch=branch,
        is_active=True,
        state='active'
    ).select_related('category', 'depreciation_method')

    # Filter by asset if provided
    asset_id = request.GET.get('asset_id')
    asset = None
    depreciations = []

    if asset_id:
        try:
            asset = Asset.objects.get(id=asset_id, company=company)
            depreciations = Depreciation.objects.filter(
                asset=asset
            ).order_by('period')
        except Asset.DoesNotExist:
            messages.error(request, _('Asset not found.'))

    context = {
        'assets': assets,
        'selected_asset': asset,
        'depreciations': depreciations,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/depreciation_schedule.html', context)


# Payment Method views
@login_required
def payment_method_list(request):
    """List all payment methods."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get filter parameters
    search_name = request.GET.get('name', '')
    selected_type = request.GET.get('type', '')
    selected_is_active = request.GET.get('is_active', '')

    # Build query
    query = Q(company=company)

    if search_name:
        query &= Q(name__icontains=search_name) | Q(code__icontains=search_name)

    if selected_type:
        query &= Q(type=selected_type)

    if selected_is_active == '1':
        query &= Q(is_active=True)
    elif selected_is_active == '0':
        query &= Q(is_active=False)

    # Get payment methods
    payment_methods = PaymentMethod.objects.filter(query).select_related('journal').order_by('name')

    context = {
        'payment_methods': payment_methods,
        'company': company,
        'branch': branch,
        'search_name': search_name,
        'selected_type': selected_type,
        'selected_is_active': selected_is_active,
        'payment_method_types': PaymentMethod.PAYMENT_METHOD_TYPES,
    }

    return render(request, 'modules/accounting/payment_method_list.html', context)


@login_required
def payment_method_create(request):
    """Create a new payment method."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Create form instance
    initial_data = {'company': company, 'is_active': True}
    form = PaymentMethodForm(request.POST or None, initial=initial_data)

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    # Filter journal choices to active journals
    form.fields['journal'].queryset = Journal.objects.filter(
        company=company,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            payment_method = form.save(commit=False)
            payment_method.created_by = request.user
            payment_method.save()
            messages.success(request, _('Payment method created successfully.'))
            return redirect('accounting:payment_method_list')

    context = {
        'form': form,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/payment_method_form.html', context)


@login_required
def payment_method_detail(request, payment_method_id):
    """View payment method details."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get payment method
    try:
        payment_method = PaymentMethod.objects.select_related('journal').get(
            id=payment_method_id,
            company=company
        )
    except PaymentMethod.DoesNotExist:
        messages.error(request, _('Payment method not found.'))
        return redirect('accounting:payment_method_list')

    # Get related vouchers
    vouchers = Voucher.objects.filter(
        payment_method=payment_method
    ).order_by('-date')[:10]  # Get the 10 most recent vouchers

    context = {
        'payment_method': payment_method,
        'vouchers': vouchers,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/payment_method_detail.html', context)


@login_required
def payment_method_edit(request, payment_method_id):
    """Edit an existing payment method."""
    # Get current company and branch
    from companies.models import Company, Branch

    # Try to get active company and branch from session
    company_id = request.session.get('active_company_id')
    branch_id = request.session.get('active_branch_id')

    company = None
    branch = None

    if company_id:
        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            pass

    if branch_id:
        try:
            branch = Branch.objects.get(id=branch_id)
        except Branch.DoesNotExist:
            pass

    if not company or not branch:
        # If user is superuser and there are companies in the system, use the first company
        if request.user.is_superuser:
            company = Company.objects.first()
            if company:
                branch = Branch.objects.filter(company=company).first()
                if branch:
                    # Set active company and branch in session
                    request.session['active_company_id'] = company.id
                    request.session['active_branch_id'] = branch.id
                    messages.info(request, _('Using company: {} and branch: {} as default.').format(company.name, branch.name))
                else:
                    messages.warning(request, _('No branches found for the default company.'))
                    return redirect('core:dashboard')
            else:
                messages.warning(request, _('No companies found in the system.'))
                return redirect('core:dashboard')
        else:
            messages.warning(request, _('Please select a company and branch first.'))
            return redirect('core:dashboard')

    # Get payment method
    try:
        payment_method = PaymentMethod.objects.select_related('journal').get(
            id=payment_method_id,
            company=company
        )
    except PaymentMethod.DoesNotExist:
        messages.error(request, _('Payment method not found.'))
        return redirect('accounting:payment_method_list')

    # Create form instance
    form = PaymentMethodForm(request.POST or None, instance=payment_method)

    # Hide company field and set it automatically
    form.fields['company'].widget = HiddenInput()
    form.fields['company'].initial = company

    # Filter journal choices to active journals
    form.fields['journal'].queryset = Journal.objects.filter(
        company=company,
        is_active=True
    )

    if request.method == 'POST':
        if form.is_valid():
            form.save()
            messages.success(request, _('Payment method updated successfully.'))
            return redirect('accounting:payment_method_detail', payment_method_id=payment_method.id)

    context = {
        'form': form,
        'payment_method': payment_method,
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/payment_method_form.html', context)


# Additional Report views
@login_required
def report_check_status(request):
    """Check status report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get active fiscal year
    fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

    # Get checks data
    checks = Check.objects.filter(
        company=company,
        branch=branch
    ).select_related('bank_account', 'bank_account__bank')

    # Filter by status if provided
    status_filter = request.GET.get('status')
    if status_filter:
        checks = checks.filter(state=status_filter)

    # Filter by date range if provided
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    if start_date:
        checks = checks.filter(date__gte=start_date)
    if end_date:
        checks = checks.filter(date__lte=end_date)

    # Calculate status summary
    status_summary = {
        'pending': checks.filter(state='pending').count(),
        'collected': checks.filter(state='collected').count(),
        'bounced': checks.filter(state='bounced').count(),
        'cancelled': checks.filter(state='cancelled').count(),
    }

    context = {
        'checks': checks,
        'status_summary': status_summary,
        'fiscal_year': fiscal_year,
        'company': company,
        'branch': branch,
        'selected_status': status_filter,
        'start_date': start_date,
        'end_date': end_date,
    }

    return render(request, 'modules/accounting/reports/check_status.html', context)

@login_required
def report_asset_list(request):
    """Asset list report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    # Get assets
    assets = Asset.objects.filter(company=company).select_related('category', 'depreciation_method')

    # Filter by category if provided
    category_id = request.GET.get('category_id')
    if category_id:
        assets = assets.filter(category_id=category_id)

    # Filter by state if provided
    state_filter = request.GET.get('state')
    if state_filter:
        assets = assets.filter(state=state_filter)

    # Get categories for filter
    categories = AssetCategory.objects.filter(company=company, is_active=True)

    # Calculate totals
    total_purchase_value = sum(asset.purchase_value for asset in assets)
    total_salvage_value = sum(asset.salvage_value for asset in assets)
    total_depreciated_value = sum(asset.accumulated_depreciation for asset in assets)
    total_current_value = total_purchase_value - total_depreciated_value

    context = {
        'assets': assets,
        'categories': categories,
        'total_purchase_value': total_purchase_value,
        'total_salvage_value': total_salvage_value,
        'total_depreciated_value': total_depreciated_value,
        'total_current_value': total_current_value,
        'company': company,
        'branch': branch,
        'selected_category_id': category_id,
        'selected_state': state_filter,
    }

    return render(request, 'modules/accounting/reports/asset_list.html', context)

@login_required
def report_customer_statement(request):
    """Customer statement report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    context = {
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/customer_statement.html', context)

@login_required
def report_vendor_statement(request):
    """Vendor statement report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    context = {
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/vendor_statement.html', context)

@login_required
def report_bank_statement(request):
    """Bank statement report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    context = {
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/bank_statement.html', context)

@login_required
def report_cash_statement(request):
    """Cash statement report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    context = {
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/cash_statement.html', context)

@login_required
def report_depreciation_schedule(request):
    """Depreciation schedule report."""
    company, branch, error_message = _get_company_branch(request)
    if error_message:
        messages.warning(request, error_message)
        return redirect('core:dashboard')

    context = {
        'company': company,
        'branch': branch,
    }

    return render(request, 'modules/accounting/reports/depreciation_schedule.html', context)