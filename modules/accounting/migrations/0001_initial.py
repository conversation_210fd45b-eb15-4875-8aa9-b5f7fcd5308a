# Generated by Django 5.2.1 on 2025-05-21 22:10

import django.db.models.deletion
import mptt.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('companies', '0003_branch_footer_image_branch_header_image'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Bank',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('swift_code', models.CharField(blank=True, max_length=20, verbose_name='SWIFT Code')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('phone', models.Char<PERSON>ield(blank=True, max_length=20, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='Email')),
                ('website', models.URLField(blank=True, verbose_name='Website')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Bank',
                'verbose_name_plural': 'Banks',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='AccountChart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Code')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_charts', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_account_charts', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Chart of Accounts',
                'verbose_name_plural': 'Charts of Accounts',
            },
        ),
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('category', models.CharField(choices=[('asset', 'Asset'), ('liability', 'Liability'), ('equity', 'Equity'), ('revenue', 'Revenue'), ('expense', 'Expense')], max_length=20, verbose_name='Category')),
                ('is_debit_balance', models.BooleanField(default=True, help_text='If checked, accounts of this type normally have debit balances', verbose_name='Is Debit Balance')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_types', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Account Type',
                'verbose_name_plural': 'Account Types',
                'ordering': ['code'],
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('level', models.IntegerField(choices=[(1, 'Level 1 - Main'), (2, 'Level 2 - Group'), (3, 'Level 3 - Subgroup'), (4, 'Level 4 - Detail'), (5, 'Level 5 - Transaction')], default=5, verbose_name='Level')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('allow_manual_transactions', models.BooleanField(default=True, verbose_name='Allow Manual Transactions')),
                ('reconcilable', models.BooleanField(default=False, verbose_name='Reconcilable')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('lft', models.PositiveIntegerField(editable=False)),
                ('rght', models.PositiveIntegerField(editable=False)),
                ('tree_id', models.PositiveIntegerField(db_index=True, editable=False)),
                ('parent', mptt.fields.TreeForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='accounting.account', verbose_name='Parent Account')),
                ('chart', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accounts', to='accounting.accountchart', verbose_name='Chart of Accounts')),
                ('type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='accounts', to='accounting.accounttype', verbose_name='Account Type')),
            ],
            options={
                'verbose_name': 'Account',
                'verbose_name_plural': 'Accounts',
                'unique_together': {('chart', 'code')},
            },
        ),
        migrations.CreateModel(
            name='AssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('depreciation_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='Depreciation Rate (%)')),
                ('useful_life_years', models.PositiveIntegerField(default=0, verbose_name='Useful Life (Years)')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('accumulated_depreciation_account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='accumulated_depreciation_asset_categories', to='accounting.account', verbose_name='Accumulated Depreciation Account')),
                ('asset_account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='asset_categories', to='accounting.account', verbose_name='Asset Account')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_categories', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_asset_categories', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('depreciation_account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='depreciation_asset_categories', to='accounting.account', verbose_name='Depreciation Account')),
            ],
            options={
                'verbose_name': 'Asset Category',
                'verbose_name_plural': 'Asset Categories',
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='BankAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('number', models.CharField(max_length=50, verbose_name='Account Number')),
                ('iban', models.CharField(blank=True, max_length=50, verbose_name='IBAN')),
                ('account_type', models.CharField(choices=[('current', 'Current Account'), ('savings', 'Savings Account'), ('deposit', 'Deposit Account'), ('credit', 'Credit Account')], default='current', max_length=20, verbose_name='Account Type')),
                ('currency', models.CharField(choices=[('USD', 'US Dollar'), ('EUR', 'Euro'), ('GBP', 'British Pound'), ('SAR', 'Saudi Riyal'), ('AED', 'UAE Dirham'), ('EGP', 'Egyptian Pound')], default='USD', max_length=3, verbose_name='Currency')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='bank_accounts', to='accounting.account', verbose_name='Account')),
                ('bank', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accounts', to='accounting.bank', verbose_name='Bank')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bank_accounts', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_bank_accounts', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Bank Account',
                'verbose_name_plural': 'Bank Accounts',
                'unique_together': {('bank', 'number', 'company')},
            },
        ),
        migrations.CreateModel(
            name='CashRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='cash_registers', to='accounting.account', verbose_name='Account')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_registers', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_registers', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_cash_registers', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Cash Register',
                'verbose_name_plural': 'Cash Registers',
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='CheckBook',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('start_number', models.CharField(max_length=20, verbose_name='Start Number')),
                ('end_number', models.CharField(max_length=20, verbose_name='End Number')),
                ('next_number', models.CharField(max_length=20, verbose_name='Next Number')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('bank_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='check_books', to='accounting.bankaccount', verbose_name='Bank Account')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_check_books', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Check Book',
                'verbose_name_plural': 'Check Books',
                'unique_together': {('bank_account', 'code')},
            },
        ),
        migrations.CreateModel(
            name='Check',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=50, verbose_name='Check Number')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Amount')),
                ('date', models.DateField(verbose_name='Date')),
                ('due_date', models.DateField(verbose_name='Due Date')),
                ('check_type', models.CharField(choices=[('incoming', 'Incoming'), ('outgoing', 'Outgoing')], max_length=10, verbose_name='Check Type')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('registered', 'Registered'), ('pending', 'Pending'), ('collected', 'Collected'), ('deposited', 'Deposited'), ('bounced', 'Bounced'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='State')),
                ('bank_name', models.CharField(blank=True, max_length=100, verbose_name='Bank Name')),
                ('account_number', models.CharField(blank=True, max_length=50, verbose_name='Account Number')),
                ('partner_name', models.CharField(max_length=100, verbose_name='Partner Name')),
                ('partner_type', models.CharField(choices=[('customer', 'Customer'), ('vendor', 'Vendor')], max_length=20, verbose_name='Partner Type')),
                ('partner_id', models.IntegerField(blank=True, null=True, verbose_name='Partner ID')),
                ('memo', models.TextField(blank=True, verbose_name='Memo')),
                ('image', models.ImageField(blank=True, null=True, upload_to='checks/', verbose_name='Check Image')),
                ('reference', models.CharField(default=uuid.uuid4, max_length=50, unique=True, verbose_name='Reference')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('bank_account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checks', to='accounting.bankaccount', verbose_name='Bank Account')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checks', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='checks', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_checks', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('check_book', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='checks', to='accounting.checkbook', verbose_name='Check Book')),
            ],
            options={
                'verbose_name': 'Check',
                'verbose_name_plural': 'Checks',
                'unique_together': {('bank_account', 'number', 'check_type')},
            },
        ),
        migrations.CreateModel(
            name='DepreciationMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('method_type', models.CharField(choices=[('straight_line', 'Straight Line'), ('declining_balance', 'Declining Balance'), ('sum_of_years_digits', 'Sum of Years Digits'), ('units_of_production', 'Units of Production'), ('custom', 'Custom')], max_length=30, verbose_name='Method Type')),
                ('factor', models.DecimalField(decimal_places=2, default=2.0, help_text='Factor for declining balance method (e.g., 2.0 for double declining)', max_digits=5, verbose_name='Factor')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciation_methods', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Depreciation Method',
                'verbose_name_plural': 'Depreciation Methods',
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='FiscalYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='Code')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('open', 'Open'), ('closed', 'Closed'), ('archived', 'Archived')], default='draft', max_length=20, verbose_name='State')),
                ('is_active', models.BooleanField(default=False, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_years', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_fiscal_years', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Fiscal Year',
                'verbose_name_plural': 'Fiscal Years',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='Journal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('type', models.CharField(choices=[('general', 'General'), ('sale', 'Sale'), ('purchase', 'Purchase'), ('cash', 'Cash'), ('bank', 'Bank'), ('asset', 'Asset'), ('closing', 'Closing')], max_length=20, verbose_name='Type')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journals', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_journals', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('default_credit_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='credit_journals', to='accounting.account', verbose_name='Default Credit Account')),
                ('default_debit_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='debit_journals', to='accounting.account', verbose_name='Default Debit Account')),
            ],
            options={
                'verbose_name': 'Journal',
                'verbose_name_plural': 'Journals',
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('reference', models.CharField(default=uuid.uuid4, max_length=50, unique=True, verbose_name='Reference')),
                ('date', models.DateField(verbose_name='Date')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='State')),
                ('partner_name', models.CharField(blank=True, max_length=100, verbose_name='Partner Name')),
                ('partner_type', models.CharField(blank=True, choices=[('customer', 'Customer'), ('vendor', 'Vendor')], max_length=20, verbose_name='Partner Type')),
                ('partner_id', models.IntegerField(blank=True, null=True, verbose_name='Partner ID')),
                ('memo', models.TextField(blank=True, verbose_name='Memo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('posted_at', models.DateTimeField(blank=True, null=True, verbose_name='Posted At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_entries', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='journal_entries', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_journal_entries', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('fiscal_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_entries', to='accounting.fiscalyear', verbose_name='Fiscal Year')),
                ('journal', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='entries', to='accounting.journal', verbose_name='Journal')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_journal_entries', to=settings.AUTH_USER_MODEL, verbose_name='Posted By')),
            ],
            options={
                'verbose_name': 'Journal Entry',
                'verbose_name_plural': 'Journal Entries',
                'ordering': ['-date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='Asset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('reference', models.CharField(default=uuid.uuid4, max_length=50, unique=True, verbose_name='Reference')),
                ('purchase_date', models.DateField(verbose_name='Purchase Date')),
                ('in_service_date', models.DateField(verbose_name='In Service Date')),
                ('purchase_value', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Purchase Value')),
                ('salvage_value', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Salvage Value')),
                ('useful_life_years', models.PositiveIntegerField(verbose_name='Useful Life (Years)')),
                ('depreciation_frequency', models.CharField(choices=[('monthly', 'Monthly'), ('quarterly', 'Quarterly'), ('semi_annual', 'Semi-Annual'), ('annual', 'Annual')], default='monthly', max_length=20, verbose_name='Depreciation Frequency')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('active', 'Active'), ('fully_depreciated', 'Fully Depreciated'), ('disposed', 'Disposed'), ('sold', 'Sold')], default='draft', max_length=20, verbose_name='State')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='Location')),
                ('serial_number', models.CharField(blank=True, max_length=50, verbose_name='Serial Number')),
                ('model', models.CharField(blank=True, max_length=50, verbose_name='Model')),
                ('manufacturer', models.CharField(blank=True, max_length=100, verbose_name='Manufacturer')),
                ('warranty_expiry', models.DateField(blank=True, null=True, verbose_name='Warranty Expiry')),
                ('disposal_date', models.DateField(blank=True, null=True, verbose_name='Disposal Date')),
                ('disposal_value', models.DecimalField(blank=True, decimal_places=2, max_digits=18, null=True, verbose_name='Disposal Value')),
                ('disposal_reason', models.TextField(blank=True, verbose_name='Disposal Reason')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('accumulated_depreciation_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='accumulated_depreciation_assets', to='accounting.account', verbose_name='Accumulated Depreciation Account')),
                ('asset_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='assets', to='accounting.account', verbose_name='Asset Account')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assets', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assets', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_assets', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('depreciation_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='depreciation_assets', to='accounting.account', verbose_name='Depreciation Account')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='assets', to='accounting.assetcategory', verbose_name='Category')),
                ('depreciation_method', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='assets', to='accounting.depreciationmethod', verbose_name='Depreciation Method')),
                ('acquisition_journal_entry', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acquired_assets', to='accounting.journalentry', verbose_name='Acquisition Journal Entry')),
                ('disposal_journal_entry', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='disposed_assets', to='accounting.journalentry', verbose_name='Disposal Journal Entry')),
            ],
            options={
                'verbose_name': 'Asset',
                'verbose_name_plural': 'Assets',
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='JournalEntryLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='Description')),
                ('debit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Debit')),
                ('credit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Credit')),
                ('partner_name', models.CharField(blank=True, max_length=100, verbose_name='Partner Name')),
                ('partner_type', models.CharField(blank=True, choices=[('customer', 'Customer'), ('vendor', 'Vendor')], max_length=20, verbose_name='Partner Type')),
                ('partner_id', models.IntegerField(blank=True, null=True, verbose_name='Partner ID')),
                ('reconciled', models.BooleanField(default=False, verbose_name='Reconciled')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='journal_lines', to='accounting.account', verbose_name='Account')),
                ('entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lines', to='accounting.journalentry', verbose_name='Journal Entry')),
            ],
            options={
                'verbose_name': 'Journal Entry Line',
                'verbose_name_plural': 'Journal Entry Lines',
            },
        ),
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('code', models.CharField(max_length=20, verbose_name='Code')),
                ('payment_type', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('check', 'Check'), ('card', 'Credit/Debit Card'), ('online', 'Online Payment'), ('other', 'Other')], max_length=20, verbose_name='Payment Type')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='payment_methods', to='accounting.account', verbose_name='Account')),
                ('bank_account', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_methods', to='accounting.bankaccount', verbose_name='Bank Account')),
                ('cash_register', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='payment_methods', to='accounting.cashregister', verbose_name='Cash Register')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_methods', to='companies.company', verbose_name='Company')),
            ],
            options={
                'verbose_name': 'Payment Method',
                'verbose_name_plural': 'Payment Methods',
                'unique_together': {('code', 'company')},
            },
        ),
        migrations.CreateModel(
            name='AccountBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('opening_debit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Opening Debit')),
                ('opening_credit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Opening Credit')),
                ('current_debit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Current Debit')),
                ('current_credit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Current Credit')),
                ('closing_debit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Closing Debit')),
                ('closing_credit', models.DecimalField(decimal_places=2, default=0, max_digits=18, verbose_name='Closing Credit')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balances', to='accounting.account', verbose_name='Account')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_balances', to='companies.branch', verbose_name='Branch')),
                ('fiscal_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_balances', to='accounting.fiscalyear', verbose_name='Fiscal Year')),
            ],
            options={
                'verbose_name': 'Account Balance',
                'verbose_name_plural': 'Account Balances',
                'unique_together': {('account', 'fiscal_year', 'branch')},
            },
        ),
        migrations.CreateModel(
            name='Depreciation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='Date')),
                ('period', models.PositiveIntegerField(verbose_name='Period')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Amount')),
                ('accumulated_depreciation', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Accumulated Depreciation')),
                ('remaining_value', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Remaining Value')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='State')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('posted_at', models.DateTimeField(blank=True, null=True, verbose_name='Posted At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('asset', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciations', to='accounting.asset', verbose_name='Asset')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_depreciations', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_depreciations', to=settings.AUTH_USER_MODEL, verbose_name='Posted By')),
                ('journal_entry', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='depreciations', to='accounting.journalentry', verbose_name='Journal Entry')),
            ],
            options={
                'verbose_name': 'Depreciation',
                'verbose_name_plural': 'Depreciations',
                'ordering': ['asset', 'period'],
                'unique_together': {('asset', 'period')},
            },
        ),
        migrations.CreateModel(
            name='Voucher',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('number', models.CharField(max_length=50, verbose_name='Number')),
                ('reference', models.CharField(default=uuid.uuid4, max_length=50, unique=True, verbose_name='Reference')),
                ('date', models.DateField(verbose_name='Date')),
                ('voucher_type', models.CharField(choices=[('receipt', 'Receipt'), ('payment', 'Payment')], max_length=20, verbose_name='Voucher Type')),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancelled', 'Cancelled')], default='draft', max_length=20, verbose_name='State')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=18, verbose_name='Amount')),
                ('partner_name', models.CharField(max_length=100, verbose_name='Partner Name')),
                ('partner_type', models.CharField(choices=[('customer', 'Customer'), ('vendor', 'Vendor')], max_length=20, verbose_name='Partner Type')),
                ('partner_id', models.IntegerField(blank=True, null=True, verbose_name='Partner ID')),
                ('memo', models.TextField(blank=True, verbose_name='Memo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('posted_at', models.DateTimeField(blank=True, null=True, verbose_name='Posted At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vouchers', to='companies.branch', verbose_name='Branch')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vouchers', to='companies.company', verbose_name='Company')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_vouchers', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
                ('fiscal_year', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vouchers', to='accounting.fiscalyear', verbose_name='Fiscal Year')),
                ('journal_entry', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vouchers', to='accounting.journalentry', verbose_name='Journal Entry')),
                ('payment_check', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vouchers', to='accounting.check', verbose_name='Check')),
                ('payment_method', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vouchers', to='accounting.paymentmethod', verbose_name='Payment Method')),
                ('posted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='posted_vouchers', to=settings.AUTH_USER_MODEL, verbose_name='Posted By')),
            ],
            options={
                'verbose_name': 'Voucher',
                'verbose_name_plural': 'Vouchers',
                'unique_together': {('number', 'voucher_type', 'company', 'fiscal_year')},
            },
        ),
    ]
