from django import forms
from django.utils.translation import gettext_lazy as _
from .models import (
    FiscalYear, AccountType, AccountChart, Account, AccountBalance,
    CashRegister, Bank, BankAccount, CheckBook, Check,
    Journal, JournalEntry, JournalEntryLine, PaymentMethod, Voucher,
    AssetCategory, DepreciationMethod, Asset, Depreciation
)


class FiscalYearForm(forms.ModelForm):
    """Form for creating and editing fiscal years"""
    class Meta:
        model = FiscalYear
        fields = ['name', 'code', 'company', 'start_date', 'end_date', 'is_active']
        widgets = {
            'start_date': forms.DateInput(attrs={'type': 'date'}),
            'end_date': forms.DateInput(attrs={'type': 'date'}),
        }


class AccountTypeForm(forms.ModelForm):
    """Form for creating and editing account types"""
    class Meta:
        model = AccountType
        fields = ['name', 'code', 'category', 'report_type', 'company', 'is_debit_balance', 'is_active', 'description']


class AccountChartForm(forms.ModelForm):
    """Form for creating and editing chart of accounts"""
    class Meta:
        model = AccountChart
        fields = ['name', 'code', 'company', 'is_active', 'description']


class AccountForm(forms.ModelForm):
    """Form for creating and editing accounts"""
    class Meta:
        model = Account
        fields = [
            'chart', 'code', 'name', 'type', 'level', 'parent',
            'is_active', 'description', 'allow_manual_transactions', 'reconcilable'
        ]


class CashRegisterForm(forms.ModelForm):
    """Form for creating and editing cash registers"""
    class Meta:
        model = CashRegister
        fields = ['name', 'code', 'company', 'branch', 'account', 'is_active', 'notes']


class BankForm(forms.ModelForm):
    """Form for creating and editing banks"""
    class Meta:
        model = Bank
        fields = ['name', 'code', 'swift_code', 'address', 'phone', 'email', 'website', 'is_active', 'notes']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter bank name'),
                'required': True
            }),
            'code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter bank code (optional)')
            }),
            'swift_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter SWIFT code (optional)')
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Enter bank address')
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter phone number')
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter email address')
            }),
            'website': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': _('Enter website URL')
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': _('Enter additional notes')
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            })
        }
        labels = {
            'name': _('Bank Name'),
            'code': _('Bank Code'),
            'swift_code': _('SWIFT Code'),
            'address': _('Address'),
            'phone': _('Phone Number'),
            'email': _('Email Address'),
            'website': _('Website'),
            'notes': _('Notes'),
            'is_active': _('Active')
        }


class BankAccountForm(forms.ModelForm):
    """Form for creating and editing bank accounts"""
    class Meta:
        model = BankAccount
        fields = [
            'name', 'number', 'iban', 'bank', 'company', 'branch', 'account',
            'account_type', 'currency', 'is_active', 'notes'
        ]


class CheckBookForm(forms.ModelForm):
    """Form for creating and editing check books"""
    class Meta:
        model = CheckBook
        fields = ['name', 'code', 'bank_account', 'start_number', 'end_number', 'next_number', 'is_active', 'notes']


class CheckForm(forms.ModelForm):
    """Form for creating and editing checks"""
    class Meta:
        model = Check
        fields = [
            'number', 'amount', 'date', 'due_date', 'check_type', 'state',
            'check_book', 'bank_name', 'account_number', 'bank_account',
            'company', 'branch', 'partner_name', 'partner_type', 'partner_id', 'memo', 'image'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
            'due_date': forms.DateInput(attrs={'type': 'date'}),
        }


class JournalForm(forms.ModelForm):
    """Form for creating and editing journals"""
    class Meta:
        model = Journal
        fields = [
            'name', 'code', 'type', 'default_debit_account',
            'default_credit_account', 'is_active', 'notes'
        ]


class JournalEntryLineForm(forms.ModelForm):
    """Form for journal entry lines"""
    class Meta:
        model = JournalEntryLine
        fields = ['account', 'name', 'debit', 'credit', 'partner_name', 'partner_type', 'partner_id']
        widgets = {
            'debit': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'credit': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
        }


class JournalEntryLineFormSet(forms.models.inlineformset_factory(
    JournalEntry, JournalEntryLine,
    form=JournalEntryLineForm,
    fields=['account', 'name', 'debit', 'credit', 'partner_name', 'partner_type', 'partner_id'],
    extra=1, can_delete=True
)):
    """Formset for journal entry lines"""
    pass


class JournalEntryForm(forms.ModelForm):
    """Form for creating and editing journal entries"""
    class Meta:
        model = JournalEntry
        fields = [
            'name', 'date', 'journal', 'fiscal_year', 'branch',
            'partner_name', 'partner_type', 'partner_id', 'memo'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
        }


class PaymentMethodForm(forms.ModelForm):
    """Form for creating and editing payment methods"""
    class Meta:
        model = PaymentMethod
        fields = [
            'name', 'code', 'payment_type', 'account',
            'cash_register', 'bank_account', 'is_active', 'notes'
        ]


class VoucherForm(forms.ModelForm):
    """Form for creating and editing vouchers"""

    # Add field for selecting existing check or creating new one
    check_option = forms.ChoiceField(
        choices=[
            ('new', _('Create New Check')),
            ('existing', _('Use Existing Check'))
        ],
        required=False,
        widget=forms.RadioSelect,
        label=_('Check Option')
    )

    # Field for selecting check book when creating new check
    check_book = forms.ModelChoiceField(
        queryset=CheckBook.objects.none(),
        required=False,
        label=_('Check Book'),
        help_text=_('Select check book for new check')
    )

    # Field for selecting existing check
    existing_check = forms.ModelChoiceField(
        queryset=Check.objects.none(),
        required=False,
        label=_('Existing Check'),
        help_text=_('Select an existing unused check')
    )

    class Meta:
        model = Voucher
        fields = [
            'number', 'date', 'voucher_type', 'amount', 'branch',
            'partner_name', 'partner_type', 'partner_id', 'payment_method', 'payment_check', 'memo'
        ]
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)

        if company:
            # Filter check books by company
            self.fields['check_book'].queryset = CheckBook.objects.filter(
                bank_account__company=company,
                is_active=True
            )

            # Filter existing checks by company (unused outgoing checks)
            self.fields['existing_check'].queryset = Check.objects.filter(
                company=company,
                check_type='outgoing',
                state='draft'
            ).select_related('check_book', 'bank_account')

    def clean(self):
        cleaned_data = super().clean()
        payment_method = cleaned_data.get('payment_method')
        check_option = cleaned_data.get('check_option')
        check_book = cleaned_data.get('check_book')
        existing_check = cleaned_data.get('existing_check')

        # Check if payment method requires a check
        if payment_method and 'check' in payment_method.name.lower():
            if check_option == 'new' and not check_book:
                raise forms.ValidationError(_('Check book is required when creating a new check'))
            elif check_option == 'existing' and not existing_check:
                raise forms.ValidationError(_('Please select an existing check'))

        return cleaned_data


class AssetCategoryForm(forms.ModelForm):
    """Form for creating and editing asset categories"""
    class Meta:
        model = AssetCategory
        fields = [
            'name', 'code', 'asset_account', 'depreciation_account',
            'accumulated_depreciation_account', 'depreciation_rate',
            'useful_life_years', 'is_active', 'notes'
        ]


class DepreciationMethodForm(forms.ModelForm):
    """Form for creating and editing depreciation methods"""
    class Meta:
        model = DepreciationMethod
        fields = ['name', 'code', 'method_type', 'factor', 'is_active', 'notes']


class AssetForm(forms.ModelForm):
    """Form for creating and editing assets"""
    class Meta:
        model = Asset
        fields = [
            'name', 'code', 'category', 'company', 'branch', 'purchase_date', 'in_service_date',
            'purchase_value', 'salvage_value', 'depreciation_method', 'useful_life_years',
            'depreciation_frequency', 'asset_account', 'depreciation_account',
            'accumulated_depreciation_account', 'is_active', 'description', 'location',
            'serial_number', 'model', 'manufacturer', 'warranty_expiry'
        ]
        widgets = {
            'purchase_date': forms.DateInput(attrs={'type': 'date'}),
            'in_service_date': forms.DateInput(attrs={'type': 'date'}),
            'warranty_expiry': forms.DateInput(attrs={'type': 'date'}),
        }


class AssetDisposeForm(forms.ModelForm):
    """Form for disposing assets"""
    class Meta:
        model = Asset
        fields = ['disposal_date', 'disposal_value', 'disposal_reason']
        widgets = {
            'disposal_date': forms.DateInput(attrs={'type': 'date'}),
        }


class DepreciationForm(forms.ModelForm):
    """Form for creating and editing depreciations"""
    class Meta:
        model = Depreciation
        fields = ['asset', 'date', 'period', 'amount', 'accumulated_depreciation', 'remaining_value']
        widgets = {
            'date': forms.DateInput(attrs={'type': 'date'}),
        }
