from django.urls import path
from . import views

app_name = 'accounting'

urlpatterns = [
    # Dashboard
    path('', views.dashboard, name='dashboard'),

    # Chart of Accounts
    path('chart/', views.chart_list, name='chart_list'),
    path('chart/create/', views.chart_create, name='chart_create'),
    path('chart/<int:chart_id>/', views.chart_detail, name='chart_detail'),
    path('chart/<int:chart_id>/edit/', views.chart_edit, name='chart_edit'),
    path('chart/<int:chart_id>/delete/', views.chart_delete, name='chart_delete'),

    # Account Types
    path('account-types/', views.account_type_list, name='account_type_list'),
    path('account-types/create/', views.account_type_create, name='account_type_create'),
    path('account-types/<int:account_type_id>/', views.account_type_detail, name='account_type_detail'),
    path('account-types/<int:account_type_id>/edit/', views.account_type_edit, name='account_type_edit'),
    path('account-types/<int:account_type_id>/delete/', views.account_type_delete, name='account_type_delete'),

    # Accounts
    path('accounts/', views.account_list, name='account_list'),
    path('accounts/create/', views.account_create, name='account_create'),
    path('accounts/<int:account_id>/', views.account_detail, name='account_detail'),
    path('accounts/<int:account_id>/edit/', views.account_edit, name='account_edit'),
    path('accounts/<int:account_id>/delete/', views.account_delete, name='account_delete'),

    # Fiscal Years
    path('fiscal-years/', views.fiscal_year_list, name='fiscal_year_list'),
    path('fiscal-years/create/', views.fiscal_year_create, name='fiscal_year_create'),
    path('fiscal-years/<int:fiscal_year_id>/', views.fiscal_year_detail, name='fiscal_year_detail'),
    path('fiscal-years/<int:fiscal_year_id>/edit/', views.fiscal_year_edit, name='fiscal_year_edit'),
    path('fiscal-years/<int:fiscal_year_id>/open/', views.fiscal_year_open, name='fiscal_year_open'),
    path('fiscal-years/<int:fiscal_year_id>/close/', views.fiscal_year_close, name='fiscal_year_close'),

    # Cash Registers
    path('cash-registers/', views.cash_register_list, name='cash_register_list'),
    path('cash-registers/create/', views.cash_register_create, name='cash_register_create'),
    path('cash-registers/<int:cash_register_id>/', views.cash_register_detail, name='cash_register_detail'),
    path('cash-registers/<int:cash_register_id>/edit/', views.cash_register_edit, name='cash_register_edit'),

    # Banks
    path('banks/', views.bank_list, name='bank_list'),
    path('banks/create/', views.bank_create, name='bank_create'),
    path('banks/<int:bank_id>/', views.bank_detail, name='bank_detail'),
    path('banks/<int:bank_id>/edit/', views.bank_edit, name='bank_edit'),

    # Bank Accounts
    path('bank-accounts/', views.bank_account_list, name='bank_account_list'),
    path('bank-accounts/create/', views.bank_account_create, name='bank_account_create'),
    path('bank-accounts/<int:bank_account_id>/', views.bank_account_detail, name='bank_account_detail'),
    path('bank-accounts/<int:bank_account_id>/edit/', views.bank_account_edit, name='bank_account_edit'),

    # Check Books
    path('check-books/', views.check_book_list, name='check_book_list'),
    path('check-books/create/', views.check_book_create, name='check_book_create'),
    path('check-books/<int:check_book_id>/', views.check_book_detail, name='check_book_detail'),
    path('check-books/<int:check_book_id>/edit/', views.check_book_edit, name='check_book_edit'),

    # Checks
    path('checks/', views.check_list, name='check_list'),
    path('checks/create/', views.check_create, name='check_create'),
    path('checks/<int:check_id>/', views.check_detail, name='check_detail'),
    path('checks/<int:check_id>/edit/', views.check_edit, name='check_edit'),
    path('checks/<int:check_id>/change-state/', views.check_change_state, name='check_change_state'),

    # Journals
    path('journals/', views.journal_list, name='journal_list'),
    path('journals/create/', views.journal_create, name='journal_create'),
    path('journals/<int:journal_id>/', views.journal_detail, name='journal_detail'),
    path('journals/<int:journal_id>/edit/', views.journal_edit, name='journal_edit'),

    # Journal Entries
    path('journal-entries/', views.journal_entry_list, name='journal_entry_list'),
    path('journal-entries/create/', views.journal_entry_create, name='journal_entry_create'),
    path('journal-entries/<int:journal_entry_id>/', views.journal_entry_detail, name='journal_entry_detail'),
    path('journal-entries/<int:journal_entry_id>/edit/', views.journal_entry_edit, name='journal_entry_edit'),
    path('journal-entries/<int:journal_entry_id>/post/', views.journal_entry_post, name='journal_entry_post'),
    path('journal-entries/<int:journal_entry_id>/cancel/', views.journal_entry_cancel, name='journal_entry_cancel'),

    # Vouchers
    path('vouchers/', views.voucher_list, name='voucher_list'),
    path('vouchers/create/', views.voucher_create, name='voucher_create'),
    path('vouchers/payment/', views.payment_voucher_list, name='payment_voucher_list'),
    path('vouchers/payment/create/', views.payment_voucher_create, name='payment_voucher_create'),
    path('vouchers/receipt/', views.receipt_voucher_list, name='receipt_voucher_list'),
    path('vouchers/receipt/create/', views.receipt_voucher_create, name='receipt_voucher_create'),
    path('vouchers/<int:voucher_id>/', views.voucher_detail, name='voucher_detail'),
    path('vouchers/<int:voucher_id>/edit/', views.voucher_edit, name='voucher_edit'),
    path('vouchers/<int:voucher_id>/post/', views.voucher_post, name='voucher_post'),
    path('vouchers/<int:voucher_id>/cancel/', views.voucher_cancel, name='voucher_cancel'),

    # Banks
    path('banks/', views.bank_list, name='bank_list'),
    path('banks/create/', views.bank_create, name='bank_create'),
    path('banks/<int:bank_id>/', views.bank_detail, name='bank_detail'),
    path('banks/<int:bank_id>/edit/', views.bank_edit, name='bank_edit'),

    # Payment Methods
    path('payment-methods/', views.payment_method_list, name='payment_method_list'),
    path('payment-methods/create/', views.payment_method_create, name='payment_method_create'),
    path('payment-methods/<int:payment_method_id>/', views.payment_method_detail, name='payment_method_detail'),
    path('payment-methods/<int:payment_method_id>/edit/', views.payment_method_edit, name='payment_method_edit'),

    # Assets
    path('assets/', views.asset_list, name='asset_list'),
    path('assets/create/', views.asset_create, name='asset_create'),
    path('assets/<int:asset_id>/', views.asset_detail, name='asset_detail'),
    path('assets/<int:asset_id>/edit/', views.asset_edit, name='asset_edit'),
    path('assets/<int:asset_id>/depreciation-board/', views.asset_depreciation_board, name='asset_depreciation_board'),
    path('assets/<int:asset_id>/create-depreciation/', views.asset_create_depreciation, name='asset_create_depreciation'),
    path('assets/<int:asset_id>/dispose/', views.asset_dispose, name='asset_dispose'),

    # Asset Categories
    path('asset-categories/', views.asset_category_list, name='asset_category_list'),
    path('asset-categories/create/', views.asset_category_create, name='asset_category_create'),
    path('asset-categories/<int:asset_category_id>/', views.asset_category_detail, name='asset_category_detail'),
    path('asset-categories/<int:asset_category_id>/edit/', views.asset_category_edit, name='asset_category_edit'),

    # Depreciation Methods
    path('depreciation-methods/', views.depreciation_method_list, name='depreciation_method_list'),
    path('depreciation-methods/create/', views.depreciation_method_create, name='depreciation_method_create'),
    path('depreciation-methods/<int:depreciation_method_id>/', views.depreciation_method_detail, name='depreciation_method_detail'),
    path('depreciation-methods/<int:depreciation_method_id>/edit/', views.depreciation_method_edit, name='depreciation_method_edit'),

    # Depreciations
    path('depreciations/', views.depreciation_list, name='depreciation_list'),
    path('depreciations/<int:depreciation_id>/', views.depreciation_detail, name='depreciation_detail'),
    path('depreciations/<int:depreciation_id>/post/', views.depreciation_post, name='depreciation_post'),
    path('depreciations/<int:depreciation_id>/cancel/', views.depreciation_cancel, name='depreciation_cancel'),

    # API endpoints
    path('api/available-checks/', views.api_available_checks, name='api_available_checks'),

    # Reports
    path('reports/', views.reports_index, name='reports_index'),
    path('reports/trial-balance/', views.report_trial_balance, name='report_trial_balance'),
    path('reports/account-statement/', views.report_account_statement, name='report_account_statement'),
    path('reports/income-statement/', views.report_income_statement, name='report_income_statement'),
    path('reports/balance-sheet/', views.report_balance_sheet, name='report_balance_sheet'),
    path('reports/check-status/', views.report_check_status, name='report_check_status'),
    path('reports/asset-list/', views.report_asset_list, name='report_asset_list'),
    path('reports/customer-statement/', views.report_customer_statement, name='report_customer_statement'),
    path('reports/vendor-statement/', views.report_vendor_statement, name='report_vendor_statement'),
    path('reports/bank-statement/', views.report_bank_statement, name='report_bank_statement'),
    path('reports/cash-statement/', views.report_cash_statement, name='report_cash_statement'),
    path('reports/depreciation-schedule/', views.report_depreciation_schedule, name='report_depreciation_schedule'),
]
