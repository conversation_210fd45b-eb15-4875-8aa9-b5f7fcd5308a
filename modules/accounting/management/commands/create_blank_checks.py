"""
Django management command to create blank checks for testing check selection functionality
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils.translation import gettext as _
from companies.models import Company, Branch
from modules.accounting.models import CheckBook, Check
import random


class Command(BaseCommand):
    help = 'Create blank checks for testing check selection functionality'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create checks for',
            required=True
        )
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of blank checks to create per checkbook (default: 10)'
        )

    def handle(self, *args, **options):
        company_id = options['company_id']
        count = options['count']

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            raise CommandError(f'Company with ID {company_id} does not exist')

        # Get the first branch for this company
        branch = Branch.objects.filter(company=company).first()
        if not branch:
            raise CommandError(f'No branches found for company {company.name}')

        # Get all checkbooks for this company
        checkbooks = CheckBook.objects.filter(
            bank_account__company=company,
            is_active=True
        )

        if not checkbooks.exists():
            raise CommandError(f'No active checkbooks found for company {company.name}')

        self.stdout.write(f'Creating {count} blank checks for each checkbook...')

        total_created = 0

        for checkbook in checkbooks:
            self.stdout.write(f'Processing checkbook: {checkbook.name}')
            
            # Create blank checks
            for i in range(count):
                try:
                    # Get next check number
                    check_number = checkbook.get_next_check_number()
                    
                    # Create blank check
                    check = Check.objects.create(
                        number=check_number,
                        amount=0,  # Blank check with no amount
                        date=None,  # No date set
                        due_date=None,  # No due date set
                        check_type='outgoing',
                        state='draft',  # Draft state for unused checks
                        check_book=checkbook,
                        bank_account=checkbook.bank_account,
                        company=company,
                        branch=branch,
                        partner_name='',  # Empty partner name
                        partner_type='supplier',  # Default type
                        memo='Blank check for selection',
                    )
                    
                    total_created += 1
                    self.stdout.write(f'  Created blank check: {check.number}')
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'  Error creating check: {e}')
                    )
                    continue

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {total_created} blank checks for company {company.name}'
            )
        )

        # Display summary
        self.stdout.write('\nSummary:')
        for checkbook in checkbooks:
            blank_checks = Check.objects.filter(
                check_book=checkbook,
                state='draft',
                amount=0
            ).count()
            self.stdout.write(f'  {checkbook.name}: {blank_checks} blank checks available')
