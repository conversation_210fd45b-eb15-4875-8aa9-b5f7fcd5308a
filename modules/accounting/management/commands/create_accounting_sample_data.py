from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

from companies.models import Company, Branch
from modules.accounting.models import (
    FiscalYear, AccountType, AccountChart, Account, AccountBalance,
    Bank, BankAccount, CheckBook, Check, CashRegister,
    Journal, JournalEntry, JournalEntryLine, PaymentMethod, Voucher,
    AssetCategory, DepreciationMethod, Asset, Depreciation
)


class Command(BaseCommand):
    help = 'Create sample data for accounting module'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create data for',
        )

    def handle(self, *args, **options):
        company_id = options.get('company_id')
        
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Company with ID {company_id} does not exist')
                )
                return
        else:
            company = Company.objects.first()
            if not company:
                self.stdout.write(
                    self.style.ERROR('No companies found. Please create a company first.')
                )
                return

        branch = Branch.objects.filter(company=company).first()
        if not branch:
            self.stdout.write(
                self.style.ERROR(f'No branches found for company {company.name}')
            )
            return

        self.stdout.write(f'Creating sample data for company: {company.name}')
        
        # Create fiscal year
        self.create_fiscal_year(company)
        
        # Create banks
        self.create_banks()
        
        # Create account types
        self.create_account_types(company)
        
        # Create chart of accounts
        self.create_chart_of_accounts(company)
        
        # Create accounts
        self.create_accounts(company)
        
        # Create bank accounts
        self.create_bank_accounts(company, branch)
        
        # Create cash registers
        self.create_cash_registers(company, branch)
        
        # Create checkbooks
        self.create_checkbooks(company, branch)
        
        # Create payment methods
        self.create_payment_methods(company)
        
        # Create asset categories
        self.create_asset_categories(company)
        
        # Create depreciation methods
        self.create_depreciation_methods()
        
        # Create assets
        self.create_assets(company, branch)
        
        # Create journals
        self.create_journals(company)
        
        # Create sample transactions
        self.create_sample_transactions(company, branch)
        
        self.stdout.write(
            self.style.SUCCESS('Successfully created accounting sample data!')
        )

    def create_fiscal_year(self, company):
        """Create fiscal year"""
        current_year = timezone.now().year
        
        fiscal_year, created = FiscalYear.objects.get_or_create(
            company=company,
            name=f'السنة المالية {current_year}',
            defaults={
                'code': f'FY{current_year}',
                'start_date': datetime(current_year, 1, 1).date(),
                'end_date': datetime(current_year, 12, 31).date(),
                'is_active': True,
            }
        )
        
        if created:
            self.stdout.write(f'Created fiscal year: {fiscal_year.name}')

    def create_banks(self):
        """Create sample banks"""
        banks_data = [
            {
                'name': 'البنك الأهلي المصري',
                'code': 'NBE',
                'swift_code': 'NBEGEGCX',
                'address': '1187 كورنيش النيل، القاهرة، مصر',
                'phone': '19623',
                'email': '<EMAIL>',
                'website': 'https://www.nbe.com.eg',
                'is_active': True,
                'notes': 'البنك الأهلي المصري - أكبر البنوك في مصر'
            },
            {
                'name': 'بنك مصر',
                'code': 'BM',
                'swift_code': 'BMISEGCX',
                'address': '151 شارع محمد فريد، القاهرة، مصر',
                'phone': '19888',
                'email': '<EMAIL>',
                'website': 'https://www.banquemisr.com',
                'is_active': True,
                'notes': 'بنك مصر - ثاني أكبر البنوك في مصر'
            },
            {
                'name': 'البنك التجاري الدولي',
                'code': 'CIB',
                'swift_code': 'CIBEEGCX',
                'address': 'برج البنك التجاري الدولي، القاهرة الجديدة',
                'phone': '19666',
                'email': '<EMAIL>',
                'website': 'https://www.cibeg.com',
                'is_active': True,
                'notes': 'البنك التجاري الدولي - بنك رائد في الخدمات المصرفية'
            },
            {
                'name': 'بنك القاهرة',
                'code': 'CAI',
                'swift_code': 'CIBEEGCX',
                'address': '16 شارع شريف، وسط البلد، القاهرة',
                'phone': '16141',
                'email': '<EMAIL>',
                'website': 'https://www.banqueducaire.com',
                'is_active': True,
                'notes': 'بنك القاهرة - بنك عريق في السوق المصري'
            },
            {
                'name': 'بنك الإسكندرية',
                'code': 'ALEX',
                'swift_code': 'ALEXEGCX',
                'address': '49 شارع قصر النيل، القاهرة',
                'phone': '19777',
                'email': '<EMAIL>',
                'website': 'https://www.alexbank.com',
                'is_active': True,
                'notes': 'بنك الإسكندرية - بنك متخصص في الخدمات المصرفية المتطورة'
            }
        ]
        
        for bank_data in banks_data:
            bank, created = Bank.objects.get_or_create(
                name=bank_data['name'],
                defaults=bank_data
            )
            if created:
                self.stdout.write(f'Created bank: {bank.name}')

    def create_account_types(self, company):
        """Create account types"""
        account_types_data = [
            # Assets
            {'name': 'الأصول المتداولة', 'code': '1000', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'النقدية والبنوك', 'code': '1100', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'العملاء والمدينون', 'code': '1200', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'المخزون', 'code': '1300', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'الأصول الثابتة', 'code': '1400', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            
            # Liabilities
            {'name': 'الخصوم المتداولة', 'code': '2000', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'الموردون والدائنون', 'code': '2100', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'القروض قصيرة الأجل', 'code': '2200', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'الخصوم طويلة الأجل', 'code': '2300', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            
            # Equity
            {'name': 'رأس المال', 'code': '3000', 'category': 'equity', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'الأرباح المحتجزة', 'code': '3100', 'category': 'equity', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            
            # Revenue
            {'name': 'الإيرادات التشغيلية', 'code': '4000', 'category': 'revenue', 'report_type': 'income_statement', 'is_debit_balance': False},
            {'name': 'الإيرادات الأخرى', 'code': '4100', 'category': 'revenue', 'report_type': 'income_statement', 'is_debit_balance': False},
            
            # Expenses
            {'name': 'تكلفة البضاعة المباعة', 'code': '5000', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
            {'name': 'المصروفات التشغيلية', 'code': '5100', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
            {'name': 'المصروفات الإدارية', 'code': '5200', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
            {'name': 'المصروفات الأخرى', 'code': '5300', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
        ]
        
        for type_data in account_types_data:
            account_type, created = AccountType.objects.get_or_create(
                company=company,
                code=type_data['code'],
                defaults={
                    'name': type_data['name'],
                    'category': type_data['category'],
                    'report_type': type_data['report_type'],
                    'is_debit_balance': type_data['is_debit_balance'],
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(f'Created account type: {account_type.name}')

    def create_chart_of_accounts(self, company):
        """Create chart of accounts"""
        chart, created = AccountChart.objects.get_or_create(
            company=company,
            name='دليل الحسابات الرئيسي',
            defaults={
                'code': 'MAIN',
                'description': 'دليل الحسابات الرئيسي للشركة',
                'is_active': True,
            }
        )
        if created:
            self.stdout.write(f'Created chart of accounts: {chart.name}')

    def create_accounts(self, company):
        """Create sample accounts"""
        chart = AccountChart.objects.filter(company=company).first()
        if not chart:
            return
            
        # Get account types
        cash_type = AccountType.objects.filter(company=company, code='1100').first()
        receivables_type = AccountType.objects.filter(company=company, code='1200').first()
        inventory_type = AccountType.objects.filter(company=company, code='1300').first()
        fixed_assets_type = AccountType.objects.filter(company=company, code='1400').first()
        payables_type = AccountType.objects.filter(company=company, code='2100').first()
        capital_type = AccountType.objects.filter(company=company, code='3000').first()
        revenue_type = AccountType.objects.filter(company=company, code='4000').first()
        expense_type = AccountType.objects.filter(company=company, code='5100').first()
        
        accounts_data = [
            # Cash and Bank Accounts
            {'code': '1101', 'name': 'الخزينة الرئيسية', 'type': cash_type, 'level': 1},
            {'code': '1102', 'name': 'البنك الأهلي المصري', 'type': cash_type, 'level': 1},
            {'code': '1103', 'name': 'بنك مصر', 'type': cash_type, 'level': 1},
            {'code': '1104', 'name': 'البنك التجاري الدولي', 'type': cash_type, 'level': 1},
            
            # Receivables
            {'code': '1201', 'name': 'العملاء', 'type': receivables_type, 'level': 1},
            {'code': '1202', 'name': 'أوراق القبض', 'type': receivables_type, 'level': 1},
            {'code': '1203', 'name': 'مدينون متنوعون', 'type': receivables_type, 'level': 1},
            
            # Inventory
            {'code': '1301', 'name': 'مخزون البضائع', 'type': inventory_type, 'level': 1},
            {'code': '1302', 'name': 'مخزون المواد الخام', 'type': inventory_type, 'level': 1},
            
            # Fixed Assets
            {'code': '1401', 'name': 'الأراضي والمباني', 'type': fixed_assets_type, 'level': 1},
            {'code': '1402', 'name': 'الآلات والمعدات', 'type': fixed_assets_type, 'level': 1},
            {'code': '1403', 'name': 'الأثاث والتجهيزات', 'type': fixed_assets_type, 'level': 1},
            {'code': '1404', 'name': 'السيارات', 'type': fixed_assets_type, 'level': 1},
            
            # Payables
            {'code': '2101', 'name': 'الموردون', 'type': payables_type, 'level': 1},
            {'code': '2102', 'name': 'أوراق الدفع', 'type': payables_type, 'level': 1},
            {'code': '2103', 'name': 'دائنون متنوعون', 'type': payables_type, 'level': 1},
            
            # Capital
            {'code': '3001', 'name': 'رأس المال المدفوع', 'type': capital_type, 'level': 1},
            
            # Revenue
            {'code': '4001', 'name': 'إيرادات المبيعات', 'type': revenue_type, 'level': 1},
            {'code': '4002', 'name': 'إيرادات الخدمات', 'type': revenue_type, 'level': 1},
            
            # Expenses
            {'code': '5101', 'name': 'مرتبات وأجور', 'type': expense_type, 'level': 1},
            {'code': '5102', 'name': 'إيجارات', 'type': expense_type, 'level': 1},
            {'code': '5103', 'name': 'كهرباء ومياه', 'type': expense_type, 'level': 1},
            {'code': '5104', 'name': 'مصروفات إدارية', 'type': expense_type, 'level': 1},
        ]
        
        for account_data in accounts_data:
            if account_data['type']:  # Only create if type exists
                account, created = Account.objects.get_or_create(
                    chart=chart,
                    code=account_data['code'],
                    defaults={
                        'name': account_data['name'],
                        'type': account_data['type'],
                        'level': account_data['level'],
                        'is_active': True,
                        'allow_manual_transactions': True,
                        'reconcilable': True,
                    }
                )
                if created:
                    self.stdout.write(f'Created account: {account.code} - {account.name}')
