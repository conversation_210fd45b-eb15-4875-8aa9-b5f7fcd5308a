from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random
from datetime import datetime, timedelta

from companies.models import Company, Branch
from modules.accounting.models import (
    FiscalYear, AccountType, AccountChart, Account, AccountBalance,
    Bank, BankAccount, CheckBook, Check, CashRegister,
    Journal, JournalEntry, JournalEntryLine, PaymentMethod, Voucher,
    AssetCategory, DepreciationMethod, Asset, Depreciation
)


class Command(BaseCommand):
    help = 'Create sample data for accounting module'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company-id',
            type=int,
            help='Company ID to create data for',
        )

    def handle(self, *args, **options):
        company_id = options.get('company_id')

        if company_id:
            try:
                company = Company.objects.get(id=company_id)
            except Company.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'Company with ID {company_id} does not exist')
                )
                return
        else:
            company = Company.objects.first()
            if not company:
                self.stdout.write(
                    self.style.ERROR('No companies found. Please create a company first.')
                )
                return

        branch = Branch.objects.filter(company=company).first()
        if not branch:
            self.stdout.write(
                self.style.ERROR(f'No branches found for company {company.name}')
            )
            return

        self.stdout.write(f'Creating sample data for company: {company.name}')

        # Create fiscal year
        self.create_fiscal_year(company)

        # Create banks
        self.create_banks()

        # Create account types
        self.create_account_types(company)

        # Create chart of accounts
        self.create_chart_of_accounts(company)

        # Create accounts
        self.create_accounts(company)

        # Create bank accounts
        self.create_bank_accounts(company, branch)

        # Create cash registers
        self.create_cash_registers(company, branch)

        # Create checkbooks
        self.create_checkbooks(company, branch)

        # Create payment methods
        self.create_payment_methods(company)

        # Create asset categories
        self.create_asset_categories(company)

        # Create depreciation methods
        self.create_depreciation_methods(company)

        # Create assets
        self.create_assets(company, branch)

        # Create journals
        self.create_journals(company)

        # Create detailed customer and supplier accounts
        self.create_customer_supplier_accounts(company, branch)

        # Create sample transactions
        self.create_sample_transactions(company, branch)

        self.stdout.write(
            self.style.SUCCESS('Successfully created accounting sample data!')
        )

    def create_fiscal_year(self, company):
        """Create fiscal year"""
        current_year = timezone.now().year

        # Check if there's already an active fiscal year for this company
        existing_fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

        if existing_fiscal_year:
            self.stdout.write(f'Using existing fiscal year: {existing_fiscal_year.name}')
            return

        # Check if there's any fiscal year for this company
        any_fiscal_year = FiscalYear.objects.filter(company=company).first()

        if any_fiscal_year:
            # Activate the first one found
            any_fiscal_year.is_active = True
            any_fiscal_year.save()
            self.stdout.write(f'Activated existing fiscal year: {any_fiscal_year.name}')
            return

        # Create new fiscal year only if none exists
        try:
            fiscal_year, created = FiscalYear.objects.get_or_create(
                company=company,
                code=f'FY{current_year}',
                defaults={
                    'name': f'السنة المالية {current_year}',
                    'start_date': datetime(current_year, 1, 1).date(),
                    'end_date': datetime(current_year, 12, 31).date(),
                    'is_active': True,
                }
            )

            if created:
                self.stdout.write(f'Created fiscal year: {fiscal_year.name}')
        except Exception as e:
            self.stdout.write(f'Could not create fiscal year: {e}')

    def create_banks(self):
        """Create sample banks"""
        banks_data = [
            {
                'name': 'البنك الأهلي المصري',
                'code': 'NBE',
                'swift_code': 'NBEGEGCX',
                'address': '1187 كورنيش النيل، القاهرة، مصر',
                'phone': '19623',
                'email': '<EMAIL>',
                'website': 'https://www.nbe.com.eg',
                'is_active': True,
                'notes': 'البنك الأهلي المصري - أكبر البنوك في مصر'
            },
            {
                'name': 'بنك مصر',
                'code': 'BM',
                'swift_code': 'BMISEGCX',
                'address': '151 شارع محمد فريد، القاهرة، مصر',
                'phone': '19888',
                'email': '<EMAIL>',
                'website': 'https://www.banquemisr.com',
                'is_active': True,
                'notes': 'بنك مصر - ثاني أكبر البنوك في مصر'
            },
            {
                'name': 'البنك التجاري الدولي',
                'code': 'CIB',
                'swift_code': 'CIBEEGCX',
                'address': 'برج البنك التجاري الدولي، القاهرة الجديدة',
                'phone': '19666',
                'email': '<EMAIL>',
                'website': 'https://www.cibeg.com',
                'is_active': True,
                'notes': 'البنك التجاري الدولي - بنك رائد في الخدمات المصرفية'
            },
            {
                'name': 'بنك القاهرة',
                'code': 'CAI',
                'swift_code': 'CIBEEGCX',
                'address': '16 شارع شريف، وسط البلد، القاهرة',
                'phone': '16141',
                'email': '<EMAIL>',
                'website': 'https://www.banqueducaire.com',
                'is_active': True,
                'notes': 'بنك القاهرة - بنك عريق في السوق المصري'
            },
            {
                'name': 'بنك الإسكندرية',
                'code': 'ALEX',
                'swift_code': 'ALEXEGCX',
                'address': '49 شارع قصر النيل، القاهرة',
                'phone': '19777',
                'email': '<EMAIL>',
                'website': 'https://www.alexbank.com',
                'is_active': True,
                'notes': 'بنك الإسكندرية - بنك متخصص في الخدمات المصرفية المتطورة'
            }
        ]

        for bank_data in banks_data:
            bank, created = Bank.objects.get_or_create(
                name=bank_data['name'],
                defaults=bank_data
            )
            if created:
                self.stdout.write(f'Created bank: {bank.name}')

    def create_account_types(self, company):
        """Create account types"""
        account_types_data = [
            # Assets
            {'name': 'الأصول المتداولة', 'code': '1000', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'النقدية والبنوك', 'code': '1100', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'العملاء والمدينون', 'code': '1200', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'المخزون', 'code': '1300', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},
            {'name': 'الأصول الثابتة', 'code': '1400', 'category': 'asset', 'report_type': 'balance_sheet', 'is_debit_balance': True},

            # Liabilities
            {'name': 'الخصوم المتداولة', 'code': '2000', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'الموردون والدائنون', 'code': '2100', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'القروض قصيرة الأجل', 'code': '2200', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'الخصوم طويلة الأجل', 'code': '2300', 'category': 'liability', 'report_type': 'balance_sheet', 'is_debit_balance': False},

            # Equity
            {'name': 'رأس المال', 'code': '3000', 'category': 'equity', 'report_type': 'balance_sheet', 'is_debit_balance': False},
            {'name': 'الأرباح المحتجزة', 'code': '3100', 'category': 'equity', 'report_type': 'balance_sheet', 'is_debit_balance': False},

            # Revenue
            {'name': 'الإيرادات التشغيلية', 'code': '4000', 'category': 'revenue', 'report_type': 'income_statement', 'is_debit_balance': False},
            {'name': 'الإيرادات الأخرى', 'code': '4100', 'category': 'revenue', 'report_type': 'income_statement', 'is_debit_balance': False},

            # Expenses
            {'name': 'تكلفة البضاعة المباعة', 'code': '5000', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
            {'name': 'المصروفات التشغيلية', 'code': '5100', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
            {'name': 'المصروفات الإدارية', 'code': '5200', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
            {'name': 'المصروفات الأخرى', 'code': '5300', 'category': 'expense', 'report_type': 'income_statement', 'is_debit_balance': True},
        ]

        for type_data in account_types_data:
            account_type, created = AccountType.objects.get_or_create(
                company=company,
                code=type_data['code'],
                defaults={
                    'name': type_data['name'],
                    'category': type_data['category'],
                    'report_type': type_data['report_type'],
                    'is_debit_balance': type_data['is_debit_balance'],
                    'is_active': True,
                }
            )
            if created:
                self.stdout.write(f'Created account type: {account_type.name}')

    def create_chart_of_accounts(self, company):
        """Create chart of accounts"""
        chart, created = AccountChart.objects.get_or_create(
            company=company,
            name='دليل الحسابات الرئيسي',
            defaults={
                'code': 'MAIN',
                'description': 'دليل الحسابات الرئيسي للشركة',
                'is_active': True,
            }
        )
        if created:
            self.stdout.write(f'Created chart of accounts: {chart.name}')

    def create_accounts(self, company):
        """Create sample accounts"""
        chart = AccountChart.objects.filter(company=company).first()
        if not chart:
            return

        # Get account types
        cash_type = AccountType.objects.filter(company=company, code='1100').first()
        receivables_type = AccountType.objects.filter(company=company, code='1200').first()
        inventory_type = AccountType.objects.filter(company=company, code='1300').first()
        fixed_assets_type = AccountType.objects.filter(company=company, code='1400').first()
        payables_type = AccountType.objects.filter(company=company, code='2100').first()
        capital_type = AccountType.objects.filter(company=company, code='3000').first()
        revenue_type = AccountType.objects.filter(company=company, code='4000').first()
        expense_type = AccountType.objects.filter(company=company, code='5100').first()

        # Create accounts in hierarchical order
        accounts_data = []

        # Level 1 - Main categories (Root accounts)
        if cash_type:
            accounts_data.append({'code': '11', 'name': 'النقدية والبنوك', 'type': cash_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if receivables_type:
            accounts_data.append({'code': '12', 'name': 'العملاء والمدينون', 'type': receivables_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if inventory_type:
            accounts_data.append({'code': '13', 'name': 'المخزون', 'type': inventory_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if fixed_assets_type:
            accounts_data.append({'code': '14', 'name': 'الأصول الثابتة', 'type': fixed_assets_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if payables_type:
            accounts_data.append({'code': '21', 'name': 'الموردون والدائنون', 'type': payables_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if capital_type:
            accounts_data.append({'code': '30', 'name': 'رأس المال', 'type': capital_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if revenue_type:
            accounts_data.append({'code': '40', 'name': 'الإيرادات', 'type': revenue_type, 'level': 1, 'allow_manual': False, 'parent': None})
        if expense_type:
            accounts_data.append({'code': '51', 'name': 'المصروفات التشغيلية', 'type': expense_type, 'level': 1, 'allow_manual': False, 'parent': None})

        # Create accounts level by level
        created_accounts = {}

        for account_data in accounts_data:
            if account_data['type']:  # Only create if type exists
                parent_account = None
                if account_data['parent']:
                    parent_account = created_accounts.get(account_data['parent'])

                account, created = Account.objects.get_or_create(
                    chart=chart,
                    code=account_data['code'],
                    defaults={
                        'name': account_data['name'],
                        'type': account_data['type'],
                        'level': account_data['level'],
                        'parent': parent_account,
                        'is_active': True,
                        'allow_manual_transactions': account_data['allow_manual'],
                        'reconcilable': account_data['allow_manual'],  # Only transaction accounts can be reconcilable
                    }
                )
                if created:
                    self.stdout.write(f'Created account: {account.code} - {account.name}')

                created_accounts[account_data['code']] = account

        # Now create level 5 transaction accounts
        transaction_accounts_data = [
            # Cash and Bank Accounts (Level 5)
            {'code': '1101', 'name': 'الخزينة الرئيسية', 'type': cash_type, 'level': 5, 'allow_manual': True, 'parent': '11'},
            {'code': '1102', 'name': 'البنك الأهلي المصري', 'type': cash_type, 'level': 5, 'allow_manual': True, 'parent': '11'},
            {'code': '1103', 'name': 'بنك مصر', 'type': cash_type, 'level': 5, 'allow_manual': True, 'parent': '11'},
            {'code': '1104', 'name': 'البنك التجاري الدولي', 'type': cash_type, 'level': 5, 'allow_manual': True, 'parent': '11'},

            # Receivables (Level 5)
            {'code': '1201', 'name': 'العملاء', 'type': receivables_type, 'level': 5, 'allow_manual': True, 'parent': '12'},
            {'code': '1202', 'name': 'أوراق القبض', 'type': receivables_type, 'level': 5, 'allow_manual': True, 'parent': '12'},
            {'code': '1203', 'name': 'مدينون متنوعون', 'type': receivables_type, 'level': 5, 'allow_manual': True, 'parent': '12'},

            # Inventory (Level 5)
            {'code': '1301', 'name': 'مخزون البضائع', 'type': inventory_type, 'level': 5, 'allow_manual': True, 'parent': '13'},
            {'code': '1302', 'name': 'مخزون المواد الخام', 'type': inventory_type, 'level': 5, 'allow_manual': True, 'parent': '13'},

            # Fixed Assets (Level 5)
            {'code': '1401', 'name': 'الأراضي والمباني', 'type': fixed_assets_type, 'level': 5, 'allow_manual': True, 'parent': '14'},
            {'code': '1402', 'name': 'الآلات والمعدات', 'type': fixed_assets_type, 'level': 5, 'allow_manual': True, 'parent': '14'},
            {'code': '1403', 'name': 'الأثاث والتجهيزات', 'type': fixed_assets_type, 'level': 5, 'allow_manual': True, 'parent': '14'},
            {'code': '1404', 'name': 'السيارات', 'type': fixed_assets_type, 'level': 5, 'allow_manual': True, 'parent': '14'},

            # Payables (Level 5)
            {'code': '2101', 'name': 'الموردون', 'type': payables_type, 'level': 5, 'allow_manual': True, 'parent': '21'},
            {'code': '2102', 'name': 'أوراق الدفع', 'type': payables_type, 'level': 5, 'allow_manual': True, 'parent': '21'},
            {'code': '2103', 'name': 'دائنون متنوعون', 'type': payables_type, 'level': 5, 'allow_manual': True, 'parent': '21'},

            # Capital (Level 5)
            {'code': '3001', 'name': 'رأس المال المدفوع', 'type': capital_type, 'level': 5, 'allow_manual': True, 'parent': '30'},

            # Revenue (Level 5)
            {'code': '4001', 'name': 'إيرادات المبيعات', 'type': revenue_type, 'level': 5, 'allow_manual': True, 'parent': '40'},
            {'code': '4002', 'name': 'إيرادات الخدمات', 'type': revenue_type, 'level': 5, 'allow_manual': True, 'parent': '40'},

            # Expenses (Level 5)
            {'code': '5101', 'name': 'مرتبات وأجور', 'type': expense_type, 'level': 5, 'allow_manual': True, 'parent': '51'},
            {'code': '5102', 'name': 'إيجارات', 'type': expense_type, 'level': 5, 'allow_manual': True, 'parent': '51'},
            {'code': '5103', 'name': 'كهرباء ومياه', 'type': expense_type, 'level': 5, 'allow_manual': True, 'parent': '51'},
            {'code': '5104', 'name': 'مصروفات إدارية', 'type': expense_type, 'level': 5, 'allow_manual': True, 'parent': '51'},
        ]

        for account_data in transaction_accounts_data:
            if account_data['type']:  # Only create if type exists
                parent_account = created_accounts.get(account_data['parent'])

                account, created = Account.objects.get_or_create(
                    chart=chart,
                    code=account_data['code'],
                    defaults={
                        'name': account_data['name'],
                        'type': account_data['type'],
                        'level': account_data['level'],
                        'parent': parent_account,
                        'is_active': True,
                        'allow_manual_transactions': account_data['allow_manual'],
                        'reconcilable': account_data['allow_manual'],
                    }
                )
                if created:
                    self.stdout.write(f'Created transaction account: {account.code} - {account.name}')

                created_accounts[account_data['code']] = account

    def create_bank_accounts(self, company, branch):
        """Create sample bank accounts"""
        banks = Bank.objects.all()
        chart = AccountChart.objects.filter(company=company).first()

        bank_accounts_data = [
            {
                'name': 'حساب جاري - البنك الأهلي المصري',
                'number': '****************',
                'iban': '*****************************',
                'bank': banks.filter(code='NBE').first(),
                'account_type': 'current',
                'currency': 'EGP',
                'is_active': True,
                'notes': 'الحساب الجاري الرئيسي للشركة'
            },
            {
                'name': 'حساب توفير - بنك مصر',
                'number': '****************',
                'iban': '*****************************',
                'bank': banks.filter(code='BM').first(),
                'account_type': 'savings',
                'currency': 'EGP',
                'is_active': True,
                'notes': 'حساب توفير للاحتياطيات'
            },
            {
                'name': 'حساب بالدولار - البنك التجاري الدولي',
                'number': '****************',
                'iban': '*****************************',
                'bank': banks.filter(code='CIB').first(),
                'account_type': 'current',
                'currency': 'USD',
                'is_active': True,
                'notes': 'حساب بالعملة الأجنبية للمعاملات الدولية'
            },
            {
                'name': 'حساب استثماري - بنك القاهرة',
                'number': '****************',
                'iban': '*****************************',
                'bank': banks.filter(code='CAI').first(),
                'account_type': 'investment',
                'currency': 'EGP',
                'is_active': True,
                'notes': 'حساب للاستثمارات قصيرة الأجل'
            }
        ]

        # Get bank account from chart
        bank_account = Account.objects.filter(chart=chart, code='1102').first()

        for account_data in bank_accounts_data:
            if account_data['bank']:
                bank_account_obj, created = BankAccount.objects.get_or_create(
                    number=account_data['number'],
                    defaults={
                        'name': account_data['name'],
                        'iban': account_data['iban'],
                        'bank': account_data['bank'],
                        'company': company,
                        'branch': branch,
                        'account': bank_account,
                        'account_type': account_data['account_type'],
                        'currency': account_data['currency'],
                        'is_active': account_data['is_active'],
                        'notes': account_data['notes'],
                    }
                )
                if created:
                    self.stdout.write(f'Created bank account: {bank_account_obj.name}')

    def create_cash_registers(self, company, branch):
        """Create sample cash registers"""
        chart = AccountChart.objects.filter(company=company).first()
        cash_account = Account.objects.filter(chart=chart, code='1101').first()

        cash_registers_data = [
            {
                'name': 'الخزينة الرئيسية',
                'code': 'MAIN_CASH',
                'notes': 'الخزينة الرئيسية للشركة'
            },
            {
                'name': 'خزينة المبيعات',
                'code': 'SALES_CASH',
                'notes': 'خزينة قسم المبيعات'
            },
            {
                'name': 'خزينة المشتريات',
                'code': 'PURCHASE_CASH',
                'notes': 'خزينة قسم المشتريات'
            }
        ]

        for register_data in cash_registers_data:
            cash_register, created = CashRegister.objects.get_or_create(
                code=register_data['code'],
                company=company,
                defaults={
                    'name': register_data['name'],
                    'branch': branch,
                    'account': cash_account,
                    'is_active': True,
                    'notes': register_data['notes'],
                }
            )
            if created:
                self.stdout.write(f'Created cash register: {cash_register.name}')

    def create_checkbooks(self, company, branch):
        """Create sample checkbooks"""
        bank_accounts = BankAccount.objects.filter(company=company)

        checkbooks_data = [
            {
                'name': 'دفتر شيكات البنك الأهلي - 001',
                'code': 'NBE_CHK_001',
                'start_number': '1000001',
                'end_number': '1000050',
                'next_number': '1000001',
                'notes': 'دفتر الشيكات الأول للبنك الأهلي'
            },
            {
                'name': 'دفتر شيكات بنك مصر - 001',
                'code': 'BM_CHK_001',
                'start_number': '2000001',
                'end_number': '2000050',
                'next_number': '2000001',
                'notes': 'دفتر الشيكات الأول لبنك مصر'
            },
            {
                'name': 'دفتر شيكات البنك التجاري - 001',
                'code': 'CIB_CHK_001',
                'start_number': '3000001',
                'end_number': '3000050',
                'next_number': '3000001',
                'notes': 'دفتر الشيكات الأول للبنك التجاري الدولي'
            }
        ]

        for i, checkbook_data in enumerate(checkbooks_data):
            if i < len(bank_accounts):
                checkbook, created = CheckBook.objects.get_or_create(
                    code=checkbook_data['code'],
                    defaults={
                        'name': checkbook_data['name'],
                        'bank_account': bank_accounts[i],
                        'start_number': checkbook_data['start_number'],
                        'end_number': checkbook_data['end_number'],
                        'next_number': checkbook_data['next_number'],
                        'is_active': True,
                        'notes': checkbook_data['notes'],
                    }
                )
                if created:
                    self.stdout.write(f'Created checkbook: {checkbook.name}')

    def create_payment_methods(self, company):
        """Create sample payment methods"""
        chart = AccountChart.objects.filter(company=company).first()
        cash_account = Account.objects.filter(chart=chart, code='1101').first()
        bank_account = Account.objects.filter(chart=chart, code='1102').first()

        cash_register = CashRegister.objects.filter(company=company).first()
        bank_account_obj = BankAccount.objects.filter(company=company).first()

        payment_methods_data = [
            {
                'name': 'نقدي',
                'code': 'CASH',
                'payment_type': 'cash',
                'account': cash_account,
                'cash_register': cash_register,
                'notes': 'الدفع النقدي'
            },
            {
                'name': 'شيك',
                'code': 'CHECK',
                'payment_type': 'check',
                'account': bank_account,
                'bank_account': bank_account_obj,
                'notes': 'الدفع بالشيك'
            },
            {
                'name': 'تحويل بنكي',
                'code': 'TRANSFER',
                'payment_type': 'bank_transfer',
                'account': bank_account,
                'bank_account': bank_account_obj,
                'notes': 'التحويل البنكي'
            },
            {
                'name': 'بطاقة ائتمان',
                'code': 'CREDIT_CARD',
                'payment_type': 'credit_card',
                'account': bank_account,
                'notes': 'الدفع بالبطاقة الائتمانية'
            }
        ]

        for method_data in payment_methods_data:
            payment_method, created = PaymentMethod.objects.get_or_create(
                code=method_data['code'],
                company=company,
                defaults={
                    'name': method_data['name'],
                    'payment_type': method_data['payment_type'],
                    'account': method_data['account'],
                    'cash_register': method_data.get('cash_register'),
                    'bank_account': method_data.get('bank_account'),
                    'is_active': True,
                    'notes': method_data['notes'],
                }
            )
            if created:
                self.stdout.write(f'Created payment method: {payment_method.name}')

    def create_asset_categories(self, company):
        """Create sample asset categories"""
        chart = AccountChart.objects.filter(company=company).first()
        asset_account = Account.objects.filter(chart=chart, code='1401').first()
        depreciation_expense_account = Account.objects.filter(chart=chart, code='5104').first()  # Administrative expenses

        if not asset_account or not depreciation_expense_account:
            self.stdout.write('Required accounts not found for asset categories')
            return

        categories_data = [
            {
                'name': 'مباني وإنشاءات',
                'code': 'BUILDINGS',
                'depreciation_rate': Decimal('5.00'),
                'useful_life_years': 20,
                'notes': 'المباني والإنشاءات الثابتة'
            },
            {
                'name': 'آلات ومعدات',
                'code': 'MACHINERY',
                'depreciation_rate': Decimal('10.00'),
                'useful_life_years': 10,
                'notes': 'الآلات والمعدات الصناعية'
            },
            {
                'name': 'أثاث وتجهيزات',
                'code': 'FURNITURE',
                'depreciation_rate': Decimal('20.00'),
                'useful_life_years': 5,
                'notes': 'الأثاث والتجهيزات المكتبية'
            },
            {
                'name': 'سيارات ومركبات',
                'code': 'VEHICLES',
                'depreciation_rate': Decimal('25.00'),
                'useful_life_years': 4,
                'notes': 'السيارات والمركبات'
            },
            {
                'name': 'أجهزة كمبيوتر',
                'code': 'COMPUTERS',
                'depreciation_rate': Decimal('33.33'),
                'useful_life_years': 3,
                'notes': 'أجهزة الكمبيوتر والتكنولوجيا'
            }
        ]

        for category_data in categories_data:
            category, created = AssetCategory.objects.get_or_create(
                code=category_data['code'],
                company=company,
                defaults={
                    'name': category_data['name'],
                    'asset_account': asset_account,
                    'depreciation_account': depreciation_expense_account,  # Use expense account
                    'accumulated_depreciation_account': asset_account,
                    'depreciation_rate': category_data['depreciation_rate'],
                    'useful_life_years': category_data['useful_life_years'],
                    'is_active': True,
                    'notes': category_data['notes'],
                }
            )
            if created:
                self.stdout.write(f'Created asset category: {category.name}')

    def create_depreciation_methods(self, company):
        """Create sample depreciation methods"""
        methods_data = [
            {
                'name': 'القسط الثابت',
                'code': 'STRAIGHT_LINE',
                'method_type': 'straight_line',
                'factor': Decimal('1.00'),
                'notes': 'طريقة القسط الثابت للإهلاك'
            },
            {
                'name': 'الرصيد المتناقص',
                'code': 'DECLINING_BALANCE',
                'method_type': 'declining_balance',
                'factor': Decimal('2.00'),
                'notes': 'طريقة الرصيد المتناقص للإهلاك'
            },
            {
                'name': 'مجموع سنوات الاستخدام',
                'code': 'SUM_OF_YEARS',
                'method_type': 'sum_of_years',
                'factor': Decimal('1.00'),
                'notes': 'طريقة مجموع سنوات الاستخدام'
            }
        ]

        for method_data in methods_data:
            method, created = DepreciationMethod.objects.get_or_create(
                code=method_data['code'],
                company=company,
                defaults={
                    'name': method_data['name'],
                    'method_type': method_data['method_type'],
                    'factor': method_data['factor'],
                    'is_active': True,
                    'notes': method_data['notes'],
                }
            )
            if created:
                self.stdout.write(f'Created depreciation method: {method.name}')

    def create_assets(self, company, branch):
        """Create sample assets"""
        categories = AssetCategory.objects.filter(company=company)
        methods = DepreciationMethod.objects.filter(company=company)
        chart = AccountChart.objects.filter(company=company).first()
        asset_account = Account.objects.filter(chart=chart, code='1401').first()

        if not categories.exists() or not methods.exists():
            return

        assets_data = [
            {
                'name': 'مبنى المكتب الرئيسي',
                'code': 'BUILDING_001',
                'category': categories.filter(code='BUILDINGS').first(),
                'purchase_value': Decimal('2000000.00'),
                'salvage_value': Decimal('200000.00'),
                'useful_life_years': 20,
                'location': 'القاهرة - مصر الجديدة',
                'description': 'المبنى الرئيسي للشركة'
            },
            {
                'name': 'آلة طباعة ديجيتال',
                'code': 'MACHINE_001',
                'category': categories.filter(code='MACHINERY').first(),
                'purchase_value': Decimal('150000.00'),
                'salvage_value': Decimal('15000.00'),
                'useful_life_years': 10,
                'location': 'قسم الإنتاج',
                'serial_number': 'DPM-2023-001',
                'manufacturer': 'شركة الطباعة المتقدمة',
                'description': 'آلة طباعة ديجيتال عالية الجودة'
            },
            {
                'name': 'مكاتب إدارية',
                'code': 'FURNITURE_001',
                'category': categories.filter(code='FURNITURE').first(),
                'purchase_value': Decimal('50000.00'),
                'salvage_value': Decimal('5000.00'),
                'useful_life_years': 5,
                'location': 'الطابق الثاني - الإدارة',
                'description': 'مجموعة مكاتب إدارية مع كراسي'
            },
            {
                'name': 'سيارة تويوتا كامري',
                'code': 'VEHICLE_001',
                'category': categories.filter(code='VEHICLES').first(),
                'purchase_value': Decimal('400000.00'),
                'salvage_value': Decimal('100000.00'),
                'useful_life_years': 4,
                'location': 'موقف السيارات',
                'serial_number': 'TOY-2023-CAM-001',
                'model': 'كامري 2023',
                'manufacturer': 'تويوتا',
                'description': 'سيارة إدارية للمدير العام'
            },
            {
                'name': 'أجهزة كمبيوتر مكتبية',
                'code': 'COMPUTER_001',
                'category': categories.filter(code='COMPUTERS').first(),
                'purchase_value': Decimal('80000.00'),
                'salvage_value': Decimal('8000.00'),
                'useful_life_years': 3,
                'location': 'قسم تكنولوجيا المعلومات',
                'description': '10 أجهزة كمبيوتر مكتبية للموظفين'
            }
        ]

        straight_line_method = methods.filter(code='STRAIGHT_LINE').first()

        for asset_data in assets_data:
            if asset_data['category']:
                purchase_date = timezone.now().date() - timedelta(days=random.randint(30, 365))
                asset, created = Asset.objects.get_or_create(
                    code=asset_data['code'],
                    defaults={
                        'name': asset_data['name'],
                        'category': asset_data['category'],
                        'company': company,
                        'branch': branch,
                        'purchase_date': purchase_date,
                        'in_service_date': purchase_date,
                        'purchase_value': asset_data['purchase_value'],
                        'salvage_value': asset_data['salvage_value'],
                        'depreciation_method': straight_line_method,
                        'useful_life_years': asset_data['useful_life_years'],
                        'depreciation_frequency': 'monthly',
                        'asset_account': asset_account,
                        'depreciation_account': asset_account,
                        'accumulated_depreciation_account': asset_account,
                        'is_active': True,
                        'state': 'active',
                        'description': asset_data['description'],
                        'location': asset_data['location'],
                        'serial_number': asset_data.get('serial_number', ''),
                        'model': asset_data.get('model', ''),
                        'manufacturer': asset_data.get('manufacturer', ''),
                    }
                )
                if created:
                    self.stdout.write(f'Created asset: {asset.name}')

    def create_journals(self, company):
        """Create sample journals"""
        chart = AccountChart.objects.filter(company=company).first()
        cash_account = Account.objects.filter(chart=chart, code='1101').first()
        bank_account = Account.objects.filter(chart=chart, code='1102').first()
        sales_account = Account.objects.filter(chart=chart, code='4001').first()
        expense_account = Account.objects.filter(chart=chart, code='5101').first()

        journals_data = [
            {
                'name': 'دفتر اليومية العام',
                'code': 'GJ',
                'type': 'general',
                'default_debit_account': None,
                'default_credit_account': None,
                'notes': 'دفتر اليومية العام لجميع المعاملات'
            },
            {
                'name': 'دفتر المبيعات',
                'code': 'SJ',
                'type': 'sale',
                'default_debit_account': cash_account,
                'default_credit_account': sales_account,
                'notes': 'دفتر يومية المبيعات'
            },
            {
                'name': 'دفتر المشتريات',
                'code': 'PJ',
                'type': 'purchase',
                'default_debit_account': expense_account,
                'default_credit_account': cash_account,
                'notes': 'دفتر يومية المشتريات'
            },
            {
                'name': 'دفتر النقدية',
                'code': 'CJ',
                'type': 'cash',
                'default_debit_account': cash_account,
                'default_credit_account': None,
                'notes': 'دفتر يومية النقدية'
            },
            {
                'name': 'دفتر البنك',
                'code': 'BJ',
                'type': 'bank',
                'default_debit_account': bank_account,
                'default_credit_account': None,
                'notes': 'دفتر يومية البنك'
            }
        ]

        for journal_data in journals_data:
            journal, created = Journal.objects.get_or_create(
                code=journal_data['code'],
                company=company,
                defaults={
                    'name': journal_data['name'],
                    'type': journal_data['type'],
                    'default_debit_account': journal_data['default_debit_account'],
                    'default_credit_account': journal_data['default_credit_account'],
                    'is_active': True,
                    'notes': journal_data['notes'],
                }
            )
            if created:
                self.stdout.write(f'Created journal: {journal.name}')

    def create_sample_transactions(self, company, branch):
        """Create sample transactions including checks and vouchers"""
        fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()
        if not fiscal_year:
            return

        # Create sample journal entries
        self.create_journal_entries(company, branch, fiscal_year)

        # Create sample checks
        self.create_sample_checks(company, branch)

        # Create sample vouchers
        self.create_sample_vouchers(company, branch)

    def create_journal_entries(self, company, branch, fiscal_year):
        """Create sample journal entries"""
        chart = AccountChart.objects.filter(company=company).first()
        journals = Journal.objects.filter(company=company)

        if not journals.exists():
            return

        # Get accounts
        cash_account = Account.objects.filter(chart=chart, code='1101').first()
        bank_account = Account.objects.filter(chart=chart, code='1102').first()
        customers_account = Account.objects.filter(chart=chart, code='1201').first()
        suppliers_account = Account.objects.filter(chart=chart, code='2101').first()
        capital_account = Account.objects.filter(chart=chart, code='3001').first()
        sales_account = Account.objects.filter(chart=chart, code='4001').first()
        salary_account = Account.objects.filter(chart=chart, code='5101').first()
        rent_account = Account.objects.filter(chart=chart, code='5102').first()

        general_journal = journals.filter(code='GJ').first()

        # Sample journal entries
        entries_data = [
            {
                'name': 'رأس المال الافتتاحي',
                'date': fiscal_year.start_date,
                'lines': [
                    {'account': cash_account, 'debit': Decimal('500000.00'), 'credit': Decimal('0.00'), 'name': 'نقدية افتتاحية'},
                    {'account': bank_account, 'debit': Decimal('1000000.00'), 'credit': Decimal('0.00'), 'name': 'رصيد بنكي افتتاحي'},
                    {'account': capital_account, 'debit': Decimal('0.00'), 'credit': Decimal('1500000.00'), 'name': 'رأس المال المدفوع'},
                ]
            },
            {
                'name': 'مبيعات نقدية',
                'date': timezone.now().date() - timedelta(days=15),
                'lines': [
                    {'account': cash_account, 'debit': Decimal('50000.00'), 'credit': Decimal('0.00'), 'name': 'مبيعات نقدية'},
                    {'account': sales_account, 'debit': Decimal('0.00'), 'credit': Decimal('50000.00'), 'name': 'إيرادات المبيعات'},
                ]
            },
            {
                'name': 'مبيعات آجلة للعميل أحمد محمد',
                'date': timezone.now().date() - timedelta(days=10),
                'lines': [
                    {'account': customers_account, 'debit': Decimal('75000.00'), 'credit': Decimal('0.00'), 'name': 'مبيعات آجلة', 'partner_name': 'أحمد محمد علي'},
                    {'account': sales_account, 'debit': Decimal('0.00'), 'credit': Decimal('75000.00'), 'name': 'إيرادات المبيعات'},
                ]
            },
            {
                'name': 'مشتريات من المورد شركة النور',
                'date': timezone.now().date() - timedelta(days=8),
                'lines': [
                    {'account': salary_account, 'debit': Decimal('30000.00'), 'credit': Decimal('0.00'), 'name': 'مشتريات بضائع'},
                    {'account': suppliers_account, 'debit': Decimal('0.00'), 'credit': Decimal('30000.00'), 'name': 'مشتريات آجلة', 'partner_name': 'شركة النور للتجارة'},
                ]
            },
            {
                'name': 'دفع مرتبات الموظفين',
                'date': timezone.now().date() - timedelta(days=5),
                'lines': [
                    {'account': salary_account, 'debit': Decimal('80000.00'), 'credit': Decimal('0.00'), 'name': 'مرتبات شهر ديسمبر'},
                    {'account': bank_account, 'debit': Decimal('0.00'), 'credit': Decimal('80000.00'), 'name': 'تحويل مرتبات'},
                ]
            },
            {
                'name': 'دفع إيجار المكتب',
                'date': timezone.now().date() - timedelta(days=3),
                'lines': [
                    {'account': rent_account, 'debit': Decimal('25000.00'), 'credit': Decimal('0.00'), 'name': 'إيجار شهر ديسمبر'},
                    {'account': cash_account, 'debit': Decimal('0.00'), 'credit': Decimal('25000.00'), 'name': 'دفع نقدي'},
                ]
            },
            {
                'name': 'تحصيل من العميل أحمد محمد',
                'date': timezone.now().date() - timedelta(days=2),
                'lines': [
                    {'account': bank_account, 'debit': Decimal('40000.00'), 'credit': Decimal('0.00'), 'name': 'تحصيل جزئي'},
                    {'account': customers_account, 'debit': Decimal('0.00'), 'credit': Decimal('40000.00'), 'name': 'تحصيل من العميل', 'partner_name': 'أحمد محمد علي'},
                ]
            }
        ]

        for entry_data in entries_data:
            # Create journal entry
            entry, created = JournalEntry.objects.get_or_create(
                name=entry_data['name'],
                date=entry_data['date'],
                company=company,
                defaults={
                    'journal': general_journal,
                    'fiscal_year': fiscal_year,
                    'branch': branch,
                    'state': 'posted',
                }
            )

            if created:
                # Create journal entry lines
                for line_data in entry_data['lines']:
                    if line_data['account']:
                        JournalEntryLine.objects.create(
                            entry=entry,
                            account=line_data['account'],
                            name=line_data['name'],
                            debit=line_data['debit'],
                            credit=line_data['credit'],
                            partner_name=line_data.get('partner_name', ''),
                            partner_type=line_data.get('partner_type', ''),
                        )

                self.stdout.write(f'Created journal entry: {entry.name}')

    def create_sample_checks(self, company, branch):
        """Create sample checks"""
        checkbooks = CheckBook.objects.filter(bank_account__company=company)

        if not checkbooks.exists():
            return

        # Sample outgoing checks (issued by company)
        outgoing_checks_data = [
            {
                'amount': Decimal('25000.00'),
                'date': timezone.now().date() - timedelta(days=7),
                'due_date': timezone.now().date() - timedelta(days=7),
                'partner_name': 'شركة الكهرباء',
                'memo': 'فاتورة كهرباء شهر نوفمبر',
                'state': 'issued'
            },
            {
                'amount': Decimal('15000.00'),
                'date': timezone.now().date() - timedelta(days=5),
                'due_date': timezone.now().date() + timedelta(days=10),
                'partner_name': 'شركة المياه',
                'memo': 'فاتورة مياه شهر نوفمبر',
                'state': 'pending'
            },
            {
                'amount': Decimal('50000.00'),
                'date': timezone.now().date() - timedelta(days=3),
                'due_date': timezone.now().date() + timedelta(days=15),
                'partner_name': 'شركة النور للتجارة',
                'memo': 'دفعة من قيمة المشتريات',
                'state': 'pending'
            }
        ]

        # Sample incoming checks (received by company)
        incoming_checks_data = [
            {
                'amount': Decimal('40000.00'),
                'date': timezone.now().date() - timedelta(days=6),
                'due_date': timezone.now().date() + timedelta(days=5),
                'partner_name': 'أحمد محمد علي',
                'memo': 'دفعة من قيمة المبيعات',
                'state': 'received',
                'bank_name': 'البنك الأهلي المصري',
                'account_number': '****************'
            },
            {
                'amount': Decimal('60000.00'),
                'date': timezone.now().date() - timedelta(days=4),
                'due_date': timezone.now().date() + timedelta(days=8),
                'partner_name': 'شركة الأمل التجارية',
                'memo': 'دفعة من قيمة المبيعات',
                'state': 'deposited',
                'bank_name': 'بنك مصر',
                'account_number': '****************'
            }
        ]

        # Create outgoing checks
        checkbook = checkbooks.first()
        if checkbook:
            for check_data in outgoing_checks_data:
                current_number = checkbook.get_next_check_number()
                check, created = Check.objects.get_or_create(
                    number=current_number,
                    check_book=checkbook,
                    defaults={
                        'amount': check_data['amount'],
                        'date': check_data['date'],
                        'due_date': check_data['due_date'],
                        'check_type': 'outgoing',
                        'state': check_data['state'],
                        'bank_account': checkbook.bank_account,
                        'company': company,
                        'branch': branch,
                        'partner_name': check_data['partner_name'],
                        'partner_type': 'supplier',
                        'memo': check_data['memo'],
                    }
                )

                if created:
                    self.stdout.write(f'Created outgoing check: {check.number} - {check.partner_name}')

        # Create incoming checks
        bank_accounts = BankAccount.objects.filter(company=company)
        default_bank_account = bank_accounts.first() if bank_accounts.exists() else None

        if default_bank_account:
            for check_data in incoming_checks_data:
                check_number = str(random.randint(5000000, 5999999))
                check, created = Check.objects.get_or_create(
                    number=check_number,
                    defaults={
                        'amount': check_data['amount'],
                        'date': check_data['date'],
                        'due_date': check_data['due_date'],
                        'check_type': 'incoming',
                        'state': check_data['state'],
                        'bank_name': check_data['bank_name'],
                        'account_number': check_data['account_number'],
                        'bank_account': default_bank_account,  # Add required bank_account
                        'company': company,
                        'branch': branch,
                        'partner_name': check_data['partner_name'],
                        'partner_type': 'customer',
                        'memo': check_data['memo'],
                    }
                )

                if created:
                    self.stdout.write(f'Created incoming check: {check.number} - {check.partner_name}')

    def create_sample_vouchers(self, company, branch):
        """Create sample vouchers (receipt and payment)"""
        payment_methods = PaymentMethod.objects.filter(company=company)
        fiscal_year = FiscalYear.objects.filter(company=company, is_active=True).first()

        if not payment_methods.exists() or not fiscal_year:
            return

        cash_method = payment_methods.filter(code='CASH').first()
        check_method = payment_methods.filter(code='CHECK').first()
        transfer_method = payment_methods.filter(code='TRANSFER').first()

        # Sample receipt vouchers
        receipt_vouchers_data = [
            {
                'number': 'RV001',
                'date': timezone.now().date() - timedelta(days=10),
                'amount': Decimal('75000.00'),
                'partner_name': 'أحمد محمد علي',
                'partner_type': 'customer',
                'payment_method': cash_method,
                'memo': 'تحصيل نقدي من العميل أحمد محمد'
            },
            {
                'number': 'RV002',
                'date': timezone.now().date() - timedelta(days=8),
                'amount': Decimal('120000.00'),
                'partner_name': 'شركة الأمل التجارية',
                'partner_type': 'customer',
                'payment_method': transfer_method,
                'memo': 'تحويل بنكي من شركة الأمل'
            },
            {
                'number': 'RV003',
                'date': timezone.now().date() - timedelta(days=5),
                'amount': Decimal('85000.00'),
                'partner_name': 'محمد أحمد سالم',
                'partner_type': 'customer',
                'payment_method': check_method,
                'memo': 'شيك من العميل محمد أحمد'
            }
        ]

        # Sample payment vouchers
        payment_vouchers_data = [
            {
                'number': 'PV001',
                'date': timezone.now().date() - timedelta(days=9),
                'amount': Decimal('30000.00'),
                'partner_name': 'شركة النور للتجارة',
                'partner_type': 'supplier',
                'payment_method': transfer_method,
                'memo': 'دفع لشركة النور - قيمة مشتريات'
            },
            {
                'number': 'PV002',
                'date': timezone.now().date() - timedelta(days=7),
                'amount': Decimal('25000.00'),
                'partner_name': 'شركة الكهرباء',
                'partner_type': 'supplier',
                'payment_method': check_method,
                'memo': 'دفع فاتورة كهرباء شهر نوفمبر'
            },
            {
                'number': 'PV003',
                'date': timezone.now().date() - timedelta(days=4),
                'amount': Decimal('15000.00'),
                'partner_name': 'شركة المياه',
                'partner_type': 'supplier',
                'payment_method': cash_method,
                'memo': 'دفع نقدي لفاتورة المياه'
            },
            {
                'number': 'PV004',
                'date': timezone.now().date() - timedelta(days=2),
                'amount': Decimal('80000.00'),
                'partner_name': 'موظفي الشركة',
                'partner_type': 'employee',
                'payment_method': transfer_method,
                'memo': 'دفع مرتبات شهر ديسمبر'
            }
        ]

        # Create receipt vouchers
        for voucher_data in receipt_vouchers_data:
            if voucher_data['payment_method']:
                voucher, created = Voucher.objects.get_or_create(
                    number=voucher_data['number'],
                    defaults={
                        'date': voucher_data['date'],
                        'voucher_type': 'receipt',
                        'amount': voucher_data['amount'],
                        'company': company,  # Add required company
                        'fiscal_year': fiscal_year,  # Add required fiscal_year
                        'branch': branch,
                        'partner_name': voucher_data['partner_name'],
                        'partner_type': voucher_data['partner_type'],
                        'payment_method': voucher_data['payment_method'],
                        'memo': voucher_data['memo'],
                        'state': 'posted',
                    }
                )
                if created:
                    self.stdout.write(f'Created receipt voucher: {voucher.number} - {voucher.partner_name}')

        # Create payment vouchers
        for voucher_data in payment_vouchers_data:
            if voucher_data['payment_method']:
                voucher, created = Voucher.objects.get_or_create(
                    number=voucher_data['number'],
                    defaults={
                        'date': voucher_data['date'],
                        'voucher_type': 'payment',
                        'amount': voucher_data['amount'],
                        'company': company,  # Add required company
                        'fiscal_year': fiscal_year,  # Add required fiscal_year
                        'branch': branch,
                        'partner_name': voucher_data['partner_name'],
                        'partner_type': voucher_data['partner_type'],
                        'payment_method': voucher_data['payment_method'],
                        'memo': voucher_data['memo'],
                        'state': 'posted',
                    }
                )
                if created:
                    self.stdout.write(f'Created payment voucher: {voucher.number} - {voucher.partner_name}')

    def create_customer_supplier_accounts(self, company, branch):
        """Create detailed customer and supplier accounts"""
        chart = AccountChart.objects.filter(company=company).first()
        customers_account = Account.objects.filter(chart=chart, code='1201').first()
        suppliers_account = Account.objects.filter(chart=chart, code='2101').first()

        if not customers_account or not suppliers_account:
            return

        # Create detailed customer accounts
        customers_data = [
            {
                'code': '120101',
                'name': 'أحمد محمد علي',
                'parent': customers_account,
                'level': 5,
                'description': 'عميل - تاجر تجزئة'
            },
            {
                'code': '120102',
                'name': 'شركة الأمل التجارية',
                'parent': customers_account,
                'level': 5,
                'description': 'عميل - شركة تجارية'
            },
            {
                'code': '120103',
                'name': 'محمد أحمد سالم',
                'parent': customers_account,
                'level': 5,
                'description': 'عميل - تاجر جملة'
            },
            {
                'code': '120104',
                'name': 'شركة النجاح للاستيراد',
                'parent': customers_account,
                'level': 5,
                'description': 'عميل - شركة استيراد'
            },
            {
                'code': '120105',
                'name': 'فاطمة حسن محمود',
                'parent': customers_account,
                'level': 5,
                'description': 'عميل - تاجرة تجزئة'
            }
        ]

        # Create detailed supplier accounts
        suppliers_data = [
            {
                'code': '210101',
                'name': 'شركة النور للتجارة',
                'parent': suppliers_account,
                'level': 5,
                'description': 'مورد - بضائع عامة'
            },
            {
                'code': '210102',
                'name': 'شركة الكهرباء',
                'parent': suppliers_account,
                'level': 5,
                'description': 'مورد - خدمات كهرباء'
            },
            {
                'code': '210103',
                'name': 'شركة المياه',
                'parent': suppliers_account,
                'level': 5,
                'description': 'مورد - خدمات مياه'
            },
            {
                'code': '210104',
                'name': 'شركة الاتصالات',
                'parent': suppliers_account,
                'level': 5,
                'description': 'مورد - خدمات اتصالات'
            },
            {
                'code': '210105',
                'name': 'شركة التوريدات الحديثة',
                'parent': suppliers_account,
                'level': 5,
                'description': 'مورد - مواد خام'
            }
        ]

        # Create customer accounts
        for customer_data in customers_data:
            account, created = Account.objects.get_or_create(
                chart=chart,
                code=customer_data['code'],
                defaults={
                    'name': customer_data['name'],
                    'type': customers_account.type,
                    'level': customer_data['level'],
                    'parent': customer_data['parent'],
                    'is_active': True,
                    'allow_manual_transactions': True,
                    'reconcilable': True,
                    'description': customer_data['description'],
                }
            )
            if created:
                self.stdout.write(f'Created customer account: {account.code} - {account.name}')

        # Create supplier accounts
        for supplier_data in suppliers_data:
            account, created = Account.objects.get_or_create(
                chart=chart,
                code=supplier_data['code'],
                defaults={
                    'name': supplier_data['name'],
                    'type': suppliers_account.type,
                    'level': supplier_data['level'],
                    'parent': supplier_data['parent'],
                    'is_active': True,
                    'allow_manual_transactions': True,
                    'reconcilable': True,
                    'description': supplier_data['description'],
                }
            )
            if created:
                self.stdout.write(f'Created supplier account: {account.code} - {account.name}')
