from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import User

class CustomUserAdmin(UserAdmin):
    """
    Custom admin for User model
    """
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name', 'email', 'phone', 'address', 'profile_picture')}),
        (_('Company info'), {'fields': ('company', 'branch')}),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    list_display = ('username', 'email', 'first_name', 'last_name', 'company', 'branch', 'is_staff')
    list_filter = ('is_staff', 'is_superuser', 'is_active', 'company', 'branch')
    search_fields = ('username', 'first_name', 'last_name', 'email', 'phone')

admin.site.register(User, CustomUserAdmin)
