from django import forms
from django.contrib.auth.models import Group, Permission
from django.utils.translation import gettext_lazy as _

class GroupForm(forms.ModelForm):
    """
    Form for creating/editing groups
    """
    class Meta:
        model = Group
        fields = ['name', 'permissions']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'permissions': forms.CheckboxSelectMultiple(),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Group permissions by content type
        permissions = Permission.objects.all().select_related('content_type')
        
        # Sort permissions by content type and name
        permissions = sorted(permissions, key=lambda p: (p.content_type.app_label, p.content_type.model, p.name))
        
        self.fields['permissions'].queryset = permissions
        self.fields['permissions'].widget.attrs['class'] = 'form-check-input'
