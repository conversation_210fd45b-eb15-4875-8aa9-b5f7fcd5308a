from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib.auth import login, logout, authenticate
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import HttpResponseRedirect
from django.urls import reverse
from .models import User
from .forms import UserForm, UserProfileForm, LoginForm

def user_login(request):
    """
    User login view
    """
    if request.user.is_authenticated:
        return redirect('core:dashboard')

    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user = authenticate(username=username, password=password)

            if user is not None:
                if user.is_active:
                    login(request, user)
                    next_url = request.GET.get('next', reverse('core:dashboard'))
                    return HttpResponseRedirect(next_url)
                else:
                    messages.error(request, _('Your account is disabled.'))
            else:
                messages.error(request, _('Invalid login credentials.'))
    else:
        form = LoginForm()

    return render(request, 'users/login.html', {'form': form})

@login_required
def user_logout(request):
    """
    User logout view
    """
    logout(request)
    messages.success(request, _('You have been successfully logged out.'))
    return redirect('users:login')

@login_required
def user_profile(request):
    """
    User profile view
    """
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, _('Your profile has been updated.'))
            return redirect('users:user_profile')
    else:
        form = UserProfileForm(instance=request.user)

    return render(request, 'users/profile.html', {'form': form})

@login_required
@permission_required('users.view_user')
def user_list(request):
    """
    List all users
    """
    # Filter users by company if user is not superuser
    if request.user.is_superuser:
        users = User.objects.all()
    else:
        # Only show users from the same company
        users = User.objects.filter(company=request.user.company)

    return render(request, 'users/user_list.html', {'users': users})

@login_required
@permission_required('users.add_user')
def user_create(request):
    """
    Create a new user
    """
    if request.method == 'POST':
        form = UserForm(request.POST, request.FILES)
        if form.is_valid():
            # Set company to user's company if not superuser
            user = form.save(commit=False)
            if not request.user.is_superuser and not user.company:
                user.company = request.user.company
            user.save()
            form.save_m2m()  # Save many-to-many relationships

            messages.success(request, _('User created successfully.'))
            return redirect('users:user_list')
    else:
        form = UserForm()

    return render(request, 'users/user_form.html', {'form': form})

@login_required
@permission_required('users.change_user')
def user_edit(request, pk):
    """
    Edit an existing user
    """
    user = get_object_or_404(User, pk=pk)

    # Check if user has permission to edit this user
    if not request.user.is_superuser and user.company != request.user.company:
        messages.error(request, _('You do not have permission to edit this user.'))
        return redirect('users:user_list')

    if request.method == 'POST':
        form = UserForm(request.POST, request.FILES, instance=user)
        if form.is_valid():
            form.save()
            messages.success(request, _('User updated successfully.'))
            return redirect('users:user_list')
    else:
        form = UserForm(instance=user)

    return render(request, 'users/user_form.html', {'form': form, 'user': user})

@login_required
@permission_required('users.delete_user')
def user_delete(request, pk):
    """
    Delete a user
    """
    user = get_object_or_404(User, pk=pk)

    # Check if user has permission to delete this user
    if not request.user.is_superuser and user.company != request.user.company:
        messages.error(request, _('You do not have permission to delete this user.'))
        return redirect('users:user_list')

    # Prevent self-deletion
    if user == request.user:
        messages.error(request, _('You cannot delete your own account.'))
        return redirect('users:user_list')

    if request.method == 'POST':
        user.delete()
        messages.success(request, _('User deleted successfully.'))
        return redirect('users:user_list')

    return render(request, 'users/user_confirm_delete.html', {'user': user})
