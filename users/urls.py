from django.urls import path
from . import views
from . import group_views

app_name = 'users'

urlpatterns = [
    # User management
    path('', views.user_list, name='user_list'),
    path('create/', views.user_create, name='user_create'),
    path('edit/<int:pk>/', views.user_edit, name='user_edit'),
    path('delete/<int:pk>/', views.user_delete, name='user_delete'),
    path('profile/', views.user_profile, name='user_profile'),

    # Authentication
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),

    # Group management
    path('groups/', group_views.group_list, name='group_list'),
    path('groups/create/', group_views.group_create, name='group_create'),
    path('groups/edit/<int:pk>/', group_views.group_edit, name='group_edit'),
    path('groups/delete/<int:pk>/', group_views.group_delete, name='group_delete'),
]
