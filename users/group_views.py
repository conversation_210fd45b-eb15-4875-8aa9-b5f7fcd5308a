from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required, permission_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import Group
from .group_forms import GroupForm

@login_required
@permission_required('auth.view_group')
def group_list(request):
    """
    List all groups
    """
    groups = Group.objects.all()
    return render(request, 'users/group_list.html', {'groups': groups})

@login_required
@permission_required('auth.add_group')
def group_create(request):
    """
    Create a new group
    """
    if request.method == 'POST':
        form = GroupForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, _('Group created successfully.'))
            return redirect('users:group_list')
    else:
        form = GroupForm()
    
    return render(request, 'users/group_form.html', {'form': form})

@login_required
@permission_required('auth.change_group')
def group_edit(request, pk):
    """
    Edit an existing group
    """
    group = get_object_or_404(Group, pk=pk)
    
    if request.method == 'POST':
        form = GroupForm(request.POST, instance=group)
        if form.is_valid():
            form.save()
            messages.success(request, _('Group updated successfully.'))
            return redirect('users:group_list')
    else:
        form = GroupForm(instance=group)
    
    return render(request, 'users/group_form.html', {'form': form, 'group': group})

@login_required
@permission_required('auth.delete_group')
def group_delete(request, pk):
    """
    Delete a group
    """
    group = get_object_or_404(Group, pk=pk)
    
    if request.method == 'POST':
        group.delete()
        messages.success(request, _('Group deleted successfully.'))
        return redirect('users:group_list')
    
    return render(request, 'users/group_confirm_delete.html', {'group': group})
